package action

import (
	"time"
)

// POItemOptimizationConfig contains configuration for purchase order item optimization
type POItemOptimizationConfig struct {
	// MaxConcurrentPriceRequests limits the number of concurrent price after rebate requests
	MaxConcurrentPriceRequests int
	
	// VendorBatchSize is the maximum number of vendors to fetch in a single batch
	VendorBatchSize int
	
	// PriceRequestTimeout is the timeout for price after rebate requests
	PriceRequestTimeout time.Duration
	
	// EnableVendorCache enables caching of vendor information
	EnableVendorCache bool
	
	// VendorCacheExpiry is the expiry time for vendor cache entries
	VendorCacheExpiry time.Duration
	
	// EnableAsyncPriceCalculation enables asynchronous price calculation
	EnableAsyncPriceCalculation bool
	
	// MaxRetryAttempts is the maximum number of retry attempts for failed requests
	MaxRetryAttempts int
	
	// RetryDelay is the delay between retry attempts
	RetryDelay time.Duration
}

// DefaultPOItemOptimizationConfig returns the default configuration
func DefaultPOItemOptimizationConfig() *POItemOptimizationConfig {
	return &POItemOptimizationConfig{
		MaxConcurrentPriceRequests:  5,
		VendorBatchSize:            50,
		PriceRequestTimeout:        30 * time.Second,
		EnableVendorCache:          true,
		VendorCacheExpiry:          5 * time.Minute,
		EnableAsyncPriceCalculation: true,
		MaxRetryAttempts:           3,
		RetryDelay:                 100 * time.Millisecond,
	}
}

// POItemMetrics contains metrics for monitoring performance
type POItemMetrics struct {
	TotalItems              int
	VendorCacheHits         int
	VendorCacheMisses       int
	SuccessfulPriceRequests int
	FailedPriceRequests     int
	TotalProcessingTime     time.Duration
	AverageProcessingTime   time.Duration
}

// NewPOItemMetrics creates a new metrics instance
func NewPOItemMetrics() *POItemMetrics {
	return &POItemMetrics{}
}

// CalculateAverageProcessingTime calculates the average processing time per item
func (m *POItemMetrics) CalculateAverageProcessingTime() {
	if m.TotalItems > 0 {
		m.AverageProcessingTime = m.TotalProcessingTime / time.Duration(m.TotalItems)
	}
}

// GetCacheHitRate returns the cache hit rate as a percentage
func (m *POItemMetrics) GetCacheHitRate() float64 {
	total := m.VendorCacheHits + m.VendorCacheMisses
	if total == 0 {
		return 0
	}
	return float64(m.VendorCacheHits) / float64(total) * 100
}

// GetSuccessRate returns the success rate for price requests as a percentage
func (m *POItemMetrics) GetSuccessRate() float64 {
	total := m.SuccessfulPriceRequests + m.FailedPriceRequests
	if total == 0 {
		return 0
	}
	return float64(m.SuccessfulPriceRequests) / float64(total) * 100
}
