package integration

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	sendEmail = "POST::/email"
)

var (
	mailProcessor *client.Client
)

func InitMailProcessorClient(database *mongo.Database) {
	const mailV1 = "/integration/mail-processor/v1"
	headers := map[string]string{"Authorization": conf.Config.APIKey}
	mailProcessor = client.NewClient(mailV1, headers, 0)
	mailProcessor.WithDatabase(database)
	mailProcessor.WithConfiguration(
		[]client.Configuration{
			{
				Path: sendEmail,
				Name: "send_email",
			},
		}...)
}

func SendEmail(opts ...client.APIOption) *common.APIResponse {
	requestOption := mailProcessor.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := mailProcessor.WithRequest(sendEmail, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
