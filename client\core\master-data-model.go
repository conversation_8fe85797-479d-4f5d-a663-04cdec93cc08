package core

type Province struct {
	Code       string  `json:"code" bson:"code,omitempty"`
	Name       string  `json:"name" bson:"name,omitempty"`
	RegionCode *string `json:"regionCode,omitempty" bson:"region_code,omitempty"`
	RegionName *string `json:"regionName,omitempty" bson:"region_name,omitempty"`
}

type ProvinceRes struct {
	Status    string      `json:"status"`
	Data      []*Province `json:"data,omitempty"`
	Message   string      `json:"message"`
	ErrorCode string      `json:"errorCode,omitempty"`
	Total     int64       `json:"total,omitempty"`
}

// RegionResponse
type RegionResponse struct {
	Status    string    `json:"status"`
	Message   string    `json:"message"`
	ErrorCode string    `json:"errorCode,omitempty"`
	Data      []*Region `json:"data"`
}

// Province ...
type Region struct {
	RegionID      int64    `json:"regionID,omitempty" bson:"region_id,omitempty"`
	Code          string   `json:"code,omitempty" bson:"code,omitempty"`
	Name          string   `json:"name" bson:"name,omitempty"`
	ProvinceCodes []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
}
