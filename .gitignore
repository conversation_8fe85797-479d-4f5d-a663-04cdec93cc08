# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
.env
# Dependency directories (remove the comment below to include it)
# vendor/
.idea
# Go workspace file
go.work

DockerFile.local

.vscode
.vscode/
__debug_bin**

install.sh
run.sh
makefile
tmp