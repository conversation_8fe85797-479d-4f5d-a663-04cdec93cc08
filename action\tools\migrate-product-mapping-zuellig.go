package tools

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

var (
	listMappingProduct = []zuelligMappingProduct{
		{ProductID: 55654, VendorID: 1000315, ZuelligID: 21221249},
		{ProductID: 2582, VendorID: 1000315, ZuelligID: 21151886},
		{ProductID: 7141, VendorID: 1000315, ZuelligID: 21151884},
		{ProductID: 2302, VendorID: 1000315, ZuelligID: 21151885},
		{ProductID: 56460, VendorID: 1000315, ZuelligID: 21199627},
		{ProductID: 3482, VendorID: 1000315, ZuelligID: 21214804},
		{ProductID: 11991, VendorID: 1000315, ZuelligID: 21084198},
		{ProductID: 1184, VendorID: 1000315, ZuelligID: 21193588},
		{ProductID: 13165, VendorID: 1000315, ZuelligID: 23208221},
		{ProductID: 87795, VendorID: 1000315, ZuelligID: 21208298},
		{ProductID: 87793, VendorID: 1000315, ZuelligID: 21208297},
		{ProductID: 10360, VendorID: 1000315, ZuelligID: 21205473},
		{ProductID: 1733, VendorID: 1000315, ZuelligID: 21203240},
		{ProductID: 5286, VendorID: 1000315, ZuelligID: 21207309},
		{ProductID: 3685, VendorID: 1000315, ZuelligID: 21200229},
		{ProductID: 8721, VendorID: 1000315, ZuelligID: 21215578},
		{ProductID: 9792, VendorID: 1000315, ZuelligID: 21200276},
		{ProductID: 8720, VendorID: 1000315, ZuelligID: 21200273},
		{ProductID: 4954, VendorID: 1000315, ZuelligID: 21200274},
		{ProductID: 10652, VendorID: 1000315, ZuelligID: 21200275},
		{ProductID: 2136, VendorID: 1000315, ZuelligID: 21200226},
		{ProductID: 8246, VendorID: 1000315, ZuelligID: 21207290},
		{ProductID: 2815, VendorID: 1000315, ZuelligID: 21200262},
		{ProductID: 2657, VendorID: 1000315, ZuelligID: 21193589},
		{ProductID: 1575, VendorID: 1000315, ZuelligID: 21193580},
		{ProductID: 1593, VendorID: 1000315, ZuelligID: 21193585},
		{ProductID: 78339, VendorID: 1000315, ZuelligID: 21178404},
		{ProductID: 78352, VendorID: 1000315, ZuelligID: 21178405},
		{ProductID: 6728, VendorID: 1000315, ZuelligID: 21084470},
		{ProductID: 10014, VendorID: 1000315, ZuelligID: 21083777},
		{ProductID: 6104, VendorID: 1000315, ZuelligID: 21203248},
		{ProductID: 16428, VendorID: 1000315, ZuelligID: 21208295},
		{ProductID: 16427, VendorID: 1000315, ZuelligID: 21208296},
		{ProductID: 2820, VendorID: 1000315, ZuelligID: 21206707},
		{ProductID: 4165, VendorID: 1000315, ZuelligID: 21084195},
		{ProductID: 6762, VendorID: 1000315, ZuelligID: 21083986},
		{ProductID: 11315, VendorID: 1000315, ZuelligID: 21083987},
		{ProductID: 3044, VendorID: 1000315, ZuelligID: 21085039},
		{ProductID: 1260, VendorID: 1000315, ZuelligID: 21199218},
		{ProductID: 81495, VendorID: 1000315, ZuelligID: 21219294},
		{ProductID: 81500, VendorID: 1000315, ZuelligID: 21219295},
		{ProductID: 4099, VendorID: 1000315, ZuelligID: 21172494},
		{ProductID: 1551, VendorID: 1000315, ZuelligID: 21084328},
		{ProductID: 1550, VendorID: 1000315, ZuelligID: 21084327},
		{ProductID: 3615, VendorID: 1000315, ZuelligID: 21193942},
		{ProductID: 8029, VendorID: 1000315, ZuelligID: 21193944},
		{ProductID: 1548, VendorID: 1000315, ZuelligID: 21193945},
		{ProductID: 5386, VendorID: 1000315, ZuelligID: 21193324},
		{ProductID: 2613, VendorID: 1000315, ZuelligID: 21193946},
		{ProductID: 5968, VendorID: 1000315, ZuelligID: 21193947},
		{ProductID: 2168, VendorID: 1000315, ZuelligID: 21193948},
		{ProductID: 7291, VendorID: 1000315, ZuelligID: 21206070},
		{ProductID: 1591, VendorID: 1000315, ZuelligID: 21202320},
		{ProductID: 8124, VendorID: 1000315, ZuelligID: 21216284},
		{ProductID: 7794, VendorID: 1000315, ZuelligID: 21219914},
		{ProductID: 11292, VendorID: 1000315, ZuelligID: 21182214},
		{ProductID: 7209, VendorID: 1000315, ZuelligID: 21219493},
		{ProductID: 50974, VendorID: 1000315, ZuelligID: 21205040},
		{ProductID: 1584, VendorID: 1000315, ZuelligID: 21193949},
		{ProductID: 5390, VendorID: 1000315, ZuelligID: 21084998},
		{ProductID: 78130, VendorID: 1000315, ZuelligID: 21199674},
		{ProductID: 78132, VendorID: 1000315, ZuelligID: 21199675},
		{ProductID: 2169, VendorID: 1000315, ZuelligID: 21199677},
		{ProductID: 1119, VendorID: 1000315, ZuelligID: 21194035},
		{ProductID: 3134, VendorID: 1000315, ZuelligID: 21178052},
		{ProductID: 1692, VendorID: 1000315, ZuelligID: 21178051},
		{ProductID: 1255, VendorID: 1000315, ZuelligID: 21193950},
		{ProductID: 1576, VendorID: 1000315, ZuelligID: 21193951},
		{ProductID: 1411, VendorID: 1000315, ZuelligID: 21204922},
		{ProductID: 1594, VendorID: 1000315, ZuelligID: 21181840},
		{ProductID: 8589, VendorID: 1000315, ZuelligID: 21084306},
		{ProductID: 53199, VendorID: 1000315, ZuelligID: 21084999},
		{ProductID: 76928, VendorID: 1000315, ZuelligID: 21224204},
		{ProductID: 55947, VendorID: 1000315, ZuelligID: 21174203},
		{ProductID: 1218, VendorID: 1000315, ZuelligID: 21166510},
		{ProductID: 6619, VendorID: 1000315, ZuelligID: 21203167},
		{ProductID: 2833, VendorID: 1000315, ZuelligID: 21166488},
		{ProductID: 1368, VendorID: 1000315, ZuelligID: 21221220},
		{ProductID: 1592, VendorID: 1000315, ZuelligID: 21219891},
		{ProductID: 2475, VendorID: 1000315, ZuelligID: 21219892},
		{ProductID: 6931, VendorID: 1000315, ZuelligID: 21219922},
		{ProductID: 6932, VendorID: 1000315, ZuelligID: 21221032},
		{ProductID: 3308, VendorID: 1000315, ZuelligID: 21170235},
		{ProductID: 4001, VendorID: 1000315, ZuelligID: 21221226},
		{ProductID: 6808, VendorID: 1000315, ZuelligID: 21221252},
		{ProductID: 7076, VendorID: 1000315, ZuelligID: 21193597},
		{ProductID: 6520, VendorID: 1000315, ZuelligID: 21193598},
		{ProductID: 5458, VendorID: 1000315, ZuelligID: 21192056},
		{ProductID: 2563, VendorID: 1000315, ZuelligID: 21186960},
		{ProductID: 2836, VendorID: 1000315, ZuelligID: 21200069},
		{ProductID: 69171, VendorID: 1000315, ZuelligID: 21175900},
		{ProductID: 4133, VendorID: 1000315, ZuelligID: 21182135},
		{ProductID: 6800, VendorID: 1000315, ZuelligID: 21209026},
		{ProductID: 1772, VendorID: 1000315, ZuelligID: 21084296},
		{ProductID: 3765, VendorID: 1000315, ZuelligID: 21084284},
		{ProductID: 2678, VendorID: 1000315, ZuelligID: 21084295},
		{ProductID: 13906, VendorID: 1000315, ZuelligID: 21084294},
		{ProductID: 8596, VendorID: 1000315, ZuelligID: 21084313},
		{ProductID: 10552, VendorID: 1000315, ZuelligID: 21172729},
		{ProductID: 6309, VendorID: 1000315, ZuelligID: 21084311},
		{ProductID: 2679, VendorID: 1000315, ZuelligID: 21180332},
		{ProductID: 2839, VendorID: 1000315, ZuelligID: 21180333},
		{ProductID: 4838, VendorID: 1000315, ZuelligID: 21164542},
		{ProductID: 3140, VendorID: 1000315, ZuelligID: 21083772},
		{ProductID: 1217, VendorID: 1000315, ZuelligID: 21173703},
		{ProductID: 14132, VendorID: 1000315, ZuelligID: 21219905},
		{ProductID: 8231, VendorID: 1000315, ZuelligID: 21221018},
		{ProductID: 7787, VendorID: 1000315, ZuelligID: 21216728},
		{ProductID: 12400, VendorID: 1000315, ZuelligID: 21219904},
		{ProductID: 1241, VendorID: 1000315, ZuelligID: 21084324},
		{ProductID: 5563, VendorID: 1000315, ZuelligID: 21084325},
		{ProductID: 84579, VendorID: 1000315, ZuelligID: 21215142},
		{ProductID: 65050, VendorID: 1000315, ZuelligID: 21215141},
		{ProductID: 2730, VendorID: 1000315, ZuelligID: 21207470},
		{ProductID: 3497, VendorID: 1000315, ZuelligID: 21083734},
		{ProductID: 4644, VendorID: 1000315, ZuelligID: 21182828},
		{ProductID: 1421, VendorID: 1000315, ZuelligID: 21200468},
		{ProductID: 12040, VendorID: 1000315, ZuelligID: 21204905},
		{ProductID: 1380, VendorID: 1000315, ZuelligID: 21199683},
		{ProductID: 1366, VendorID: 1000315, ZuelligID: 21199684},
		{ProductID: 1410, VendorID: 1000315, ZuelligID: 21204906},
		{ProductID: 8798, VendorID: 1000315, ZuelligID: 21083911},
		{ProductID: 1549, VendorID: 1000315, ZuelligID: 21193952},
		{ProductID: 4657, VendorID: 1000315, ZuelligID: 21168876},
		{ProductID: 1694, VendorID: 1000315, ZuelligID: 21084336},
		{ProductID: 12113, VendorID: 1000315, ZuelligID: 21193581},
		{ProductID: 1504, VendorID: 1000315, ZuelligID: 21193592},
		{ProductID: 1505, VendorID: 1000315, ZuelligID: 21193593},
		{ProductID: 3083, VendorID: 1000315, ZuelligID: 21193583},
		{ProductID: 11466, VendorID: 1000315, ZuelligID: 21193567},
		{ProductID: 3354, VendorID: 1000315, ZuelligID: 21221236},
		{ProductID: 85929, VendorID: 1000315, ZuelligID: 21084329},
		{ProductID: 81223, VendorID: 1000315, ZuelligID: 21219982},
		{ProductID: 81217, VendorID: 1000315, ZuelligID: 21219980},
		{ProductID: 81224, VendorID: 1000315, ZuelligID: 21219984},
		{ProductID: 81222, VendorID: 1000315, ZuelligID: 21219985},
		{ProductID: 70002, VendorID: 1000315, ZuelligID: 21203287},
		{ProductID: 1535, VendorID: 1000315, ZuelligID: 21203286},
		{ProductID: 80750, VendorID: 1000315, ZuelligID: 21084209},
		{ProductID: 80752, VendorID: 1000315, ZuelligID: 21084208},
		{ProductID: 80754, VendorID: 1000315, ZuelligID: 21084201},
		{ProductID: 80748, VendorID: 1000315, ZuelligID: 21084221},
		{ProductID: 8217, VendorID: 1000315, ZuelligID: 21174442},
		{ProductID: 10117, VendorID: 1000315, ZuelligID: 21200213},
		{ProductID: 6417, VendorID: 1000315, ZuelligID: 21084297},
		{ProductID: 78299, VendorID: 1000315, ZuelligID: 21084216},
		{ProductID: 13303, VendorID: 1000315, ZuelligID: 21193956},
		{ProductID: 1415, VendorID: 1000315, ZuelligID: 21083955},
		{ProductID: 1422, VendorID: 1000315, ZuelligID: 21193587},
		{ProductID: 8954, VendorID: 1000315, ZuelligID: 21203243},
		{ProductID: 14127, VendorID: 1000315, ZuelligID: 21221241},
		{ProductID: 14129, VendorID: 1000315, ZuelligID: 21221019},
		{ProductID: 1783, VendorID: 1000315, ZuelligID: 21221231},
		{ProductID: 10116, VendorID: 1000315, ZuelligID: 21221232},
		{ProductID: 14131, VendorID: 1000315, ZuelligID: 21084113},
		{ProductID: 9215, VendorID: 1000315, ZuelligID: 21183704},
		{ProductID: 10955, VendorID: 1000315, ZuelligID: 21183705},
		{ProductID: 11501, VendorID: 1000315, ZuelligID: 21183703},
		{ProductID: 78136, VendorID: 1000315, ZuelligID: 21084980},
		{ProductID: 1392, VendorID: 1000315, ZuelligID: 21199689},
		{ProductID: 55946, VendorID: 1000315, ZuelligID: 21199690},
		{ProductID: 10380, VendorID: 1000315, ZuelligID: 21084446},
		{ProductID: 78157, VendorID: 1000315, ZuelligID: 21199693},
		{ProductID: 67088, VendorID: 1000315, ZuelligID: 21193599},
		{ProductID: 54238, VendorID: 1000315, ZuelligID: 21208193},
		{ProductID: 8334, VendorID: 1000315, ZuelligID: 21193584},
		{ProductID: 2051, VendorID: 1000315, ZuelligID: 21193569},
		{ProductID: 78009, VendorID: 1000315, ZuelligID: 21163466},
		{ProductID: 78001, VendorID: 1000315, ZuelligID: 21084471},
		{ProductID: 78159, VendorID: 1000315, ZuelligID: 21199694},
		{ProductID: 11620, VendorID: 1000315, ZuelligID: 21083719},
		{ProductID: 1209, VendorID: 1000315, ZuelligID: 21173030},
		{ProductID: 1210, VendorID: 1000315, ZuelligID: 21172929},
		{ProductID: 3033, VendorID: 1000315, ZuelligID: 21193117},
		{ProductID: 8743, VendorID: 1000315, ZuelligID: 21193115},
		{ProductID: 3034, VendorID: 1000315, ZuelligID: 21193116},
		{ProductID: 92811, VendorID: 1000315, ZuelligID: 21193957},
		{ProductID: 92814, VendorID: 1000315, ZuelligID: 21193958},
		{ProductID: 11344, VendorID: 1000315, ZuelligID: 21193960},
		{ProductID: 1716, VendorID: 1000315, ZuelligID: 21083956},
		{ProductID: 2150, VendorID: 1000315, ZuelligID: 21083957},
		{ProductID: 1577, VendorID: 1000315, ZuelligID: 21193966},
		{ProductID: 78354, VendorID: 1000315, ZuelligID: 21180870},
		{ProductID: 1715, VendorID: 1000315, ZuelligID: 21221251},
		{ProductID: 1435, VendorID: 1000315, ZuelligID: 21221174},
		{ProductID: 1427, VendorID: 1000315, ZuelligID: 21221253},
		{ProductID: 13247, VendorID: 1000315, ZuelligID: 21193967},
		{ProductID: 11698, VendorID: 1000315, ZuelligID: 21207476},
		{ProductID: 2928, VendorID: 1000315, ZuelligID: 21207478},
		{ProductID: 15757, VendorID: 1000315, ZuelligID: 21192624},
		{ProductID: 4751, VendorID: 1000315, ZuelligID: 21221238},
		{ProductID: 7583, VendorID: 1000315, ZuelligID: 21206626},
		{ProductID: 10379, VendorID: 1000315, ZuelligID: 21199216},
		{ProductID: 1510, VendorID: 1000315, ZuelligID: 21221747},
		{ProductID: 1589, VendorID: 1000315, ZuelligID: 21174470},
		{ProductID: 6614, VendorID: 1000315, ZuelligID: 21174471},
		{ProductID: 5877, VendorID: 1000315, ZuelligID: 21174449},
		{ProductID: 5993, VendorID: 1000315, ZuelligID: 21221177},
		{ProductID: 2521, VendorID: 1000315, ZuelligID: 21221176},
		{ProductID: 3948, VendorID: 1000315, ZuelligID: 21199699},
		{ProductID: 4053, VendorID: 1000315, ZuelligID: 21199700},
		{ProductID: 3509, VendorID: 1000315, ZuelligID: 21207479},
		{ProductID: 9455, VendorID: 1000315, ZuelligID: 21221237},
		{ProductID: 5883, VendorID: 1000315, ZuelligID: 21221250},
		{ProductID: 6555, VendorID: 1000315, ZuelligID: 21221474},
		{ProductID: 4382, VendorID: 1000315, ZuelligID: 21222803},
		{ProductID: 2483, VendorID: 1000315, ZuelligID: 21221221},
		{ProductID: 8359, VendorID: 1000315, ZuelligID: 21221428},
		{ProductID: 8538, VendorID: 1000315, ZuelligID: 21200261},
		{ProductID: 2236, VendorID: 1000315, ZuelligID: 21221233},
		{ProductID: 10440, VendorID: 1000315, ZuelligID: 21221228},
		{ProductID: 10441, VendorID: 1000315, ZuelligID: 21221234},
		{ProductID: 5261, VendorID: 1000315, ZuelligID: 21221227},
		{ProductID: 1337, VendorID: 1000315, ZuelligID: 21221222},
		{ProductID: 1815, VendorID: 1000315, ZuelligID: 21221223},
		{ProductID: 5094, VendorID: 1000315, ZuelligID: 21224168},
		{ProductID: 2460, VendorID: 1000315, ZuelligID: 23244254},
		{ProductID: 5282, VendorID: 1000315, ZuelligID: 23170271},
		{ProductID: 13700, VendorID: 1000315, ZuelligID: 23242925},
		{ProductID: 2060, VendorID: 1000315, ZuelligID: 23144997},
		{ProductID: 10438, VendorID: 1000315, ZuelligID: 23170280},
		{ProductID: 2611, VendorID: 1000315, ZuelligID: 21221475},
		{ProductID: 3866, VendorID: 1000315, ZuelligID: 21221180},
		{ProductID: 13173, VendorID: 1000315, ZuelligID: 23208223},
		{ProductID: 13170, VendorID: 1000315, ZuelligID: 23208222},
		{ProductID: 1139, VendorID: 1000315, ZuelligID: 21221014},
		{ProductID: 3888, VendorID: 1000315, ZuelligID: 21221175},
		{ProductID: 12597, VendorID: 1000315, ZuelligID: 21203241},
		{ProductID: 1336, VendorID: 1000315, ZuelligID: 21221015},
		{ProductID: 2809, VendorID: 1000315, ZuelligID: 21221017},
		{ProductID: 5880, VendorID: 1000315, ZuelligID: 21221179},
		{ProductID: 8540, VendorID: 1000315, ZuelligID: 21207473},
		{ProductID: 2110, VendorID: 1000315, ZuelligID: 21221178},
		{ProductID: 97131, VendorID: 1000315, ZuelligID: 23242924},
		{ProductID: 9390, VendorID: 1000315, ZuelligID: 21207475},
		{ProductID: 6916, VendorID: 1000315, ZuelligID: 21207477},
		{ProductID: 98917, VendorID: 1000315, ZuelligID: 21207292},
		{ProductID: 90328, VendorID: 1000315, ZuelligID: 21193325},
		{ProductID: 98398, VendorID: 1000315, ZuelligID: 21193953},
		{ProductID: 14605, VendorID: 1000315, ZuelligID: 21199679},
		{ProductID: 10141, VendorID: 1000315, ZuelligID: 21086593},
		{ProductID: 7499, VendorID: 1000315, ZuelligID: 21207419},
		{ProductID: 2762, VendorID: 1000315, ZuelligID: 21204923},
		{ProductID: 90365, VendorID: 1000315, ZuelligID: 21216729},
		{ProductID: 106802, VendorID: 1000315, ZuelligID: 21084057},
		{ProductID: 106791, VendorID: 1000315, ZuelligID: 21183120},
		{ProductID: 5895, VendorID: 1000315, ZuelligID: 21206627},
		{ProductID: 96687, VendorID: 1000315, ZuelligID: 21193586},
		{ProductID: 107792, VendorID: 1000315, ZuelligID: 21193959},
		{ProductID: 107261, VendorID: 1000315, ZuelligID: 21221254},
		{ProductID: 98938, VendorID: 1000315, ZuelligID: 21207303},
		{ProductID: 96682, VendorID: 1000315, ZuelligID: 21193566},
		{ProductID: 108167, VendorID: 1000315, ZuelligID: 21221307},
		{ProductID: 107845, VendorID: 1000315, ZuelligID: 21083958},
		{ProductID: 107395, VendorID: 1000315, ZuelligID: 21203372},
		{ProductID: 106769, VendorID: 1000315, ZuelligID: 21166497},
		{ProductID: 104340, VendorID: 1000315, ZuelligID: 21193568},
		{ProductID: 108851, VendorID: 1000315, ZuelligID: 21219494},
		{ProductID: 107847, VendorID: 1000315, ZuelligID: 21178161},
		{ProductID: 107324, VendorID: 1000315, ZuelligID: 21224345},
		{ProductID: 107326, VendorID: 1000315, ZuelligID: 21219575},
		{ProductID: 106800, VendorID: 1000315, ZuelligID: 21084056},
		{ProductID: 2974, VendorID: 1000315, ZuelligID: 21207412},
		{ProductID: 109051, VendorID: 1000315, ZuelligID: 21224400},
		{ProductID: 108846, VendorID: 1000315, ZuelligID: 21088719},
		{ProductID: 109327, VendorID: 1000315, ZuelligID: 21182831},
		{ProductID: 108164, VendorID: 1000315, ZuelligID: 21150361},
		{ProductID: 107790, VendorID: 1000315, ZuelligID: 21193961},
		{ProductID: 107867, VendorID: 1000315, ZuelligID: 21206706},
		{ProductID: 108849, VendorID: 1000315, ZuelligID: 21166277},
		{ProductID: 106767, VendorID: 1000315, ZuelligID: 21166495},
		{ProductID: 109036, VendorID: 1000315, ZuelligID: 21221205},
		{ProductID: 108374, VendorID: 1000315, ZuelligID: 21221206},
		{ProductID: 108369, VendorID: 1000315, ZuelligID: 21222169},
		{ProductID: 108298, VendorID: 1000315, ZuelligID: 21221429},
		{ProductID: 108171, VendorID: 1000315, ZuelligID: 21219918},
		{ProductID: 107263, VendorID: 1000315, ZuelligID: 21197744},
		{ProductID: 109340, VendorID: 1000315, ZuelligID: 21207800},
		{ProductID: 107813, VendorID: 1000315, ZuelligID: 21221009},
		{ProductID: 107310, VendorID: 1000315, ZuelligID: 21153573},
		{ProductID: 108982, VendorID: 1000315, ZuelligID: 21198991},
		{ProductID: 106773, VendorID: 1000315, ZuelligID: 21206628},
		{ProductID: 96676, VendorID: 1000315, ZuelligID: 21183734},
		{ProductID: 9407, VendorID: 1000315, ZuelligID: 21193955},
		{ProductID: 8771, VendorID: 1000315, ZuelligID: 21207474},
		{ProductID: 107787, VendorID: 1000315, ZuelligID: 21193963},
		{ProductID: 107276, VendorID: 1000315, ZuelligID: 21085000},
		{ProductID: 98953, VendorID: 1000315, ZuelligID: 21215552},
		{ProductID: 108300, VendorID: 1000315, ZuelligID: 21182458},
		{ProductID: 108140, VendorID: 1000315, ZuelligID: 21200485},
	}
)

const ZUELLIG = "ZUELLIG"

func MigrateProductMappingForZuellig(_ sdk.APIRequest, res sdk.APIResponder) error {
	go migrateZuelligMappingProduct()
	return res.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "request migrate zullig mapping product success",
	})
}

type zuelligMappingProduct struct {
	ProductID    int64
	VendorID     int64
	ZuelligID    int64
	ErrorMessage string
}

func migrateZuelligMappingProduct() {
	log.Println("start migrate zullig mapping product")
	defer log.Println("end migrate zullig mapping product")

	collectError := make([]zuelligMappingProduct, 0)

loopZuelligMapping:
	for _, zuelligMapping := range listMappingProduct {
		if zuelligMapping.ProductID == 0 || zuelligMapping.VendorID == 0 || zuelligMapping.ZuelligID == 0 {
			continue
		}

		query := model.ProductMapping{
			ProductID: zuelligMapping.ProductID,
		}
		productMappingQuery := model.ProductMappingDB.QueryOne(query)

		if productMappingQuery.Status != common.APIStatus.Ok {
			zuelligMapping.ErrorMessage = fmt.Sprint("product mapping not found, productID :", zuelligMapping.ProductID)
			collectError = append(collectError, zuelligMapping)
			continue loopZuelligMapping
		}

		productMapping := productMappingQuery.Data.([]*model.ProductMapping)[0]
		vendorCode, ok := getVendorCodeVendorID(zuelligMapping.VendorID)
		if !ok {
			zuelligMapping.ErrorMessage = fmt.Sprintf("cannot get vendor code for vendorID: %d, productID: %d", zuelligMapping.VendorID, zuelligMapping.ProductID)
			collectError = append(collectError, zuelligMapping)
			continue loopZuelligMapping
		}

		// không có vendor mapping
		if productMapping.ProductVendors == nil || len(*productMapping.ProductVendors) == 0 {

			productVendorName := formatProductVendorForZuelligProduct(zuelligMapping.ZuelligID)
			productVendor := model.ProductVendor{
				VendorCode:         vendorCode,
				ProductVendorNames: []string{productVendorName},
				ProductVendorSlugs: []string{strings.ToLower(productVendorName)},
			}

			productMapping.ProductVendors = &[]model.ProductVendor{productVendor}
			updateResp := model.ProductMappingDB.UpdateOne(query, productMapping)

			// update
			if updateResp.Status != common.APIStatus.Ok {
				zuelligMapping.ErrorMessage = fmt.Sprintf("cannot update product mapping for productID: %d, vendorID: %d, zuelligID: %d",
					zuelligMapping.ProductID, zuelligMapping.VendorID, zuelligMapping.ZuelligID)
				collectError = append(collectError, zuelligMapping)
				continue loopZuelligMapping
			}
		} else {
			for i, productVendor := range *productMapping.ProductVendors {
				if productVendor.VendorCode == vendorCode {
					// skip đã có vendor mapping code với zuellig
					for _, productVendor := range productVendor.ProductVendorNames {
						if containValidZuelligMappingCode(productVendor) {
							continue loopZuelligMapping
						}
					}

					//trường hợp không có product code mapping với zuellig
					productVendorName := formatProductVendorForZuelligProduct(zuelligMapping.ZuelligID)
					productVendor.ProductVendorNames = append(productVendor.ProductVendorNames, productVendorName)
					productVendor.ProductVendorSlugs = append(productVendor.ProductVendorSlugs, strings.ToLower(productVendorName))
					(*productMapping.ProductVendors)[i] = productVendor

					// update
					updateResp := model.ProductMappingDB.UpdateOne(query, productMapping)
					if updateResp.Status != common.APIStatus.Ok {
						zuelligMapping.ErrorMessage = fmt.Sprintf("cannot update product mapping for productID: %d, vendorID: %d, zuelligID: %d",
							zuelligMapping.ProductID, zuelligMapping.VendorID, zuelligMapping.ZuelligID)
						collectError = append(collectError, zuelligMapping)
					}
					continue loopZuelligMapping
				}
			}
			productVendorName := formatProductVendorForZuelligProduct(zuelligMapping.ZuelligID)
			productVendor := model.ProductVendor{
				VendorCode:         vendorCode,
				ProductVendorNames: []string{productVendorName},
				ProductVendorSlugs: []string{strings.ToLower(productVendorName)},
			}
			*productMapping.ProductVendors = append(*productMapping.ProductVendors, productVendor)
			// update
			updateResp := model.ProductMappingDB.UpdateOne(query, productMapping)
			if updateResp.Status != common.APIStatus.Ok {
				zuelligMapping.ErrorMessage = fmt.Sprintf("cannot update product mapping for productID: %d, vendorID: %d, zuelligID: %d",
					zuelligMapping.ProductID, zuelligMapping.VendorID, zuelligMapping.ZuelligID)
				collectError = append(collectError, zuelligMapping)
			}
		}
	}

	if len(collectError) > 0 {
		log.Printf("%+v\n", collectError)
	}
}

func getVendorCodeVendorID(id int64) (string, bool) {
	option := client.APIOption{
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerID":    fmt.Sprint(id),
		},
	}

	vendorResp := seller.GetSellers(option)
	if vendorResp.Status != common.APIStatus.Ok {
		return "", false
	}

	vendor := vendorResp.Data.([]*model.Seller)[0]
	return vendor.Code, true
}

func formatProductVendorForZuelligProduct(zulligID int64) string {
	return fmt.Sprintf("ZUELLIG:%d", zulligID)
}

func containValidZuelligMappingCode(s string) bool {
	sl := strings.Split(s, ":")
	if len(sl) != 2 {
		return false
	}

	if sl[0] != ZUELLIG {
		return false
	}

	if _, err := strconv.Atoi(sl[1]); err != nil {
		return false
	}
	return true
}
