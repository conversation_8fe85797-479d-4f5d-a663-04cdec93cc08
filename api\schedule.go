package api

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson"
)

// Function to add a schedule for vendor rebates
// AddScheduleVendorRebate handles the addition of vendor rebate schedules.
// It retrieves configuration data from the request, processes each configuration,
// and upserts the schedule configuration into the database. The function responds
// with the results of the upsert operations.
//
// Parameters:
//   - req: sdk.APIRequest containing the request data.
//   - res: sdk.APIResponder used to send the response.
//
// Returns:
//   - error: An error if the content retrieval or response fails.
func AddScheduleVendorRebate(req sdk.APIRequest, res sdk.APIResponder) error {
	// Declare a slice to hold configuration data
	var configs []bson.M
	// Retrieve content from the request and store it in configs
	err := req.GetContent(&configs)
	if err != nil {
		// Respond with an error if content retrieval fails
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Data:    []string{err.Error()},
			Message: err.Error(),
		})
	}

	// Initialize a slice to store API response results
	results := make([]*common.APIResponse, 0, len(configs))

	// Get the database for configuration schedule rebate results
	db := model.ConfigSchedule.GetConfigDB()

	// Iterate over each configuration in the configs slice
	for _, config := range configs {
		// Extract the topic from the configuration
		topic, ok := config["topic"].(string)
		if !ok || topic == "" {
			continue
		}

		// Create filter and updater objects for the schedule configuration
		filter := schedule.Config{
			Topic: topic,
		}
		updater := schedule.Config{
			Topic: topic,
		}
		// Parse the next run time if provided in the configuration
		if next := config["next_run"].(string); next != "" {
			t, _ := time.Parse(time.RFC3339, next)
			updater.NextRun = &t
		}
		// Upsert the configuration into the database
		result := db.Upsert(filter, updater)
		// Append successful results to the results slice
		if result.Status == common.APIStatus.Ok {
			results = append(results, result)
		}
	}

	// Respond with the accumulated results
	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   results,
	})
}
