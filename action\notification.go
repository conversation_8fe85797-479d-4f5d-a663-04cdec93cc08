package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/integration"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/sso"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// CreateNotificationVendor creates a notification for a vendor based on the provided vendor code and notification data.
// It performs the following steps:
// 1. Retrieves vendor information using the provided vendor code.
// 2. Validates the vendor's entity ID.
// 3. Retrieves all members associated with the vendor's entity ID.
// 4. Sets the notification data's receiver type, username, and entity ID.
// 5. Iterates through the members and creates a notification for each member.
//
// Parameters:
// - vendorCode: A string representing the vendor's code.
// - data: A model.Notification object containing the notification data.
//
// Returns:
// - A pointer to a common.APIResponse object indicating the result of the operation.
func CreateNotificationVendor(vendorCode string, data model.Notification) *common.APIResponse {

	// Get vendor info
	vendorInfoResp := GetVendorInfo(vendorCode)
	if vendorInfoResp.Status != common.APIStatus.Ok {
		return vendorInfoResp
	}
	vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]
	if vendorInfo.EntityID == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid vendor's entity ID",
			ErrorCode: string(enum.ErrorCodeInvalid.EntityID),
		}
	}

	// Get all member
	memberResp := sso.EntityClient.GetEntityMemberForLegacy(vendorInfo.EntityID)
	if memberResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Notfound any member",
			ErrorCode: string(enum.ErrorCodeInvalid.AccountID),
		}
	}
	members := memberResp.Data

	// Create notification for each member
	data.ReceiverType = &enum.AccountType.VENDOR
	data.Username = vendorInfo.Name
	data.EntityID = vendorInfo.EntityID

	// Create notification for each member
	for _, member := range members {
		data.UserId = member.LinkedAccountID

		notiResp := integration.CreateNotification(client.APIOption{Body: data})
		if notiResp.Status != common.APIStatus.Ok {
			return notiResp
		}
	}

	// Return success
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create notification success",
	}
}

// CreateNotificationForUserAccount creates a notification for a user account.
// It sets the receiver type, user ID, and username in the notification data,
// and then sends the notification using the integration client.
//
// Parameters:
//   - acc: The account for which the notification is being created.
//   - data: The notification data to be sent.
//   - receiverType: The type of account that will receive the notification.
//
// Returns:
//   - *common.APIResponse: The response from the notification creation process.
func CreateNotificationForUserAccount(acc model.Account, data model.Notification, receiverType *enum.AccountTypeValue) *common.APIResponse {
	data.ReceiverType = receiverType
	data.UserId = acc.AccountID
	data.Username = acc.Fullname

	return integration.CreateNotification(client.APIOption{Body: data})
}
