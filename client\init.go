package client

import (
	"encoding/json"
	"errors"
	"reflect"
	"strconv"
	"strings"
	"time"

	sdk_client "gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	defaultTimeout = 30 * time.Second
)

var (
	ErrConfiguration = errors.New("invalid endpoint configuration")
	ErrTimeout       = errors.New("timeout")
	ErrMarshal       = errors.New("marshal error")
	ErrUnmarshal     = errors.New("unmarshal error")
	ErrNotPointer    = errors.New("not a pointer")
	ErrNotSettable   = errors.New("cannot set value of template")
)

var (
	ErrConfigurationCode = "INVALID_CONFIGURATION"
	ErrTimeoutCode       = "TIMEOUT"
	ErrMarshalCode       = "MARSHAL_ERROR"
	ErrUnmarshalCode     = "UNMARSHAL_ERROR"
	ErrNotPointerCode    = "NOT_A_POINTER"
	ErrNotSettableCode   = "NOT_SETTABLE"
	ErrInternalCode      = "INTERNAL_ERROR"
)

type APIOption struct {
	Keys          []string          // for query log client (if not disable SaveLog)
	SaveLog       *bool             // disable logging if SaveLog = false
	Headers       map[string]string // request headers
	Params        map[string]string // request params
	PathVariables map[string]string // path variables
	GetTotal      *bool             // will be merged into Params
	Offset        *int              // will be merged into Params
	Limit         *int              // will be merged into Params
	Q             interface{}       // will be merged into Params; just accept string or obj / map
	Body          interface{}       // request body
}

// get APIOption to single api
func getAPIOption(opts ...APIOption) APIOption {
	initOption := APIOption{
		Keys:          make([]string, 0),
		Headers:       make(map[string]string),
		Params:        make(map[string]string),
		PathVariables: make(map[string]string),
	}

	for _, opt := range opts {
		if opt.Keys != nil {
			initOption.Keys = append(initOption.Keys, opt.Keys...)
		}
		if opt.SaveLog != nil {
			initOption.SaveLog = opt.SaveLog
		}
		if opt.Headers != nil {
			for k, v := range opt.Headers {
				initOption.Headers[k] = v
			}
		}
		if opt.Params != nil {
			for key, v := range opt.Params {
				initOption.Params[key] = v
			}
		}
		if opt.PathVariables != nil {
			for key, v := range opt.PathVariables {
				initOption.PathVariables[key] = v
			}
		}
		if opt.Q != nil {
			if s, ok := opt.Q.(string); ok {
				initOption.Params["q"] = s
			} else {
				b, _ := json.Marshal(opt.Q)
				initOption.Params["q"] = string(b)
			}
		}
		if opt.GetTotal != nil {
			initOption.Params["getTotal"] = strconv.FormatBool(*opt.GetTotal)
		}

		if opt.Offset != nil {
			initOption.Params["offset"] = strconv.Itoa(*opt.Offset)
		}
		if opt.Limit != nil {
			initOption.Params["limit"] = strconv.Itoa(*opt.Limit)
		}
		if opt.Body != nil {
			initOption.Body = opt.Body
		}

	}
	return initOption
}

type Configuration struct {
	Path     string          // api path
	Name     string          // log name
	Database *mongo.Database // log db
	Timeout  time.Duration   // log timeout
}

type Client struct {
	database         *mongo.Database
	host             string
	clientWithoutLog *sdk_client.RestClient
	clients          map[string]*sdk_client.RestClient
	headers          map[string]string
}

func (c *Client) WithDatabase(d ...*mongo.Database) {
	if d != nil {
		c.database = d[0]
	}
}

func (c *Client) WithConfiguration(conf ...Configuration) {
	for _, config := range conf {
		if config.Timeout < 0 {
			config.Timeout = defaultTimeout
		}
		cl := sdk_client.NewRESTClient(c.host, config.Name, config.Timeout, 0, 0)
		if config.Database != nil {
			cl.SetDBLog(config.Database)
		} else if c.database != nil {
			cl.SetDBLog(c.database)
		}
		cl.AcceptHTTPError(true)
		c.clients[config.Path] = cl
	}
}

func (c *Client) WithAPI(api ...string) *sdk_client.RestClient {
	if api == nil {
		return c.clientWithoutLog
	}

	if cl, ok := c.clients[api[0]]; !ok || cl == nil {
		return c.clientWithoutLog
	} else {
		return cl
	}
}

func (c *Client) WithMethod(method ...string) sdk_client.HTTPMethod {
	if method == nil {
		return sdk_client.HTTPMethods.Head
	}

	switch method[0] {
	case "GET":
		return sdk_client.HTTPMethods.Get
	case "POST":
		return sdk_client.HTTPMethods.Post
	case "PUT":
		return sdk_client.HTTPMethods.Put
	case "DELETE":
		return sdk_client.HTTPMethods.Delete
	case "QUERY":
		return "QUERY"
	default:
		return sdk_client.HTTPMethods.Head
	}
}

func (c *Client) WithAPIOption(opts ...APIOption) APIOption {
	return getAPIOption(opts...)
}

func (c *Client) WithRequest(api string, o APIOption, template interface{}) (code int, err error) {
	if err = checkValidTemplate(template); err != nil {
		return 0, err
	}
	cl := c.WithAPI(api)
	if o.SaveLog != nil && !*o.SaveLog {
		cl = c.clientWithoutLog
	}
	parts := strings.Split(api, "::")
	if len(parts) != 2 {
		return 0, ErrConfiguration
	}
	m := c.WithMethod(parts[0])
	formatPath := c.WithReplacePath(parts[1], o.PathVariables)
	result, err := cl.MakeHTTPRequestWithKey(m, c.WithHeader(o.Headers), o.Params, o.Body, formatPath, &o.Keys)
	if err != nil {
		return 500, err
	}
	return result.Code, utils.Pointer.WithResponse(result.Body, template)
}

func (c *Client) WithHeader(header map[string]string) map[string]string {
	headers := make(map[string]string, len(c.headers))
	for k, v := range c.headers {
		headers[k] = v
	}
	for k, v := range header {
		headers[k] = v
	}
	return headers
}

func (c *Client) WithReplacePath(path string, mapVars map[string]string) string {
	vars := flatMap(mapVars)
	newPath := strings.NewReplacer(vars...).Replace(path)
	return newPath
}

// New client instance
func NewClient(path string, headers map[string]string, timeout time.Duration) *Client {
	if path == "" {
		panic("invalid configuration")
	}
	if timeout < 0 {
		timeout = defaultTimeout
	}
	baseURL := conf.Config.APIHost + path
	c := &Client{
		host:    baseURL,
		clients: make(map[string]*sdk_client.RestClient),
	}
	c.clientWithoutLog = sdk_client.NewRESTClient(baseURL, "", timeout, 0, 0)
	c.clientWithoutLog.AcceptHTTPError(true)
	if headers != nil {
		c.headers = headers
	} else {
		c.headers = map[string]string{}
	}
	return c
}

// New client instance customize host
func NewClientWithHost(host, path string, headers map[string]string, timeout time.Duration) *Client {
	if timeout < 0 {
		timeout = defaultTimeout
	}
	baseURL := host + path
	c := &Client{
		host:    baseURL,
		clients: make(map[string]*sdk_client.RestClient),
	}
	c.clientWithoutLog = sdk_client.NewRESTClient(baseURL, "", timeout, 0, 0)
	c.clientWithoutLog.AcceptHTTPError(true)
	if headers != nil {
		c.headers = headers
	} else {
		c.headers = map[string]string{}
	}
	return c
}

func flatMap(m map[string]string) []string {
	// flat map
	vars := make([]string, 0, len(m))
	for k, v := range m {
		vars = append(vars, k, v)
	}
	return vars
}

func checkValidTemplate(p interface{}) error {
	pVal := reflect.ValueOf(p)
	if pVal.Kind() != reflect.Ptr {
		return ErrNotPointer
	}
	indirectVal := reflect.Indirect(pVal)
	if !indirectVal.CanSet() {
		return ErrNotSettable
	}
	return nil
}

// Format response
func FormatResponse(res common.APIResponse, data interface{}, err error) *common.APIResponse {
	if err != nil {
		ErrResp := &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: err.Error(),
		}

		switch err {
		case ErrConfiguration:
			ErrResp.ErrorCode = ErrConfigurationCode
		case ErrNotPointer:
			ErrResp.ErrorCode = ErrNotPointerCode
		case ErrNotSettable:
			ErrResp.ErrorCode = ErrNotSettableCode
		default: // unknown error
			ErrResp.ErrorCode = ErrInternalCode
		}

		return ErrResp
	}

	res.Data = data
	return &res
}

func responseStatus(code int) string {
	var r = code / 100
	switch r {
	case 2:
		return common.APIStatus.Ok
	case 4:
		switch code {
		case 400:
			return common.APIStatus.Invalid
		case 401:
			return common.APIStatus.Unauthorized
		case 403:
			return common.APIStatus.Forbidden
		case 404:
			return common.APIStatus.NotFound
		case 409:
			return common.APIStatus.Existed
		}
	case 5:
		return common.APIStatus.Error
	}
	return "code didn't implement"
}

func FormatResponseWithCode(res common.APIResponse, data interface{}, err error, code int) *common.APIResponse {
	resp := FormatResponse(res, data, err)
	resp.Status = responseStatus(code)
	return resp
}
