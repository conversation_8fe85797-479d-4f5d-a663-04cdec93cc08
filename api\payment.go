package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetPayment ...
func GetPayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset      = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit       = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal    = req.GetParam("getTotal") == "true"
		qStr        = req.GetParam("q")
		isAvailable = req.GetParam("isAvailable") == "true"
		sort        = req.GetParam("sort")
	)

	// validate input
	var query model.PaymentPlatform
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}
	qOption := model.QueryOption{
		Total: getTotal,
	}
	if isAvailable {
		query.IsHaveBalance = true
	}
	// do action
	return resp.Respond(action.GetNewPayment(query, offset, limit, qOption, sort))
}

// CreatePayment
func CreatePayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PaymentPlatform
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		input.CreatedByID = acc.AccountID
		// input.CreatedByName = acc.Fullname
		return resp.Respond(action.CreateNewPayment(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// UpdatePayment
func UpdatePayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PaymentPlatform
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.PaymentCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PaymentCode",
			ErrorCode: string(enum.ErrorCodeInvalid.PaymentCode),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdateNewPayment(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// DeletePayment ...
func DeletePayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.PaymentPlatform
	)

	// parse input
	if err := json.Unmarshal([]byte(qStr), &query); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DeleteNewPayment(query.PaymentCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})

}

// UpdatePaymentInfo
func UpdatePaymentInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PaymentPlatform

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.PaymentCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PaymentCode",
			ErrorCode: string(enum.ErrorCodeInvalid.PaymentCode),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.UpdatePaymentInfo(input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// Update payment item for VB
func ApplyPaymentVoucherForVB(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.AutoApplyPaymentVoucherToVB(input.VendorBillCode))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}


// GetPaymentReason ...
func GetPaymentReason(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.ReasonSetting
	)

	if err := json.Unmarshal([]byte(qStr), &query); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.GetPaymentReason(query))
}
