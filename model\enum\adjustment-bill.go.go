package enum

/**
 * Status type
 */
type (
	AdjustmentBillStatusType    string
	invoiceAdjustmentStatusType struct {
		DRAFT        AdjustmentBillStatusType
		WAIT_TO_PAID AdjustmentBillStatusType
		PAID         AdjustmentBillStatusType
		CANCEL       AdjustmentBillStatusType
	}
)

var AdjustmentBillStatus = &invoiceAdjustmentStatusType{
	DRAFT:        "DRAFT",
	WAIT_TO_PAID: "WAIT_TO_PAID",
	PAID:         "PAID",
	CANCEL:       "CANC<PERSON>",
}

/**
 * Adjust type
 */
type (
	AdjustmentBillType string
	adjustmentBillType struct {
		INCREASE AdjustmentBillType
		DECREASE AdjustmentBillType
	}
)

var AdjustBill = &adjustmentBillType{
	INCREASE: "INCREASE",
	DECREASE: "DECREASE",
}
