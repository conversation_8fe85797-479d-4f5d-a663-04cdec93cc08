package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetSkuConfig
func GetSkuConfig(query *model.SkuConfig, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	sortFields := &primitive.M{"_id": -1}
	switch sort {
	case "createdTime":
		sortFields = &primitive.M{"created_time": 1}
	case "-createdTime":
		sortFields = &primitive.M{"created_time": -1}
	case "id":
		sortFields = &primitive.M{"_id": 1}
	case "-id":
		sortFields = &primitive.M{"_id": -1}
	}

	// Query primary DB
	if query.ProductCodeIn != nil && len(*query.ProductCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"product_code": bson.M{
			"$in": query.ProductCodeIn,
		}})
	}

	if query.ProductIDIn != nil && len(*query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"product_id": bson.M{
			"$in": query.ProductIDIn,
		}})
	}

	if len(query.SKUIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"sku": bson.M{
			"$in": query.SKUIn,
		}})
	}

	// search by vendor code
	if query.VendorCodeSearch != nil && *query.VendorCodeSearch != "" {
		query.ComplexQuery = append(query.ComplexQuery,
			&bson.M{
				"vendors.vendor_code": *query.VendorCodeSearch,
			},
		)
	}

	// search by purchaser code
	if query.CreatedTimeFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
			},
		})
	}

	// search by purchaser code
	if query.CreatedTimeTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	// search by purchaser code
	if query.IDFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$gt": query.IDFrom,
			},
		})
	}

	// search by purchaser code
	if query.IDTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$lt": query.IDTo,
			},
		})
	}

	// search by purchaser code
	skuConfigResp := model.SkuConfigDB.Query(query, offset, limit, sortFields)
	if getTotal {
		countResp := model.SkuConfigDB.Count(query)
		skuConfigResp.Total = countResp.Total
	}

	return skuConfigResp
}

// CreateSkuConfig
func CreateSkuConfig(input *model.SkuConfig) *common.APIResponse {
	if response, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return response
	}
	// tìm theo purchaserCode và skuMain
	skuConfigQuery := model.SkuConfig{
		SKU:           input.SKU,
		PurchaserCode: input.PurchaserCode,
	}
	skuConfigResp := model.SkuConfigDB.QueryOne(skuConfigQuery)
	if skuConfigResp.Status == common.APIStatus.Error {
		return skuConfigResp
	}
	if skuConfigResp.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "The vendor's SKU already exists",
			ErrorCode: string(enum.ErrorCodeInvalid.ExistedBefore),
			// ErrorCode: "SKU_VENDOR_EXISTS_BEFORE",
		}
	}
	// // create wishlist
	// createWishlistBySkuConfig(*input)

	createResp := model.SkuConfigDB.Create(input)
	return createResp
}

// UpdateSkuConfig
func UpdateSkuConfig(input *model.SkuConfig) *common.APIResponse {
	// validate
	if response, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return response
	}
	skuConfigQuery := model.SkuConfig{
		SKU:           input.SKU,
		PurchaserCode: input.PurchaserCode,
	}

	// tìm theo purchaserCode và skuMain
	skuConfigResp := model.SkuConfigDB.QueryOne(skuConfigQuery)
	if skuConfigResp.Status != common.APIStatus.Ok {
		return skuConfigResp
	}
	skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]

	var oldFavVendor model.VendorConfig
	if skuConfig.Vendors != nil {
		for _, vendor := range *skuConfig.Vendors {
			if vendor.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
				oldFavVendor = vendor
				break
			}
		}

	}

	var newFavVendor model.VendorConfig
	if input.Vendors != nil {
		for _, vendor := range *input.Vendors {
			if vendor.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
				newFavVendor = vendor
				break
			}
		}
	}
	// off tính năng
	// input.IsForceUpdate = true
	existDealList := GetDealCodesCreatedByVendor(input.SKU, oldFavVendor.VendorCode)
	var isUpdateFavVendor = false
	if len(existDealList) > 0 &&
		newFavVendor.VendorCode != "" &&
		oldFavVendor.VendorCode != "" &&
		newFavVendor.VendorCode != oldFavVendor.VendorCode {
		isUpdateFavVendor = true
		if !input.IsForceUpdate {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Không được đổi fav vendor khi đang có deal",
				Data:      existDealList,
				ErrorCode: "EXISTED_DEAL",
			}
		}

	}

	BLANK := ""
	if utils.IsNil(input.EstActiveTimne) {
		input.EstActiveTimne = &BLANK
	}

	// trigger update wishlist
	resp := model.SkuConfigDB.UpdateOne(model.SkuConfig{ID: skuConfig.ID}, input)

	//  nếu hiện tại đang bật sku config thì
	if resp.Status == common.APIStatus.Ok && skuConfig.IsActive != nil {
		if isUpdateFavVendor {
			for _, dealCode := range existDealList {
				updateDealResp := marketplace.UpdateDeal(
					client.APIOption{
						Body: model.MarketplaceDeal{
							Code:   dealCode,
							Status: "INACTIVE",
						},
						Keys: []string{"INACTIVE_DEAL_BY_CHANGE_FAV_VENDOR", skuConfig.SKU, skuConfig.ProductCode, dealCode},
					},
				)
				if updateDealResp.Status != common.APIStatus.Ok {
					return updateDealResp
				}
			}
		}
	}
	// trigger update sku status
	if resp.Status == common.APIStatus.Ok && skuConfig.IsActive != nil && input.IsActive != nil && *skuConfig.IsActive != *input.IsActive {

		warehouse, ok := PurchaserWarehouseCode[skuConfig.PurchaserCode]
		if ok {
			// update sku status
			for _, wh := range warehouse {
				skuItem := GetSkuItemFromSKU(input.ProductID, input.SellerCode, wh)
				if skuItem != nil && skuItem.ItemCode != "" {
					go marketplace.UpdateSKUStatus(
						client.APIOption{
							Body: model.UpdateSKUStatusRequest{
								SKU:           skuConfig.SKU,
								ProductID:     skuConfig.ProductID,
								ProductCode:   skuConfig.ProductCode,
								WarehouseCode: wh,
								ItemCode:      skuItem.ItemCode,
							},
							Keys: []string{skuConfig.SKU, "UPDATE_SKU_STATUS"},
						},
					)
				}
			}
		}
	}

	// cập nhật thành công, nếu có đổi fav vendor:
	// + trigger tính lại giá sau rebate
	if resp.Status == common.APIStatus.Ok && newFavVendor.VendorCode != "" && oldFavVendor.VendorCode != newFavVendor.VendorCode {

		var (
			warehouse, ok        = PurchaserWarehouseCode[skuConfig.PurchaserCode]
			effectVendor         = []string{oldFavVendor.VendorCode, newFavVendor.VendorCode}
			mapContractPriceItem = map[string]*model.ContractPriceItem{}
		)

		if ok {
			// duỵệt qia từng warehouse và từng effectVendor để gọi hàm GetContractPriceItemV2
			for _, wh := range warehouse {
				for _, vendor := range effectVendor {
					// Get the contract price item
					contractPriceItemResp := GetContractPriceItemV2(skuConfig.ProductID, wh, vendor)
					if contractPriceItemResp.Status != common.APIStatus.Ok {
						continue
					}
					contractPriceItem := contractPriceItemResp.Data.([]*model.ContractPriceItem)[0]
					mapContractPriceItem[contractPriceItem.ContractPriceCode] = contractPriceItem
				}
			}

			if len(mapContractPriceItem) > 0 {
				for _, contractPriceItem := range mapContractPriceItem {
					if contractPriceItem != nil && contractPriceItem.ContractPriceCode != "" {
						// trigger tính lại giá sau rebate
						seller.TriggerReCalcPriceAfterRebate(client.APIOption{
							Params: map[string]string{
								"contractPriceCode": contractPriceItem.ContractPriceCode,
							},
						})
					}
				}
			}

		}
	}
	return resp
}

// tạo sku-config từ purchase-order đã hoàn tất
func CreateSkuConfigWithCompletedPO(poCode string) *common.APIResponse {

	// check PO
	poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
		POCode: poCode,
		Status: enum.PurchaseOrderStatus.COMPLETED,
		// CreatedBySystem: "SUPPLIER", // Chỉ áp dụng đối với PO tạo từ logic supplier
	})

	if poResp.Status != common.APIStatus.Ok {
		return poResp
	}
	po := poResp.Data.([]*model.PurchaseOrder)[0]

	// Query PO item
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: poCode,
	}, 0, 1000, nil)
	if poItemResp.Status != common.APIStatus.Ok {
		return poItemResp
	}

	// Loop qua từng item trong PO
	poItems := poItemResp.Data.([]*model.PurchaseOrderItem)

	for _, poItem := range poItems {
		// skip item không tạo từ quotation
		if poItem.ReferPrice == 0 {
			continue
		}

		//skip completed-po item giá sau CK = 0
		if poItem.PriceAfterDiscount == nil || *poItem.PriceAfterDiscount == 0 {
			continue
		}

		// Query sku-config
		skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
			SKU:           poItem.SKU,
			PurchaserCode: po.PurchaserCode,
		})

		// case chưa có SKU config
		// có khã năng hỗ trợ import
		if skuConfigResp.Status == common.APIStatus.NotFound {
			// new line
			vendorConfig := newVendorConfig(*po, *poItem)
			vendorConfig.Priority = &utils.DEFAULT_PRIORITY

			skuConfig := model.SkuConfig{
				SellerCode:    po.SellerCode,
				PurchaserCode: po.PurchaserCode,
				ProductCode:   poItem.ProductCode,
				ProductID:     poItem.ProductID,
				SKU:           poItem.SKU,
				// Unit:        poItem.Unit,
				Vendors: &[]model.VendorConfig{vendorConfig},
			}

			// // create wishlist
			// createWishlistBySkuConfig(skuConfig)

			model.SkuConfigDB.Create(skuConfig)

		} else if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]

			// case có SKU nhưng vendors rỗng
			if utils.IsNil(skuConfig.Vendors) {
				// new line
				vendorConfig := newVendorConfig(*po, *poItem)
				vendorConfig.Priority = &utils.DEFAULT_PRIORITY

				skuConfig.Vendors = &[]model.VendorConfig{vendorConfig}

				// create wishlist
				// createWishlistBySkuConfig(*skuConfig)

				model.SkuConfigDB.UpdateOne(model.SkuConfig{ID: skuConfig.ID}, skuConfig)

				// case có SKU có vendors
			} else {

				var (
					maxPriority     int64 = -1
					isExistedVendor bool  = false
				)

				for i := range *skuConfig.Vendors {
					vendorConfig := (*skuConfig.Vendors)[i]

					// Lấy giá trị Priority lớn nhất trong sku-config
					if !utils.IsNil(vendorConfig.Priority) && *vendorConfig.Priority > maxPriority {
						maxPriority = *vendorConfig.Priority
					}

					// exist thì update lại cho vendor đó
					if vendorConfig.VendorCode == poItem.VendorCode {
						// if poItem.UnitPrice != 0 {
						// 	vendorConfig.UnitPrice = poItem.UnitPrice
						// }
						// if poItem.DiscountPercent != nil {
						// 	vendorConfig.DiscountPercent = *poItem.DiscountPercent
						// }
						// if poItem.VAT != nil {
						// 	vendorConfig.VAT = int64(*poItem.VAT)
						// }
						// (*skuConfig.Vendors)[i] = vendorConfig

						// isExistedVendor = true
						// break
						return &common.APIResponse{
							Status:  common.APIStatus.Ok,
							Message: "Skip sku-config",
						}
					}
				}

				// Trường hợp hợp ưu tiên cuối cùng trong sku config nhỏ hơn < 10, auto thêm vào sku-config
				if !isExistedVendor && maxPriority < utils.MAX_AUTO_PRIORITY {

					// Trường hợp vendor có config ncc nhưng không set ưu tiên => Mặc định là 1
					// Ngược lại thì += 1
					if maxPriority == -1 {
						maxPriority = utils.DEFAULT_PRIORITY
					} else {
						maxPriority += 1
					}

					// new line
					vendorConfig := newVendorConfig(*po, *poItem)
					vendorConfig.Priority = &maxPriority

					*skuConfig.Vendors = append(*skuConfig.Vendors, vendorConfig)
				}

				// // create wishlist
				// createWishlistBySkuConfig(*skuConfig)

				// update SkuConfigDB
				model.SkuConfigDB.UpdateOne(model.SkuConfig{ID: skuConfig.ID}, skuConfig)
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update sku-config success",
	}
}

// GetSkuItemFromSKU
func newVendorConfig(po model.PurchaseOrder, poItem model.PurchaseOrderItem) model.VendorConfig {
	vendorConfig := model.VendorConfig{
		VendorCode: po.VendorCode,
		UnitPrice:  poItem.UnitPrice,
	}
	if poItem.DiscountPercent != nil {
		vendorConfig.DiscountPercent = *poItem.DiscountPercent
	}
	if poItem.VAT != nil {
		vendorConfig.VAT = int64(*poItem.VAT)
	}

	return vendorConfig
}
