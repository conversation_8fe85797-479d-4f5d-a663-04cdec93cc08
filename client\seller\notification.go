package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var notificationClient *client.Client

const (
	sendZNSMessage = "POST::/message/send-zns"
)

func InitSellerNotification(database *mongo.Database) {
	const sellerNotificationV1 = "/seller/notification/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	notificationClient = client.NewClient(sellerNotificationV1, headers, 0)
	notificationClient.WithDatabase(database)
	notificationClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     sendZNSMessage,
				Name:     "seller__ZNS__send_zns_message",
				Database: database,
			},
		}...,
	)
}

func SendZNSMessage(opts ...client.APIOption) *common.APIResponse {
	requestOption := notificationClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := notificationClient.WithRequest(sendZNSMessage, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
