package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetInboundRequestItemList ...
// GetInitialStockDraftList retrieves a list of initial stock drafts based on the provided query parameters.
// 
// Parameters:
//   - query: The query object containing the filter criteria for the initial stock drafts.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of records in the response.
//   - sort: The field by which to sort the results. Supported values are "createdTime", "-createdTime", "id", and "-id".
//
// Returns:
//   - *common.APIResponse: The API response containing the list of initial stock drafts and optionally the total count.
func GetInitialStockDraftList(query model.InitialStockDraft, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}
	switch sort {
	case "createdTime":
		sortField = &primitive.M{"created_time": 1}
	case "-createdTime":
		sortField = &primitive.M{"created_time": -1}
	case "id":
		sortField = &primitive.M{"_id": 1}
	case "-id":
		sortField = &primitive.M{"_id": -1}
	}

	genResp := model.InitialStockDraftDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.InitialStockDraftDB.Count(query)
		genResp.Total = countResp.Total
	}

	return genResp
}

// CreateInitialStockDraft ...
// CreateInitialStockDraft creates an initial stock draft based on the provided input.
// It validates the input fields and returns an API response indicating the result of the operation.
//
// Parameters:
//   - input: model.InitialStockDraft
//     The initial stock draft data to be created.
//
// Returns:
//   - *common.APIResponse
//     The API response containing the status, message, and error code (if any).
//
// Validation:
//   - SKU must not be empty.
//   - SellerCode must not be empty.
//   - WarehouseCode must not be empty.
//   - InitialStock, if provided, must be greater than or equal to 0.
//   - MaxQuantity, if provided, must be greater than or equal to 0.
//
// If an initial stock draft with the same SKU and WarehouseCode already exists, an error response is returned.
func CreateInitialStockDraft(input model.InitialStockDraft) *common.APIResponse {

	// validate
	if input.SKU == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SKU is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}
	// Check if SellerCode is empty
	if input.SellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SellerCode is required",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		}
	}

	// Check if WarehouseCode is empty
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode is required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// Check if InitialStock is less than 0
	if input.InitialStock != nil && *input.InitialStock < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Initial Stock must be greater than 0",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check if MaxQuantity is less than 0
	if input.MaxQuantity != nil && *input.MaxQuantity < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Max Quantity must be greater than 0",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// query
	checkResp := model.InitialStockDraftDB.QueryOne(&model.InitialStockDraft{
		SKU:           input.SKU,
		WarehouseCode: input.WarehouseCode,
	})
	if checkResp.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "This stock daft does exist!",
			ErrorCode: string(enum.ErrorCodeInvalid.InitialStockDaftExist),
		}
	}

	// create
	createResp := model.InitialStockDraftDB.Create(input)
	return createResp
}

// UpdateInitialStockDraft ...
// UpdateInitialStockDraft updates the initial stock draft based on the provided input.
// It validates the input fields and returns an API response with the status of the operation.
//
// Parameters:
//   - input: model.InitialStockDraft - The initial stock draft data to be updated.
//
// Returns:
//   - *common.APIResponse: The response containing the status, message, and error code (if any).
//
// Validation:
//   - SKU must not be empty.
//   - WarehouseCode must not be empty.
//   - InitialStock, if provided, must be greater than or equal to 0.
//   - MaxQuantity, if provided, must be greater than or equal to 0.
//
// Query:
//   - Checks if the initial stock draft exists in the database.
//   - If it exists, updates the initial stock draft with the provided input data.
func UpdateInitialStockDraft(input model.InitialStockDraft) *common.APIResponse {

	// validate
	if input.SKU == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SKU",
			ErrorCode: string(enum.ErrorCodeInvalid.SKU),
		}
	}

	// Check if WarehouseCode is empty
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode is required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// Check if InitialStock is less than 0
	if input.InitialStock != nil && *input.InitialStock < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Initial Stock must be greater than 0",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check if MaxQuantity is less than 0
	if input.MaxQuantity != nil && *input.MaxQuantity < 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Max Quantity must be greater than 0",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// query
	checkResp := model.InitialStockDraftDB.QueryOne(&model.InitialStockDraft{
		SKU:           input.SKU,
		WarehouseCode: input.WarehouseCode,
	})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	initialStockDraftData := checkResp.Data.([]*model.InitialStockDraft)[0]

	// update
	updateResp := model.InitialStockDraftDB.UpdateOne(
		model.InitialStockDraft{
			SKU:           initialStockDraftData.SKU,
			WarehouseCode: input.WarehouseCode,
		},
		input)
	return updateResp
}
