package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BankingPaymentOrder struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBy       string              `json:"createdBy,omitempty" bson:"created_by,omitempty"`

	BankingPaymentOrderID   int64  `json:"bankingPaymentOrderId,omitempty" bson:"banking_payment_order_id,omitempty"`
	BankingPaymentOrderCode string `json:"bankingPaymentOrderCode,omitempty" bson:"banking_payment_order_code,omitempty"`

	BankingPaymentOrderType enum.BankingPaymentOrderTypeValue   `json:"bankingPaymentOrderType,omitempty" bson:"banking_payment_order_type,omitempty"` // ADVANCE || DEBT
	Note                    string                              `json:"note,omitempty" bson:"note,omitempty"`
	ConfirmedBy             string                              `json:"confirmedBy,omitempty" bson:"confirmed_by,omitempty"`
	ConfirmedTime           *time.Time                          `json:"confirmedTime,omitempty" bson:"confirmed_time,omitempty"`
	ApprovedBy              string                              `json:"approvedBy,omitempty" bson:"approved_by,omitempty"`
	ApprovedTime            *time.Time                          `json:"approvedTime,omitempty" bson:"approved_time,omitempty"`
	Status                  enum.BankingPaymentOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"` // PENDING || CONFIRMED || APPROVED || CANCELLED
	IsCitad                 *bool                               `json:"isCitad,omitempty" bson:"is_citad,omitempty"`

	TransferStatus enum.TransferPaymentStatusValue `json:"transferStatus,omitempty" bson:"transfer_status,omitempty"` // SUCCESSFUL_TRANSFERRED || PARTIAL_SUCCESSFUL_TRANSFERRED || FAILED_TRANSFERRED
	Items          []*BankingPaymentOrderItem      `json:"items,omitempty" bson:"-"`

	POCodes       []string `json:"poCodes,omitempty" bson:"po_codes,omitempty"`
	VendorBillIDs []int64  `json:"vendorBillIds,omitempty" bson:"vendor_bill_ids,omitempty"`
	VendorCodes   []string `json:"vendorCodes,omitempty" bson:"vendor_codes,omitempty"`

	TotalAmount                *int64 `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	TotalAmountTransferSuccess *int64 `json:"totalAmountTransferSuccess,omitempty" bson:"total_amount_transfer_success,omitempty"`
	TotalAmountTransferFail    *int64 `json:"totalAmountTransferFail,omitempty" bson:"total_amount_transfer_fail,omitempty"`

	ComplexQuery           []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedTimeFromQuery   *time.Time `json:"createdTimeFromQuery,omitempty" bson:"-"`
	CreatedTimeToQuery     *time.Time `json:"createdTimeToQuery,omitempty" bson:"-"`
	POCodeIn               []string   `json:"poCodeIn,omitempty" bson:"-"`
	VendorBillIDIn         []int64    `json:"vendorBillIDIn,omitempty" bson:"-"`
	VendorCodeIn           []string   `json:"vendorCodeIn,omitempty" bson:"-"`
	BankingPaymentOrderIDs []int64    `json:"bankingPaymentOrderIDs,omitempty" bson:"-"`
}

// BankingPaymentOrderDB ...
var BankingPaymentOrderDB = &db.Instance{
	ColName:        "banking_payment_order",
	TemplateObject: &BankingPaymentOrder{},
}

// InitBankingPaymentOrderModel ...
func InitBankingPaymentOrderModel(s *mongo.Database) {
	BankingPaymentOrderDB.ApplyDatabase(s)
}

type BankingPaymentOrderItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	BankingPaymentOrderItemID   int64  `json:"bankingPaymentOrderItemId,omitempty" bson:"banking_payment_order_item_id,omitempty"`
	BankingPaymentOrderItemCode string `json:"bankingPaymentOrderItemCode,omitempty" bson:"banking_payment_order_item_code,omitempty"`
	BankingPaymentOrderCode     string `json:"bankingPaymentOrderCode,omitempty" bson:"banking_payment_order_code,omitempty"`

	VendorCode    string   `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	POCodes       []string `json:"poCodes,omitempty" bson:"po_codes,omitempty"`
	VendorBillIDs []int64  `json:"vendorBillIds,omitempty" bson:"vendor_bill_ids,omitempty"`
	Amount        int64    `json:"amount,omitempty" bson:"amount,omitempty"`
	Currency      string   `json:"currency,omitempty" bson:"currency,omitempty"`
	Remark        string   `json:"remark,omitempty" bson:"remark,omitempty"`
	IsForceCITAD  *bool    `json:"isForceCITAD,omitempty" bson:"is_force_citad,omitempty"`

	RequestId                string      `json:"requestId,omitempty" bson:"-"`
	BankAccountName          string      `json:"bankAccountName,omitempty" bson:"bank_account_name,omitempty"`
	BankAccountNumber        string      `json:"bankAccountNumber,omitempty" bson:"bank_account_number,omitempty"`
	BeneficiaryBankShortName string      `json:"beneficiaryBankShortName,omitempty" bson:"beneficiary_bank_short_name,omitempty"` // Bank name
	BankBranch               *BankBranch `json:"bankBranch,omitempty" bson:"bank_branch,omitempty"`
	SourceNumber             string      `json:"sourceNumber,omitempty" bson:"source_number,omitempty"`
	TargetNumber             string      `json:"targetNumber,omitempty" bson:"target_number,omitempty"`
	BankBin                  string      `json:"bankBin,omitempty" bson:"bank_bin,omitempty"`
	BankCode                 string      `json:"bankCode,omitempty" bson:"bank_code,omitempty"`
	// sync to bank
	Branch *BankBranch `json:"branch,omitempty" bson:"branch,omitempty"`

	Status  enum.BankingPaymentOrderItemStatusValue `json:"status,omitempty" bson:"status,omitempty"` // PROCESSING || SUCCESS || FAILED
	Content string                                  `json:"content,omitempty" bson:"content,omitempty"`
	Reason  string                                  `json:"reason,omitempty" bson:"reason,omitempty"`

	//map transfer result
	RefNum          string `json:"referenceNumber,omitempty" bson:"reference_number,omitempty"` //RefNum
	TransactionId   string `json:"transactionId,omitempty" bson:"transaction_id,omitempty"`
	TransferResult  string `json:"transferResult,omitempty" bson:"transfer_result,omitempty"`
	TransactionDate string `json:"transactionDate,omitempty" bson:"transaction_date,omitempty"`
	TransferDate    string `json:"transferDate,omitempty" bson:"transfer_date,omitempty"`
	// Signature       string `json:"signature,omitempty" bson:"signature,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type BankBranch struct {
	Name    string `json:"name,omitempty" bson:"name,omitempty"`
	Code    string `json:"code,omitempty" bson:"code,omitempty"`
	Channel string `json:"channel,omitempty" bson:"channel,omitempty"`
}

// BankingPaymentOrderItemDB ...
var BankingPaymentOrderItemDB = &db.Instance{
	ColName:        "banking_payment_item_order",
	TemplateObject: &BankingPaymentOrderItem{},
}

// InitBankingPaymentOrderItemModel ...
func InitBankingPaymentOrderItemModel(s *mongo.Database) {
	BankingPaymentOrderItemDB.ApplyDatabase(s)
}

type TransferResult struct {
	RefNum          string `json:"referenceNumber,omitempty" bson:"-"`
	TransactionId   string `json:"transactionId,omitempty" bson:"-"`
	TransferResult  string `json:"transferResult,omitempty" bson:"-"`
	TransactionDate string `json:"transactionDate,omitempty" bson:"-"`
	TransferDate    string `json:"tranferDate,omitempty" bson:"-"`
	Signature       string `json:"signature,omitempty" bson:"-"`
}

type Transaction struct {
	RefNum            string             `json:"referenceNumber,omitempty" bson:"-"`
	TransactionID     int64              `json:"transactionID,omitempty" bson:"-"`
	TransferResult    string             `json:"transferResult,omitempty" bson:"-"`
	TransactionDate   string             `json:"transactionDate,omitempty" bson:"-"`
	TransferDate      string             `json:"tranferDate,omitempty" bson:"-"`
	Signature         string             `json:"signature,omitempty" bson:"-"`
	RequestId         string             `json:"requestId,omitempty" bson:"-"`
	TransactionStatus *TransactionStatus `json:"transactionStatus,omitempty" bson:"transaction_status,omitempty"`
	ErrorDescription  string             `json:"errorDescription,omitempty" bson:"error_description,omitempty"`
}

type TransactionStatus struct {
	Status enum.TransactionStatusEnum `json:"status,omitempty" bson:"status,omitempty"`
}
