package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

// forward action to platform
func CreateTransferRequest(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.BankingPaymentOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Parse request data error. " + req.GetContentText(),
		})
	}

	acc := getActionSource(req)
	createdByEmployeeID := acc.AccountID

	// create transfer request
	return resp.Respond(action.CreateTransferRequest(input, createdByEmployeeID))
}

// forward action to platform
func CallbackTransferRequest(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.TransferRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Parse request data error. " + req.GetContentText(),
		})
	}

	// create transfer request
	return resp.Respond(action.CallbackTransferRequest(input))
}
