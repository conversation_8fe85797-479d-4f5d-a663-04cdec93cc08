package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	json "go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateReason(input model.Reason) *common.APIResponse {
	// create
	input.ReasonID, _ = model.GetReasonID()
	// query
	resp := model.ReasonDB.QueryOne(&model.Reason{
		ReasonCode: input.ReasonCode,
	})

	if resp.Status == common.APIStatus.NotFound {
		createResp := model.ReasonDB.Create(input)

		return createResp
	}

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		ErrorCode: string(enum.ErrorCodeInvalid.ReasonExist),
		Message:   "This reason was exist",
	}
}

func UpdateReason(input model.Reason) *common.APIResponse {
	// query
	resp := model.ReasonDB.QueryOne(&model.Reason{
		ReasonCode: input.ReasonCode,
	})

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	respData := resp.Data.([]*model.Reason)[0]
	input.ReasonCode = respData.ReasonCode
	updateResp := model.ReasonDB.UpdateOne(
		model.Reason{
			ReasonCode: input.ReasonCode,
		},
		input)

	return updateResp
}

// Delete Reason ...
func DeleteReason(reasonCode string) *common.APIResponse {
	reasonResp := GetReasonByCode(reasonCode)
	if reasonResp.Status != common.APIStatus.Ok {
		return reasonResp
	}
	return model.ReasonDB.Delete(&model.Reason{ID: reasonResp.Data.([]*model.Reason)[0].ID})
}

func GetReasonByCode(reasonCode string) *common.APIResponse {
	return model.ReasonDB.QueryOne(&model.Reason{ReasonCode: reasonCode})
}

func GetReasonList(query *model.Reason, offset, limit int64, getTotal bool) *common.APIResponse {
	if len(query.ReasonCodeIn) > 0 {
		query.ReasonCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &json.M{"reason_code": json.M{
			"$in": query.ReasonCodeIn,
		}})
	}
	result := model.ReasonDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if getTotal {
		result.Total = model.ReasonDB.Count(query).Total
	}

	return result
}
