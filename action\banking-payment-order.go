package action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetBankingPaymentOrder retrieves a list of banking payment orders based on the provided query, offset, and limit.
// It also allows sorting by a specified field and optionally includes the total count of matching records.
//
// Parameters:
//   - query: model.BankingPaymentOrder - The query object to filter the banking payment orders.
//   - offset: int64 - The number of records to skip before starting to return records.
//   - limit: int64 - The maximum number of records to return.
//   - getTotal: bool - A flag indicating whether to include the total count of matching records.
//   - sortField: *bson.M - The field by which to sort the results. Defaults to sorting by "_id" in descending order if nil.
//
// Returns:
//   - *common.APIResponse - The API response containing the list of banking payment orders and optionally the total count.
func GetBankingPaymentOrder(query model.BankingPaymentOrder, offset, limit int64, getTotal bool, sortField *bson.M) *common.APIResponse {
	if sortField == nil {
		sortField = &bson.M{"_id": -1}
	}

	getResp := model.BankingPaymentOrderDB.Query(query, offset, limit, sortField)
	if getTotal {
		getResp.Total = model.BankingPaymentOrderDB.Count(query).Total
	}

	return getResp
}

// CreateBankingPaymentOrder creates a new banking payment order with the given input and account information.
// It initializes the order and its items with unique IDs and sets their status to DRAFT.
// The function also calculates the total amount of the order and updates the order's metadata.
// If the account information is provided, it sets the CreatedBy field to the account's fullname.
// The function then saves the order and its items to the database.
//
// Parameters:
//   - input: A pointer to a BankingPaymentOrder model containing the order details.
//   - acc: A pointer to an Account model containing the account information.
//
// Returns:
//   - A pointer to an APIResponse containing the status and any error messages.
func CreateBankingPaymentOrder(input *model.BankingPaymentOrder, acc *model.Account) *common.APIResponse {
	input.BankingPaymentOrderID, input.BankingPaymentOrderCode = model.GetBankingPaymentOrderID()
	input.Status = enum.BankingPaymentOrderStatus.DRAFT
	if acc != nil {
		input.CreatedBy = acc.Fullname
	}
	t := time.Now()
	input.CreatedTime = &t

	var totalAmount int64 = 0

	// loop through the items to set their IDs and codes
	for _, item := range input.Items {
		item.BankingPaymentOrderItemID, item.BankingPaymentOrderItemCode = model.GetBankingPaymentOrderItemID()
		item.BankingPaymentOrderCode = input.BankingPaymentOrderCode
		item.Status = enum.BankingPaymentOrderItemStatus.DRAFT
		totalAmount += item.Amount
		if len(item.POCodes) > 0 {
			input.POCodes = append(input.POCodes, item.POCodes...)
		}
		if len(item.VendorBillIDs) > 0 {
			input.VendorBillIDs = append(input.VendorBillIDs, item.VendorBillIDs...)
		}
		if item.VendorCode != "" {
			input.VendorCodes = append(input.VendorCodes, item.VendorCode)
		}
	}
	// set the total amount of the order
	input.TotalAmount = &totalAmount
	input.TotalAmountTransferSuccess = utils.Pointer.WithInt64(0.0)
	input.TotalAmountTransferFail = utils.Pointer.WithInt64(0.0)

	// save the order to the database
	createBankingResp := model.BankingPaymentOrderDB.Create(input)
	if createBankingResp.Status != common.APIStatus.Ok {
		return createBankingResp
	}

	// save the items to the database
	model.BankingPaymentOrderItemDB.CreateMany(input.Items)

	return createBankingResp
}

// UpdateBankingPaymentOrder updates an existing banking payment order with the provided input data.
// It performs the following steps:
// 1. Queries the existing banking payment order using the provided BankingPaymentOrderCode.
// 2. Deletes the existing banking payment order items associated with the order.
// 3. Calculates the total amount for the new items and updates the input with additional information such as POCodes, VendorBillIDs, and VendorCodes.
// 4. Updates the banking payment order with the new data.
// 5. Creates new banking payment order items with the updated information.
//
// Parameters:
// - input: A pointer to a BankingPaymentOrder model containing the updated data.
//
// Returns:
// - A pointer to an APIResponse indicating the status of the operation.
func UpdateBankingPaymentOrder(input *model.BankingPaymentOrder) *common.APIResponse {
	resp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrderItem{BankingPaymentOrderCode: input.BankingPaymentOrderCode})
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	bankingPaymentOrder := resp.Data.([]*model.BankingPaymentOrder)[0]

	deleteResp := model.BankingPaymentOrderItemDB.Delete(model.BankingPaymentOrderItem{BankingPaymentOrderCode: bankingPaymentOrder.BankingPaymentOrderCode})
	if deleteResp.Status != common.APIStatus.Ok {
		return deleteResp
	}

	var totalAmount int64 = 0
	for _, item := range input.Items {
		item.BankingPaymentOrderItemID, item.BankingPaymentOrderItemCode = model.GetBankingPaymentOrderItemID()
		item.BankingPaymentOrderCode = input.BankingPaymentOrderCode
		item.Status = enum.BankingPaymentOrderItemStatus.DRAFT
		totalAmount += item.Amount
		if len(item.POCodes) > 0 {
			input.POCodes = append(input.POCodes, item.POCodes...)
		}
		if len(item.VendorBillIDs) > 0 {
			input.VendorBillIDs = append(input.VendorBillIDs, item.VendorBillIDs...)
		}
		if item.VendorCode != "" {
			input.VendorCodes = append(input.VendorCodes, item.VendorCode)
		}
	}
	input.TotalAmount = &totalAmount

	updateResp := model.BankingPaymentOrderDB.UpdateOne(
		model.BankingPaymentOrder{ID: bankingPaymentOrder.ID},
		input)

	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	createResp := model.BankingPaymentOrderItemDB.CreateMany(input.Items)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}
	return updateResp
}

// CloneBankingPaymentOrder clones a banking payment order based on the provided code.
// It retrieves the banking payment order and its items from the database, and resets
// various fields to their default values. If isOnlyFailed is true, only items with
// a status of FAILED are included.
//
// Parameters:
// - code: The code of the banking payment order to clone.
// - acc: The account associated with the banking payment order.
// - isOnlyFailed: A boolean indicating whether to include only items with a status of FAILED.
//
// Returns:
// - *common.APIResponse: The API response containing the cloned banking payment order.
func CloneBankingPaymentOrder(code string, acc *model.Account, isOnlyFailed bool) *common.APIResponse {
	resp := model.BankingPaymentOrderDB.Query(model.BankingPaymentOrder{BankingPaymentOrderCode: code}, 0, 1, nil)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	bankingPaymentOrder := resp.Data.([]*model.BankingPaymentOrder)[0]

	query := model.BankingPaymentOrderItem{BankingPaymentOrderCode: code}
	// filter only item FAILED
	if isOnlyFailed {
		query.Status = enum.BankingPaymentOrderItemStatus.FAILED
	}
	getItemsResp := model.BankingPaymentOrderItemDB.Query(query, 0, 1000, nil)
	bankingPaymentOrder.Items = getItemsResp.Data.([]*model.BankingPaymentOrderItem)
	// default value
	bankingPaymentOrder.ID = nil
	bankingPaymentOrder.CreatedTime = nil
	bankingPaymentOrder.LastUpdatedTime = nil
	bankingPaymentOrder.ConfirmedBy = ""
	bankingPaymentOrder.ConfirmedTime = nil
	bankingPaymentOrder.ApprovedBy = ""
	bankingPaymentOrder.ApprovedTime = nil
	bankingPaymentOrder.BankingPaymentOrderID = 0
	bankingPaymentOrder.BankingPaymentOrderCode = ""
	bankingPaymentOrder.TotalAmountTransferSuccess = utils.Pointer.WithInt64(0)
	bankingPaymentOrder.TotalAmountTransferFail = utils.Pointer.WithInt64(0)
	bankingPaymentOrder.POCodes = []string{}
	bankingPaymentOrder.VendorBillIDs = []int64{}
	bankingPaymentOrder.TransferStatus = ""
	bankingPaymentOrder.Note = ""
	bankingPaymentOrder.IsCitad = utils.Pointer.WithBool(false)
	for _, item := range bankingPaymentOrder.Items {
		item.ID = nil
		item.CreatedTime = nil
		item.LastUpdatedTime = nil
		item.BankingPaymentOrderItemCode = ""
		item.BankingPaymentOrderItemID = 0
		item.BankingPaymentOrderCode = ""
		item.RefNum = ""
		item.TransactionId = ""
		item.TransferResult = ""
		item.TransactionDate = ""
		item.TransferDate = ""
		item.Reason = ""
		item.Content = ""
	}

	return CreateBankingPaymentOrder(bankingPaymentOrder, acc)
}

// SwitchStatusBankingPaymentOrder updates the status of a banking payment order and performs additional actions based on the new status.
//
// Parameters:
//   - input: model.BankingPaymentOrder containing the new status and other relevant information.
//   - headers: map[string]string containing headers for any additional requests.
//
// Returns:
//   - *common.APIResponse: The response from the database query or update operation.
//
// The function performs the following steps:
//  1. Queries the database for the banking payment order using the provided BankingPaymentOrderCode.
//  2. If the query is successful, it updates the status of the banking payment order.
//  3. If the new status is APPROVED, it sets the ApprovedBy and ApprovedTime fields.
//  4. If the new status is CONFIRMED, it sets the ConfirmedBy and ConfirmedTime fields.
//  5. Updates the banking payment order in the database.
//  6. If the update is successful and the new status is APPROVED, it triggers the CreatePaymentTransfer function asynchronously.
func SwitchStatusBankingPaymentOrder(input model.BankingPaymentOrder, headers map[string]string) *common.APIResponse {
	resp := model.BankingPaymentOrderDB.Query(model.BankingPaymentOrder{BankingPaymentOrderCode: input.BankingPaymentOrderCode}, 0, 1, nil)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	updater := model.BankingPaymentOrder{
		Status: input.Status,
	}
	t := time.Now()
	if input.Status == enum.BankingPaymentOrderStatus.APPROVED {
		updater.ApprovedBy = input.ApprovedBy
		updater.ApprovedTime = &t
	}
	if input.Status == enum.BankingPaymentOrderStatus.CONFIRMED {
		updater.ConfirmedBy = input.ConfirmedBy
		updater.ConfirmedTime = &t
	}

	bankingPaymentOrder := resp.Data.([]*model.BankingPaymentOrder)[0]
	updateResp := model.BankingPaymentOrderDB.UpdateOne(
		model.BankingPaymentOrder{ID: bankingPaymentOrder.ID},
		updater)

	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}
	// action payment VP bank
	dataUpdate := updateResp.Data.([]*model.BankingPaymentOrder)[0]
	if dataUpdate.Status == enum.BankingPaymentOrderStatus.APPROVED {
		go CreatePaymentTransfer(dataUpdate, headers)
	}

	return updateResp
}

// GetBankingPaymentOrderItem retrieves a list of BankingPaymentOrderItem based on the provided query, offset, limit, and sort field.
// If getTotal is true, it also returns the total count of items matching the query.
//
// Parameters:
//   - query: The query parameters to filter the BankingPaymentOrderItem.
//   - offset: The number of items to skip before starting to collect the result set.
//   - limit: The maximum number of items to return.
//   - getTotal: A boolean indicating whether to include the total count of items matching the query.
//   - sortField: The field by which to sort the results. If nil, defaults to sorting by "_id" in descending order.
//
// Returns:
//   - *common.APIResponse: The API response containing the list of BankingPaymentOrderItem and optionally the total count.
func GetBankingPaymentOrderItem(query model.BankingPaymentOrderItem, offset, limit int64, getTotal bool, sortField *bson.M) *common.APIResponse {
	if sortField == nil {
		sortField = &bson.M{"_id": -1}
	}

	getResp := model.BankingPaymentOrderItemDB.Query(query, offset, limit, sortField)
	if getTotal {
		getResp.Total = model.BankingPaymentOrderItemDB.Count(query).Total
	}

	return getResp
}

// CreatePaymentTransfer processes a banking payment order by creating payment transfers for each item in the order.
// It first queries the items associated with the given banking payment order code. For each item, it determines whether
// to force CITAD based on the input and item amount. It then maps the bank branch if necessary and sets the request ID.
// The function sends a request to create the payment transfer and handles the response by updating the item's status
// and the total amount of successful or failed transfers in the input.
//
// Parameters:
//   - input: A pointer to a BankingPaymentOrder model containing the details of the payment order.
//   - headers: A map of string key-value pairs representing the headers to be included in the API request.
//
// The function does not return any value.
func CreatePaymentTransfer(input *model.BankingPaymentOrder, headers map[string]string) {
	getItemsBankingResp := model.BankingPaymentOrderItemDB.Query(model.BankingPaymentOrderItem{BankingPaymentOrderCode: input.BankingPaymentOrderCode}, 0, 1000, nil)
	if getItemsBankingResp.Status != common.APIStatus.Ok {
		return
	}

	items := getItemsBankingResp.Data.([]*model.BankingPaymentOrderItem)
	for _, item := range items {
		// mapping citad
		if input.IsCitad != nil && *input.IsCitad {
			item.IsForceCITAD = utils.Pointer.WithBool(true)
		} else {
			if item.Amount > ********* {
				item.IsForceCITAD = utils.Pointer.WithBool(true)
			} else {
				item.IsForceCITAD = utils.Pointer.WithBool(false)
			}
		}
		// map bank branch
		if item.IsForceCITAD != nil && *item.IsForceCITAD && item.BankBranch != nil {
			item.Branch = item.BankBranch
		}
		item.RequestId = item.BankingPaymentOrderItemCode
		opt := client.APIOption{
			Keys:    []string{item.RequestId, item.BankingPaymentOrderItemCode},
			Body:    item,
			Headers: headers,
		}
		createBankingResp := payment.CreatePaymentTransfer(opt)
		if createBankingResp.Status == common.APIStatus.Ok {

			data := createBankingResp.Data.([]*model.TransferResult)

			dataBaking := data[0]

			// in case not receive data from Payment service
			if dataBaking.TransactionId == "" {
				item.Status = enum.BankingPaymentOrderItemStatus.PROCESSING
				item.Content = createBankingResp.Message
				item.RefNum = dataBaking.RefNum
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.PROCESSING)
			} else {
				// still error for STATUS OK
				// because Just payment core is OK, not mean step connect to VP Bank is OK
				// so we need to check status of VP Bank
				// if dataBaking.TransferResult !=

				mapBankingResult(item, dataBaking)

				item.Status = enum.BankingPaymentOrderItemStatus.SUCCESS
				// plus amount success
				if input.TotalAmountTransferSuccess != nil {
					input.TotalAmountTransferSuccess = utils.Pointer.WithInt64(*input.TotalAmountTransferSuccess + item.Amount)
				} else {
					input.TotalAmountTransferSuccess = utils.Pointer.WithInt64(item.Amount)
				}
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.SUCCESS)
			}
		} else {
			if createBankingResp.Status == common.APIStatus.Error {
				item.Status = enum.BankingPaymentOrderItemStatus.PROCESSING
				item.Content = createBankingResp.Message
				input.TotalAmountTransferFail = utils.Pointer.WithInt64(*input.TotalAmountTransferFail + item.Amount)
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.PROCESSING)
			} else {
				item.Status = enum.BankingPaymentOrderItemStatus.FAILED
				input.TotalAmountTransferFail = utils.Pointer.WithInt64(*input.TotalAmountTransferFail + item.Amount)
				item.Content = createBankingResp.Message
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.FAILED)
			}

		}
		model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: input.BankingPaymentOrderCode}, input)
	}
}

// mapBankingResult maps the result of a banking transfer to a banking payment order item.
// It takes two parameters:
// - bankingPaymentOrder: a pointer to a BankingPaymentOrderItem struct where the result will be mapped to.
// - bankingCore: a pointer to a TransferResult struct containing the transfer result details.
func mapBankingResult(bankingPaymentOrder *model.BankingPaymentOrderItem, bankingCore *model.TransferResult) {
	bankingPaymentOrder.RefNum = bankingCore.RefNum
	bankingPaymentOrder.TransactionId = bankingCore.TransactionId
	bankingPaymentOrder.TransferResult = bankingCore.TransferResult
	bankingPaymentOrder.TransactionDate = bankingCore.TransactionDate
	bankingPaymentOrder.TransferDate = bankingCore.TransferDate
}

// switchStatusBankingPaymentOrderItem updates the status of a BankingPaymentOrderItem and
// subsequently updates the status of the associated BankingPaymentOrder if necessary.
//
// Parameters:
// - input: The BankingPaymentOrderItem containing the new status and other details to be updated.
// - status: The new status to be set for the BankingPaymentOrderItem.
//
// The function performs the following steps:
// 1. Queries the database for the BankingPaymentOrderItem using the provided input's code.
// 2. If the item is found and its current status is DRAFT, it updates the item with the new status and details.
// 3. Queries the database for all items associated with the same BankingPaymentOrder.
// 4. Checks the status of all items and determines the overall status of the BankingPaymentOrder:
//   - If all items are successfully transferred, sets the order status to SUCCESSFUL_TRANSFERRED.
//   - If no items are successfully transferred, sets the order status to FAILED_TRANSFERRED.
//   - If some items are successfully transferred, sets the order status to PARTIAL_SUCCESSFUL_TRANSFERRED.
//
// 5. Updates the BankingPaymentOrder with the determined status.
func switchStatusBankingPaymentOrderItem(input model.BankingPaymentOrderItem, status enum.BankingPaymentOrderItemStatusValue) {
	resp := model.BankingPaymentOrderItemDB.QueryOne(model.BankingPaymentOrderItem{BankingPaymentOrderItemCode: input.BankingPaymentOrderItemCode})
	if resp.Status != common.APIStatus.Ok {
		return
	}

	dataCheck := resp.Data.([]*model.BankingPaymentOrderItem)[0]
	if dataCheck.Status != enum.BankingPaymentOrderItemStatus.DRAFT {
		return
	}

	updateResp := model.BankingPaymentOrderItemDB.UpdateOne(
		model.BankingPaymentOrderItem{ID: dataCheck.ID},
		model.BankingPaymentOrderItem{
			Status:          status,
			RefNum:          input.RefNum,
			TransactionId:   input.TransactionId,
			TransferResult:  input.TransferResult,
			TransactionDate: input.TransactionDate,
			TransferDate:    input.TransferDate,
			Content:         input.Content,
		})

	if updateResp.Status != common.APIStatus.Ok {
		return
	}

	// update status banking payment order
	respItem := model.BankingPaymentOrderItemDB.Query(model.BankingPaymentOrderItem{BankingPaymentOrderCode: dataCheck.BankingPaymentOrderCode}, 0, 1000, nil)
	if respItem.Status == common.APIStatus.Ok {
		// all item is SUCCESSFUL_TRANSFERRED
		// update status banking payment order
		statusOrder := enum.TransferPaymentStatus.SUCCESSFUL_TRANSFERRED
		indexSuccessTransfer := 0
		items := respItem.Data.([]*model.BankingPaymentOrderItem)
		for _, item := range items {
			if item.Status == enum.BankingPaymentOrderItemStatus.DRAFT || item.Status == enum.BankingPaymentOrderItemStatus.PROCESSING {
				// still item not transfer
				return
			}
			if item.Status == enum.BankingPaymentOrderItemStatus.SUCCESS {
				indexSuccessTransfer += 1
			}
		}
		if indexSuccessTransfer == len(items) {
			// all item success
			statusOrder = enum.TransferPaymentStatus.SUCCESSFUL_TRANSFERRED
		} else if indexSuccessTransfer == 0 {
			// all item fail
			statusOrder = enum.TransferPaymentStatus.FAILED_TRANSFERRED
		} else {
			// partial success
			statusOrder = enum.TransferPaymentStatus.PARTIAL_SUCCESSFUL_TRANSFERRED
		}
		updaterBankingPaymentOrder := model.BankingPaymentOrder{
			TransferStatus: statusOrder,
		}
		model.BankingPaymentOrderDB.UpdateOne(
			model.BankingPaymentOrder{BankingPaymentOrderCode: dataCheck.BankingPaymentOrderCode},
			updaterBankingPaymentOrder)
	}
}

// SyncBankingPaymentOrderItemStatus synchronizes the status of items in a banking payment order.
// It performs the following steps:
// 1. Validates the input to ensure the BankingPaymentOrderCode is provided.
// 2. Checks if the banking payment order exists in the database.
// 3. Retrieves the items associated with the banking payment order.
// 4. Validates that the banking payment order is not cancelled.
// 5. Initializes counters for successful and failed transfers.
// 6. Iterates through each item in the banking payment order and performs the following:
//   - Retrieves the transaction details from the payment service.
//   - Updates the item status to DRAFT before switching status.
//   - Updates the item status based on the transaction status (SUCCESS or FAILED).
//   - Updates the total amount of successful and failed transfers.
//
// 7. Updates the banking payment order with the total amounts of successful and failed transfers.
// 8. Updates the transfer status of the banking payment order based on the counts of successful and failed items.
// 9. Returns an API response indicating the result of the synchronization.
//
// Parameters:
// - input: A pointer to a BankingPaymentOrder model containing the order details.
// - headers: A map of headers to be included in the API request.
//
// Returns:
// - A pointer to an APIResponse indicating the result of the synchronization.
func SyncBankingPaymentOrderItemStatus(input *model.BankingPaymentOrder, headers map[string]string) *common.APIResponse {

	if input.BankingPaymentOrderCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderCode is required.",
		}
	}

	// check banking payment order exists
	getBankingPOResp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrder{BankingPaymentOrderCode: input.BankingPaymentOrderCode})
	if getBankingPOResp.Status != common.APIStatus.Ok {
		return getBankingPOResp
	}

	// check banking payment order items exists
	bankingPO := getBankingPOResp.Data.([]*model.BankingPaymentOrder)[0]
	getBankingPOItemsResp := model.BankingPaymentOrderItemDB.Query(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, 0, 1000, nil)
	if getBankingPOItemsResp.Status != common.APIStatus.Ok {
		return getBankingPOItemsResp
	}
	bankingPO.Items = getBankingPOItemsResp.Data.([]*model.BankingPaymentOrderItem)

	// check banking payment order is not cancelled
	if bankingPO.Status == enum.BankingPaymentOrderStatus.CANCELLED {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not sync items of cancelled banking payment order.",
		}
	}

	bankingPO.TotalAmountTransferSuccess = utils.Pointer.WithInt64(0.0)
	bankingPO.TotalAmountTransferFail = utils.Pointer.WithInt64(0.0)

	failCount := 0
	successCount := 0

	// loop through the items to set their IDs and codes
	for _, item := range bankingPO.Items {

		opt := client.APIOption{
			Body:    model.Transaction{RequestId: item.BankingPaymentOrderItemCode},
			Headers: headers,
		}
		getBankingTransactionResp := payment.GetTransactionList(opt)
		if getBankingTransactionResp.Status == common.APIStatus.Ok {
			bankingTransactionData := getBankingTransactionResp.Data.([]*model.Transaction)[0]

			bankingPOTransferData := &model.TransferResult{
				TransactionId:   fmt.Sprintf("%v", bankingTransactionData.TransactionID),
				RefNum:          bankingTransactionData.RefNum,
				TransferResult:  bankingTransactionData.TransferResult,
				TransactionDate: bankingTransactionData.TransactionDate,
				TransferDate:    bankingTransactionData.TransferDate,
			}

			// update banking PO items to DRAFT before switch status
			updateBankingPOItem := model.BankingPaymentOrderItem{
				Status: enum.BankingPaymentOrderItemStatus.DRAFT,
			}
			// update banking payment order item
			model.BankingPaymentOrderItemDB.UpdateOne(model.BankingPaymentOrderItem{ID: item.ID}, updateBankingPOItem)

			if bankingTransactionData.TransactionStatus.Status == enum.TransactionStatusValue.Success {
				mapBankingResult(item, bankingPOTransferData)

				item.Status = enum.BankingPaymentOrderItemStatus.SUCCESS
				successCount += 1

				// increase amount success
				if bankingPO.TotalAmountTransferSuccess != nil {
					bankingPO.TotalAmountTransferSuccess = utils.Pointer.WithInt64(*bankingPO.TotalAmountTransferSuccess + item.Amount)
				} else {
					bankingPO.TotalAmountTransferSuccess = utils.Pointer.WithInt64(item.Amount)
				}

				// update banking payment order item
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.SUCCESS)
			} else if bankingTransactionData.TransactionStatus.Status == enum.TransactionStatusValue.Fail {
				item.Status = enum.BankingPaymentOrderItemStatus.FAILED
				item.Content = bankingTransactionData.ErrorDescription
				item.RefNum = bankingPOTransferData.RefNum

				failCount += 1

				// increase amount success
				if bankingPO.TotalAmountTransferFail != nil {
					bankingPO.TotalAmountTransferFail = utils.Pointer.WithInt64(*bankingPO.TotalAmountTransferFail + item.Amount)
				} else {
					bankingPO.TotalAmountTransferFail = utils.Pointer.WithInt64(item.Amount)
				}

				// update banking payment order item
				switchStatusBankingPaymentOrderItem(*item, enum.BankingPaymentOrderItemStatus.FAILED)
			} else {
				// model.BankingPaymentOrderItemDB.UpdateOne(
				// 	model.BankingPaymentOrderItem{ID: item.ID},
				// 	model.BankingPaymentOrderItem{Status: enum.BankingPaymentOrderItemStatus.PROCESSING},
				// )
			}
		} else {
			// model.BankingPaymentOrderItemDB.UpdateOne(
			// 	model.BankingPaymentOrderItem{ID: item.ID},
			// 	model.BankingPaymentOrderItem{Status: enum.BankingPaymentOrderItemStatus.PROCESSING},
			// )
		}
	}

	// update banking payment order
	model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, model.BankingPaymentOrder{
		TotalAmountTransferSuccess: bankingPO.TotalAmountTransferSuccess,
		TotalAmountTransferFail:    bankingPO.TotalAmountTransferFail,
	})

	// update banking payment order status
	if successCount == len(bankingPO.Items) {
		model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, model.BankingPaymentOrder{
			TransferStatus: enum.TransferPaymentStatus.SUCCESSFUL_TRANSFERRED,
		})
	} else if failCount == len(bankingPO.Items) {
		model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, model.BankingPaymentOrder{
			TransferStatus: enum.TransferPaymentStatus.FAILED_TRANSFERRED,
		})
	} else if successCount > 0 {
		model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, model.BankingPaymentOrder{
			TransferStatus: enum.TransferPaymentStatus.PARTIAL_SUCCESSFUL_TRANSFERRED,
		})
	} else {
		model.BankingPaymentOrderDB.UpdateOne(model.BankingPaymentOrder{BankingPaymentOrderCode: bankingPO.BankingPaymentOrderCode}, model.BankingPaymentOrder{
			TransferStatus: " ",
		})
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Sync banking payment order item success",
	}
}
