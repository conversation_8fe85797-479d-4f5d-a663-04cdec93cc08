package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SkuConfig struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`

	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`
	// Unit        string `json:"unit,omitempty" bson:"unit,omitempty"`

	PublishPriceAfterVAT *float64 `json:"publishPrice,omitempty" bson:"publish_price,omitempty"` // đã bao gồm VAT, cập nhật realtime giá tốt nhất
	ReferPrice           *float64 `json:"referPrice,omitempty" bson:"refer_price,omitempty"`     // đã bao gồm VAT
	PurchaseVAT          *int64   `json:"purchaseVat,omitempty" bson:"purchase_vat,omitempty"`

	IsActive       *bool   `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Reason         string  `json:"reason,omitempty" bson:"reason,omitempty"`
	EstActiveTimne *string `json:"estActiveTime,omitempty" bson:"est_active_timne,omitempty"`
	// MinInStock int64 `json:"minInStock,omitempty" bson:"min_in_stock,omitempty"`

	Vendors *[]VendorConfig `json:"vendors,omitempty" bson:"vendors,omitempty"`

	StopPublishBidding           *bool   `json:"stopPublishBidding,omitempty" bson:"stop_publish_bidding,omitempty"`                  // chặn báo giá công khai
	ReasonStopPublishBiddingCode *string `json:"reasonStopPublishBiddingCode,omitempty" bson:"reason_stop_publish_bidding,omitempty"` // chặn báo giá công khai

	StopFavQuotation *bool                           `json:"stopFavQuotation,omitempty" bson:"stop_fav_quotation,omitempty"` // chặn báo giá yêu thích
	StopFavDays      *StopFavoriteQuotationDayConfig `json:"stopFavDays,omitempty" bson:"stop_fav_days,omitempty"`           // chặn báo giá yêu thích theo ngày

	BiddingBlacklist *[]string `json:"biddingBlacklist,omitempty" bson:"bidding_blacklist,omitempty"` // danh sách vendor_code chặn báo giá công khai

	// For queue
	VendorSelected *VendorConfig `json:"-" bson:"vendor_selected,omitempty"`

	ComplexQuery     []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductCodeIn    *[]string `json:"productCodeIn,omitempty" bson:"-"`
	ProductIDIn      *[]int64  `json:"productIDIn,omitempty" bson:"-"`
	SKUIn            []string  `json:"skuIn,omitempty" bson:"-"`
	VendorCodeSearch *string   `json:"vendorCodeSearch,omitempty" bson:"-"`

	CreatedTimeFrom *time.Time          `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo   *time.Time          `json:"createdTimeTo,omitempty" bson:"-"`
	IDFrom          *primitive.ObjectID `json:"idFrom,omitempty" bson:"-"`
	IDTo            *primitive.ObjectID `json:"idTo,omitempty" bson:"-"`
	IsForceUpdate   bool                `json:"isForceUpdate,omitempty" bson:"-"`
}

type VendorConfig struct {
	VendorCode         string  `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	MinPerStep         int64   `json:"minPerStep,omitempty" bson:"min_per_step,omitempty"`
	MinPerOrder        int64   `json:"minPerOrder,omitempty" bson:"min_per_order,omitempty"`
	UnitPrice          float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	ReferPriceAfterVAT float64 `json:"referPrice,omitempty" bson:"refer_price,omitempty"` // đã bao gồm VAT
	DiscountPercent    float64 `json:"discountPercent,omitempty" bson:"discount_percent,omitempty"`
	VAT                int64   `json:"vat,omitempty" bson:"vat,omitempty"`
	Priority           *int64  `json:"priority,omitempty" bson:"priority,omitempty"`
	// PriceQuotationPercent int64   `json:"priceQuotationPercent,omitempty" bson:"price_quotation_percent,omitempty"` // default & 100% is same
}

type StopFavoriteQuotationDayConfig struct {
	Monday    *bool `json:"monday,omitempty" bson:"monday,omitempty"`
	Tuesday   *bool `json:"tuesday,omitempty" bson:"tuesday,omitempty"`
	Wednesday *bool `json:"wednesday,omitempty" bson:"wednesday,omitempty"`
	Thursday  *bool `json:"thursday,omitempty" bson:"thursday,omitempty"`
	Friday    *bool `json:"friday,omitempty" bson:"friday,omitempty"`
	Saturday  *bool `json:"saturday,omitempty" bson:"saturday,omitempty"`
	Sunday    *bool `json:"sunday,omitempty" bson:"sunday,omitempty"`
}

// key: sku, val: VendorID
var MapSkuConfig map[string]SkuConfig

// SkuConfigDB ...
var SkuConfigDB = &db.Instance{
	ColName:        "sku_config",
	TemplateObject: &SkuConfig{},
}

var SkuConfigReadDB = &db.Instance{
	ColName:        "sku_config",
	TemplateObject: &SkuConfig{},
}

// InitSkuModel ...
func InitSkuConfigModel(s *mongo.Database) {
	SkuConfigDB.ApplyDatabase(s)
	// // - INDEX
	// // - BE
	// // - FE
	// // productCode

	// t := true
	// _ = SkuConfigDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = SkuConfigDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = SkuConfigDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = SkuConfigDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = SkuConfigDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendors.vendor_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

func InitSkuConfigReadModel(s *mongo.Database) {
	SkuConfigReadDB.ApplyDatabase(s)
}
