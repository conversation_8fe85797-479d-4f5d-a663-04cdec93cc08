package action

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// {
//     "bankingPaymentOrderType": "DEBT",
//     "items": [
//         {
//             "vendorCode": "AMJL7STYAC",
//             "poCodes": [
//                 "PO215581"
//             ],
//             "vendorBillIds": [
//                 159194
//             ],
//             "amount": 270000,
//             "currency": "VND",
//             "bankCode": "AXDTGUBCV8",
//             "bankAccountName": "test",
//             "bankAccountNumber": "2323232",
//             "targetNumber": "2323232",
//             "remark": "Medx TT TAI KHOAN TEST - PO215581 . ID 1008334"
//         }
//     ]
// }

func CreateTransferRequest(input model.BankingPaymentOrder, createdByEmployeeID int64) *common.APIResponse {
	outputs := []*model.TransferRequest{}

	napasIndex := 0
	citadIndex := 1

	// split items
	inputItemSplitArr := [][]*model.BankingPaymentOrderItem{
		{}, {},
	}
	for i := range input.Items {
		item := input.Items[i]
		if item.Amount < 2000 || item.Amount >= ********* {
			inputItemSplitArr[citadIndex] = append(inputItemSplitArr[citadIndex], item)
		} else {
			inputItemSplitArr[napasIndex] = append(inputItemSplitArr[napasIndex], item)
		}
	}

	buildItemArr := [][]*model.TransferRequestItem{
		{}, {},
	}
	for i := range inputItemSplitArr {
		input.Items = inputItemSplitArr[i]

		if len(input.Items) == 0 {
			continue
		}

		// build transfer request items
		builtItemResp := CreateTransferRequestItems(&input)
		if builtItemResp.Status != common.APIStatus.Ok {
			return builtItemResp
		}
		buildItemArr[i] = append(buildItemArr[i], builtItemResp.Data.([]*model.TransferRequestItem)...)
	}

	for i := range buildItemArr {
		items := buildItemArr[i]

		if len(items) == 0 {
			continue
		}

		// build transfer request
		tr := &model.TransferRequest{
			CreatedByEmployeeID: createdByEmployeeID,

			OrgID:        2,
			BranchCode:   "VN",
			CurrencyCode: "VND",
			// TransactionBanking: "NAPAS",

			ReceiverType: "THUOCSIVN_VENDOR",

			Items: items,

			WorkflowCode: conf.Config.WorkflowTransferVendor,
		}

		if i == citadIndex {
			tr.TransactionBanking = "CITAD"
		}

		// change type để FE linh hoạt
		if input.BankingPaymentOrderType == enum.BankingPaymentOrderType.ADVANCE {
			tr.Type = enum.TransferRuleType.THUOCSIVN_PO
		} else if input.BankingPaymentOrderType == enum.BankingPaymentOrderType.DEBT {
			tr.Type = enum.TransferRuleType.THUOCSIVN_VENDOR_BILL
		}

		// forward
		cliOption := client.APIOption{
			SaveLog: utils.Pointer.WithBool(true),
			Body:    tr,
		}
		postResp := payment_platform.CreatePaymentOrder(cliOption)
		if postResp.Status != common.APIStatus.Ok {
			return postResp
		}
		outputs = append(outputs, postResp.Data.([]*model.TransferRequest)[0])

		// update transfer request code to PO, VB
		go updateCreatedTransferRequestCode(input, postResp.Data.([]*model.TransferRequest)[0])
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
		Data:    outputs,
	}
}

// update transfer request code to PO, VB
func updateCreatedTransferRequestCode(input model.BankingPaymentOrder, transferRequest *model.TransferRequest) {

	if transferRequest.TransferRequestCode != "" {
		return
	}

	for _, inputItem := range input.Items {
		if input.BankingPaymentOrderType == enum.BankingPaymentOrderType.ADVANCE {

			for _, code := range inputItem.POCodes {

				// get old
				poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: code})
				if poResp.Status != common.APIStatus.Ok {
					continue
				}
				po := poResp.Data.([]*model.PurchaseOrder)[0]

				// update CreatedTransferRequestCodes
				po.CreatedTransferRequestCodes = append(po.CreatedTransferRequestCodes, transferRequest.TransferRequestCode)
				model.PurchaseOrderDB.UpdateOne(
					model.PurchaseOrder{ID: po.ID},
					model.PurchaseOrder{CreatedTransferRequestCodes: po.CreatedTransferRequestCodes},
				)
			}

		} else if input.BankingPaymentOrderType == enum.BankingPaymentOrderType.DEBT {

			for _, id := range inputItem.VendorBillIDs {

				// get old
				vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillID: id})
				if vbResp.Status != common.APIStatus.Ok {
					continue
				}
				vb := vbResp.Data.([]*model.VendorBill)[0]

				// update CreatedTransferRequestCodes
				vb.CreatedTransferRequestCodes = append(vb.CreatedTransferRequestCodes, transferRequest.TransferRequestCode)
				model.VendorBillDB.UpdateOne(
					model.VendorBill{ID: vb.ID},
					model.VendorBill{CreatedTransferRequestCodes: vb.CreatedTransferRequestCodes},
				)
			}
		}
	}
}

func CreateTransferRequestItems(input *model.BankingPaymentOrder) *common.APIResponse {

	listItemResult := []*model.TransferRequestItem{}

	relateType := input.BankingPaymentOrderType
	if relateType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderType is required",
		}
	}

	vbMap := map[int64]*model.VendorBill{}
	vendorMap := map[string]*model.Seller{}

	nowYMD := time.Now().In(utils.TimeZoneVN).Format("2006-01-02 15:04")

	// validate
	for _, inputItem := range input.Items {

		if inputItem.Currency != "VND" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Currency is invalid",
			}
		}

		// get vendor
		vendorResp := getVendorInfoTR(inputItem.VendorCode, vendorMap, inputItem)
		if vendorResp.Status != common.APIStatus.Ok {
			return vendorResp
		}
		vendorMap[inputItem.VendorCode] = vendorResp.Data.([]*model.Seller)[0]

		if relateType == enum.BankingPaymentOrderType.DEBT {
			for _, relateID := range inputItem.VendorBillIDs {

				// get vb info
				vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillID: relateID})
				if vbResp.Status != common.APIStatus.Ok {
					return vbResp
				}
				vb := vbResp.Data.([]*model.VendorBill)[0]
				if vb.Status != enum.VendorBillStatus.WAIT_TO_PAID {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Data:    listItemResult,
						Message: fmt.Sprintf("VB %s not confirmed", vb.VendorBillCode),
					}
				}

				// map
				vbMap[relateID] = vb
			}

		} else if relateType == enum.BankingPaymentOrderType.ADVANCE {
			for _, relateCode := range inputItem.POCodes {
				// get po info
				poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: relateCode})
				if poResp.Status != common.APIStatus.Ok {
					return poResp
				}
				po := poResp.Data.([]*model.PurchaseOrder)[0]
				if po.Status != enum.PurchaseOrderStatus.CONFIRMED && po.Status != enum.PurchaseOrderStatus.HANDOVER_COMPLETED &&
					// po.Status != enum.PurchaseOrderStatus.PROCESSING
					po.Status != enum.PurchaseOrderStatus.PARTIALLY_RECEIVED && po.Status != enum.PurchaseOrderStatus.RECEIVED &&
					po.Status != enum.PurchaseOrderStatus.COMPLETED &&
					po.Status != enum.PurchaseOrderStatus.AWAITING_BILLED && po.Status != enum.PurchaseOrderStatus.BILLED {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: fmt.Sprintf("PO %s not in processing status", po.POCode),
					}
				}
			}
		} else {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    listItemResult,
				Message: "Invalid relate type",
			}
		}
	}

	// relateCodes is vendor bill code, po code
	for _, inputItem := range input.Items {

		itemTransferRequest := &model.TransferRequestItem{}

		if relateType == enum.BankingPaymentOrderType.DEBT {

			// amount need to trasfer
			var amount float64

			vbCodes := []string{}
			for _, relateCode := range inputItem.VendorBillIDs {
				// get vb info
				one := vbMap[relateCode]
				if one == nil {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: fmt.Sprintf("Vendor Bill %s not found", relateCode),
					}
				}

				if one.RemainingMoney != nil {
					amount += *one.RemainingMoney
				}

				vbCodes = append(vbCodes, one.VendorBillCode)
			}

			// get vendor
			vendor := vendorMap[inputItem.VendorCode]
			if vendor == nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: fmt.Sprintf("Vendor %s not found", inputItem.VendorCode),
				}
			}

			// build transfer request item
			itemTransferRequest = &model.TransferRequestItem{
				RelateType: enum.TransferRuleType.THUOCSIVN_VENDOR_BILL,
				RelateCode: strings.Join(vbCodes, ",") + ":" + nowYMD,

				ReceiverType:      "THUOCSIVN_VENDOR",
				ReceiverID:        vendor.SellerID,
				ReceiverCode:      vendor.Code,
				ItemObjectType:    enum.TransferRuleType.THUOCSIVN_VENDOR_BILL,
				ItemObjectCode:    strings.Join(vbCodes, ","),
				ItemObjectRefType: enum.TransferRuleType.THUOCSIVN_PO,
				ItemObjectRefCode: strings.Join(inputItem.POCodes, ","),

				ItemObjectExtras: &[]model.ItemObjectExtra{},

				Content: inputItem.Remark,

				Amount: amount,

				LogicalObject: &model.LogicalObject{
					MaxAmount:       amount,
					MaxLengthRemark: 150,
				},
			}

			// extra
			for _, id := range inputItem.VendorBillIDs {
				code := ""
				if vbMap[id] != nil {
					code = vbMap[id].VendorBillCode
				}
				*itemTransferRequest.ItemObjectExtras = append(*itemTransferRequest.ItemObjectExtras, model.ItemObjectExtra{
					Type:    string(enum.TransferRuleType.THUOCSIVN_VENDOR_BILL),
					ID:      id,
					Code:    code,
					Display: true,
				})
			}
			// for _, code := range inputItem.POCodes {
			// 	*itemTransferRequest.ItemObjectExtras = append(*itemTransferRequest.ItemObjectExtras, model.ItemObjectExtra{
			// 		Type: string(enum.TransferRuleType.THUOCSIVN_PO),
			// 		Code: code,
			// 	})
			// }

			// BankInfo
			// itemTransferRequest.ReceiverBankAccountName
			// itemTransferRequest.ReceiverBankBranchCode
			// itemTransferRequest.ReceiverBankBranchID
			itemTransferRequest.ReceiverBankName = vendor.PaymentInfo.BankName
			itemTransferRequest.ReceiverBankBranchName = &vendor.PaymentInfo.BankBranchName
			itemTransferRequest.ReceiverBankID = vendor.PaymentInfo.GlobalBankID
			itemTransferRequest.ReceiverBankCode = vendor.PaymentInfo.BankCode
			itemTransferRequest.ReceiverBankAccountName = inputItem.BankAccountName
			itemTransferRequest.ReceiverBankAccountNumber = inputItem.BankAccountNumber
			itemTransferRequest.ReceiverBankGlobalCode = "VN" + "_" + strconv.FormatInt(vendor.PaymentInfo.GlobalBankID, 10)

		} else if relateType == enum.BankingPaymentOrderType.ADVANCE {

			// get vendor
			vendor := vendorMap[inputItem.VendorCode]
			if vendor == nil {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: fmt.Sprintf("Vendor %s not found", inputItem.VendorCode),
				}
			}

			// build transfer request item
			itemTransferRequest = &model.TransferRequestItem{
				RelateType: enum.TransferRuleType.THUOCSIVN_PO,
				RelateCode: strings.Join(inputItem.POCodes, ",") + ":" + nowYMD,

				ReceiverType:   "THUOCSIVN_VENDOR",
				ReceiverID:     vendor.SellerID,
				ReceiverCode:   vendor.Code,
				ItemObjectType: enum.TransferRuleType.THUOCSIVN_PO,
				ItemObjectCode: strings.Join(inputItem.POCodes, ","),

				ItemObjectExtras: &[]model.ItemObjectExtra{},

				Content: inputItem.Remark,

				Amount: float64(inputItem.Amount),

				LogicalObject: &model.LogicalObject{
					MaxAmount:       float64(inputItem.Amount),
					MaxLengthRemark: 150,
				},
			}

			// extra
			for _, code := range inputItem.POCodes {
				*itemTransferRequest.ItemObjectExtras = append(*itemTransferRequest.ItemObjectExtras, model.ItemObjectExtra{
					Type: string(enum.TransferRuleType.THUOCSIVN_PO),
					Code: code,
				})
			}

			// BankInfo
			// itemTransferRequest.ReceiverBankAccountName
			// itemTransferRequest.ReceiverBankBranchCode
			// itemTransferRequest.ReceiverBankBranchID
			itemTransferRequest.ReceiverBankName = vendor.PaymentInfo.BankName
			itemTransferRequest.ReceiverBankBranchName = &vendor.PaymentInfo.BankBranchName
			itemTransferRequest.ReceiverBankID = vendor.PaymentInfo.GlobalBankID
			itemTransferRequest.ReceiverBankCode = vendor.PaymentInfo.BankCode
			itemTransferRequest.ReceiverBankAccountName = inputItem.BankAccountName
			itemTransferRequest.ReceiverBankAccountNumber = inputItem.BankAccountNumber
			itemTransferRequest.ReceiverBankGlobalCode = "VN" + "_" + strconv.FormatInt(vendor.PaymentInfo.GlobalBankID, 10)
		}

		listItemResult = append(listItemResult, itemTransferRequest)
	}

	if len(listItemResult) < len(input.Items) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Data:      listItemResult,
			Message:   "Fail to create transfer request item",
			ErrorCode: "CREATE_TRANSFER_REQUEST_ITEM_FAIL",
		}
	}

	// validate bank info
	for _, item := range listItemResult {
		if item.ReceiverBankAccountNumber == "" || item.ReceiverBankAccountName == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    listItemResult,
				Message: "Bank info is required for all items",
			}
		}
		if item.ReceiverCode == "" || item.ReceiverType == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    listItemResult,
				Message: "ReceiverCode, ReceiverType is required for all items",
			}
		}
		if item.ReceiverCode == "" || item.RelateCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    listItemResult,
				Message: "ReceiverCode, RelateCode is required for all items",
			}
		}
		if item.Amount <= 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Data:    listItemResult,
				Message: "Amount <= 0",
			}
		}
	}

	// desc amount
	sort.Slice(listItemResult, func(i, j int) bool {
		return listItemResult[i].Amount > listItemResult[j].Amount
	})

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Data:    listItemResult,
		Message: "Success",
	}
}

func getVendorInfoTR(vendorCode string, vendorMapCache map[string]*model.Seller, inputItem *model.BankingPaymentOrderItem) *common.APIResponse {
	if vendorCode == "" || inputItem.BankCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "VendorCode, BankCode is required",
		}
	}

	vendor := vendorMapCache[vendorCode]
	if vendorMapCache[vendorCode] == nil {
		// get vendor
		opts := client.APIOption{
			Q: model.Seller{
				SellerClass: model.CLASS_VENDOR,
			},
			Limit: utils.Pointer.WithInt(1),
			Params: map[string]string{
				"sellerCode": vendorCode,
				// "getPaymentInfo": "true",
			},
		}
		getVendorResp := seller.GetSellers(opts)
		if getVendorResp.Status != common.APIStatus.Ok {
			return getVendorResp
		}
		vendor = getVendorResp.Data.([]*model.Seller)[0]
		vendorMapCache[vendorCode] = vendor
	}

	// if vendor.PaymentInfo == nil {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Invalid,
	// 		Message: fmt.Sprintf("Vendor %s not have bank info", vendorCode),
	// 	}
	// }

	// get bank config
	opts := client.APIOption{
		Params: map[string]string{"bankCode": inputItem.BankCode},
		Limit:  utils.Pointer.WithInt(1),
	}
	bankResp := seller.GetBankConfig(opts)
	if bankResp.Status != common.APIStatus.Ok {
		return bankResp
	}
	bank := bankResp.Data.([]*model.Bank)[0]

	vendor.PaymentInfo = &model.PaymentInfo{
		SellerID:   vendor.SellerID,
		SellerCode: vendor.Code,

		BankAccountName:   inputItem.BankAccountName,
		BankAccountNumber: inputItem.BankAccountNumber,
		BankName:          bank.Name,
		BankCode:          inputItem.BankCode,
		BankID:            bank.BankID,

		GlobalBankID: bank.GlobalBankID,

		// HasPaymentForParentVendor
		// ParentVendorCode
	}

	// // get parent vendor
	// if vendor.PaymentInfo.HasPaymentForParentVendor && vendor.PaymentInfo.ParentVendorCode != "" {
	// 	vendor := vendorMapCache[vendor.PaymentInfo.ParentVendorCode]

	// 	if vendorMapCache[vendorCode] == nil {
	// 		// get parent vendor
	// 		opts := client.APIOption{
	// 			Q: model.Seller{
	// 				SellerClass: model.CLASS_VENDOR,
	// 			},
	// 			Limit: utils.Pointer.WithInt(1),
	// 			Params: map[string]string{
	// 				"sellerCode":     vendor.PaymentInfo.ParentVendorCode,
	// 				"getPaymentInfo": "true",
	// 			},
	// 		}
	// 		getParentVendorResp := seller.GetSellers(opts)
	// 		if getParentVendorResp.Status != common.APIStatus.Ok {
	// 			return getParentVendorResp
	// 		}
	// 		vendor = getParentVendorResp.Data.([]*model.Seller)[0]
	// 		vendorMapCache[vendor.PaymentInfo.ParentVendorCode] = vendor

	// 		if vendor.PaymentInfo == nil {
	// 			return &common.APIResponse{
	// 				Status:  common.APIStatus.Invalid,
	// 				Message: fmt.Sprintf("Parent Vendor %s not have bank info", vendor.PaymentInfo.ParentVendorCode),
	// 			}
	// 		}
	// 	}
	// }

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.Seller{vendor},
	}
}

func CallbackTransferRequest(input model.TransferRequest) *common.APIResponse {
	ttl := utils.GetFirstTimeOfDate(time.Now().AddDate(1, 0, 0))

	// validate
	if input.TransferRequestCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "TransferRequestCode is required",
		}
	}

	for _, item := range input.Items {

		// validate
		if item.ItemObjectType == "" || item.ItemObjectCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "ItemObjectType, ItemObjectCode is required for all items",
			}
		}

		// update po, vb
		if item.ItemObjectType == enum.TransferRuleType.THUOCSIVN_PO {
			if item.Status == "SUCCESS" {

				// 1 ItemObjectCode only create 1 payment
				lockResp := model.LockActionDB.Create(model.LockAction{
					Key:         "SUCCESS_TRI:" + item.TransferRequestItemCode,
					CreatedTime: &ttl,
				})
				if lockResp.Status != common.APIStatus.Ok {
					fmt.Println("CreateNewPayment error: ", lockResp.Message, item.TransferRequestItemCode)
					continue
				}

				// do create payment
				createPMResp := CreateNewPayment(&model.PaymentPlatform{
					Type:            "PAYMENT",
					Purpose:         "ADVANCE_MONEY",
					PaymentMethod:   "BANK",
					PartnerCode:     item.ReceiverCode,
					Amount:          item.Amount,
					Balance:         item.Amount,
					PaidTime:        item.LastUpdatedTime,
					TransactionCode: item.TransactionCode,
					Note:            item.TransactionCode,
					PORelatedCodes:  strings.Split(item.ItemObjectCode, ","),
					PrepaidPOCodes:  strings.Split(item.ItemObjectCode, ","),
					ExtraData: []model.RefMetadata{
						{
							Key:      "TRANSFER_REQUEST_ITEM_CODE",
							Value:    item.TransferRequestItemCode,
							KeyValue: fmt.Sprintf("%v:%v", "TRANSFER_REQUEST_ITEM_CODE", item.TransferRequestItemCode),
						},
					},
				})
				if createPMResp.Status != common.APIStatus.Ok {
					fmt.Println("CreateNewPayment error: ", createPMResp.Message, item.TransferRequestItemCode)
					continue
				}
				payment := createPMResp.Data.([]*model.PaymentPlatform)[0]

				// chi tạm ứng thành công -> tự động áp dụng cho các VB đang chờ thanh toán
				for _, poCode := range payment.DecodePrepaidPOs() {
					vbResp := model.VendorBillDB.Query(model.VendorBill{POCode: poCode, Status: enum.VendorBillStatus.WAIT_TO_PAID}, 0, 100, nil)
					if vbResp.Status != common.APIStatus.Ok {
						continue
					}
					for _, vb := range vbResp.Data.([]*model.VendorBill) {
						AutoApplyPaymentVoucherToVB(vb.VendorBillCode)
					}
				}
			}

		} else if item.ItemObjectType == enum.TransferRuleType.THUOCSIVN_VENDOR_BILL {
			if item.Status == "SUCCESS" {

				// 1 ItemObjectCode only create 1 payment
				lockResp := model.LockActionDB.Create(model.LockAction{
					Key:         "SUCCESS_TRI:" + item.TransferRequestItemCode,
					CreatedTime: &ttl,
				})
				if lockResp.Status != common.APIStatus.Ok {
					fmt.Println("CreateNewPayment error: ", lockResp.Message, item.TransferRequestItemCode)
					continue
				}

				// do create payment
				createPMResp := CreateNewPayment(&model.PaymentPlatform{
					Type:            "PAYMENT",
					Purpose:         "BILL",
					PaymentMethod:   "BANK",
					PartnerCode:     item.ReceiverCode,
					Amount:          item.Amount,
					Balance:         item.Amount,
					PaidTime:        item.LastUpdatedTime,
					TransactionCode: item.TransactionCode,
					Note:            item.TransactionCode,
					PORelatedCodes:  strings.Split(item.ItemObjectCode, ","),
					ExtraData: []model.RefMetadata{
						{
							Key:      "TRANSFER_REQUEST_ITEM_CODE",
							Value:    item.TransferRequestItemCode,
							KeyValue: fmt.Sprintf("%v:%v", "TRANSFER_REQUEST_ITEM_CODE", item.TransferRequestItemCode),
						},
					},
				})
				if createPMResp.Status != common.APIStatus.Ok {
					fmt.Println("CreateNewPayment error: ", createPMResp.Message, item.TransferRequestItemCode)
					continue
				}

				// AutoApplyPaymentVoucherToVB
				if item.ItemObjectExtras != nil {
					for _, item := range *item.ItemObjectExtras {
						if item.Type == string(enum.TransferRuleType.THUOCSIVN_VENDOR_BILL) {
							AutoApplyPaymentByPM(item.Code, createPMResp.Data.([]*model.PaymentPlatform)[0].PaymentCode)
						}
					}
				}
			}
		} else {
			fmt.Println("ItemObjectType not support: ", item.ItemObjectType, item.TransferRequestItemCode)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success",
	}
}

func DebugAutoApplyPaymentByPM(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PaymentItemPlatform
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if input.PaymentCode == "" || input.VendorBillCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentCode, VendorBillCode is required",
		})
	}

	AutoApplyPaymentByPM(input.VendorBillCode, input.PaymentCode)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Success!!",
	})
}

func AutoApplyPaymentByPM(vendorBillCode string, paymentCode string) {
	if vendorBillCode == "" || paymentCode == "" {
		fmt.Println("vendorBillCode == '' || paymentCode == ''", vendorBillCode, paymentCode)
		return
	}

	// get full VB data
	vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: vendorBillCode})
	if vbResp.Status != common.APIStatus.Ok {
		fmt.Println("GetVendorBill error: ", vbResp.Message, vendorBillCode, paymentCode)
		return
	}
	vbData := vbResp.Data.([]*model.VendorBill)[0]

	if vbData.Status == enum.VendorBillStatus.PAID {
		return
	}

	//Get payment info
	paymentResp := GetNewPayment(
		model.PaymentPlatform{
			PaymentCode: paymentCode,
		},
		0, 1, model.QueryOption{}, "-_id",
	)
	if paymentResp.Status != common.APIStatus.Ok {
		fmt.Println("GetNewPayment error: ", paymentResp.Message, vendorBillCode, paymentCode)
		return
	}
	payment := paymentResp.Data.([]*model.PaymentPlatform)[0]

	// Calculate total price of vendor bill when apply payment
	if payment.Balance == 0 {
		fmt.Println("payment.Balance == 0", vendorBillCode, paymentCode)
		return
	}
	if vbData.RemainingMoney == nil || *vbData.RemainingMoney <= 0 {
		fmt.Println("vbData.RemainingMoney <= 0", vendorBillCode, paymentCode)
		return
	}

	payAmount := payment.Balance
	if *vbData.RemainingMoney < payment.Balance {
		payAmount = *vbData.RemainingMoney
	}

	// call add payment item
	paymentItemFilter := &model.PaymentItemPlatform{
		PaymentCode:     payment.PaymentCode,
		VendorBillCode:  vbData.VendorBillCode,
		PartnerCode:     vbData.VendorCode,
		Amount:          &payAmount,
		TransactionType: "VENDOR_BILL",
	}
	resp := AddPaymentItemVB(paymentItemFilter)
	if resp.Status != common.APIStatus.Ok {
		fmt.Println("AddPaymentItemVB error: ", resp.Message, vendorBillCode, paymentCode)
		return
	}
}
