package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type InboundRequest struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ActionTime      *time.Time          `json:"actionTime,omitempty" bson:"action_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdById,omitempty" bson:"created_by_id,omitempty"`

	Name    string `json:"name,omitempty" bson:"name,omitempty"`
	HashTag string `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`

	ApplyTime string `json:"applyTime,omitempty" bson:"apply_time,omitempty"` // YYYY-MM-DD
	StartTime string `json:"startTime,omitempty" bson:"start_time,omitempty"` // YYYY-MM-DD
	EndTime   string `json:"endTime,omitempty" bson:"end_time,omitempty"`     // YYYY-MM-DD
	// StartWeekday int    `json:"startWeekday,omitempty" bson:"start_weekday,omitempty"` // 2,3,4,5,6,7,8
	EndWeekday int `json:"endWeekday,omitempty" bson:"end_weekday,omitempty"` // 2,3,4,5,6,7,8

	SellerCode    string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	InboundRequestID   int64  `json:"inboundRequestID,omitempty" bson:"inbound_request_id,omitempty"`
	InboundRequestCode string `json:"inboundRequestCode,omitempty" bson:"inbound_request_code,omitempty"`

	Status   enum.InboundRequestStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	Tags     *[]string                      `json:"tags,omitempty" bson:"tags,omitempty"`
	TagIn    []string                       `json:"tagIn,omitempty" bson:"-"`

	MigrateMetadata *MigrateMetadata `json:"-" bson:"migrate_metadata,omitempty"`
	SkuCodesSkip    *[]string        `json:"skuCodesSkip,omitempty" bson:"sku_codes_skip,omitempty"`

	ComplexQuery         []*bson.M             `json:"-" bson:"$and,omitempty"`
	Items                []*InboundRequestItem `json:"items,omitempty" bson:"-"`
	InboundRequestCodeIn []string              `json:"inboundRequestCodeIn,omitempty" bson:"-"`

	VendorCode *string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	InboundRequestType enum.InboundRequestTypeValue          `json:"inboundRequestType,omitempty" bson:"inbound_request_type,omitempty"`
	Reason             enum.InboundRequestReasonToOtherValue `json:"reason,omitempty" bson:"reason,omitempty"`
	TotalRebate        float64                               `json:"totalRebate,omitempty" bson:"total_rebate,omitempty"`
	PromotionCode      *string                               `json:"promotionCode,omitempty" bson:"promotion_code,omitempty"`
	PurchaseOrderCode  *string                               `json:"purchaseOrderCode,omitempty" bson:"purchase_order_code,omitempty"`
	POCodes            *[]string                             `json:"poCodes,omitempty" bson:"po_codes,omitempty"`
	ContractPriceCode  *string                               `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	DealTicketCode     *string                               `json:"dealTicketCode,omitempty" bson:"deal_ticket_code,omitempty"`
	IsPurchased        *bool                                 `json:"isPurchased,omitempty" bson:"is_purchased,omitempty"`

	//client query
	SkuCodeIn     []string   `json:"skuCodeIn,omitempty" bson:"-"`
	POCodeIn      []string   `json:"poCodeIn,omitempty" bson:"-"`
	ApplyTimeFrom string `json:"applyTimeFrom,omitempty" bson:"-"`
	ApplyTimeTo   string `json:"applyTimeTo,omitempty" bson:"-"`
}

type MigrateMetadata struct {
	OldSeller string `json:"-" bson:"old_seller,omitempty"`
	OldSKU    string `json:"-" bson:"old_sku,omitempty"`
}
type InboundRequestItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ApplyTime string `json:"applyTime,omitempty" bson:"apply_time,omitempty"` // YYYY-MM-DD
	StartTime string `json:"startTime,omitempty" bson:"start_time,omitempty"` // YYYY-MM-DD
	EndTime   string `json:"endTime,omitempty" bson:"end_time,omitempty"`     // YYYY-MM-DD
	// StartWeekday int    `json:"startWeekday,omitempty" bson:"start_weekday,omitempty"` // 2,3,4,5,6,7,8
	// EndWeekday   int    `json:"endWeekday,omitempty" bson:"end_weekday,omitempty"`     // 2,3,4,5,6,7,8

	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	SellerCode    string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	InboundRequestID       int64  `json:"inboundRequestID,omitempty" bson:"inbound_request_id,omitempty"`
	InboundRequestCode     string `json:"inboundRequestCode,omitempty" bson:"inbound_request_code,omitempty"`
	InboundRequestItemID   int64  `json:"inboundRequestItemID,omitempty" bson:"inbound_request_item_id,omitempty"`
	InboundRequestItemCode string `json:"inboundRequestItemCode,omitempty" bson:"inbound_request_item_code,omitempty"`
	POCode                 string `json:"poCode,omitempty" bson:"po_code,omitempty"`

	InboundRequestType enum.InboundRequestTypeValue `json:"inboundRequestType,omitempty" bson:"inbound_request_type,omitempty"`
	ExpectQuantity     int64                        `json:"expectQuantity,omitempty" bson:"expect_quantity,omitempty"`

	Metadata      *map[string]interface{}        `json:"metadata,omitempty" bson:"metadata,omitempty"` // AvgDemand, NumDayInStock

	MigrateMetadata *MigrateMetadata `json:"-" bson:"migrate_metadata,omitempty"`

	Status   enum.InboundRequestStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`

	VendorCode *string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	// Contract price
	UnitPrice              float64    `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	VAT                    float64    `json:"vat,omitempty" bson:"vat,omitempty"`
	Discount               float64    `json:"discount,omitempty" bson:"discount,omitempty"`
	ContractPriceCode      string     `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	ContractPriceStartTime *time.Time `json:"contractPriceStartTime,omitempty" bson:"contract_price_start_time,omitempty"`
	ContractPriceEndTime   *time.Time `json:"contractPriceEndTime,omitempty" bson:"contract_price_end_time,omitempty"`

	// Promotion - Deal
	DealTicketCode string  `json:"dealTicketCode,omitempty" bson:"deal_ticket_code,omitempty"`
	Seller         *Seller `json:"seller,omitempty" bson:"-"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// InboundRequestDB ...
var InboundRequestDB = &db.Instance{
	ColName:        "inbound_request",
	TemplateObject: &InboundRequest{},
}

// InboundRequestItemDB ...
var InboundRequestItemDB = &db.Instance{
	ColName:        "inbound_request_item",
	TemplateObject: &InboundRequestItem{},
}

// InitInboundRequestItemModel ...
func InitInboundRequestItemModel(s *mongo.Database) {
	InboundRequestDB.ApplyDatabase(s)
	InboundRequestItemDB.ApplyDatabase(s)

	// t := true
	// _ = InboundRequestDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "inbound_request_type", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// t := true
	// _ = InboundRequestDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "promotion_code", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "apply_time", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "inbound_request_type", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	PartialFilterExpression: bson.D{
	// 		primitive.E{Key: "promotion_code", Value: bson.D{
	// 			primitive.E{Key: "$exists", Value: true},
	// 		},
	// 		},
	// 	},
	// 	Background: &t,
	// })

	// _ = InboundRequestItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "inbound_request_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = InboundRequestItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "inbound_request_type", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	//
	// 	primitive.E{Key: "end_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = InboundRequestItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "apply_time", Value: 1},
	// 	primitive.E{Key: "is_active", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
