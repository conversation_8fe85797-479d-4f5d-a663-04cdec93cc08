package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SkuTradingPrice struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// basic
	Sku           string    `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode    string    `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	ProductID     int64     `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode   string    `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ItemCode      string    `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	WarehouseCode string    `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"` // kho quản lý tỉnh, vùng đăng bán
	LocationCode  *[]string `json:"locationCode,omitempty" bson:"location_code,omitempty"`

	// main
	VendorCode       string   `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`              // nhà cung cấp ưu tiên
	MarginPercent    *float64 `json:"marginPercent,omitempty" bson:"margin_percent,omitempty"`        // margin %
	PriceAfterRebate *float64 `json:"priceAfterRebate,omitempty" bson:"price_after_rebate,omitempty"` // giá vốn
	AverageCostPrice *float64 `json:"averageCostPrice,omitempty" bson:"average_cost_price,omitempty"` // giá vốn bình quân
	SettingPrice     *float64 `json:"settingPrice,omitempty" bson:"setting_price,omitempty"`          // giá cài đặt -> tính toán
	MinRetailPrice   *float64 `json:"minRetailPrice,omitempty" bson:"min_retail_price,omitempty"`     // giá bán lẻ/web tối thiểu
	MaxRetailPrice   *float64 `json:"maxRetailPrice,omitempty" bson:"max_retail_price,omitempty"`     // giá bán lẻ/web tối đa
	ProfitAmount     *float64 `json:"profitPrice,omitempty" bson:"profit_price,omitempty"`            // lợi nhuận
	PriceAfterMargin *float64 `json:"priceAfterMargin,omitempty" bson:"price_after_margin,omitempty"` // giá sau margin
	// log
	RetailPriceType           string      `json:"retailPriceType,omitempty" bson:"retail_price_type,omitempty"`                       // loại giá bán lẻ
	Version                   string      `json:"version,omitempty" bson:"version,omitempty"`                                         // version
	Formulars                 *[]string   `json:"fomular,omitempty" bson:"fomular,omitempty"`                                         // công thức tính giá
	LoopCount                 *int        `json:"loopCount,omitempty" bson:"loop_count,omitempty"`                                    // lần lặp
	ChildVendorCode           string      `json:"childVendorCode,omitempty" bson:"child_vendor_code,omitempty"`                       // Nhà cung cấp con fav
	FeeApply                  *[]string   `json:"feeCodeApply,omitempty" bson:"fee_code_apply,omitempty"`                             // phí áp dụng
	ErrorLogs                 *[]ErrorLog `json:"errorLogs,omitempty" bson:"error_logs,omitempty"`                                    //
	UsePriceAfterReabateAdhoc bool        `json:"usePriceAfterReabateAdhoc,omitempty" bson:"use_price_after_reabate_adhoc,omitempty"` // dùng giá sau rebate adhoc
	ContractPriceCode         string      `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`                   // mã hợp đồng
	ContractPriceID           int64       `json:"contractPriceID,omitempty" bson:"contract_price_id,omitempty"`                       // id hợp đồng
	// max/min fee
	MaxCustomerLevelFee *float64 `json:"maxCustomerLevelFee,omitempty" bson:"max_customer_level_fee,omitempty"`
	MinCustomerLevelFee *float64 `json:"minCustomerLevelFee,omitempty" bson:"min_customer_level_fee,omitempty"`
	MaxDeliveryFee      *float64 `json:"maxDeliveryFee,omitempty" bson:"max_delivery_fee,omitempty"`
	MinDeliveryFee      *float64 `json:"minDeliveryFee,omitempty" bson:"min_delivery_fee,omitempty"`

	// PriceRangeValue           *float64           `json:"priceRangeValue,omitempty" bson:"price_range_value,omitempty"`                       // giá trị khoảng giá
	// DynamicLevelValue         *float64           `json:"dynamicLevelValue,omitempty" bson:"dynamic_level_value,omitempty"`                   // giá trị level động
	FeeParams map[string]float64 `json:"feeParams,omitempty" bson:"fee_params,omitempty"` // fee params
	// info
	Hashtag      string   `json:"hashtag,omitempty" bson:"hashtag,omitempty"`
	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`

	// client query
	SkuItemCodeIn []string `json:"skuItemCodeIn,omitempty" bson:"-"` // sku + item code
}

var SkuTradingPriceDB = &db.Instance{
	ColName:        "sku_trading_price",
	TemplateObject: new(SkuTradingPrice),
}

func InitSkuTradingPriceDB(s *mongo.Database) {
	SkuTradingPriceDB.ApplyDatabase(s)
}
