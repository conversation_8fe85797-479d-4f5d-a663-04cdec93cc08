package api

import (
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SellerCreateBiddingRates
func SellerCreateBiddingRates(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetParam("sellerCode")
	)

	var inputs action.CreateBiddingRateInput

	err := req.GetContent(&inputs)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.SellerCreateBiddingRate(inputs, sellerCode))
}

// GetSellerBiddingRate ... normal is "$gt": 0
func GetSellerBiddingRate(req sdk.APIRequest, resp sdk.APIResponder) error {

	var payload model.GetSellerBiddingRateRequest

	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	query := payload.Query

	if len(query.WarehouseCodes) > 0 {
		query.WarehouseCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}

	// fulltext search
	if payload.Search != "" {
		payload.Search = strings.Replace(utils.NormalizeString(payload.Search), " ", "-", -1)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$text": bson.M{
				"$search": "\"" + payload.Search + "\"",
			},
		})

	}
	if len(query.SellerCodes) > 0 {
		query.SellerCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"seller_code": bson.M{
				"$in": query.SellerCodes,
			},
		})
	}
	if query.POCodes != nil && len(*query.POCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"po_codes": bson.M{"$in": *query.POCodes},
		})
		query.POCodes = nil
	}
	if payload.GetToday {
		query.DaySpan = utils.GetCurrentVersionDDMMYYYY()
		query.IsValid = true
	}

	// do action
	return resp.Respond(action.GetBiddingRateSearch(query, payload.Offset, payload.Limit, payload.GetTotal, payload.GetSkuConfig, payload.GetToday, payload.Sort))
}

// DeleteBiddingRates
func DeleteBiddingRates(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode       = req.GetParam("sellerCode")
		warehouseCode    = req.GetParam("warehouseCode")
		isForceDeleteAll = req.GetParam("isForceDeleteAll") == "true"
		productCodes     = req.GetParam("productCodes")
		skus             = req.GetParam("skus")
	)
	sellerCode = strings.ToUpper(sellerCode)
	warehouseCode = strings.ToUpper(warehouseCode)

	codesArr := []string{}
	if len(productCodes) > 0 {
		spArr := strings.Split(productCodes, ",")
		for _, code := range spArr {
			code = strings.TrimSpace(code)
			if code != "" {
				codesArr = append(codesArr, code)
			}
		}
	}

	skusArr := []string{}
	if len(skus) > 0 {
		spArr := strings.Split(skus, ",")
		for _, code := range spArr {
			code = strings.TrimSpace(code)
			if code != "" {
				skusArr = append(skusArr, code)
			}
		}
	}

	if acc := getActionSource(req); acc != nil {
		if acc.Type == "EMPLOYEE" {
			fmt.Println(acc.Username + " delete all bidding rate")
			return resp.Respond(action.DeleteAllBiddingRates(isForceDeleteAll, sellerCode, codesArr, skusArr, warehouseCode))
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// do not use
// func MigrateBiddingRate(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	var (
// 		areYouSure = req.GetParam("areYouSure")
// 	)
// 	if areYouSure != "true" {
// 		return resp.Respond(&common.APIResponse{
// 			Status:  common.APIStatus.Invalid,
// 			Message: "areYouSure.",
// 		})
// 	}
// 	// get all quotation
// 	// get all quotation hedging
// 	// get all bidding rate

// 	return resp.Respond(&common.APIResponse{
// 		Status:  common.APIStatus.Ok,
// 		Message: "Done.",
// 	})
// }

// SellerRecountBiddingRates
// SellerRecountBiddingRates handles the recounting of bidding rates for a given purchase order code (poCode).
// It validates the presence of the poCode parameter and triggers an asynchronous recount operation.
//
// Parameters:
//   - req: The API request containing the parameters.
//   - resp: The API responder used to send the response.
//
// Returns:
//   - error: An error if the poCode parameter is missing, otherwise nil.
//
// The function performs the following steps:
//   1. Extracts the poCode parameter from the request.
//   2. Validates that the poCode parameter is not empty.
//   3. If the poCode is empty, responds with an invalid status and an appropriate message.
//   4. If the poCode is present, triggers an asynchronous recount operation for the given poCode.
//   5. Responds with a status indicating that the recounting process has started.
func SellerRecountBiddingRates(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		poCode = req.GetParam("poCode")
	)
	if poCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCode is required.",
		})
	}

	go sdk.Execute(func() {
		action.RecountQuantityPoCodesToBiddingRate(poCode, []string{})
	})

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Recounting",
	})
}

// SellerUpdateBiddingRates
func SellerUpdateBiddingRates(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.BiddingRate

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.SellerUpdateBiddingRate(input))
}

// GetSkuItem
func GetSkuItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sku       = req.GetParam("sku")
		Productid = sdk.ParseInt64(req.GetParam("productid"), 0)
		wh        = req.GetParam("wh")
	)
	action.GetSkuItemFromSKU(Productid, sku, wh)
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Recounting",
	})
}

// PutBiddingRatesNote
func PutBiddingRatesNote(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PUTBiddingRateNoteRequest

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.SKU == "" || input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "SKU and WarehouseCode is required.",
		})
	}
	if input.OldStatus == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "OldStatus is required.",
		})
	}
	if input.NewStatus == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "NewStatus is required.",
		})
	}

	return resp.Respond(action.PUTBiddingRateNote(input))
}

// UpdateIsValidField
func UpdateIsValidField(req sdk.APIRequest, resp sdk.APIResponder) error {

	areYouSure := req.GetParam("areYouSure") == "true"
	revertNote := req.GetParam("revertNote") == "true"
	if areYouSure {
		model.BiddingRateDB.UpdateMany(
			&model.BiddingRate{
				ComplexQuery: []*bson.M{{
					"$or": []bson.M{
						{"is_valid": bson.M{"$exists": false}},
						{"is_valid": false},
					},
				}},
			},
			&model.BiddingRate{
				IsValid: true,
			},
		)
	}
	if revertNote {
		_id := primitive.NilObjectID
		for {
			biddingRateResp := model.BiddingRateDB.Query(model.BiddingRate{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			}, 0, 1000, &primitive.M{"_id": 1})
			if biddingRateResp.Status != common.APIStatus.Ok {
				break
			}
			biddingRateData := biddingRateResp.Data.([]*model.BiddingRate)
			for _, bd := range biddingRateData {

				_id = *bd.ID
				if bd == nil || bd.Notes == nil || len(*bd.Notes) == 0 {
					continue
				}

				newNotes := []string{}
				for _, note := range *bd.Notes {
					newNotes = append([]string{note}, newNotes...)
				}
				model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: bd.ID}, &model.BiddingRate{Notes: &newNotes})
			}
		}
	}
	// do action
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done",
	})
}
