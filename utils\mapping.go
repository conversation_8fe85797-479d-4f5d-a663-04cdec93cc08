package utils

import (
	"sync"

	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

type Warehouse struct {
	ID   int64
	Code string
}

var (
	purchaserMutex          sync.RWMutex
	wmsMutex                sync.RWMutex
	mapPurchaserToWarehouse = map[string][]Warehouse{}
	mapWarehouseToPurchaser = map[Warehouse]string{}
	PURCHASERS_CODES        = []string{}
)

func WarehouseCodeToPurchaserCode(warehouseCode string) (string, bool) {
	wms, ok := SelectWMSByCode(warehouseCode)
	if ok {
		return mapWarehouseToPurchaser[wms], true
	}
	return "", false
}

func IsInterceptWarehouseOfSinglePurchaser(warehouseCodes ...string) bool {
	prevPurchaserCode := ""
	for i, wmsCode := range warehouseCodes {
		if i == 0 {
			purchaserCodeTemp, ok := WarehouseCodeToPurchaserCode(wmsCode)
			if !ok {
				return false
			}
			prevPurchaserCode = purchaserCodeTemp
		}

		purchaserCodeTemp, ok := WarehouseCodeToPurchaserCode(wmsCode)
		if !ok {
			return false
		}
		if purchaserCodeTemp != prevPurchaserCode {
			return false
		}
	}
	return true
}

func SetMapPurchaserToWarehouses(pMap map[string][]Warehouse) {
	purchaserMutex.Lock()
	defer purchaserMutex.Unlock()
	mapPurchaserToWarehouse = pMap
}

func SetMapWarehouseToPurchaser(pMap map[Warehouse]string) {
	purchaserMutex.Lock()
	defer purchaserMutex.Unlock()
	mapWarehouseToPurchaser = pMap
}

func SetPurchaserCodes(purCodes []string) {
	PURCHASERS_CODES = purCodes
}

func SetWarehouses(wms []Warehouse) {
	wmsMutex.Lock()
	defer wmsMutex.Unlock()
	WAREHOUSES = wms
}

func SelectWMSByID(id int64) (Warehouse, bool) {
	for _, wms := range WAREHOUSES {
		if wms.ID == id {
			return wms, true
		}
	}
	return Warehouse{}, false
}

func SelectWMSByCode(code string) (Warehouse, bool) {
	for _, wms := range WAREHOUSES {
		if wms.Code == code {
			return wms, true
		}
	}
	return Warehouse{}, false
}

func SetWarehousesModel(wms []*model.Warehouse) {
	wmsMutex.Lock()
	defer wmsMutex.Unlock()
	WAREHOUSES_MODEL = wms
}

func GetWarehouseByID(id int) (model.Warehouse, bool) {
	for _, wms := range WAREHOUSES_MODEL {
		if wms.WarehouseID == id {
			return *wms, true
		}
	}
	return model.Warehouse{}, false
}

func GetWarehouseByCode(code string) (model.Warehouse, bool) {
	for _, wms := range WAREHOUSES_MODEL {
		if wms.Code == code {
			return *wms, true
		}
	}
	return model.Warehouse{}, false
}
