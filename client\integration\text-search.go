package integration

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/mongo"
)

var searchClient *client.Client

const (
	postSearchProduct = "POST::/search"
	putDocument       = "PUT::/document"
	putListDocument   = "PUT::/document/list"
	deleteDocument    = "DELETE::/document"
)

func InitProductTextSearch(database *mongo.Database) {
	const textSearchV1 = "/integration/text-search/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	searchClient = client.NewClient(textSearchV1, headers, 0)
	searchClient.WithDatabase(database)
	searchClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     postSearchProduct,
				Name:     "text_search__POST__search_query",
				Database: database,
			},

			{
				Path:     putDocument,
				Name:     "text_search__PUT__update_document",
				Database: database,
			},
			{
				Path:     putListDocument,
				Name:     "text_search__PUT__update_list_document",
				Database: database,
			},
			{
				Path:     deleteDocument,
				Name:     "text_search__DELETE__remove_document",
				Database: database,
			},
		}...,
	)
}

func SearchProduct(opts ...client.APIOption) *common.APIResponse {
	requestOption := searchClient.WithAPIOption(opts...)
	searchInput, ok := validateSearch(requestOption)
	if !ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid request body",
		}
	}

	limitOrg := searchInput.Limit
	offsetLimit := searchInput.Offset + searchInput.Limit
	if searchInput.Limit > 100 {
		searchInput.Limit = 100
	}

	// do
	resp := search(requestOption)

	// loop search
	if limitOrg > 100 {
		for {
			searchInput.Offset += searchInput.Limit
			if searchInput.Offset >= offsetLimit || int64(len(resp.Data)) >= resp.Total {
				break
			}

			resultL := search(requestOption)
			if resultL.Status != common.APIStatus.Ok {
				break
			}
			resp.Data = append(resp.Data, resultL.Data...)
		}
	}

	return client.FormatResponse(resp.APIResponse, resp.Data, nil)
}

// do search
func search(requestOption client.APIOption) *SearchResponse {
	var resp SearchResponse
	searchClient.WithRequest(postSearchProduct, requestOption, &resp)
	return &resp
}

// validate search input
func validateSearch(searchOption client.APIOption) (model.SearchInput, bool) {
	searchInput, ok := searchOption.Body.(model.SearchInput)
	if !ok {
		return searchInput, false
	}
	return searchInput, true
}

func UpdateProductContent(opts ...client.APIOption) *common.APIResponse {
	requestOption := searchClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := searchClient.WithRequest(putDocument, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func UpdateMultiProductContent(opts ...client.APIOption) *common.APIResponse {
	requestOption := searchClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := searchClient.WithRequest(putListDocument, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func RemoveProduct(opts ...client.APIOption) *common.APIResponse {
	requestOption := searchClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := searchClient.WithRequest(deleteDocument, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
