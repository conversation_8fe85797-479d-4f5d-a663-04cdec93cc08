package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSkuTradingPrice
func GetSkuTradingPrice(req sdk.APIRequest, resp sdk.APIResponder) error {

	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		sort     = req.GetParam("sort")
		qStr     = req.GetParam("q")

		query = model.SkuTradingPrice{}
	)

	// parse input
	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// validate input
	if len(query.SkuItemCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"item_code": bson.M{"$in": query.SkuItemCodeIn},
		})
	}

	// validate input
	return resp.Respond(action.GetSkuTradingPrice(query, offset, limit, getTotal, enum.SkuTradingPriceSort(sort)))
}
