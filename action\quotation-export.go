package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetQuotationExport retrieves quotation export data based on the provided query parameters.
// It first checks the status of the provided group code and updates the query version accordingly.
// Then, it queries the primary database for quotation export data with the specified offset and limit.
// If getTotal is true, it also retrieves the total count of matching records.
//
// Parameters:
//   - query: A pointer to a model.QuotationExport struct containing the query parameters.
//   - groupCode: A string representing the group code to check the status of.
//   - offset: An int64 representing the offset for pagination.
//   - limit: An int64 representing the limit for pagination.
//   - getTotal: A boolean indicating whether to retrieve the total count of matching records.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the query results or an error status.
func GetQuotationExport(query *model.QuotationExport, groupCode string, offset, limit int64, getTotal bool) *common.APIResponse {

	// Check status of groupCode
	adminPushingGroupResp := model.AdminPushingGroupDB.QueryOne(model.AdminPushingGroup{
		GroupCode: groupCode,
	})
	if adminPushingGroupResp.Status != common.APIStatus.Ok {
		return adminPushingGroupResp
	}
	adminPushingGroup := adminPushingGroupResp.Data.([]*model.AdminPushingGroup)[0]
	query.Version = adminPushingGroup.Version

	// Query primary DB
	quotationResp := model.QuotationExportDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.QuotationExportDB.Count(query)
		quotationResp.Total = countResp.Total
	}

	return quotationResp
}

// GetQuotationExportDraft retrieves a draft of quotation exports based on the provided query parameters.
// It first checks the admin pushing group associated with the given group code and sets the version of the query
// to match the version of the admin pushing group. Depending on the quotation type, it queries the appropriate
// draft database for the quotation exports. If getTotal is true, it also retrieves the total count of matching
// quotations.
//
// Parameters:
//   - query: A pointer to a QuotationExport model containing the query parameters.
//   - groupCode: A string representing the group code to identify the admin pushing group.
//   - offset: An int64 representing the offset for pagination.
//   - limit: An int64 representing the limit for pagination.
//   - getTotal: A boolean indicating whether to retrieve the total count of matching quotations.
//   - quotationType: An enum representing the type of quotation.
//
// Returns:
//   - *common.APIResponse: The API response containing the queried quotation exports or an error status.
func GetQuotationExportDraft(query *model.QuotationExport, groupCode string, offset, limit int64, getTotal bool, quotationType enum.QuotationType) *common.APIResponse {
	adminPushingGroupResp := model.AdminPushingGroupDB.QueryOne(model.AdminPushingGroup{
		GroupCode: groupCode,
	})
	if adminPushingGroupResp.Status != common.APIStatus.Ok {
		return adminPushingGroupResp
	}
	adminPushingGroup := adminPushingGroupResp.Data.([]*model.AdminPushingGroup)[0]
	query.Version = adminPushingGroup.Version

	var db *db.Instance
	// Query draft DB
	switch quotationType {
	case enum.QuotationTypeVal.HEDGING:
		db = model.QuotationExportHedgingDraftDB
	default:
		db = model.QuotationExportDraftDB
	}
	
	// Query draft DB
	quotationResp := db.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := db.Count(query)
		quotationResp.Total = countResp.Total
	}
	return quotationResp
}
