package model

import (
	"fmt"
	"strconv"
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

type PaymentPlatform struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty"`
	PaidTime        *time.Time `json:"paidTime,omitempty"`
	// CreatedByAccountID int64      `json:"createdByAccountID,omitempty"`
	CreatedBySystem bool       `json:"createdBySystem,omitempty"`
	PayDueTime      *time.Time `json:"payDueTime,omitempty"` //Hạn thanh toán

	OrgID        int64  `json:"orgID,omitempty"`      // 1 || 2 || 3
	BranchCode   string `json:"branchCode,omitempty"` // VN || KH || TH
	CurrencyCode string `json:"currencyCode,omitempty"`

	CompanyCode string `json:"companyCode,omitempty"`

	PaymentID   int64  `json:"paymentID,omitempty"`
	PaymentCode string `json:"paymentCode,omitempty"`

	Type enum.PaymentPlatfromType `json:"type,omitempty"`

	HashTag         string    `json:"hashtag,omitempty"`
	ObjectTypes     *[]string `json:"objectTypes,omitempty"`
	ObjectTypeCodes *[]string `json:"objectTypeCodes,omitempty"`

	// người nhận (ts gửi) hoặc người gửi (gửi cho ts)
	PartnerType enum.PaymentPlatformPartnerTypeValue `json:"partnerType,omitempty"` // CUSTOMER
	PartnerID   int64                                `json:"partnerID,omitempty"`
	PartnerCode string                               `json:"partnerCode,omitempty"`
	PartnerName string                               `json:"partnerName,omitempty"`
	ExtraData   []RefMetadata                        `json:"extraData,omitempty"` // cho partner custom data, trường hợp cần lưu thêm thông tin
	ReasonCode  string                               `json:"reasonCode,omitempty"`

	Amount  float64 `json:"amount,omitempty"`
	Balance float64 `json:"balance,omitempty"`

	PaymentMethod enum.PaymentPlatformMethodValue `json:"paymentMethod,omitempty"` // enum
	Source        enum.PaymentCreatedBySourceEnum `json:"source,omitempty"`        // required

	Note            string `json:"note,omitempty"`
	TransactionCode string `json:"transactionCode,omitempty"`

	Status enum.PaymentPlatformStatusValue `json:"status,omitempty"`

	Items []*PaymentItemPlatform `json:"items,omitempty"`

	// query
	CreatedTimeFrom    *time.Time `json:"createdTimeFrom,omitempty"`
	CreatedTimeTo      *time.Time `json:"createdTimeTo,omitempty"`
	LastUpdateTimeFrom *time.Time `json:"lastUpdateTimeFrom,omitempty"`
	LastUpdateTimeTo   *time.Time `json:"lastUpdateTimeTo,omitempty"`
	Search             string     `json:"search,omitempty"`
	// ActionType         string     `json:"actionType,omitempty"`

	// forward query
	PaymentCodeIn     []string   `json:"paymentCodeIn,omitempty"`
	PaidFrom          *time.Time `json:"paidFrom,omitempty"`
	PaidTo            *time.Time `json:"paidTo,omitempty"`
	IsHaveBalance     bool       `json:"isHaveBalance,omitempty"`
	ExtraDataKeyValIn []string   `json:"extraDataKeyValIn,omitempty"`
	PartnerCodeIn     []string   `json:"partnerCodeIn,omitempty"`

	// only logic
	Reason ReasonPaymentParse `json:"-"`

	// support fields
	PrepaidPOCodes     []string `json:"prePaidPOCodes,omitempty"`     // PO (trả tạm ứng)
	RefundBillCode     string   `json:"refundBillCode,omitempty"`     // link to refund bill
	AdjustmentBillCode string   `json:"adjustmentBillCode,omitempty"` // hoá đơn điều chỉnh (loại giảm)
	PORelatedCodes     []string `json:"poCodes,omitempty"`            // PO liên quan
	Token              string   `json:"token,omitempty"`
	LegacyPaymentCode  string   `json:"legacyPaymentCode,omitempty"`
	LegacyPaymendID    int64    `json:"legacyPaymendID,omitempty"`
	CreatedByID        int64    `json:"createdById,omitempty" bson:"created_by_id,omitempty"`
	Purpose            string   `json:"purpose,omitempty"` // mục đích thanh toán (search, tracking)

	// suport query
	POCodeIn             []string `json:"poCodeIn,omitempty"`
	LegacyPaymentCodeIn  []string `json:"legacyPaymentCodeIn,omitempty"`
	RefundBillCodeIn     []string `json:"refundBillCodeIn,omitempty"`
	PrepaidPOCodeIn      []string `json:"prepaidPOCodeIn,omitempty"`
	AdjustmentBillCodeIn []string `json:"adjustmentBillCodeIn,omitempty"`

	// ObjectType      enum.PaymentObjectTypeValue `json:"objectType,omitempty"`
	// ObjectCodes     []string                    `json:"objectCodes,omitempty"`
}

func (p *PaymentPlatform) FillData(option ...OptionFillDataPaymentPlatform) {
	opt := mergeOptionFillDataPaymentPlatform(option)
	p.OrgID = conf.Config.OrgID
	p.CompanyCode = conf.Config.CompanyCode
	p.PartnerType = enum.PaymentPlatformPartnerType.THUOCSIVN_VENDOR
	p.Status = enum.PaymentPlatformStatus.WAIT_TO_APPROVED

	if opt.ExtraData {
		for _, poCode := range p.PORelatedCodes {
			ref := RefMetadata{
				Key:      "PO_RELATED",
				Value:    poCode,
				KeyValue: "PO_RELATED:" + poCode,
			}
			p.ExtraData = append(p.ExtraData, ref)
		}
		for _, poCode := range p.PrepaidPOCodes {
			ref := RefMetadata{
				Key:      "PREPAID_PO_CODE",
				Value:    poCode,
				KeyValue: "PREPAID_PO_CODE:" + poCode,
			}

			p.ExtraData = append(p.ExtraData, ref)
		}
		if p.RefundBillCode != "" {
			ref := RefMetadata{
				Key:      "REFUND_BILL_CODE",
				Value:    p.RefundBillCode,
				KeyValue: "REFUND_BILL_CODE:" + p.RefundBillCode,
			}

			p.ExtraData = append(p.ExtraData, ref)

		}

		if p.AdjustmentBillCode != "" {
			ref := RefMetadata{
				Key:      "ADJUSTMENT_BILL_CODE",
				Value:    p.AdjustmentBillCode,
				KeyValue: "ADJUSTMENT_BILL_CODE:" + p.AdjustmentBillCode,
			}
			p.ExtraData = append(p.ExtraData, ref)
		}
		if p.LegacyPaymentCode != "" {
			ref := RefMetadata{
				Key:      "LEGACY_PAYMENT_CODE",
				Value:    p.LegacyPaymentCode,
				KeyValue: "LEGACY_PAYMENT_CODE:" + p.LegacyPaymentCode,
			}
			p.ExtraData = append(p.ExtraData, ref)
		}

		if p.LegacyPaymendID != 0 {
			ref := RefMetadata{
				Key:      "LEGACY_PAYMENT_ID",
				Value:    fmt.Sprint(p.LegacyPaymendID),
				KeyValue: "LEGACY_PAYMENT_ID:" + fmt.Sprint(p.LegacyPaymendID),
			}
			p.ExtraData = append(p.ExtraData, ref)
		}

		if p.CreatedByID != 0 {
			ref := RefMetadata{
				Key:      "CREATED_BY_ID",
				Value:    fmt.Sprint(p.CreatedByID),
				KeyValue: "CREATED_BY_ID:" + fmt.Sprint(p.CreatedByID),
			}
			p.ExtraData = append(p.ExtraData, ref)
		}

		{ // rm duplicate
			mapDup := map[string]bool{}
			extraData := []RefMetadata{}
			for i := range p.ExtraData {
				extra := p.ExtraData[i]
				if _, ok := mapDup[extra.KeyValue]; !ok {
					mapDup[extra.KeyValue] = true
					extraData = append(extraData, extra)
				}
			}

			p.ExtraData = extraData
		}
	}

}

func (p *PaymentPlatform) FillSuportField() {
	p.PrepaidPOCodes = p.DecodePrepaidPOs()
	p.RefundBillCode = p.DecodeRefundBillCode()
	p.AdjustmentBillCode = p.DecodeAdjustmentBillCode()
	p.PORelatedCodes = p.DecodePORelatedCode()
	p.LegacyPaymentCode = p.DecodeLegacyPaymentCode()
	p.CreatedByID = p.DecodeCreatedByID()
	p.LegacyPaymendID = p.DecodeLegacyPaymentID()
	p.Purpose = p.ReasonCode
}

func (p *PaymentPlatform) DecodePrepaidPOs() []string {
	ouput := []string{}
	for _, ref := range p.ExtraData {
		if ref.Key == "PREPAID_PO_CODE" {
			ouput = append(ouput, ref.Value)
		}
	}
	return ouput
}

func (p *PaymentPlatform) GenQueryPrepaidPOCode(input []string) []string {
	ouput := []string{}
	for _, poCode := range input {
		ouput = append(ouput, "PREPAID_PO_CODE:"+poCode)
	}
	return ouput
}

func (p *PaymentPlatform) DecodeRefundBillCode() string {
	for _, ref := range p.ExtraData {
		if ref.Key == "REFUND_BILL_CODE" {
			return ref.Value
		}
	}
	return ""
}

func (p *PaymentPlatform) GenQueryRefundBillCode(input []string) []string {
	ouput := []string{}
	for _, refCode := range input {
		ouput = append(ouput, "REFUND_BILL_CODE:"+refCode)
	}
	return ouput
}

func (p *PaymentPlatform) DecodeAdjustmentBillCode() string {
	for _, ref := range p.ExtraData {
		if ref.Key == "ADJUSTMENT_BILL_CODE" {
			return ref.Value
		}
	}
	return ""
}

func (p *PaymentPlatform) DecodeLegacyPaymentID() int64 {
	for _, ref := range p.ExtraData {
		if ref.Key == "LEGACY_PAYMENT_ID" {
			i, _ := strconv.Atoi(ref.Value)
			return int64(i)
		}
	}
	return 0
}

func (p *PaymentPlatform) DecodeLegacyPaymentCode() string {
	for _, ref := range p.ExtraData {
		if ref.Key == "LEGACY_PAYMENT_CODE" {
			return ref.Value
		}
	}
	return ""
}
func (p *PaymentPlatform) DecodeCreatedByID() int64 {
	for _, ref := range p.ExtraData {
		if ref.Key == "CREATED_BY_ID" {
			i, _ := strconv.Atoi(ref.Value)
			return int64(i)
		}
	}
	return 0
}
func (p *PaymentPlatform) GenQueryAdjustmentBillCode(input []string) []string {
	ouput := []string{}
	for _, refCode := range input {
		ouput = append(ouput, "ADJUSTMENT_BILL_CODE:"+refCode)
	}
	return ouput
}
func (p *PaymentPlatform) GenQueryLegacyPaymentCode(input []string) []string {
	ouput := []string{}
	for _, refCode := range input {
		ouput = append(ouput, "LEGACY_PAYMENT_CODE:"+refCode)
	}
	return ouput
}
func (p *PaymentPlatform) GenQueryCreatedByAccountID(input int64) []string {
	ouput := []string{"CREATED_BY_ID:" + strconv.Itoa(int(input))}
	return ouput
}

// func (p *PaymentPlatform) GenQueryVendorBillCode(input []string) []string {
// 	ouput := []string{}
// 	for _, refCode := range input {
// 		ouput = append(ouput, "VENDOR_BILL_CODE:"+refCode)
// 	}
// 	return ouput
// }

// func (p *PaymentPlatform) GenQueryPaymentCode(input []string) []string {
// 	ouput := []string{}
// 	for _, refCode := range input {
// 		ouput = append(ouput, "PAYMENT_CODE:"+refCode)
// 	}
// 	return ouput
// }

func (p *PaymentPlatform) DecodePORelatedCode() []string {
	ouput := []string{}
	for _, ref := range p.ExtraData {
		if ref.Key == "PO_RELATED" {
			ouput = append(ouput, ref.Value)
		}
	}

	return ouput
}

func (p *PaymentPlatform) GenQueryPORelatedCode(input []string) []string {
	ouput := []string{}
	for _, refCode := range input {
		ouput = append(ouput, "PO_RELATED:"+refCode)
	}
	return ouput
}

type RefMetadata struct {
	Key      string `json:"key,omitempty" bson:"key,omitempty"`
	Value    string `json:"value,omitempty" bson:"value,omitempty"`
	KeyValue string `json:"keyValue,omitempty" bson:"key_value,omitempty"`
}

type ReasonPaymentParse struct {
	Data enum.PaymentPlatformReasonType `json:"data,omitempty" bson:"data,omitempty"`
	Code string                         `json:"code,omitempty" bson:"code,omitempty"`
}

type OptionFillDataPaymentPlatform struct {
	ExtraData bool
}

func mergeOptionFillDataPaymentPlatform(opts []OptionFillDataPaymentPlatform) OptionFillDataPaymentPlatform {
	var opt OptionFillDataPaymentPlatform
	for _, o := range opts {
		if o.ExtraData {
			opt.ExtraData = true
		}
	}
	return opt
}
