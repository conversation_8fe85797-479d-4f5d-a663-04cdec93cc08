package api

import (
	"bytes"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action/sftp_action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/zuellig"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// UploadFile ...
func TestUploadFile(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input = model.FileUpload{}
	)

	// parse input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// upload file
	buffer := bytes.NewBufferString(input.Data)
	err := zuellig.UploadContent(input.Destination, buffer)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Message: err.Error(),
			Status:  common.APIStatus.Error,
		})
	}

	// response
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "upload file success",
	})
}

// SendPOFile
func SendPOFile(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input = model.PurchaseOrder{}
	)

	// parse input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if len(input.POCode) == 0 {
		return resp.Respond(
			&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "need PO code",
			},
		)
	}

	// do action
	return resp.Respond(sftp_action.SendPOZuellig(input.POCode))

}

// func ListFiles(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	dirName := req.GetParam("dir")
// 	files, err := zuellig.ListFiles(dirName)
// 	if err != nil {
// 		return resp.Respond(
// 			&common.APIResponse{
// 				Status:  common.APIStatus.Error,
// 				Message: err.Error(),
// 			})
// 	}

// 	filesResp := []model.FileInfo{}
// 	for _, f := range files {
// 		var file = model.FileInfo{
// 			Name:         f.Name(),
// 			ModifiedTime: utils.Pointer.WithTime(f.ModTime()),
// 			Size:         f.Size(),
// 			IsDir:        f.IsDir(),
// 		}
// 		filesResp = append(filesResp, file)
// 	}

// 	return resp.Respond(&common.APIResponse{
// 		Status: common.APIStatus.Ok,
// 		Data:   filesResp,
// 	})
// }
