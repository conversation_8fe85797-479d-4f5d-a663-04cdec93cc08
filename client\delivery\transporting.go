package delivery

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	cancelShippingOrder = "PUT::/shipping-order/cancel"
)

var (
	transportingClient *client.Client
)

func InitTransportingClient(database *mongo.Database) {
	const transporting = "/delivery/transporting/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	transportingClient = client.NewClient(transporting, headers, 0)
	transportingClient.WithDatabase(database)
	transportingClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     cancelShippingOrder,
				Name:     "cancel_shipping_order",
				Database: database,
			},
		}...,
	)
}

func CancelShippingOrder(opts ...client.APIOption) *common.APIResponse {
	requestOpts := transportingClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := transportingClient.WithRequest(cancelShippingOrder, requestOpts, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
