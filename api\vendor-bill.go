package api

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetVendorBillItemList ...
func GetVendorBillItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
		sort     = req.GetParam("sort")
	)

	if limit < 0 {
		limit = 20
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.VendorBillItem{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// fill query
	acc := getActionSource(req)
	if acc != nil && acc.Type == "VENDOR" {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
	}

	// add complex query
	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"po_code": bson.M{
				"$in": query.POCodeIn,
			}},
		)
	}

	// add complex query
	if len(query.VendorBillIDIn) > 0 {
		query.VendorBillID = 0
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"vendor_bill_id": bson.M{
				"$in": query.VendorBillIDIn,
			}},
		)
	}

	// add complex query
	if len(query.VendorBillCodeIn) > 0 {
		query.VendorBillCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"vendor_bill_code": bson.M{
				"$in": query.VendorBillCodeIn,
			}},
		)
	}

	// do action
	return resp.Respond(action.GetVendorBillItemList(query, offset, limit, getTotal, sort))
}

// GetVendorBillList ...
func GetVendorBillList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		getItems = req.GetParam("getItems") == "true"
		search   = req.GetParam("search")
		q        = req.GetParam("q")
		sort     = req.GetParam("sort")
	)

	if limit < 0 {
		limit = 20
	}

	if offset < 0 {
		offset = 0
	}

	// fill query
	query := model.VendorBill{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// fill query
	acc := getActionSource(req)
	if acc != nil && acc.Type == "VENDOR" {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
	}

	// if want to search
	if len(search) > 0 {
		search := utils.ParserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: search},
		})
	}

	// DueTimeQuery
	if query.DueTimeQuery != nil {
		y, m, d := query.DueTimeQuery.In(utils.TimeZoneVN).Date()
		beginDate := time.Date(y, m, d, 0, 0, 0, 0, utils.TimeZoneVN)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"due_time": bson.M{
				"$gte": beginDate,
				"$lt":  beginDate.Add(24 * time.Hour),
			},
		})
	}
	// IssuedTimeQuery
	if query.IssuedTimeQuery != nil {
		y, m, d := query.IssuedTimeQuery.In(utils.TimeZoneVN).Date()
		beginDate := time.Date(y, m, d, 0, 0, 0, 0, utils.TimeZoneVN)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$gte": beginDate,
				"$lt":  beginDate.Add(24 * time.Hour),
			},
		})
	}

	// IssuedTime
	if query.IssuedTimeFromQuery != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$gte": query.IssuedTimeFromQuery,
			},
		})
	}

	// IssuedTime
	if query.IssuedTimeToQuery != nil {
		y, m, d := query.IssuedTimeToQuery.In(utils.TimeZoneVN).Date()
		endDate := time.Date(y, m, d, 23, 59, 59, 0, utils.TimeZoneVN)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$lt": endDate,
			},
		})
	}

	// Duetime
	if query.DueTimeFromQuery != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"due_time": bson.M{
				"$gte": query.DueTimeFromQuery,
			},
		})
	}

	// Duetime
	if query.DueTimeToQuery != nil {
		y, m, d := query.DueTimeToQuery.In(utils.TimeZoneVN).Date()
		endDate := time.Date(y, m, d, 23, 59, 59, 0, utils.TimeZoneVN)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"due_time": bson.M{
				"$lt": endDate,
			},
		})
	}

	// add complex query
	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"po_code": bson.M{
				"$in": query.POCodeIn,
			},
		})
	}

	// add complex query
	if len(query.VendorBillCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"vendor_bill_code": bson.M{
				"$in": query.VendorBillCodeIn,
			},
		})
	}

	// add complex query
	if len(query.StatusIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"status": bson.M{
			"$in": query.StatusIn,
		}})
	}

	// add complex query
	if len(query.InvoiceNumberIn) > 0 {
		query.InvoiceNumber = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"invoice_number": bson.M{
			"$in": query.InvoiceNumberIn,
		}})
	}

	// add complex query
	if len(query.VendorBillIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"vendor_bill_id": bson.M{
			"$in": query.VendorBillIDIn,
		}})
	}

	// add complex query
	if len(query.VendorCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"vendor_code": bson.M{
			"$in": query.VendorCodeIn,
		}})
	}

	// add complex query
	if len(query.ProductCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"product_codes": query.ProductCodeIn[0]})
	}

	// add complex query
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"created_time": bson.M{"$gte": query.CreatedFrom}})
	}

	// add complex query
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"created_time": bson.M{"$lt": query.CreatedTo}})
	}

	// add complex query
	if query.LastestID != nil && !query.LastestID.IsZero() {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"_id": bson.M{"$gt": *query.LastestID}})
	}

	if len(query.ReasonTagIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"reason_tag": bson.M{"$in": query.ReasonTagIn}})
	}

	// do action
	return resp.Respond(action.GetVendorBillList(query, offset, limit, getTotal, getItems, sort))
}

// CreateVendorBill ...
func CreateVendorBill(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data: " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.Status != "" && input.Status != enum.VendorBillStatus.DRAFT {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		})
	}

	// validate input
	if input.SellerCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SellerCode.",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		})
	}

	// validate input
	return resp.Respond(action.CreateVendorBill(input))
}

// UpdateVendorBill ...
func UpdateVendorBill(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.VendorBillCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: VendorBillCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorBillCode),
		})
	}

	// do action
	return resp.Respond(action.UpdateVendorBill(input))
}

// UpdateVendorBill ...
func UpdateVendorBillInfo(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.VendorBillCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: VendorBillCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorBillCode),
		})
	}

	return resp.Respond(action.UpdateVendorBillInfo(input))
}

// SwitchStatusVendorBill ...
func SwitchStatusVendorBill(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.VendorBillCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: VendorBillCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorBillCode),
		})
	}

	acc := getActionSource(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			// Message: "Unauthorized: getActionSource",
			Status:    common.APIStatus.Unauthorized,
			Message:   "Your account cannot perform this action",
			ErrorCode: string(enum.ErrorCodeInvalid.Action),
		})
	}

	// cancel
	if input.Status == enum.VendorBillStatus.CANCEL {
		return resp.Respond(action.CancelVendorBill(input))
	}

	// switch status
	return resp.Respond(action.SwitchStatusVendorBill(input, acc))
}

func CreateVendorBillByPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	// var poCode = req.GetParam("poCode")
	po := &model.PurchaseOrder{}
	err := req.GetContent(po)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	return resp.Respond(action.CreateVendorBillByPOCode(po.POCode))
}

func CloneVendorBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var vendorBillCode = req.GetParam("vendorBillCode")

	if vendorBillCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid vendorBillCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorBillCode),
		})
	}

	return resp.Respond(action.CloneVendorBill(vendorBillCode))
}
