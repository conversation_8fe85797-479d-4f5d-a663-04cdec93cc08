package utils

import "math"

// Get max safe int
func MaxInt64(ints ...int64) int64 {
	if len(ints) == 0 {
		return 0
	}
	max := ints[0]
	for _, v := range ints {
		if v > max && v < math.MaxInt64 {
			max = v
		}
	}
	return max
}

// Get max safe float64
func MaxFloat64(floats ...float64) float64 {
	if len(floats) == 0 {
		return 0
	}
	max := floats[0]
	for _, v := range floats {
		if v > max && v < math.MaxFloat64 {
			max = v
		}
	}
	return max
}

func ValueFromPecent(base, percent float64) float64 {
	if percent <= 0 {
		return 0
	}
	return base / 100 * percent
}

func UnitPriceFromVAT(priceAfterVAT float64, vatPercent float64) float64 {
	return priceAfterVAT / (1 + vatPercent/100)
}

// Least common multiple
func LCM(a, b int64) int64 {
	return a * b / GCD(a, b)
}

// Greatest common divisor
func GCD(a, b int64) int64 {
	if a == 0 {
		return b
	}
	return GCD(b%a, a)
}
