package tools

import (
	"fmt"
	"log"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	DEFAULT_LIMIT = 100
	DEFAULT_SLEEP = 200

	SELLER_HCM = "MEDX"
	SELLER_HN  = "MEDX-HN"

	WAREHOUSE_BD  = "BD"
	WAREHOUSE_HCM = "HCM"
	WAREHOUSE_HN  = "HN"

	PUR_HCM = "PUR_HCM"
	PUR_HN  = "PUR_HN"

	PO_LIMIT = 50
)

type migateInput struct {
	POCodes []string `json:"poCodes"`
}

func MigratePurchaser(req sdk.APIRequest, res sdk.APIResponder) error {
	var (
		db = req.GetVar("db")

		input migateInput
	)

	switch db {
	case "sku_config":
		go SkuConfig()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "quotation_po_history":
		go QuotationPOHistory()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "bidding_history":
		go BiddingHistory()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "purchase_order":
		go PurchaseOrder()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "purchase_order_item":
		go PurchaseOrderItem()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "vendor_bill":
		go VendorBill()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "bidding":
		go Bidding()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "sku_config_medx_hn":
		go SKUConfigMEDXHN()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "inbound_request_medx_hn":
		go InboundRequestMedxHN()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "revert_inbound_request_medx_hn":
		go RevertInboundRequestMedxHN()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "vb_medx_hn":
		if err := req.GetContent(&input); err != nil {
			return res.Respond(&common.APIResponse{
				Message: err.Error(),
				Status:  common.APIStatus.Invalid,
			})
		}
		if len(input.POCodes) == 0 {
			return res.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "POCodes is required"})
		}

		go VBMedxHN(input.POCodes)
	case "revert_vb_medx_hn":
		go RevertVBMedxHN()
	case "callback_store_medx_hn":
		if err := req.GetContent(&input); err != nil {
			return res.Respond(&common.APIResponse{
				Message: err.Error(),
				Status:  common.APIStatus.Invalid,
			})
		}
		if len(input.POCodes) == 0 {
			return res.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "POCodes is required"})
		}

		go MigrateCallbackStoreMedxHN(input.POCodes)
	case "revert_callback_store_medx_hn":
		go RevertCallbackStoreMedxHN()
	default:
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "Invalid db params"})
	}

	return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func SkuConfig() {
	log.Println("Migrate SKU Config")
	defer log.Println("Migrate SKU Config done")

	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		skuConfigResp := model.SkuConfigDB.Query(
			&model.SkuConfig{
				SellerCode: SELLER_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)
		if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)
			_id = *skuConfigs[len(skuConfigs)-1].ID
			model.SkuConfigDB.UpdateMany(
				&model.SkuConfig{SellerCode: SELLER_HCM, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}, // filter
				}},
				&model.SkuConfig{PurchaserCode: PUR_HCM}) // updater
			_id_offset = _id
			// log.Printf("Migrate SKU Config HCM: %v\n", _id_offset)
		} else if skuConfigResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate SKU Config HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}

	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		skuConfigResp := model.SkuConfigDB.Query(
			&model.SkuConfig{
				SellerCode: SELLER_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}}}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)
		if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)
			_id = *skuConfigs[len(skuConfigs)-1].ID
			model.SkuConfigDB.UpdateMany(
				&model.SkuConfig{SellerCode: SELLER_HN, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}}, // filter
				}},
				&model.SkuConfig{PurchaserCode: PUR_HN}) // updater
			_id_offset = _id
			// log.Printf("Migrate SKU Config HN: %v\n", _id_offset)
		} else if skuConfigResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate SKU Config HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}

	}

	// MARKETING
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		skuConfigResp := model.SkuConfigDB.Query(
			&model.SkuConfig{
				SellerCode: "MARKETING",
				ComplexQuery: []*primitive.M{{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)
		if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)
			_id = *skuConfigs[len(skuConfigs)-1].ID
			model.SkuConfigDB.UpdateMany(
				&model.SkuConfig{SellerCode: "MARKETING", ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
				&model.SkuConfig{PurchaserCode: PUR_HCM})
			_id_offset = _id
			// log.Printf("Migrate SKU Config MARKETING: %v\n", _id_offset)
		} else if skuConfigResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate SKU Config MARKETING error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}

	}

}

func QuotationPOHistory() {
	log.Println("Migrate Quotation PO History")
	defer log.Println("Migrate Quotation PO History done")
	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		quotationPOHistoryResp := model.QuotationPOHistoryDB.Query(
			&model.QuotationPOHistory{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)
		if quotationPOHistoryResp.Status == common.APIStatus.Ok {
			quotationPOHistories := quotationPOHistoryResp.Data.([]*model.QuotationPOHistory)
			_id = *quotationPOHistories[len(quotationPOHistories)-1].ID
			model.QuotationPOHistoryDB.UpdateMany(
				&model.QuotationPOHistory{WarehouseCode: WAREHOUSE_HCM, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}, // filter
				}},
				&model.QuotationPOHistory{PurchaserCode: PUR_HCM}) // updater
			_id_offset = _id
			// log.Printf("Migrate QuotationPOHistory HCM: %v\n", _id_offset)
		} else if quotationPOHistoryResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate QuotationPOHistory HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}

	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		quotationPOHistoryResp := model.QuotationPOHistoryDB.Query(
			&model.QuotationPOHistory{
				WarehouseCode: WAREHOUSE_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)
		if quotationPOHistoryResp.Status == common.APIStatus.Ok {
			quotationPOHistories := quotationPOHistoryResp.Data.([]*model.QuotationPOHistory)
			_id = *quotationPOHistories[len(quotationPOHistories)-1].ID
			model.QuotationPOHistoryDB.UpdateMany(
				&model.QuotationPOHistory{WarehouseCode: WAREHOUSE_HN, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}}, // filter
				}},
				&model.QuotationPOHistory{PurchaserCode: PUR_HN}) // updater
			_id_offset = _id
			// log.Printf("Migrate QuotationPOHistory HN: %v\n", _id_offset)
		} else if quotationPOHistoryResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate QuotationPOHistory HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func BiddingHistory() {
	log.Println("Migrate Bidding History")
	defer log.Println("Migrate Bidding History done")
	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		biddingHistoryResp := model.BiddingHistoryDB.Query(
			&model.BiddingHistory{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"delivery_warehouse_code": bson.M{"$ne": WAREHOUSE_HCM}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if biddingHistoryResp.Status == common.APIStatus.Ok {
			biddingHistories := biddingHistoryResp.Data.([]*model.BiddingHistory)
			_id = *biddingHistories[len(biddingHistories)-1].ID

			model.BiddingHistoryDB.UpdateMany(
				&model.BiddingHistory{WarehouseCode: WAREHOUSE_HCM, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"delivery_warehouse_code": bson.M{"$ne": WAREHOUSE_HCM}}, // filter
				}},
				&model.BiddingHistory{DeliveryWarehouseCode: WAREHOUSE_HCM}) // updater

			_id_offset = _id
			// log.Printf("Migrate BiddingHistory HCM: %v\n", _id_offset)
		} else if biddingHistoryResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate BiddingHistory HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id
	for {
		biddingHistoryResp := model.BiddingHistoryDB.Query(
			&model.BiddingHistory{
				WarehouseCode: WAREHOUSE_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"delivery_warehouse_code": bson.M{"$ne": WAREHOUSE_HN}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if biddingHistoryResp.Status == common.APIStatus.Ok {
			biddingHistories := biddingHistoryResp.Data.([]*model.BiddingHistory)
			_id = *biddingHistories[len(biddingHistories)-1].ID

			model.BiddingHistoryDB.UpdateMany(
				&model.BiddingHistory{WarehouseCode: WAREHOUSE_HN, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"delivery_warehouse_code": bson.M{"$ne": WAREHOUSE_HN}}, // filter
				}},
				&model.BiddingHistory{DeliveryWarehouseCode: WAREHOUSE_HN}) // updater

			_id_offset = _id
			// log.Printf("Migrate BiddingHistory HN: %v\n", _id_offset)
		} else if biddingHistoryResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate BiddingHistory HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func PurchaseOrder() {
	log.Println("Migrate Purchase Order")
	defer log.Println("Migrate Purchase Order done")
	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			&model.PurchaseOrder{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if purchaseOrderResp.Status == common.APIStatus.Ok {
			purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
			_id = *purchaseOrders[len(purchaseOrders)-1].ID

			model.PurchaseOrderDB.UpdateMany(
				&model.PurchaseOrder{WarehouseCode: WAREHOUSE_HCM, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}, // filter
				}},
				&model.PurchaseOrder{PurchaserCode: PUR_HCM}) // updater

			_id_offset = _id
			// log.Printf("Migrate PurchaseOrder HCM: %v\n", _id_offset)
		} else if purchaseOrderResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate PurchaseOrder HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			&model.PurchaseOrder{
				WarehouseCode: WAREHOUSE_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if purchaseOrderResp.Status == common.APIStatus.Ok {
			purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
			_id = *purchaseOrders[len(purchaseOrders)-1].ID

			model.PurchaseOrderDB.UpdateMany(
				&model.PurchaseOrder{WarehouseCode: WAREHOUSE_HN, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}}, // filter
				}},
				&model.PurchaseOrder{PurchaserCode: PUR_HN}) // updater
			_id_offset = _id
			// log.Printf("Migrate PurchaseOrder HN: %v\n", _id_offset)
		} else if purchaseOrderResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate PurchaseOrder HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func PurchaseOrderItem() {

	log.Println("Migrate Purchase Order Item")
	defer log.Println("Migrate Purchase Order Item done")
	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			&model.PurchaseOrder{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
				}},
			0,
			PO_LIMIT,
			&primitive.M{"_id": 1},
		)

		if purchaseOrderResp.Status == common.APIStatus.Ok {
			purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
			_id = *purchaseOrders[len(purchaseOrders)-1].ID
			for _, po := range purchaseOrders {
				model.PurchaseOrderItemDB.UpdateMany(
					&model.PurchaseOrderItem{POCode: po.POCode,
						ComplexQuery: []*primitive.M{
							{"purchaser_code": bson.M{"$ne": PUR_HCM}}, // filter
						}},
					&model.PurchaseOrderItem{PurchaserCode: PUR_HCM}) // updater
			}
			_id_offset = _id
			// log.Printf("Migrate PurchaseOrderItem HCM: %v\n", _id_offset)
		} else if purchaseOrderResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate PurchaseOrderItem HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			&model.PurchaseOrder{
				WarehouseCode: WAREHOUSE_HN,
				ComplexQuery:  []*primitive.M{{"_id": bson.M{"$gt": _id_offset}}}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if purchaseOrderResp.Status == common.APIStatus.Ok {
			purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
			_id = *purchaseOrders[len(purchaseOrders)-1].ID
			for _, po := range purchaseOrders {
				model.PurchaseOrderItemDB.UpdateMany(
					&model.PurchaseOrderItem{POCode: po.POCode,
						ComplexQuery: []*primitive.M{
							{"purchaser_code": bson.M{"$ne": PUR_HN}}, // filter
						}},
					&model.PurchaseOrderItem{PurchaserCode: PUR_HN}) // updater
			}
			_id_offset = _id
			// log.Printf("Migrate PurchaseOrderItem HN: %v\n", _id_offset)
		} else if purchaseOrderResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate PurchaseOrderItem HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func VendorBill() {
	log.Println("Migrate Vendor Bill")
	defer log.Println("Migrate Vendor Bill done")
	// HCM
	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		vendorBillResp := model.VendorBillDB.Query(
			&model.VendorBill{
				SellerCode: SELLER_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}}},
			0,
			PO_LIMIT,
			&primitive.M{"_id": 1},
		)

		if vendorBillResp.Status == common.APIStatus.Ok {
			vendorBills := vendorBillResp.Data.([]*model.VendorBill)
			_id = *vendorBills[len(vendorBills)-1].ID

			model.VendorBillDB.UpdateMany(
				&model.VendorBill{SellerCode: SELLER_HCM, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}}, // filter
				}},
				&model.VendorBill{PurchaserCode: PUR_HCM}) // updater

			_id_offset = _id
			// log.Printf("Migrate VendorBill HCM: %v\n", _id_offset)
		} else if vendorBillResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate VendorBill HCM error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	// HN
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		vendorBillResp := model.VendorBillDB.Query(
			&model.VendorBill{
				SellerCode: SELLER_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if vendorBillResp.Status == common.APIStatus.Ok {
			vendorBills := vendorBillResp.Data.([]*model.VendorBill)
			_id = *vendorBills[len(vendorBills)-1].ID

			model.VendorBillDB.UpdateMany(
				&model.VendorBill{SellerCode: SELLER_HN, ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HN}}, // filter
				}},
				&model.VendorBill{PurchaserCode: PUR_HN}) // updater

			_id_offset = _id
			// log.Printf("Migrate VendorBill HN: %v\n", _id_offset)
		} else if vendorBillResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate VendorBill HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	// MARKETING
	_id = primitive.NilObjectID
	_id_offset = _id

	for {
		vendorBillResp := model.VendorBillDB.Query(
			&model.VendorBill{
				SellerCode: "MARKETING",
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if vendorBillResp.Status == common.APIStatus.Ok {
			vendorBills := vendorBillResp.Data.([]*model.VendorBill)
			_id = *vendorBills[len(vendorBills)-1].ID

			model.VendorBillDB.UpdateMany(
				&model.VendorBill{SellerCode: "MARKETING", ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"_id": bson.M{"$lte": _id}},
					{"purchaser_code": bson.M{"$ne": PUR_HCM}},
				}},
				&model.VendorBill{PurchaserCode: PUR_HCM})

			_id_offset = _id
			// log.Printf("Migrate VendorBill HN: %v\n", _id_offset)
		} else if vendorBillResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// log.Println("Migrate VendorBill HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func Bidding() {
	//HCM
	model.BiddingDB.UpdateMany(model.Bidding{WarehouseCode: WAREHOUSE_HCM}, model.Bidding{DeliveryWarehouseCode: WAREHOUSE_HCM})
	//HN
	model.BiddingDB.UpdateMany(model.Bidding{WarehouseCode: WAREHOUSE_HN}, model.Bidding{DeliveryWarehouseCode: WAREHOUSE_HN})
}

// migrate merge seller
func SKUConfigMEDXHN() {
	log.Println("Start : Migrate SKUConfig MEDX HN")
	defer log.Println("End : Migrate SKUConfig MEDX HN")
	var (
		_id_offset = primitive.NilObjectID
		errList    []string
	)

	for {
		skuConfigResp := model.SkuConfigDB.Query(
			&model.SkuConfig{
				SellerCode:    SELLER_HN,
				PurchaserCode: PUR_HN,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)

			for i := range skuConfigs {
				skuConf := skuConfigs[i]
				_id_offset = *skuConf.ID
				var (
					oldSKU = skuConf.SKU
				)

				// gỡ
				skuConf.ID = nil
				skuConf.LastUpdatedTime = nil
				skuConf.CreatedTime = nil

				// check sku marketplace đã tồn tại
				option := client.APIOption{
					Limit: utils.Pointer.WithInt(1),
					Q: model.SKUItem{
						ProductCode: skuConf.ProductCode,
						SellerCode:  SELLER_HCM,
					},
				}
				getSKUItemResp := marketplace.GetListSKUItem(option)
				if getSKUItemResp.Status != common.APIStatus.Ok {
					errList = append(errList, oldSKU)
					continue
				}
				skuItem := getSKUItemResp.Data.([]*model.SKUItem)[0]
				// change
				skuConf.SKU = skuItem.SKU
				skuConf.SellerCode = SELLER_HCM
				skuConf.PurchaserCode = PUR_HN

				// create
				if resp := model.SkuConfigDB.Create(skuConf); resp.Status != common.APIStatus.Ok {
					// log.Println("Migrate SKUConfig MEDX HN error: ", skuConf.SKU, resp.Message)
				}
			}

		} else if skuConfigResp.Status == common.APIStatus.NotFound {
			break
		} else {
			// error case
			// log.Println("Migrate SKUConfig HN error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	log.Println("Error list: ", errList)
}

func InboundRequestMedxHN() {
	log.Println("Start : Migrate InboundRequestMedxHN")
	defer log.Println("End : Migrate InboundRequestMedxHN")

	var (
		_id_offset = primitive.NilObjectID
		errList1   []string
		errList2   []string
		errList3   []string
		errList4   []string
	)

	for {
		getInboundMain := model.InboundRequestDB.Query(
			&model.InboundRequest{
				SellerCode: SELLER_HN, // MEDX-HN
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getInboundMain.Status == common.APIStatus.Ok {
			listInboundMain := getInboundMain.Data.([]*model.InboundRequest)

		LOOP_INBOUND_MAIN:
			for i := range listInboundMain {
				inboundMain := listInboundMain[i]
				// tăng offset
				_id_offset = *inboundMain.ID

				// --------------------- inbound main ---------------------------------
				// change
				inboundMain.MigrateMetadata = &model.MigrateMetadata{
					OldSeller: inboundMain.SellerCode,
				}
				inboundMain.SellerCode = SELLER_HCM

				filter := model.InboundRequest{
					ID: inboundMain.ID,
				}
				// update
				if resp := model.InboundRequestDB.UpdateOne(filter, inboundMain); resp.Status != common.APIStatus.Ok {
					errList1 = append(errList1, fmt.Sprint(inboundMain.InboundRequestID))
					continue LOOP_INBOUND_MAIN
				}

				//  ----------------------------- inbound item --------------------------

				// get list inbound item
				if inboundMain.InboundRequestType == enum.InboundRequestType.SAVE_INITIAL_STOCK {
					continue LOOP_INBOUND_MAIN
				}

				getInboundItem := model.InboundRequestItemDB.Query(
					&model.InboundRequestItem{
						InboundRequestCode: inboundMain.InboundRequestCode,
					}, 0, 1000, nil)

				if getInboundItem.Status != common.APIStatus.Ok {
					errList2 = append(errList2, fmt.Sprint(inboundMain.InboundRequestID))
					continue LOOP_INBOUND_MAIN
				}

				listInboundItem := getInboundItem.Data.([]*model.InboundRequestItem)

			LOOP_INBOUND_ITEM:
				for j := range listInboundItem {
					inboundItem := listInboundItem[j]
					// check sku marker place
					option := client.APIOption{
						Limit: utils.Pointer.WithInt(1),
						Q: model.SKUItem{
							ProductCode: inboundItem.ProductCode,
							SellerCode:  SELLER_HCM,
						},
					}
					getSKUItemResp := marketplace.GetListSKUItem(option)
					if getSKUItemResp.Status != common.APIStatus.Ok {
						errList3 = append(errList3, fmt.Sprintf("%s-%d", inboundItem.SKU, inboundItem.InboundRequestItemID))
						continue LOOP_INBOUND_ITEM
					}
					skuItem := getSKUItemResp.Data.([]*model.SKUItem)[0]
					// change
					inboundItem.MigrateMetadata = &model.MigrateMetadata{
						OldSeller: inboundItem.SellerCode,
						OldSKU:    inboundItem.SKU,
					}
					inboundItem.SKU = skuItem.SKU
					inboundItem.SellerCode = SELLER_HCM

					// filter item
					filter := model.InboundRequestItem{ID: inboundItem.ID}

					// update
					if resp := model.InboundRequestItemDB.UpdateOne(filter, inboundItem); resp.Status != common.APIStatus.Ok {
						errList4 = append(errList4, fmt.Sprintf("%s-%d", inboundItem.SKU, inboundItem.InboundRequestItemID))
						continue LOOP_INBOUND_ITEM
					}
				}
			}

		} else if getInboundMain.Status == common.APIStatus.NotFound {
			break
		} else {
			// error case
			log.Println("Migrate error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	log.Println("errList1: ", errList1)
	log.Println("errList2: ", errList2)
	log.Println("errList3: ", errList3)
	log.Println("errList4: ", errList4)
}

func RevertInboundRequestMedxHN() {
	log.Println("Start : RevertInboundRequestMedxHN")
	defer log.Println("End : RevertInboundRequestMedxHN")

	var (
		_id_offset = primitive.NilObjectID
		errList0   []string
		errList1   []string
		errList2   []string
		errList3   []string
		errList4   []string
	)

	for {
		getInboundMain := model.InboundRequestDB.Query(
			&model.InboundRequest{
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"migrate_metadata": bson.M{"$exists": true}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getInboundMain.Status == common.APIStatus.Ok {
			listInboundMain := getInboundMain.Data.([]*model.InboundRequest)

		LOOP_INBOUND_MAIN:
			for i := range listInboundMain {
				inboundMain := listInboundMain[i]
				// tăng offset
				_id_offset = *inboundMain.ID

				// --------------------- inbound main ---------------------------------
				// chưa migrate
				if inboundMain.MigrateMetadata == nil {
					errList0 = append(errList0, fmt.Sprint(inboundMain.InboundRequestID))
				}
				// roll-back
				inboundMain.SellerCode = inboundMain.MigrateMetadata.OldSeller
				filter := model.InboundRequest{
					ID: inboundMain.ID,
				}

				// update
				if resp := model.InboundRequestDB.UpdateOne(filter, inboundMain); resp.Status != common.APIStatus.Ok {
					errList1 = append(errList1, fmt.Sprint(inboundMain.InboundRequestID))
					continue LOOP_INBOUND_MAIN
				}

				//  ----------------------------- inbound item --------------------------

				// get list inbound item
				if inboundMain.InboundRequestType == enum.InboundRequestType.SAVE_INITIAL_STOCK {
					continue LOOP_INBOUND_MAIN
				}

				getInboundItem := model.InboundRequestItemDB.Query(
					&model.InboundRequestItem{
						InboundRequestCode: inboundMain.InboundRequestCode,
					}, 0, 1000, nil)

				if getInboundItem.Status != common.APIStatus.Ok {
					errList2 = append(errList2, fmt.Sprint(inboundMain.InboundRequestID))
					continue LOOP_INBOUND_MAIN
				}

				listInboundItem := getInboundItem.Data.([]*model.InboundRequestItem)

			LOOP_INBOUND_ITEM:
				for j := range listInboundItem {
					inboundItem := listInboundItem[j]
					// chưa migrate
					if inboundItem.MigrateMetadata == nil {
						errList3 = append(errList3, fmt.Sprint(inboundItem.InboundRequestItemID))
					}

					// roll-back
					inboundItem.SKU = inboundItem.MigrateMetadata.OldSKU
					inboundItem.SellerCode = inboundItem.MigrateMetadata.OldSeller

					// filter item
					filter := model.InboundRequestItem{ID: inboundItem.ID}
					if resp := model.InboundRequestItemDB.UpdateOne(filter, inboundItem); resp.Status != common.APIStatus.Ok {
						errList4 = append(errList4, fmt.Sprintf("%s-%d", inboundItem.SKU, inboundItem.InboundRequestItemID))
						continue LOOP_INBOUND_ITEM
					}
				}
			}

		} else if getInboundMain.Status == common.APIStatus.NotFound {
			break
		} else {
			// error case
			log.Println("Migrate  error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	log.Println("errList0: ", errList0)
	log.Println("errList1: ", errList1)
	log.Println("errList2: ", errList2)
	log.Println("errList3: ", errList3)
	log.Println("errList4: ", errList4)
}

// -------------------------------- VB migrate -------------------------------
func VBMedxHN(poCodes []string) {
	log.Println("Start : Migrate VBMedxHN")
	defer log.Println("End : Migrate VBMedxHN")

	var (
		errList1 []string
		errList2 []string
		errList3 []string
		errList4 []string
	)

	getVBMain := model.VendorBillDB.Query(
		&model.VendorBill{
			ComplexQuery: []*primitive.M{
				{"po_code": bson.M{"$in": poCodes}},
			}},
		0,
		1000,
		&primitive.M{"_id": 1},
	)

	if getVBMain.Status == common.APIStatus.Ok {
		listVBMain := getVBMain.Data.([]*model.VendorBill)

	LOOP_VB_MAIN:
		for i := range listVBMain {
			vbMain := listVBMain[i]

			// --------------------- vb main ---------------------------------
			// change
			vbMain.MigrateMetadata = &model.MigrateMetadata{
				OldSeller: vbMain.SellerCode,
			}
			vbMain.SellerCode = SELLER_HCM

			filter := model.VendorBill{
				ID: vbMain.ID,
			}

			// update
			if resp := model.VendorBillDB.UpdateOne(filter, vbMain); resp.Status != common.APIStatus.Ok {
				errList1 = append(errList1, fmt.Sprint(vbMain.VendorBillID))
				continue LOOP_VB_MAIN
			}

			//  ----------------------------- vb item --------------------------

			getVBItem := model.VendorBillItemDB.Query(
				&model.VendorBillItem{
					VendorBillCode: vbMain.VendorBillCode,
				}, 0, 1000, nil)

			if getVBItem.Status != common.APIStatus.Ok {
				errList2 = append(errList2, fmt.Sprint(vbMain.VendorBillID))
				continue LOOP_VB_MAIN
			}

			listVBItem := getVBItem.Data.([]*model.VendorBillItem)

		LOOP_VB_ITEM:
			for j := range listVBItem {
				vbItem := listVBItem[j]

				// check sku marker place
				option := client.APIOption{
					Limit: utils.Pointer.WithInt(1),
					Q: model.SKUItem{
						ProductCode: vbItem.ProductCode,
						SellerCode:  SELLER_HCM,
					},
				}
				getSKUItemResp := marketplace.GetListSKUItem(option)
				if getSKUItemResp.Status != common.APIStatus.Ok {
					errList3 = append(errList3, fmt.Sprintf("%s-%s", vbItem.SKU, vbItem.VendorBillItemCode))
					continue LOOP_VB_ITEM
				}
				skuItem := getSKUItemResp.Data.([]*model.SKUItem)[0]
				// change
				vbItem.MigrateMetadata = &model.MigrateMetadata{
					OldSeller: vbMain.MigrateMetadata.OldSeller,
					OldSKU:    vbItem.SKU,
				}
				vbItem.SKU = skuItem.SKU

				// filter item
				filter := model.VendorBillItem{ID: vbItem.ID}

				// update
				if resp := model.VendorBillItemDB.UpdateOne(filter, vbItem); resp.Status != common.APIStatus.Ok {
					errList4 = append(errList4, fmt.Sprintf("%s-%s", vbItem.SKU, vbItem.VendorBillItemCode))
					continue LOOP_VB_ITEM
				}
			}
		}

	} else {
		// error case
		log.Println("Migrate error")
		time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
	}

	log.Println("errList1: ", errList1)
	log.Println("errList2: ", errList2)
	log.Println("errList3: ", errList3)
	log.Println("errList4: ", errList4)
}

func RevertVBMedxHN() {
	log.Println("Start : RevertVBMedxHN")
	defer log.Println("End : RevertVBMedxHN")

	var (
		_id_offset = primitive.NilObjectID
		errList0   []string
		errList1   []string
		errList2   []string
		errList3   []string
		errList4   []string
	)

	for {
		getVBMain := model.VendorBillDB.Query(
			&model.VendorBill{
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"migrate_metadata": bson.M{"$exists": true}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getVBMain.Status == common.APIStatus.Ok {
			listVBMain := getVBMain.Data.([]*model.VendorBill)

		LOOP_VB_MAIN:
			for i := range listVBMain {
				vbMain := listVBMain[i]

				// tăng offset
				_id_offset = *vbMain.ID

				// --------------------- vb main ---------------------------------
				// chưa migrate
				if vbMain.MigrateMetadata == nil {
					errList0 = append(errList0, fmt.Sprint(vbMain.VendorBillID))
				}
				// roll-back
				vbMain.SellerCode = vbMain.MigrateMetadata.OldSeller
				filter := model.VendorBill{
					ID: vbMain.ID,
				}

				// update
				if resp := model.VendorBillDB.UpdateOne(filter, vbMain); resp.Status != common.APIStatus.Ok {
					errList1 = append(errList1, fmt.Sprint(vbMain.VendorBillID))
					continue LOOP_VB_MAIN
				}

				//  ----------------------------- vb item --------------------------

				getVBItem := model.VendorBillItemDB.Query(
					&model.VendorBillItem{
						VendorBillCode: vbMain.VendorBillCode,
					}, 0, 1000, nil)

				if getVBItem.Status != common.APIStatus.Ok {
					errList2 = append(errList2, fmt.Sprint(vbMain.VendorBillID))
					continue LOOP_VB_MAIN
				}

				listVBItem := getVBItem.Data.([]*model.VendorBillItem)

			LOOP_VB_ITEM:
				for j := range listVBItem {
					vbItem := listVBItem[j]

					// chưa migrate
					if vbItem.MigrateMetadata == nil {
						errList3 = append(errList3, vbItem.VendorBillItemCode)
					}

					vbItem.SKU = vbItem.MigrateMetadata.OldSKU

					// filter item
					filter := model.VendorBillItem{ID: vbItem.ID}
					if resp := model.VendorBillItemDB.UpdateOne(filter, vbItem); resp.Status != common.APIStatus.Ok {
						errList4 = append(errList4, fmt.Sprintf("%s-%s", vbItem.SKU, vbItem.VendorBillItemCode))
						continue LOOP_VB_ITEM
					}
				}
			}

		} else if getVBMain.Status == common.APIStatus.NotFound {
			break
		} else {
			// error case
			log.Println("Migrate error")
		}
	}

	log.Println("errList0: ", errList0)
	log.Println("errList1: ", errList1)
	log.Println("errList2: ", errList2)
	log.Println("errList3: ", errList3)
	log.Println("errList4: ", errList4)
}

// ----------------------------- Callback Store ---------------------------------
func MigrateCallbackStoreMedxHN(poCodes []string) {
	log.Println("Start : MigrateCallbackStoreMedxHN")
	defer log.Println("End : MigrateCallbackStoreMedxHN")

	var (
		errList1 []string
	)

	getCBStore := model.InboundCallbackStoreDB.Query(
		&model.InboundCallbackStore{
			ComplexQuery: []*primitive.M{
				{"po_code": bson.M{"$in": poCodes}},
			}},
		0,
		DEFAULT_LIMIT,
		&primitive.M{"_id": 1},
	)

	if getCBStore.Status == common.APIStatus.Ok {

		listInboundCBStore := getCBStore.Data.([]*model.InboundCallbackStore)

	LOOP_CB_STORE:
		for i := range listInboundCBStore {
			cbStore := listInboundCBStore[i]
			// change
			cbStore.MigrateMetadata = &model.MigrateMetadata{
				OldSeller: cbStore.SellerCode,
			}

			cbStore.SellerCode = SELLER_HCM

			filter := model.InboundCallbackStore{
				ID: cbStore.ID,
			}
			// update
			if resp := model.InboundCallbackStoreDB.UpdateOne(filter, cbStore); resp.Status != common.APIStatus.Ok {
				errList1 = append(errList1, cbStore.POCode)
				continue LOOP_CB_STORE
			}
		}

	} else {
		// error case
		log.Println("Migrate error")
	}

	log.Println("errList1: ", errList1)
}

func RevertCallbackStoreMedxHN() {
	log.Println("Start : RevertCallbackStoreMedxHN")
	defer log.Println("End : RevertCallbackStoreMedxHN")

	var (
		_id_offset = primitive.NilObjectID
		errList1   []string
		errList2   []string
	)

	for {
		getCBStore := model.InboundCallbackStoreDB.Query(
			&model.InboundCallbackStore{
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"migrate_metadata": bson.M{"$exists": true}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getCBStore.Status == common.APIStatus.Ok {
			listCBstore := getCBStore.Data.([]*model.InboundCallbackStore)

		LOOP_CB_STORE:
			for i := range listCBstore {
				cbStore := listCBstore[i]
				// tăng offset
				_id_offset = *cbStore.ID

				// --------------------- inbound main ---------------------------------
				// chưa migrate
				if cbStore.MigrateMetadata == nil {
					errList1 = append(errList1, fmt.Sprint(cbStore.POCode))
				}
				// roll-back
				cbStore.SellerCode = cbStore.MigrateMetadata.OldSeller
				filter := model.InboundCallbackStore{
					ID: cbStore.ID,
				}

				// update
				if resp := model.InboundCallbackStoreDB.UpdateOne(filter, cbStore); resp.Status != common.APIStatus.Ok {
					errList2 = append(errList2, cbStore.POCode)
					continue LOOP_CB_STORE
				}

			}
		} else if getCBStore.Status == common.APIStatus.NotFound {
			break
		} else {
			// error case
			log.Println("Migrate  error")
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}

	log.Println("errList1: ", errList1)
	log.Println("errList2: ", errList2)
}

// MEDX && MEDX-HN
func MigratePOFromMEDXHNToMEDX(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input migateInput
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Message: err.Error(),
			Status:  common.APIStatus.Invalid,
		})
	}
	if len(input.POCodes) == 0 {
		return resp.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "POCodes is required"})
	}

	fmt.Println("MigratePOFromMEDXHNToMEDX BEGIN...")
	defer fmt.Println("MigratePOFromMEDXHNToMEDX END...")

	for _, poCode := range input.POCodes {
		poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode, SellerCode: SELLER_HN})
		if poResp.Status != common.APIStatus.Ok {
			continue
		}
		po := poResp.Data.([]*model.PurchaseOrder)[0]

		//call api get product information
		productCodes := []string{}

		oldItemsResp := model.PurchaseOrderItemDB.Query(
			model.PurchaseOrderItem{POCode: po.POCode},
			0, 1000, nil,
		)
		if oldItemsResp.Status != common.APIStatus.Ok {
			continue
		}

		poItems := oldItemsResp.Data.([]*model.PurchaseOrderItem)

		for _, item := range poItems {
			if item.SellerCode == SELLER_HN {
				productCodes = append(productCodes, item.ProductCode)
			}
		}
		if len(productCodes) == 0 {
			continue
		}

		option := client.APIOption{
			Q:      model.SKUItem{SellerCode: SELLER_HCM},
			Limit:  utils.Pointer.WithInt(1000),
			Params: map[string]string{"productCodes": strings.Join(productCodes, ",")},
		}
		skuResp := marketplace.GetListSKUItem(option)
		if listSKU, ok := skuResp.Data.([]*model.SKUItem); (skuResp.Status == common.APIStatus.Ok) && ok {
			for i := range poItems {
				poItem := poItems[i]

				for j := range listSKU {
					skuAPI := listSKU[j]
					if poItem.ProductCode == skuAPI.ProductCode {
						if poItem.SellerCode == SELLER_HCM {
							continue
						}
						migrateSeller := model.MigrateSeller{
							OldSeller: SELLER_HN,
							OldSKU:    poItem.SKU,
						}

						poItem.SellerCode = SELLER_HCM
						poItem.SKU = skuAPI.SKU

						if poItem.Metadata != nil {
							poItem.Metadata.MigrateSeller = migrateSeller
						} else {
							poItem.Metadata = &model.POItemMetadata{
								MigrateSeller: migrateSeller,
							}
						}

						model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{ID: poItem.ID}, poItem)
					}
				}
			}
		}

		TRUE := true
		po.SellerCode = SELLER_HCM
		po.IsMigrateSeller = &TRUE

		//update po
		model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: poCode}, po)
	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

// MEDX && MEDX-HN
func MigratePOFromMEDXHNToMEDX2(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input migateInput
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Message: err.Error(),
			Status:  common.APIStatus.Invalid,
		})
	}
	if len(input.POCodes) == 0 {
		return resp.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "POCodes is required"})
	}

	fmt.Println("MigratePOFromMEDXHNToMEDX BEGIN...")
	defer fmt.Println("MigratePOFromMEDXHNToMEDX END...")

	for _, poCode := range input.POCodes {
		poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode, SellerCode: "MEDX"})
		if poResp.Status != common.APIStatus.Ok {
			continue
		}
		po := poResp.Data.([]*model.PurchaseOrder)[0]
		po.SKUs = []string{}

		oldItemsResp := model.PurchaseOrderItemDB.Query(
			model.PurchaseOrderItem{POCode: po.POCode},
			0, 1000, nil,
		)
		if oldItemsResp.Status != common.APIStatus.Ok {
			continue
		}

		poItems := oldItemsResp.Data.([]*model.PurchaseOrderItem)

		for i := range poItems {
			poItem := poItems[i]
			if !utils.IsContains(po.SKUs, poItem.SKU) {
				po.SKUs = append(po.SKUs, poItem.SKU)
			}
		}

		//update po
		model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: poCode}, po)
	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func RevertMigratePOFromMEDXHNToMEDX(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("RevertMigratePOFromMEDXHNToMEDX BEGIN")
	defer fmt.Println("RevertMigratePOFromMEDXHNToMEDX END")

	_id := primitive.NilObjectID
	TRUE := true
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
				IsMigrateSeller: &TRUE,
			},
			0, 100, &primitive.M{"_id": 1},
		)
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			break
		}

		poData := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for i := range poData {
			po := poData[i]

			_id = *po.ID

			oldItemsResp := model.PurchaseOrderItemDB.Query(
				model.PurchaseOrderItem{POCode: po.POCode},
				0, 1000, nil,
			)
			if oldItemsResp.Status != common.APIStatus.Ok {
				continue
			}

			poItems := oldItemsResp.Data.([]*model.PurchaseOrderItem)
			for j := range poItems {
				poItem := poItems[j]

				if poItem.Metadata == nil {
					continue
				}

				poItem.SKU = poItem.Metadata.MigrateSeller.OldSKU
				poItem.SellerCode = poItem.Metadata.MigrateSeller.OldSeller

				model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{ID: poItem.ID}, poItem)

			}
			po.SellerCode = SELLER_HN

			//update po
			model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: po.POCode}, po)
		}
	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigratePIC(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("MigratePIC BEGIN")
	defer fmt.Println("MigratePIC END")

	picResp := model.PICDB.Query(
		model.PIC{
			ObjectType:    "SKU",
			WarehouseCode: "HN",
			ComplexQuery: []*bson.M{
				{"object_code": bson.M{"$in": []string{
					"medx.010bd4dadf67",
					"medx.04b0a2caf8f5",
					"medx.06e7c2601b43",
					"medx.06fa836076e3",
					"medx.088ab37c1fbc",
					"medx.08f18732-d9ce-4378-9226-9d09cb485445",
					"medx.092f2bed-7b5c-490c-a335-1ce4fcf129ec",
					"medx.0cb75fd5411a",
					"medx.10774fb7c976",
					"medx.16667cb295ea",
					"medx.1ab458e7c987",
					"medx.1bb6c6cb144a",
					"medx.1f33c444-94c7-4fda-b8e2-78e25fe3855a",
					"medx.2110dcfad25e",
					"medx.2231816d1da3",
					"medx.3070837d-495b-40a0-9bf1-a14ffcf7cfea",
					"medx.32f7e4d1af9a",
					"medx.3463630d1a08",
					"medx.354b3e98351a",
					"medx.381825c74ab9",
					"medx.39542a35f853",
					"medx.396ec90b07b4",
					"medx.39879ee27224",
					"medx.39a8ad7b185d",
					"medx.46bbc17a07bb",
					"medx.46e71758dec0",
					"medx.473a930f3dc0",
					"medx.47b55335e84d",
					"medx.5b4213a5d8495",
					"medx.5f3e69aeb966",
					"medx.5fef04833b48",
					"medx.604eec8a76e4",
					"medx.62c88986-331a-4854-9487-4196b9e46849",
					"medx.6a082fb77027",
					"medx.71a59983094a",
					"medx.7c710b1fa24a",
					"medx.84cf804edd77",
					"medx.8d4204a23e73",
					"medx.8df96ed1-fbe0-41a3-b42a-fffc5d4a67df",
					"medx.8e894b52849b",
					"medx.931fa50ed397",
					"medx.944afb05dd09",
					"medx.9f93efdf-ca7d-4825-930d-875eba9c1a6e",
					"medx.a2b62c33ea81",
					"medx.a39c3a20fb80",
					"medx.a62f92f16252",
					"medx.a8ef7aa8eeac",
					"medx.a98bea7ad9cd",
					"medx.ad11fb9fe7ba",
					"medx.ad6d451b8cf4",
					"medx.ae68731fa310",
					"medx.agi-agi-023",
					"medx.agi-tri-001",
					"medx.agm-agi-009",
					"medx.b5194df41094",
					"medx.b7c0a55e56a2",
					"medx.bb92516b0d73",
					"medx.bbcdcdfc12a2",
					"medx.bifido-001",
					"medx.bifido-002",
					"medx.bifido-003",
					"medx.bifido-004",
					"medx.bos-clo-01",
					"medx.bronson1",
					"medx.bxd-bll-100",
					"medx.c5153be2e292",
					"medx.c58477233d05",
					"medx.ceramm",
					"medx.dbc3dae8c970",
					"medx.dddec3e69dcb",
					"medx.dmts04a5083e06ba",
					"medx.dmts2aaf3f1c1dc6",
					"medx.dmts48cb6e400663",
					"medx.dmts5019e9302d2f",
					"medx.dmts70d50f333c86",
					"medx.dmts72469435d470",
					"medx.dmts8d8bba6dbdf1",
					"medx.dmtsa2d9ab976368",
					"medx.dmtsa6551f312bd4",
					"medx.dmtsa85c6a4e2f45",
					"medx.dmtsbb1b6ac594d6",
					"medx.dmtsf2279b2f0786",
					"medx.dos-doxy-01",
					"medx.dvp-rvl-005",
					"medx.e8d301c60a31",
					"medx.ea23738b8adf",
					"medx.f3166f673b61",
					"medx.gsk-fli-001",
					"medx.hbn-ghy-671",
					"medx.jnam-meb-001",
					"medx.kha-kat-002",
					"medx.mek-inh-001",
					"medx.nul-cef-001",
					"medx.nul-cuo-001",
					"medx.nul-tan-001",
					"medx.oral-b-essential",
					"medx.sdv-gel-001",
					"medx.sta-pri-001",
					"medx.tam-tam-002",
					"medx.usp-ome-002c",
				}}},
			},
		},
		0, 100, &primitive.M{"_id": 1},
	)

	picData := picResp.Data.([]*model.PIC)
	for i := range picData {
		pic := picData[i]

		objectCode := strings.ReplaceAll(pic.ObjectCode, "medx.", "MEDX.")
		model.PICDB.UpdateOne(model.PIC{ID: pic.ID}, model.PIC{
			ObjectCode: objectCode,
		})

	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigratePICSellerCode(_ sdk.APIRequest, resp sdk.APIResponder) error {

	go sdk.Execute(migratePICSellerCode)

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})

}

func migratePICSellerCode() {
	log.Println("Start migratePICSellerCode")
	defer log.Println("End migratePICSellerCode")

	var _id *primitive.ObjectID
	var f primitive.M
	listPIC := []*model.PIC{}
	for {
		if _id != nil && !_id.IsZero() {
			f = primitive.M{"_id": primitive.M{"$gt": _id}}
		}

		picResp := model.PICDB.Query(f, 0, 100, &primitive.M{"_id": 1})
		if picResp.Status != common.APIStatus.Ok {
			break
		}

		picData := picResp.Data.([]*model.PIC)
		for _, pic := range picData {
			// tang offset
			_id = pic.ID
			// add to list
			listPIC = append(listPIC, pic)
		}
	}

	log.Println("Total PIC: ", len(listPIC))
	skuErrorList := []string{}
	updateErr := []string{}
	progress := 0
	for i, pic := range listPIC {
		if len(pic.SellerCode) > 0 {
			continue
		}
		if i%300 == 0 {
			log.Println("Progress: ", progress)
		}

		if pic.ObjectType == enum.ObjectType.SKU {
			switch {
			case strings.HasPrefix(pic.ObjectCode, "MEDX."):
				pic.SellerCode = "MEDX"
			case strings.HasPrefix(pic.ObjectCode, "MEDX_E."):
				pic.SellerCode = "MEDX_E"
			case strings.HasPrefix(pic.ObjectCode, "MARKETING."):
				pic.SellerCode = "MARKETING"
			default:
				skuErrorList = append(skuErrorList, pic.ObjectCode)
			}
		}

		// update
		updateResp := model.PICDB.UpdateOne(model.PIC{ID: pic.ID}, pic)
		if updateResp.Status != common.APIStatus.Ok {
			updateErr = append(updateErr, fmt.Sprintf("objecCode :%s, warehouseCode: %s, objecType: %s -- ", pic.ObjectCode, pic.WarehouseCode, pic.ObjectType))
		}
	}

	log.Println("Total SKU Error: ", len(skuErrorList))
	log.Println("Total Update Error: ", len(updateErr))
}
