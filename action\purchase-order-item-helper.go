package action

import (
	"fmt"
	"strings"
	"sync"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// VendorInfoCache caches vendor information to avoid duplicate API calls
type VendorInfoCache struct {
	cache map[string]*model.Seller
	mutex sync.RWMutex
}

// NewVendorInfoCache creates a new vendor info cache
func NewVendorInfoCache() *VendorInfoCache {
	return &VendorInfoCache{
		cache: make(map[string]*model.Seller),
	}
}

// Get retrieves vendor info from cache or API
func (v *VendorInfoCache) Get(vendorCode string) (*model.Seller, error) {
	// Check cache first
	v.mutex.RLock()
	if vendor, exists := v.cache[vendorCode]; exists {
		v.mutex.RUnlock()
		return vendor, nil
	}
	v.mutex.RUnlock()

	// Not in cache, fetch from API
	vendorInfoResp := GetVendorInfo(vendorCode)
	if vendorInfoResp.Status != common.APIStatus.Ok {
		return nil, fmt.Errorf("failed to get vendor info: %s", vendorInfoResp.Message)
	}

	vendor := vendorInfoResp.Data.([]*model.Seller)[0]

	// Store in cache
	v.mutex.Lock()
	v.cache[vendorCode] = vendor
	v.mutex.Unlock()

	return vendor, nil
}

// BatchGetVendors fetches multiple vendors in a single API call
func (v *VendorInfoCache) BatchGetVendors(vendorCodes []string) map[string]*model.Seller {
	result := make(map[string]*model.Seller)
	uncachedCodes := []string{}

	// Check cache first
	v.mutex.RLock()
	for _, code := range vendorCodes {
		if vendor, exists := v.cache[code]; exists {
			result[code] = vendor
		} else {
			uncachedCodes = append(uncachedCodes, code)
		}
	}
	v.mutex.RUnlock()

	// Fetch uncached vendors in batch
	if len(uncachedCodes) > 0 {
		option := client.APIOption{
			Params: map[string]string{
				"sellerClass": model.CLASS_VENDOR,
				"sellerCodes": strings.Join(uncachedCodes, ","),
			},
			Limit: utils.Pointer.WithInt(len(uncachedCodes)),
		}

		vendorResp := seller.GetSellers(option)
		if vendorResp.Status == common.APIStatus.Ok {
			vendors := vendorResp.Data.([]*model.Seller)

			v.mutex.Lock()
			for _, vendor := range vendors {
				v.cache[vendor.Code] = vendor
				result[vendor.Code] = vendor
			}
			v.mutex.Unlock()
		}
	}

	return result
}

// PriceAfterRebateRequest represents a request for price after rebate
type PriceAfterRebateRequest struct {
	Version       string
	WarehouseCode string
	VendorCode    string
	ProductID     int64
	POItem        *model.PurchaseOrderItem
}

// BatchGetPriceAfterRebate fetches price after rebate for multiple items using goroutines
func BatchGetPriceAfterRebate(requests []PriceAfterRebateRequest) *POItemMetrics {
	metrics := NewPOItemMetrics()
	metrics.TotalItems = len(requests)

	if len(requests) == 0 {
		return metrics
	}

	config := DefaultPOItemOptimizationConfig()

	// Use semaphore to limit concurrent requests
	sema := utils.NewSemaphore(config.MaxConcurrentPriceRequests)
	var wg sync.WaitGroup
	var metricsMutex sync.Mutex

	for i := range requests {
		sema.Acquire()
		wg.Add(1)

		go func(req PriceAfterRebateRequest) {
			defer sema.Release()
			defer wg.Done()

			otps := client.APIOption{
				Params: map[string]string{
					"version":       req.Version,
					"warehouseCode": req.WarehouseCode,
					"vendorCode":    req.VendorCode,
					"productID":     fmt.Sprint(req.ProductID),
				},
			}

			getPriceAfterRebateResp := seller.GetPriceAfterRebateLastest(otps)

			metricsMutex.Lock()
			if getPriceAfterRebateResp.Status == common.APIStatus.Ok {
				metrics.SuccessfulPriceRequests++
				priceData := getPriceAfterRebateResp.Data.([]*model.PriceAfterRebate)
				if len(priceData) > 0 {
					req.POItem.PriceAfterRebate = priceData[0].PriceAfterRebateWithAdhocPromotion
				}
			} else {
				metrics.FailedPriceRequests++
			}
			metricsMutex.Unlock()
		}(requests[i])
	}

	sema.Wait()
	wg.Wait()

	return metrics
}

// ProcessPOItemsWithOptimization processes purchase order items with vendor info and price after rebate
// using optimized batch processing and caching
func ProcessPOItemsWithOptimization(poItems []*model.PurchaseOrderItem) *POItemMetrics {
	totalMetrics := NewPOItemMetrics()
	totalMetrics.TotalItems = len(poItems)

	if len(poItems) == 0 {
		return totalMetrics
	}

	// Step 1: Collect unique vendor codes
	vendorCodesSet := make(map[string]bool)
	for _, poItem := range poItems {
		if poItem.VendorCode != "" {
			vendorCodesSet[poItem.VendorCode] = true
		}
	}

	// Convert to slice
	vendorCodes := make([]string, 0, len(vendorCodesSet))
	for code := range vendorCodesSet {
		vendorCodes = append(vendorCodes, code)
	}

	// Step 2: Batch fetch vendor info
	vendorCache := NewVendorInfoCache()
	vendorMap := vendorCache.BatchGetVendors(vendorCodes)

	// Update metrics for vendor cache
	for _, poItem := range poItems {
		if _, exists := vendorMap[poItem.VendorCode]; exists {
			totalMetrics.VendorCacheHits++
		} else {
			totalMetrics.VendorCacheMisses++
		}
	}

	// Step 3: Prepare price after rebate requests
	priceRequests := make([]PriceAfterRebateRequest, 0, len(poItems))
	for _, poItem := range poItems {
		if vendor, exists := vendorMap[poItem.VendorCode]; exists {
			vendorCode := vendor.Code
			if len(vendor.LineManager) > 0 {
				vendorCode = vendor.LineManager
			}

			version := utils.ConvertTimeToStringYYYYMMDD(poItem.CreatedTime, "")
			priceRequests = append(priceRequests, PriceAfterRebateRequest{
				Version:       version,
				WarehouseCode: poItem.WarehouseCode,
				VendorCode:    vendorCode,
				ProductID:     poItem.ProductID,
				POItem:        poItem,
			})
		}
	}

	// Step 4: Batch fetch price after rebate using goroutines
	priceMetrics := BatchGetPriceAfterRebate(priceRequests)

	// Combine metrics
	totalMetrics.SuccessfulPriceRequests = priceMetrics.SuccessfulPriceRequests
	totalMetrics.FailedPriceRequests = priceMetrics.FailedPriceRequests

	return totalMetrics
}

// GetUniqueStrings returns unique strings from a slice
func GetUniqueStrings(slice []string) []string {
	keys := make(map[string]bool)
	result := []string{}

	for _, item := range slice {
		if !keys[item] && item != "" {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// ChunkSlice splits a slice into chunks of specified size
func ChunkSlice(slice []string, chunkSize int) [][]string {
	var chunks [][]string
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}
