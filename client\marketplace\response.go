package marketplace

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

/*
Product response
*/
type (
	SKUItemResponse struct {
		Data []*model.SKUItem `json:"data"`
		common.APIResponse
	}

	SKUListResponse struct {
		Data []*model.Sku `json:"data"`
		common.APIResponse
	}

	ProductResponse struct {
		Data []*model.Product `json:"data"`
		common.APIResponse
	}

	CICProductResponse struct {
		Data []*model.ProductCategoryInCharge `json:"data"`
		common.APIResponse
	}
)

/*
Order response
*/

type (
	OrderResponse struct {
		Data []*model.Order `json:"data"`
		common.APIResponse
	}

	OrderItemResponse struct {
		Data []*model.OrderItem `json:"data"`
		common.APIResponse
	}
)
