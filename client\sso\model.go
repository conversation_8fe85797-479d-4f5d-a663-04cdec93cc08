package sso

type EntityMemberResponse struct {
	Status    string               `json:"status"`
	Data      []*LegacyAccountInfo `json:"data"`
	Message   string               `json:"message"`
	ErrorCode string               `json:"errorCode"`
}

type LegacyAccountInfo struct {
	AccountID   int64  `json:"accountId,omitempty" bson:"account_id,omitempty" `
	Fullname    string `json:"fullname,omitempty" bson:"fullname,omitempty" `
	Username    string `json:"username,omitempty" bson:"username,omitempty" `
	Email       string `json:"email,omitempty" bson:"email,omitempty"`
	PhoneNumber string `json:"phoneNumber,omitempty" bson:"phone_number,omitempty"`
	CountryCode string `json:"countryCode,omitempty" bson:"country_code,omitempty"`

	// Type            string `json:"type,omitempty" bson:"type,omitempty" `
	LinkedAccountID int64 `json:"linkedAccountID,omitempty" bson:"linked_account_id,omitempty"`
}
