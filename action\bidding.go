package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	quotationDiscountPercent = float64(0)
)

// GetVendorBidding ...
func GetVendorBidding(query model.Bidding, offset, limit int64, getTotal bool) *common.APIResponse {

	biddingResp := model.BiddingDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.BiddingDB.Count(query)
		biddingResp.Total = countResp.Total
	}

	return biddingResp
}

// GetVendorBiddingHistory ...
func GetVendorBiddingHistory(query model.BiddingHistory, offset, limit int64, getTotal bool) *common.APIResponse {

	biddingHistoryResp := model.BiddingHistoryDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.BiddingHistoryDB.Count(query)
		biddingHistoryResp.Total = countResp.Total
	}
	return biddingHistoryResp
}

// GetSellerBidding ...
func GetSellerBidding(query model.Bidding, offset, limit int64, getTotal bool) *common.APIResponse {
	biddingResp := model.BiddingDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.BiddingDB.Count(query)
		biddingResp.Total = countResp.Total
	}

	return biddingResp
}

// BiddingQuotation ...
func BiddingQuotation(input *model.BiddingQuotationInput) *common.APIResponse {

	if utils.IsNil(input.Vendor) || input.Vendor.Code == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor is required",
			ErrorCode: string(enum.ErrorCodeInvalid.Vendor),
		}
	}

	// validate bidding
	if res, ok := validateBidding(input.Items); !ok {
		return res
	}

	var (
		vendorCode = input.Vendor.Code
	)

	// Check dup action in same time
	chilKeys := []string{}
	for _, item := range input.Items {
		chilKeys = append(chilKeys, fmt.Sprintf("%v.%v", item.WarehouseCode, item.SKU))
	}

	checkLockAcitonResp := CreateLockActions("CREATE_BIDDING", input.Vendor.Code, chilKeys)
	if checkLockAcitonResp.Status != common.APIStatus.Ok {
		checkLockAcitonResp.Message = "Có lỗi xảy ra khi thao tác đồng thời, vui lòng thử lại sau ít phút"
		checkLockAcitonResp.ErrorCode = string(enum.ErrorCodeInvalid.LockAction)
		return checkLockAcitonResp
	}

	for _, bidding := range input.Items {
		if bidding.SKU == "" {
			continue
		}

		// Trường hợp edit số lượng bằng 0 - Xóa dữ liệu của bidding
		if bidding.QuantityBidding == nil || *bidding.QuantityBidding <= 0 {
			// xoá bidding product theo kho cần
			model.BiddingDB.Delete(model.Bidding{
				ProductID:     bidding.ProductID,
				SKU:           bidding.SKU,
				WarehouseCode: bidding.WarehouseCode,
				VendorCode:    vendorCode,
			})

			// Gỡ tag isBidding = true
			// nếu không có vendor khác đang bidding
			checkBiddingResp := model.BiddingDB.QueryOne(model.Bidding{
				ProductID:     bidding.ProductID,
				SKU:           bidding.SKU,
				WarehouseCode: bidding.WarehouseCode,
			})

			// nếu kho cần đó không còn vendor nào bidding cho đó
			// -> thay đổi status quotation không có ai đang bidding cho quotation đó
			if checkBiddingResp.Status == common.APIStatus.NotFound {
				model.QuotationDB.UpdateMany(
					model.Quotation{
						ProductID:     bidding.ProductID,
						SKU:           bidding.SKU,
						WarehouseCode: bidding.WarehouseCode,
					},
					model.Quotation{
						IsBidding: utils.Pointer.WithBool(false),

						// BI
						// ActionTrackingLastest: enum.QuotationActionTracking.BIDDING,
					},
				)

				// update ES search
				// WarmupSearch(bidding.ProductCode, nil)
			}
			continue
		}

		if bidding.VAT != nil {
			bidding.UnitPrice = utils.GetPriceFromPriceAfterVAT(bidding.UnitPriceAfterVAT, quotationDiscountPercent,
				float64(*bidding.VAT))
		}

		// check kho cần kho nhận bidding
		if bidding.DeliveryWarehouseCode == "" || bidding.WarehouseCode == "" {
			continue
		}

		// check dữ liệu trong quotation theo dữ liệu bidding
		quotaionResp := model.QuotationDB.Query(model.Quotation{
			ProductID:     bidding.ProductID,
			SKU:           bidding.SKU,
			WarehouseCode: bidding.WarehouseCode,
			ComplexQuery: []*bson.M{{
				"quantity_expect": &bson.M{
					"$gt": 0,
				},
			}},
		}, 0, 10, nil)
		if quotaionResp.Status != common.APIStatus.Ok {
			return quotaionResp
		}
		quotations := quotaionResp.Data.([]*model.Quotation)

		var quotationPurchaserCode string
		var sellerCodes []string
		for _, quota := range quotations {
			sellerCodes = append(sellerCodes, quota.SellerCode)
			quotationPurchaserCode = quota.PurchaserCode
			if quota.IsBidding == nil || !*quota.IsBidding {
				model.QuotationDB.UpdateOne(
					model.Quotation{ID: quota.ID},
					model.Quotation{
						IsBidding: utils.Pointer.WithBool(true),

						// BI
						// ActionTrackingLastest: enum.QuotationActionTracking.BIDDING,
					},
				)

				// // update ES search
				// WarmupSearch(bidding.ProductCode, nil)
			}
		}

		bidding.VendorCode = vendorCode

		checkBiddingResp := model.BiddingDB.QueryOne(model.Bidding{
			VendorCode:    vendorCode,
			ProductID:     bidding.ProductID,
			SKU:           bidding.SKU,
			WarehouseCode: bidding.WarehouseCode,
		})
		// bidding đã tồn tại
		if checkBiddingResp.Status == common.APIStatus.Ok {
			checkBidding := checkBiddingResp.Data.([]*model.Bidding)[0]
			if bidding.VAT != nil {
				// các field đã validate
				updater := model.Bidding{
					VAT:               bidding.VAT,
					UnitPrice:         bidding.UnitPrice,
					UnitPriceAfterVAT: bidding.UnitPriceAfterVAT,
					QuantityBidding:   bidding.QuantityBidding,
				}

				updateBiddingResp := model.BiddingDB.UpdateOne(model.Bidding{ID: checkBidding.ID},
					updater)
				if updateBiddingResp.Status != common.APIStatus.Ok {
					return updateBiddingResp
				}
			}

		} else if checkBiddingResp.Status == common.APIStatus.NotFound {

			// Check if priority vendor is favorite
			selectedSeller := []string{}
			for _, sellerCode := range sellerCodes {
				purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(bidding.WarehouseCode)
				if !ok {
					continue
				}
				skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
					ProductID:     bidding.ProductID,
					SellerCode:    sellerCode,
					PurchaserCode: purchaserCode,
				})
				if skuConfigResp.Status == common.APIStatus.Ok {
					skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]
					// Check if sku-config has vendors
					if !utils.IsNil(skuConfig.Vendors) {
						// Loop list vendor of sku-config
						for _, vendor := range *skuConfig.Vendors {
							// Check if current vendor is favorite
							if vendor.VendorCode == bidding.VendorCode &&
								vendor.Priority != nil &&
								utils.IsInt64Contains(utils.ALL_PRIORITY, *vendor.Priority) {
								bidding.VendorPriority = vendor.Priority
								checkSelectBysResp := model.BiddingDB.QueryOne(model.Bidding{
									ProductID:     bidding.ProductID,
									SKU:           bidding.SKU,
									WarehouseCode: bidding.WarehouseCode,
									// không có điều kiện DeliveryWarehouseCode
									ComplexQuery: []*bson.M{{"selected_bys": sellerCode}},
								})
								if checkSelectBysResp.Status == common.APIStatus.NotFound {
									selectedSeller = append(selectedSeller, sellerCode)
								}
								break
							}
						}
					}
				}
			}

			// Create bidding
			if len(selectedSeller) > 0 {
				bidding.SelectedBys = &selectedSeller
			}
			// set purchaser_code for supplier page query
			bidding.PurchaserCode = quotationPurchaserCode
			createBiddingResp := model.BiddingDB.Create(bidding)
			if createBiddingResp.Status != common.APIStatus.Ok {
				return createBiddingResp
			}
		} else {

			return checkBiddingResp
		}

		// Write history
		model.BiddingHistoryDB.Create(model.BiddingHistory{
			Version:               utils.GetCurrentVersionDDMMYYYY(),
			ProductID:             bidding.ProductID,
			SKU:                   bidding.SKU,
			ProductCode:           bidding.ProductCode,
			DeliveryWarehouseCode: bidding.DeliveryWarehouseCode,
			WarehouseCode:         bidding.WarehouseCode,
			PurchaserCode:         quotationPurchaserCode,
			VendorCode:            bidding.VendorCode,
			UnitPrice:             bidding.UnitPrice,
			UnitPriceAfterVAT:     bidding.UnitPriceAfterVAT,
			VAT:                   bidding.VAT,
			QuantityBidding:       bidding.QuantityBidding,
			QuantityExpect:        bidding.QuantityExpect,
		})
	}

	// update sku config after supplier bidding
	updateSkuConfigAfterBidding(input.Items)
	DeleteLockActions("CREATE_BIDDING", input.Vendor.Code, chilKeys)

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create bidding success",
	}
}

// updateSkuConfigAfterBidding updates the SKU configuration after bidding.
// It takes a slice of Bidding models as input, retrieves the corresponding SKU configurations
// from the database, and updates the publish price after VAT if certain conditions are met.
//
// Parameters:
// - biddings: A slice of Bidding models containing the bidding information.
//
// The function performs the following steps:
//  1. Extracts the product codes from the biddings.
//  2. Queries the SKU configurations from the database based on the product codes.
//  3. Iterates through the retrieved SKU configurations and updates the publish price after VAT
//     if the bidding SKU matches and the new price meets the criteria for updating.
//
// The criteria for updating the publish price after VAT are:
//   - The new price must be greater than 0.
//   - The new price must be less than the current publish price after VAT and greater than
//     a minimum threshold, which is calculated as a percentage of the current publish price after VAT.
//
// If the SKU configuration's publish price after VAT is nil, the new price is always allowed to update.
func updateSkuConfigAfterBidding(biddings []model.Bidding) {
	// get sku config
	productCodeIn := []string{}
	for _, bid := range biddings {
		productCodeIn = append(productCodeIn, bid.ProductCode)
	}
	skuConfigResp := model.SkuConfigDB.Query(model.SkuConfig{
		ComplexQuery: []*bson.M{
			{
				"product_code": bson.M{
					"$in": productCodeIn,
				},
			},
		},
	}, 0, 1000, nil)
	if skuConfigResp.Status != common.APIStatus.Ok {
		return
	}
	skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)
	// update sku config
	for _, skuConfig := range skuConfigs {
		for _, bidding := range biddings {
			if bidding.SKU != "" && skuConfig.SKU == bidding.SKU {
				floatPrice := float64(bidding.UnitPriceAfterVAT)
				minForUpdatePublishPrice := 0.00
				if skuConfig.PublishPriceAfterVAT != nil {
					minForUpdatePublishPrice = *skuConfig.PublishPriceAfterVAT * utils.MAX_ACCEPT_UPDATE_PUBLISHED_PRICE_FROM_BIDDING_PERCENT / 100
				}
				// greater than 50% of current publish price sku-config => allow update new publish price
				if floatPrice > 0 && (skuConfig.PublishPriceAfterVAT == nil ||
					(*skuConfig.PublishPriceAfterVAT > floatPrice && floatPrice > minForUpdatePublishPrice)) {
					model.SkuConfigDB.UpdateOne(
						model.SkuConfig{ID: skuConfig.ID},
						model.SkuConfig{PublishPriceAfterVAT: &floatPrice},
					)
				}
			}
		}
	}
}

// CreatePOFromBidding ...
// CreatePOFromBidding creates a Purchase Order (PO) from a bidding process.
// It validates the input data, checks the bidding and quotation quantities, and updates the PO accordingly.
// If the PO creation or update is successful, it updates the bidding and quotation records and creates a history entry.
//
// Parameters:
//   - input: model.CreatePOFromBiddingInput containing the PO and related data.
//   - acc: *model.Account representing the account performing the action.
//
// Returns:
//   - *common.APIResponse indicating the result of the operation, including status, message, and error code if applicable.
func CreatePOFromBidding(input model.CreatePOFromBiddingInput, acc *model.Account) *common.APIResponse {
	type biddingQuota struct {
		bidding   *model.Bidding
		quotation *model.Quotation
	}

	if input.PO.WarehouseCode == "" || input.PO.DeliveryWarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode, DeliveryWarehouseCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// validate seller and purchaser
	if response, ok := utils.ValidSellerPurchaser(input.PO.SellerCode, input.PO.PurchaserCode); !ok {
		return response
	}

	var (
		biddingQuotations = make([]biddingQuota, 0)
		inputPO           = input.PO
		items             = inputPO.Items
	)

	// Compare PO items with quotation/bidding & calculate bidding and quotation quantity should be if PO create success
	for _, item := range items {
		// PO item đã được tạo với PO khác
		if !utils.IsZero(item.POItemID) {
			continue
		}

		// validate
		if utils.HasZero(item.ExpectQuantity, item.ProductID,
			item.WarehouseCode, item.DeliveryWarehouseCode) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The info is not in the correct format",
				ErrorCode: string(enum.ErrorCodeInvalid.Info),
			}
		}

		// Check in bidding
		biddingQuery := model.Bidding{
			VendorCode:            inputPO.VendorCode,
			ProductID:             item.ProductID,
			SKU:                   item.SKU,
			WarehouseCode:         item.WarehouseCode,
			DeliveryWarehouseCode: item.DeliveryWarehouseCode,
		}
		biddingResp := model.BiddingDB.QueryOne(biddingQuery)
		if biddingResp.Status != common.APIStatus.Ok {
			continue
		}
		bidding := biddingResp.Data.([]*model.Bidding)[0]

		// Query quotation với kho cần bidding
		quotationQuery := model.Quotation{
			ProductID:     bidding.ProductID,
			SKU:           bidding.SKU,
			SellerCode:    item.SellerCode,
			WarehouseCode: bidding.WarehouseCode,
			ComplexQuery: []*bson.M{{
				"quantity_expect": &bson.M{
					"$gt": 0,
				},
			}},
		}

		// Check in quotation
		quotationResp := model.QuotationDB.QueryOne(quotationQuery)
		if quotationResp.Status != common.APIStatus.Ok {
			quotationResp.ErrorCode = string(enum.ErrorCodeNotFound.Quotations)
			quotationResp.Message = "Có sự thay đổi dữ liệu báo giá, vui lòng tải lại trang"
			return quotationResp
		}
		quotation := quotationResp.Data.([]*model.Quotation)[0]

		// Check quantity
		if utils.IsNil(quotation) || *quotation.QuantityExpect == 0 {
			return &common.APIResponse{
				Status: common.APIStatus.Invalid,
				Message: fmt.Sprintf("Quantity expect of product %v not found",
					quotation.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.QuantityExpect),
			}
		}

		// Check quantity
		if item.ExpectQuantity > *quotation.QuantityExpect {
			return &common.APIResponse{
				Status: common.APIStatus.Invalid,
				Message: fmt.Sprintf("po_item.ExpectQuantity > quotation.QuantityExpect: %v > %v",
					item.ExpectQuantity, *quotation.QuantityExpect),
				ErrorCode: string(enum.ErrorCodeInvalid.QuantityConfirmGTQuantityExpect),
			}
		}

		// Check quantity
		if utils.IsNil(bidding.QuantityBidding) {
			return &common.APIResponse{
				Status: common.APIStatus.Invalid,
				Message: fmt.Sprintf("Quantity bidding of product %v not found",
					quotation.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.QuantityBidding),
			}
		}
		if item.ExpectQuantity > *bidding.QuantityBidding {
			return &common.APIResponse{
				Status: common.APIStatus.Invalid,
				Message: fmt.Sprintf("QuantityConfirmed > QuantityBidding: %v > %v",
					item.ExpectQuantity, *bidding.QuantityBidding),
				ErrorCode: string(enum.ErrorCodeInvalid.QuantityExpectGTQuantityBidding),
			}
		}

		*quotation.QuantityExpect -= item.ExpectQuantity
		*bidding.QuantityBidding -= item.ExpectQuantity
		quotation.QuantityConfirmed += item.ExpectQuantity       // For FE internal display
		quotation.CurrentQuantityConfirmed = item.ExpectQuantity // For log

		biddingQuotations = append(biddingQuotations, biddingQuota{bidding: bidding, quotation: quotation})

	}

	if len(biddingQuotations) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Bidding Quotation not found",
		}
	}

	//Create or Update PO
	createPOResp := &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Error case",
	}

	// Create or Update PO
	if inputPO.POCode == "" {
		if inputPO.CreatedBySystem == "SUPPLIER" {
			now := utils.GetVietnamTimeNow()
			inputPO.StartQuotationTime = &now
		}
		createPOResp = CreatePurchaseOrder(inputPO, acc)
	} else {
		createPOResp = UpdatePurchaseOrder(inputPO, false)
	}
	if createPOResp.Status != common.APIStatus.Ok {
		if inputPO.POCode == "" {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.CreatePO)
		} else {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.UpdatePO)
		}
		return createPOResp
	}
	po := createPOResp.Data.([]*model.PurchaseOrder)[0]

	// update bidding & quotation
	for _, biddingQuota := range biddingQuotations {

		var (
			bidding   = biddingQuota.bidding
			quotation = biddingQuota.quotation
		)
		// ------------ bidding -----------
		// nếu QuantityBidding = 0 thì xóa
		if bidding.QuantityBidding == nil || *bidding.QuantityBidding == 0 {
			model.BiddingDB.Delete(model.Bidding{ID: bidding.ID})

			// nếu QuantityBidding > 0 thì cập nhật lại selectedBys
		} else {
			model.BiddingDB.UpdateOne(model.Bidding{ID: bidding.ID}, bidding)

			// gở sellerCode khỏi selectedBys
			if bidding.QuantityBidding != nil && *bidding.QuantityBidding != 0 {
				selectedBys := []string{}
				if bidding.SelectedBys != nil {
					for i := range *bidding.SelectedBys {
						sellerCode := (*bidding.SelectedBys)[i]
						if sellerCode == inputPO.SellerCode {
							continue
						}
						selectedBys = append(selectedBys, sellerCode)
					}
				}
				model.BiddingDB.UpdateOne(
					model.Bidding{ID: bidding.ID},
					model.Bidding{SelectedBys: &selectedBys},
				)
			}
		}

		// ------------ quotation -----------

		// xử lý xóa quotation: nếu QuantityExpect = 0
		if quotation.QuantityExpect == nil || *quotation.QuantityExpect == 0 {
			quotation = executeQuotationWithQuantity0(inputPO, *quotation)

		} else {
			quotation = executeQuotationWithQuantityGt0(inputPO, *quotation)
			// nếu kho đó đã hết bidding cho product đó
			// gở cờ IsBidding của tất cả seller cùng kho cần
			checkBiddingResp := model.BiddingDB.QueryOne(model.Bidding{
				ProductID:     quotation.ProductID,
				SKU:           quotation.SKU,
				WarehouseCode: quotation.WarehouseCode,
			})
			if checkBiddingResp.Status == common.APIStatus.NotFound {
				model.QuotationDB.UpdateMany(
					model.Quotation{
						ProductID:     quotation.ProductID,
						SKU:           quotation.SKU,
						WarehouseCode: quotation.WarehouseCode,
					},
					model.Quotation{
						IsBidding: utils.Pointer.WithBool(false),

						// BI
						// ActionTrackingLastest: enum.QuotationActionTracking.PO_INCREASE,
					},
				)
			}
		}

		// get poItem cho history
		poItem := model.PurchaseOrderItem{}
		for i := range inputPO.Items {
			item := inputPO.Items[i]
			if item.SKU == quotation.SKU {
				poItem = *item
			}
		}

		// tạo history
		model.QuotationPOHistoryDB.Create(model.QuotationPOHistory{
			CreatedBySystem:   inputPO.CreatedBySystem,
			CreatedByName:     acc.Fullname,
			SellerCode:        quotation.SellerCode,
			PurchaserCode:     inputPO.PurchaserCode,
			WarehouseCode:     quotation.WarehouseCode,
			VendorCode:        inputPO.VendorCode,
			Version:           utils.GetCurrentVersionDDMMYYYY(),
			ProductName:       quotation.ProductName,
			ProductCode:       quotation.ProductCode,
			ProductID:         quotation.ProductID,
			SKU:               quotation.SKU,
			POCode:            po.POCode,
			POStatus:          string(inputPO.Status),
			QuantityConfirmed: quotation.CurrentQuantityConfirmed,
			ReferPrice:        poItem.ReferPrice,
		})

		// update confirmQuantity
		getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
			WarehouseCode: quotation.WarehouseCode,
			Version:       utils.GetCurrentVersionDDMMYYYY(),
			SKU:           quotation.SKU,
		})
		if getTrackingQuotationResp.Status == common.APIStatus.Ok {
			oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
			if oldTrackingQuotation.TotalQuantityConfirmed == nil {
				oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
			}
			newConfirmedQty := *oldTrackingQuotation.TotalQuantityConfirmed + quotation.CurrentQuantityConfirmed
			model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
				Version:       utils.GetCurrentVersionDDMMYYYY(),
				WarehouseCode: quotation.WarehouseCode,
				SKU:           quotation.SKU,
			}, model.QuotationTracking{
				TotalQuantityConfirmed: &newConfirmedQty,
			})
		}
	}

	return createPOResp
}

// SellerUpdateBidding ...
// SellerUpdateBidding updates a bidding record in the database based on the provided input.
// It constructs a selector using the VendorCode, ProductID, SKU, and WarehouseCode from the input,
// and then updates the matching record with the new data.
//
// Parameters:
//   - input: model.Bidding - The bidding data to update in the database.
//
// Returns:
//   - *common.APIResponse - The response from the database update operation.
func SellerUpdateBidding(input model.Bidding) *common.APIResponse {
	selector := model.Bidding{
		VendorCode:    input.VendorCode,
		ProductID:     input.ProductID,
		SKU:           input.SKU,
		WarehouseCode: input.WarehouseCode,
	}

	return model.BiddingDB.UpdateOne(
		selector,
		input,
	)
}

// check if bidding pass validate or not
// validateBidding validates a slice of Bidding objects.
// It checks if the WarehouseCode and DeliveryWarehouseCode of each Bidding object are valid.
// If any of the codes are invalid, it returns an APIResponse with the appropriate error message and false.
// If all codes are valid, it returns nil and true.
//
// Parameters:
// - biddings: A slice of Bidding objects to be validated.
//
// Returns:
// - *common.APIResponse: An APIResponse object containing the status, message, and error code if validation fails, otherwise nil.
// - bool: A boolean indicating whether all Bidding objects are valid.
func validateBidding(biddings []model.Bidding) (*common.APIResponse, bool) {
	for _, bidding := range biddings {
		// Check if WarehouseCode is valid
		if !utils.ValidWarehouseCode(bidding.WarehouseCode) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "WarehouseCode is invalid",
				ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
			}, false
		}
		// Check if DeliveryWarehouseCode is valid
		if !utils.ValidWarehouseCode(bidding.DeliveryWarehouseCode) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "DeliveryWarehouseCode is invalid",
				ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
			}, false
		}
	}

	return nil, true
}
