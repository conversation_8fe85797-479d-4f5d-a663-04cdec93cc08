package warehouse

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var transportingClient *client.Client

const (
	pathUpdateValueShippingOrder = "PUT::/shipping-order/order-value"
)

func InitTransportingClient(database *mongo.Database) {
	const transportingV1 = "/delivery/transporting/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	transportingClient = client.NewClient(transportingV1, headers, 0)
	transportingClient.WithDatabase(database)
	transportingClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     pathUpdateValueShippingOrder,
				Name:     "transporting__PUT__order_value",
				Database: database,
			},
		}...,
	)
}

func UpdateValueShippingOrder(opts ...client.APIOption) *common.APIResponse {
	requestOption := transportingClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := transportingClient.WithRequest(pathUpdateValueShippingOrder, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
