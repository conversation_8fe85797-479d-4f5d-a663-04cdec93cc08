package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

func GetProductLastBillInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query action.BillDetailOfLastPOQuery
	)

	// parse input
	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// do action
	return resp.Respond(action.GetBillDetailOfLastPO(query))
}
