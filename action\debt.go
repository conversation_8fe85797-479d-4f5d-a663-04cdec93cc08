package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetDebt
// GetDebt retrieves a list of debts based on the provided query parameters.
// It supports pagination and sorting by various fields.
//
// Parameters:
//   - query: A pointer to a model.Debt struct containing the query criteria.
//   - offset: The number of records to skip (for pagination).
//   - limit: The maximum number of records to return (for pagination).
//   - getTotal: A boolean indicating whether to include the total count of matching records.
//   - sort: A string specifying the field to sort by. Prefix with '-' for descending order.
//
// Supported sort fields:
//   - "billTotalPrice", "-billTotalPrice"
//   - "poWaitTotalPrice", "-poWaitTotalPrice"
//   - "poNewTotalPrice", "-poNewTotalPrice"
//   - "debtTotalPrice", "-debtTotalPrice"
//   - "balanceTotal", "-balanceTotal"
//   - "fiscalPosition", "-fiscalPosition"
//   - "remainingFiscalPosition", "-remainingFiscalPosition"
//
// Returns:
//   - A pointer to a common.APIResponse containing the list of debts and optionally the total count.
func GetDebt(query *model.Debt, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}
	switch sort {
	case "billTotalPrice":
		sortField = &primitive.M{"bill_total_price": 1}
	case "-billTotalPrice":
		sortField = &primitive.M{"bill_total_price": -1}
	case "poWaitTotalPrice":
		sortField = &primitive.M{"po_wait_total_price": 1}
	case "-poWaitTotalPrice":
		sortField = &primitive.M{"po_wait_total_price": -1}
	case "poNewTotalPrice":
		sortField = &primitive.M{"po_new_total_price": 1}
	case "-poNewTotalPrice":
		sortField = &primitive.M{"po_new_total_price": -1}
	case "debtTotalPrice":
		sortField = &primitive.M{"debt_total_price": 1}
	case "-debtTotalPrice":
		sortField = &primitive.M{"debt_total_price": -1}
	case "balanceTotal":
		sortField = &primitive.M{"balance_total": 1}
	case "-balanceTotal":
		sortField = &primitive.M{"balance_total": -1}
	case "fiscalPosition":
		sortField = &primitive.M{"fiscal_position": 1}
	case "-fiscalPosition":
		sortField = &primitive.M{"fiscal_position": -1}
	case "remainingFiscalPosition":
		sortField = &primitive.M{"remaining_fiscal_fosition": 1}
	case "-remainingFiscalPosition":
		sortField = &primitive.M{"remaining_fiscal_fosition": -1}
	}

	// Query the database
	debtResp := model.DebtDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.DebtDB.Count(query)
		debtResp.Total = countResp.Total
	}

	return debtResp
}

// GetDebt
// GetDebtPlatform retrieves debt information from the platform based on the provided query parameters.
// 
// Parameters:
//   - query: A pointer to a model.Debt struct containing the query criteria.
//   - offset: An int64 representing the number of records to skip.
//   - limit: An int64 representing the maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of matching records.
//   - sort: A string specifying the field by which to sort the results. 
//           Supported values include:
//           "billTotalPrice", "-billTotalPrice", "poWaitTotalPrice", "-poWaitTotalPrice",
//           "poNewTotalPrice", "-poNewTotalPrice", "debtTotalPrice", "-debtTotalPrice",
//           "balanceTotal", "-balanceTotal", "fiscalPosition", "-fiscalPosition",
//           "remainingFiscalPosition", "-remainingFiscalPosition".
// 
// Returns:
//   - A pointer to a common.APIResponse struct containing the query results.
func GetDebtPlatform(query *model.Debt, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}
	switch sort {
	case "billTotalPrice":
		sortField = &primitive.M{"bill_total_price": 1}
	case "-billTotalPrice":
		sortField = &primitive.M{"bill_total_price": -1}
	case "poWaitTotalPrice":
		sortField = &primitive.M{"po_wait_total_price": 1}
	case "-poWaitTotalPrice":
		sortField = &primitive.M{"po_wait_total_price": -1}
	case "poNewTotalPrice":
		sortField = &primitive.M{"po_new_total_price": 1}
	case "-poNewTotalPrice":
		sortField = &primitive.M{"po_new_total_price": -1}
	case "debtTotalPrice":
		sortField = &primitive.M{"debt_total_price": 1}
	case "-debtTotalPrice":
		sortField = &primitive.M{"debt_total_price": -1}
	case "balanceTotal":
		sortField = &primitive.M{"balance_total": 1}
	case "-balanceTotal":
		sortField = &primitive.M{"balance_total": -1}
	case "fiscalPosition":
		sortField = &primitive.M{"fiscal_position": 1}
	case "-fiscalPosition":
		sortField = &primitive.M{"fiscal_position": -1}
	case "remainingFiscalPosition":
		sortField = &primitive.M{"remaining_fiscal_fosition": 1}
	case "-remainingFiscalPosition":
		sortField = &primitive.M{"remaining_fiscal_fosition": -1}
	}

	// Query the database
	debtResp := model.DebtPlatformDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.DebtPlatformDB.Count(query)
		debtResp.Total = countResp.Total
	}

	return debtResp
}
