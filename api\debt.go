package api

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// GetDebt ...
func GetDebt(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		filter   = req.GetParam("filter")
		sort     = req.GetParam("sort")
	)

	// parse input
	var query model.Debt
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// search version
	if query.Version == "" {
		query.Version = time.Now().In(utils.TimeZoneVN).Format("2006-01-02")
	}

	if filter == "OVER_FISCAL_POSITION" {
		query.IsOverFiscalPosition = true

	}
	// else if filter == "HAVE_VB_WTP" {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"vendor_bill_code_wtps": bson.M{
	// 			"$ne": []string{},
	// 		},
	// 	})
	// }

	// do action
	return resp.Respond(action.GetDebt(&query, offset, limit, getTotal, sort))
}

// GetDebt ...
func GetDebtPlatform(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		filter   = req.GetParam("filter")
		sort     = req.GetParam("sort")
	)

	// parse input
	var query model.Debt
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// search version
	if query.Version == "" {
		query.Version = time.Now().In(utils.TimeZoneVN).Format("2006-01-02")
	}

	if filter == "OVER_FISCAL_POSITION" {
		query.IsOverFiscalPosition = true

	}
	// else if filter == "HAVE_VB_WTP" {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"vendor_bill_code_wtps": bson.M{
	// 			"$ne": []string{},
	// 		},
	// 	})
	// }

	// do action
	return resp.Respond(action.GetDebtPlatform(&query, offset, limit, getTotal, sort))
}
