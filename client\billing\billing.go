package billing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

const (
	AccountUrl         = "/seller/core/v1/account"
	EntityEndpoint     = "/iam/core/v1/entity"
	VendorBillEndpoint = "/billing/analyze/v1/receive-vendor-bill"
	ABillEndpoint      = "/billing/analyze/v1/receive-adjustment-bill"
)

type BCSvc struct {
	svc           *client.RestClient
	ssoSvc        *client.RestClient
	sellerSvc     *client.RestClient
	headers       map[string]string
	ssoHeaders    map[string]string
	sellerHeaders map[string]string
}

func NewBillingCenterSvcClient() *BCSvc {
	if conf.Config.BillingHost == "" || conf.Config.BillingToken == "" || conf.Config.BillingSSOHost == "" || conf.Config.BillingSSOToken == "" {
		return nil
	}
	return &BCSvc{
		svc:       client.NewRESTClient(conf.Config.BillingHost, "billing", 3*time.Second, 1, 3*time.Second),
		ssoSvc:    client.NewRESTClient(conf.Config.BillingSSOHost, "sso", 3*time.Second, 1, 3*time.Second),
		sellerSvc: client.NewRESTClient(conf.Config.APIHost, "seller", 3*time.Second, 1, 3*time.Second),
		headers: map[string]string{
			"Authorization": conf.Config.BillingToken,
		},
		ssoHeaders: map[string]string{
			"Authorization": conf.Config.BillingSSOToken,
		},
		sellerHeaders: map[string]string{
			"Authorization": conf.Config.APIKey,
		},
	}
}

func (s BCSvc) GetVendor(vendorCode string) (*model.Seller, error) {
	params := map[string]string{
		"sellerCode":  vendorCode,
		"sellerClass": "VENDOR",
	}
	res, err := s.sellerSvc.MakeHTTPRequestWithKey(client.HTTPMethods.Get, s.sellerHeaders, params, nil, AccountUrl, &[]string{vendorCode})
	if err != nil {
		return nil, err
	}
	var result *seller.SellerResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return nil, err
	}
	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return nil, fmt.Errorf("%v", result.Message)
	}
	return result.Data[0], nil
}

type App struct {
	Name  string `json:"name"`
	AppId int64  `json:"appID"`
	Code  string `json:"code"`
}

type EntityTf struct {
	EntityID int64  `json:"entityID"`
	OrgID    int64  `json:"orgID"`
	Name     string `json:"name"`
	App      App    `json:"app"`
}

type EntityResponse struct {
	common.APIResponse
	Data []*EntityTf `json:"data"`
}

func (s BCSvc) GetEntity(entityID int64) (rs EntityTf, err error) {
	res, err := s.ssoSvc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Get,
		s.ssoHeaders,
		map[string]string{
			"entityID":   strconv.Itoa(int(entityID)),
			"getAppInfo": "true",
		},
		nil,
		EntityEndpoint,
		&[]string{},
	)
	if err != nil {
		return rs, err
	}

	var result *EntityResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return rs, err
	}
	if result.Status != common.APIStatus.Ok || result.Data == nil {
		return rs, fmt.Errorf("%v", result.Message)
	}
	return *result.Data[0], nil
}

func (s BCSvc) PutVendorBill(data interface{}) error {
	_, err := s.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		s.headers,
		map[string]string{},
		data,
		VendorBillEndpoint,
		&[]string{},
	)
	if err != nil {
		return err
	}
	return nil
}

func (s BCSvc) PutABill(data interface{}) error {
	_, err := s.svc.MakeHTTPRequestWithKey(
		client.HTTPMethods.Post,
		s.headers,
		map[string]string{},
		data,
		ABillEndpoint,
		&[]string{},
	)
	if err != nil {
		return err
	}
	return nil
}
