package utils

import (
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

var mapVNICode = map[string]string{
	"a": "[à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ]",
	"ê": "[è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ]",
	"i": "[ì|í|ị|ỉ|ĩ]",
	"o": "[ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ]",
	"u": "[ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ]",
	"y": "[ỳ|ý|ỵ|ỷ|ỹ]",
	"d": "[đ]",

	"A": "[À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ]",
	"Ê": "[È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ]",
	"I": "[Ì|Í|Ị|Ỉ|Ĩ]",
	"O": "[Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ]",
	"U": "[Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ]",
	"Y": "[Ỳ|Ý|Ỵ|Ỷ|Ỹ]",
	"D": "[Đ]",
}

// NormalizeString ...
func NormalizeString(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, strings.ToLower(val))
	normStr = strings.Replace(normStr, "|", "", -1)
	normStr = formmatVNCode(normStr)
	normStr = strings.Replace(normStr, " ", "-", -1)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "/", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)

	r, _ := regexp.Compile(`(\\W)`)
	qCheck := make(map[string]int)
	for i, v := range r.FindAllString(normStr, -1) {
		if v == "-" || qCheck[v] > 0 {
			continue
		}
		qCheck[v] = i + 1
		normStr = strings.ReplaceAll(normStr, v, ``)
	}

	return strings.Replace(normStr, "--", "-", -1)
}

// replace LOT ... [àáảãạâầấẩẫậăằắẳẵặèéẻẽẹêềếểễệđìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵÀÁẢÃẠÂẦẤẨẪẬĂẰẮẲẴẶÈÉẺẼẸÊỀẾỂỄỆĐÌÍỈĨỊÒÓỎÕỌÔỒỐỔỖỘƠỜỚỞỠỢÙÚỦŨỤƯỪỨỬỮỰỲÝỶỸỴ`~!@#$%^&*()?<>+=_\|{},.][;""”]|
func ReplaceLot(val string) string {
	//nolint
	tran := transform.Chain(norm.NFD, transform.RemoveFunc(isMn), norm.NFC)
	normStr, _, _ := transform.String(tran, val)
	normStr = strings.Replace(normStr, "|", "", -1)
	normStr = formmatVNCode(normStr)
	normStr = strings.Replace(normStr, ",", "", -1)
	normStr = strings.Replace(normStr, ";", "", -1)
	normStr = strings.Replace(normStr, "(", "", -1)
	normStr = strings.Replace(normStr, ")", "", -1)
	normStr = strings.Replace(normStr, "&", "", -1)
	normStr = strings.Replace(normStr, "%", "", -1)
	normStr = strings.Replace(normStr, "[", "", -1)
	normStr = strings.Replace(normStr, "]", "", -1)
	normStr = strings.Replace(normStr, "~", "", -1)
	normStr = strings.Replace(normStr, "!", "", -1)
	normStr = strings.Replace(normStr, "@", "", -1)
	normStr = strings.Replace(normStr, "#", "", -1)
	normStr = strings.Replace(normStr, "$", "", -1)
	normStr = strings.Replace(normStr, "^", "", -1)
	normStr = strings.Replace(normStr, "*", "", -1)
	normStr = strings.Replace(normStr, "?", "", -1)
	normStr = strings.Replace(normStr, "<", "", -1)
	normStr = strings.Replace(normStr, ">", "", -1)
	normStr = strings.Replace(normStr, "+", "", -1)
	normStr = strings.Replace(normStr, "=", "", -1)
	normStr = strings.Replace(normStr, "_", "", -1)
	normStr = strings.Replace(normStr, "\\", "", -1)
	normStr = strings.Replace(normStr, "{", "", -1)
	normStr = strings.Replace(normStr, "}", "", -1)
	normStr = strings.Replace(normStr, "\"", "", -1)
	normStr = strings.Replace(normStr, "'", "", -1)
	normStr = strings.Replace(normStr, "`", "", -1)

	return normStr
}

func isMn(r rune) bool {
	return unicode.Is(unicode.Mn, r)
}

func formmatVNCode(str string) string {
	for key, val := range mapVNICode {
		m := regexp.MustCompile(val)
		str = m.ReplaceAllString(str, key)
	}
	return str
}

func ParserQ(q string) string {
	return strings.Replace(NormalizeString(q), " ", "-", -1)
}

func IsContains(arr []string, key string) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}

func GetMinArray(arr []int64) int64 {
	if len(arr) == 0 {
		return 0
	}
	if len(arr) == 1 {
		return arr[1]
	}

	min := arr[1]
	for index := range arr {
		if min > arr[index] {
			min = arr[index]
		}
	}
	return min
}

func RemoveLastIndexElement(element string, arr []string) []string {

	// Find lasted index of element
	index := -1
	for i, val := range arr {
		if val == element {
			index = i
		}
	}
	if index != -1 {
		return append(arr[:index], arr[index+1:]...)
	}
	return arr
}

func JoinInt64Param(int64s ...int64) string {
	var list = make([]string, len(int64s))
	for _, v := range int64s {
		list = append(list, strconv.Itoa(int(v)))
	}
	return strings.Join(list, ",")
}

func IsContainsString(arr []string, key string) bool {
	if len(arr) == 0 {
		return false
	}

	for _, s := range arr {
		if key == s {
			return true
		}
	}

	return false
}

func UniqueStringSlice(str []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range str {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}
