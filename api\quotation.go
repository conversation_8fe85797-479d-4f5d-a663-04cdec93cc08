package api

import (
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// SearchQuotation ...
func SearchQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		// offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		// limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		// qStr       = req.GetParam("q")
		isDistinct = req.GetParam("isDistinct") == "true"
	)
	// var query model.Quotation
	// if qStr != "" {
	// 	err := json.Unmarshal([]byte(qStr), &query)
	// 	if err != nil {
	// 		return resp.Respond(&common.APIResponse{
	// 			Status:  common.APIStatus.Invalid,
	// 			Message: "Can not parse input data. " + err.Error(),
	// 		})

	// 	}
	// }

	var query action.SearchQuery
	err := req.GetContent(&query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	// return resp.Respond(action.SearchQuotation(query, offset, limit, isDistinct))
	return resp.Respond(action.SearchQuotation(query, 0, 0, isDistinct))
}

// GetVendorQuotation ...
func GetVendorQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	// var (
	// 	offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
	// 	limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
	// 	getTotal      = req.GetParam("getTotal") == "true"
	// 	qStr          = req.GetParam("q")
	// 	isOnlyVendor  = req.GetParam("isOnlyVendor") == "true"
	// 	isLastUpdated = req.GetParam("isLastUpdated") == "true"
	// 	isMarket      = req.GetParam("isMarket") == "true"
	// )

	// // BLANK := ""
	// var query model.Quotation
	// if qStr != "" {
	// 	err := json.Unmarshal([]byte(qStr), &query)
	// 	if err != nil {
	// 		return resp.Respond(&common.APIResponse{
	// 			Status:    common.APIStatus.Invalid,
	// 			Message:   "Can not parse input data. " + err.Error(),
	// 			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
	// 		})

	// 	}
	// }
	var payload model.GetVendorQuotationPayload
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	query := payload.Query

	// add complex query
	query.ComplexQuery = []*bson.M{
		{"quantity_expect": &bson.M{
			"$gt": 0,
		}},
	}

	// add complex query
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// add complex query
	if len(query.PrWhHashtagIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$in": query.PrWhHashtagIn,
			},
		})
	}

	// add complex query
	if len(query.PrWhHashtagNotIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$nin": query.PrWhHashtagNotIn,
			},
		})
	}

	// add complex query
	if len(query.WarehouseCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	// add complex query
	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}
	sort := "-product_id"
	if payload.IsLastUpdated {
		sort = "-lastUpdatedTime"
	}

	if payload.IsMarket {
		return resp.Respond(action.GetVendorQuotation(query, payload.Offset, payload.Limit, payload.GetTotal, sort))
	} else if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendor := vendorResp.Data.([]*model.Seller)[0]

		settingResp := model.SettingDB.Query(model.Setting{}, 0, 1, nil)
		if settingResp.Status != common.APIStatus.Ok {
			return resp.Respond(settingResp)
		}
		setting := settingResp.Data.([]*model.Setting)[0]

		if setting.MapTradingNonTradingSellers == nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "Quotation not found",
			})
		}

		// Check if seller is trading or non-trading
		for sellerType, sellerCodes := range setting.MapTradingNonTradingSellers {
			if sellerType == string(vendor.SellerType) {
				// Seller codes in map setting not found
				if len(sellerCodes) <= 0 {
					return resp.Respond(&common.APIResponse{
						Status:  common.APIStatus.NotFound,
						Message: "Quotation not found",
					})
				}

				query.SellerCode = ""
				query.SellerCodeIn = []string{}
				query.ComplexQuery = append(query.ComplexQuery, &bson.M{
					"seller_code": bson.M{
						"$in": sellerCodes,
					},
				})
				break
			}
		}

		// If have marked as block release to fav session -> not show in both fav and publish bidding
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"block_release_to_fav_session": &bson.M{
				"$ne": true,
			},
		})

		if payload.IsOnlyVendor {
			// do action
			// query.VendorCodes = &[]string{vendorResp.Data[0].Code}
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"vendor_codes": vendor.Code,
			})

		} else {
			// query.VendorCodes = &[]string{}
			query.QuotationStatus = enum.QuotationStatus.PUBLISH

			query.ComplexQuery = append(query.ComplexQuery, &bson.M{
				"block_release_to_bidding_session": &bson.M{
					"$ne": true,
				},
			})
		}

		// do action
		return resp.Respond(action.GetVendorQuotation(query, payload.Offset, payload.Limit, payload.GetTotal, sort))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetSellerQuotation ... normal is "$gt": 0
func GetSellerQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
	)

	// parse input data
	var payload model.GetSellerQuotationPayload
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	query := payload.Query

	query.SellerCode = strings.ToUpper(sellerCode)

	// add more ComplexQuery
	if len(query.SellerCodeIn) > 0 {
		query.SellerCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"seller_code": bson.M{
				"$in": query.SellerCodeIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.WarehouseCodes) > 0 {
		query.WarehouseCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	// add more ComplexQuery
	if !payload.IsCountAll {
		query.ComplexQuery = []*bson.M{
			{"quantity_expect": &bson.M{
				"$gt": 0,
			}},
		}
	}

	// Add fillter
	if payload.Filter != "" {
		switch payload.Filter {
		case "SUPPLIER_VIEWABLE":
			query.ComplexQuery = append(query.ComplexQuery,
				&bson.M{
					"$or": []bson.M{
						{
							"quotation_status": enum.QuotationStatus.PUBLISH,
						},
						{
							"vendor_codes": bson.M{"$ne": []string{}},
						},
					},
				},
			)
		case "PUBLISH_BIDDING":
			query.QuotationStatus = enum.QuotationStatus.PUBLISH
		case "STOP_PUBLISH_BIDDING":
			query.QuotationStatus = enum.QuotationStatus.STOP_PUBLISH
		case "WAITING_FAVORITE":
			query.ComplexQuery = append(query.ComplexQuery,
				&bson.M{
					"vendor_codes": bson.M{"$ne": []string{}},
				},
			)
		}
	}

	// add more ComplexQuery
	if len(query.PrWhHashtagIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$in": query.PrWhHashtagIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.PrWhHashtagNotIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$nin": query.PrWhHashtagNotIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}

	// fulltext search
	if payload.Search != "" {
		payload.Search = strings.Replace(utils.NormalizeString(payload.Search), " ", "-", -1)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$text": bson.M{
				"$search": "\"" + payload.Search + "\"",
			},
		})

	}

	// do action
	return resp.Respond(action.GetVendorQuotation(query, payload.Offset, payload.Limit, payload.GetTotal, payload.Sort))
}

// VendorCreatePOFromQuotation
func VendorCreatePOFromQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input action.CreatePOFromQuotationInput
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		input.PO.VendorCode = vendors[0].Code
		return resp.Respond(action.CreatePOFromQuotation(&input, acc))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// VendorCreatePOFromQuotation
func VendorCreatePOFromV2Quotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input action.CreatePOFromQuotationInput
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		input.PO.VendorCode = vendors[0].Code
		return resp.Respond(action.CreatePOFromQuotationV2(&input, acc))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// CreateQuotations
func SellerCreateQuotations(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
	)
	sellerCode = strings.ToUpper(sellerCode)

	// parse input data
	var inputs action.CreateQuotationsInput
	err := req.GetContent(&inputs)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	quotationResp := action.SellerCreateQuotations(inputs, sellerCode)
	return resp.Respond(quotationResp)
}

// SellerUpdateQuotation
func SellerUpdateQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
	)
	sellerCode = strings.ToUpper(sellerCode)

	// parse input data
	var input model.Quotation
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	input.SellerCode = sellerCode
	if acc := getActionSource(req); acc != nil {
		input.ActionByID = acc.AccountID
		input.ActionByName = acc.Fullname
	}

	return resp.Respond(action.SellerUpdateQuotation(input))

}

// DeleteAllQuotations
func DeleteAllQuotations(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode       = req.GetVar("sellerCode")
		warehouseCode    = req.GetParam("warehouseCode")
		isForceDeleteAll = req.GetParam("isForceDeleteAll") == "true"
		productCodes     = req.GetParam("productCodes")
		skus             = req.GetParam("skus")
	)
	sellerCode = strings.ToUpper(sellerCode)
	warehouseCode = strings.ToUpper(warehouseCode)

	// parse input data
	codesArr := []string{}
	if len(productCodes) > 0 {
		spArr := strings.Split(productCodes, ",")
		for _, code := range spArr {
			code = strings.TrimSpace(code)
			if code != "" {
				codesArr = append(codesArr, code)
			}
		}
	}

	// parse input data
	skusArr := []string{}
	if len(skus) > 0 {
		spArr := strings.Split(skus, ",")
		for _, code := range spArr {
			code = strings.TrimSpace(code)
			if code != "" {
				skusArr = append(skusArr, code)
			}
		}
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		if acc.Type == "EMPLOYEE" {
			fmt.Println(acc.Username + " delete all")
			go sdk.Execute(func() {
				fmt.Println(acc.Username + " DELETE BIDDING RATE")
				action.DeleteAllBiddingRates(isForceDeleteAll, sellerCode, codesArr, skusArr, warehouseCode)
			})
			return resp.Respond(action.DeleteAllQuotations(isForceDeleteAll, sellerCode, codesArr, skusArr, warehouseCode))
		}
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}
