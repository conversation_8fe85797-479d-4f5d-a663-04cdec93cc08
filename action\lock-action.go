package action

import (
	"fmt"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

// CreateLockActions creates multiple lock action trackers based on the provided action type, main key, and child keys.
// It constructs a list of LockAction objects with keys formatted as "actionType.mainkey.chilKey" and stores them in the database.
//
// Parameters:
//   - actionType: A string representing the type of action.
//   - mainkey: A string representing the main key.
//   - chilKeys: A slice of strings representing the child keys.
//
// Returns:
//   - *common.APIResponse: The response from the database after attempting to create the lock actions.
func CreateLockActions(actionType, mainkey string, chilKeys []string) *common.APIResponse {

	// Create many lock action tracker
	lockActions := []model.LockAction{}
	for _, chilKey := range chilKeys {
		lockActions = append(lockActions, model.LockAction{
			Key: fmt.Sprintf("%v.%v.%v", actionType, mainkey, chilKey),
		})
	}
	return model.LockActionDB.CreateMany(lockActions)
}

// CreateLockActionsWithExpiredTime creates multiple lock action trackers with a specified expiration time.
// It takes the following parameters:
// - actionType: a string representing the type of action.
// - mainkey: a string representing the main key.
// - chilKeys: a slice of strings representing the child keys.
// - expiredTime: a pointer to a time.Time representing the expiration time.
//
// It returns a pointer to a common.APIResponse indicating the result of the operation.
func CreateLockActionsWithExpiredTime(actionType, mainkey string, chilKeys []string, expiredTime *time.Time) *common.APIResponse {
	// Create many lock action tracker
	lockActions := []model.LockAction{}
	for _, chilKey := range chilKeys {
		lockActions = append(lockActions, model.LockAction{
			Key:         fmt.Sprintf("%v.%v.%v", actionType, mainkey, chilKey),
			CreatedTime: expiredTime,
		})
	}
	return model.LockActionDB.CreateMany(lockActions)
}

// DeleteLockActions deletes lock actions for a given action type, main key, and a list of child keys.
// It constructs a composite key using the action type, main key, and each child key, and then deletes
// the corresponding lock action from the LockActionDB.
//
// Parameters:
//   - actionType: A string representing the type of action.
//   - mainkey: A string representing the main key.
//   - chilKeys: A slice of strings representing the child keys.
func DeleteLockActions(actionType, mainkey string, chilKeys []string) {
	// Create many lock action tracker
	for _, chilKey := range chilKeys {
		Key := fmt.Sprintf("%v.%v.%v", actionType, mainkey, chilKey)
		model.LockActionDB.Delete(model.LockAction{Key: Key})
	}
}
