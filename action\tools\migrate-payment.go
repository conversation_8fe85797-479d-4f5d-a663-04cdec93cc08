package tools

import (
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigratePaymentV2(_ sdk.APIRequest, resp sdk.APIResponder) error {

	go migratePaymentV2()

	// do action
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request migrate payment success",
	})
}

func migratePaymentV2() {
	const (
		DEFAULT_PAYMENT_METHOD = "BANK"
	)

	log.Println("Start : migratePaymentV2")
	defer log.Println("End : migratePaymentV2")

	var (
		_id_offset      = primitive.NilObjectID
		listPaymentMain []*model.PaymentLegacy
	)
	for {
		getPayemntMain := model.PaymentLegacyDB.Query(
			&model.PaymentLegacy{
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getPayemntMain.Status != common.APIStatus.Ok {
			break
		}

		listMain := getPayemntMain.Data.([]*model.PaymentLegacy)
		for _, main := range listMain {

			// tăng offset
			_id_offset = *main.ID
			listPaymentMain = append(listPaymentMain, main)
		}
	}

	log.Println("Total payment main: ", len(listPaymentMain))

	var (
		errPrePaidPO     = []string{}
		errNotAvalidType = []string{}
		errUpdate        = []string{}
		progress         = 1000
		skipCount        = 0
	)

LOOP_PAYMENT_MAIN:
	for i, paymentMain := range listPaymentMain {
		if i%progress == 0 {
			log.Println("Progress: ", i)
		}
		if utils.IsContains([]string{string(enum.PayType.PAYMENT_VOUCHER), string(enum.PayType.RECEIPT_VOUCHER)}, string(paymentMain.Type)) {
			skipCount++
			continue LOOP_PAYMENT_MAIN
		}

		switch paymentMain.PaymentMethod {
		case "BANK": // vb, ab

			paymentMain.Type = enum.PayType.PAYMENT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.BILL

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

		case "CASH": // ab, vb

			paymentMain.Type = enum.PayType.PAYMENT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.BILL

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

		case "OTHER": // ab, vb

			paymentMain.Type = enum.PayType.PAYMENT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.BILL

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

		case "ADVANCE":
			if len(paymentMain.PrePaidPOCodes) == 0 {
				errPrePaidPO = append(errPrePaidPO, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

			// chi
			paymentMain.Type = enum.PayType.PAYMENT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.ADVANCE_MONEY

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

		case "CLEARING_CREDIT": // hoan tien

			paymentMain.Type = enum.PayType.RECEIPT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.DEBT

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}

		case "RETURN_MONEY": // hoan tien

			paymentMain.Type = enum.PayType.RECEIPT_VOUCHER
			paymentMain.Purpose = enum.PaymentPurpose.DEBT

			filter := model.PaymentLegacy{ID: paymentMain.ID}
			if resp := model.PaymentLegacyDB.UpdateOne(filter, paymentMain); resp.Status != common.APIStatus.Ok {
				errUpdate = append(errUpdate, paymentMain.PaymentCode)
				continue LOOP_PAYMENT_MAIN
			}
		default:
			errNotAvalidType = append(errNotAvalidType, paymentMain.PaymentCode)
		}
	}

	log.Println("errPrePaidPO", errPrePaidPO)
	log.Println("errNotAvalidType", errNotAvalidType)
	log.Println("errUpdate", errUpdate)
	log.Println("skipCount", skipCount)
}

func MigratePaymentLastUpdatedTime(_ sdk.APIRequest, resp sdk.APIResponder) error {

	go migratePaymentLastUpdatedTime()

	// do action
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request MigratePaymentLastUpdatedTime success",
	})
}

func migratePaymentLastUpdatedTime() {
	var (
		_id_offset         = primitive.NilObjectID
		totalMatch         = 0
		totalUpdateSuccess = 0

		errList []string
	)

	for {
		getPayemntMain := model.PaymentLegacyDB.Query(
			&model.PaymentLegacy{
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}},
					{"last_updated_time": bson.M{"$eq": nil}},
				}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if getPayemntMain.Status != common.APIStatus.Ok {
			break
		}

		listMain := getPayemntMain.Data.([]*model.PaymentLegacy)
		// log
		totalMatch += len(listMain)

		for _, main := range listMain {

			// tăng offset
			_id_offset = *main.ID

			filter := model.PaymentLegacy{ID: main.ID}
			updater := model.PaymentLegacy{LastUpdatedTime: main.CreatedTime}

			updateResp := model.PaymentLegacyDB.UpdateOne(filter, updater)
			if updateResp.Status != common.APIStatus.Ok {
				errList = append(errList, fmt.Sprintf("|%s: %s| ", main.PaymentCode, updateResp.Message))
				continue
			} else {
				totalUpdateSuccess++
			}

		}
	}

	log.Println("errList", errList)
	log.Println("totalMatch", totalMatch)
	log.Println("totalUpdateSuccess", totalUpdateSuccess)

}

func MigratePaymentPOPaid(_ sdk.APIRequest, resp sdk.APIResponder) error {

	go migratePaymentPOPaid()

	// do action
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Request MigratePaymentPOPaid success",
	})
}

func migratePaymentPOPaid() {
	log.Println("START:migratePaymentPOPaid")
	defer log.Println("END:migratePaymentPOPaid")
	var (
		_id_offset = primitive.NilObjectID
		progess    = 0

		errList  []string
		errList1 []string
	)

	for {
		paymentMainResp := model.PaymentLegacyDB.Query(
			model.PaymentLegacy{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id_offset}},
				},
			},
			0,
			1000,
			&bson.M{"_id": 1},
		)

		if paymentMainResp.Status != common.APIStatus.Ok {
			break
		}

		paymentMainList := paymentMainResp.Data.([]*model.PaymentLegacy)
		for _, main := range paymentMainList {
			_id_offset = *main.ID
			progess++

			if progess%300 == 0 {
				log.Println("progess", progess)
			}
			// get payment item
			if len(main.PaymentCode) == 0 {
				continue
			}
			paidPOCodes := []string{}
			filter := model.PaymentLegacy{ID: main.ID}
			updater := model.PaymentLegacy{POCodes: paidPOCodes} // po thanh toán

			paymentItemResp := model.PaymentItemLegacyDB.Query(
				model.PaymentItemLegacy{PaymentCode: main.PaymentCode},
				0, 1000, nil)
			switch paymentItemResp.Status {
			case common.APIStatus.Ok:
				paymentItemList := paymentItemResp.Data.([]*model.PaymentItemLegacy)
				for _, item := range paymentItemList {
					if item.POCode == "" {
						continue
					}

					(updater.POCodes) = append((updater.POCodes), item.POCode)
				}
			case common.APIStatus.NotFound:
				// để trống
			default:
				errList = append(errList, fmt.Sprintf("|%s: %s| ", main.PaymentCode, paymentItemResp.Message))
			}

			updateResp := model.PaymentLegacyDB.UpdateOne(filter, updater)
			if updateResp.Status != common.APIStatus.Ok {
				errList1 = append(errList1, fmt.Sprintf("|%s: %s| ", main.PaymentCode, updateResp.Message))
			}
		}
	}
	log.Println("total matched :", progess)
	log.Println("errList", errList)
	log.Println("errList1", errList1)

}
