package model

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type VendorPromotion struct {
	// basic
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedByID     int64               `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`

	// info
	VendorPromotionCode string    `json:"vendorPromotionCode,omitempty" bson:"vendor_promotion_code,omitempty"`
	VendorPromotionID   int64     `json:"vendorPromotionID,omitempty" bson:"vendor_promotion_id,omitempty"`
	PromotionName       string    `json:"promotionName,omitempty" bson:"promotion_name,omitempty"`
	Groups              *[]string `json:"groups,omitempty" bson:"groups,omitempty"`

	VendorCode     string   `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	WarehouseCodes []string `json:"warehouseCodes,omitempty" bson:"warehouse_codes,omitempty"` // khu vực áp dụng

	// type & status
	PromotionType enum.VendorPromotionTypeValue   `json:"promotionType,omitempty" bson:"promotion_type,omitempty"` // loại chiết khấu
	Status        enum.VendorPromotionStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"` // ngày bắt đầu áp dụng
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`     // ngày kết thúc áp dụng

	// other
	Note    string `json:"note,omitempty" bson:"note,omitempty"` // ghi chú
	Hashtag string `json:"hashtag,omitempty" bson:"hashtag,omitempty"`

	// client upsert
	VendorGiftItems     []*VendorGiftItem     `json:"vendorGiftItems,omitempty" bson:"vendor_gift_items,omitempty"`         // danh sách Mua X tặng Y
	VendorDiscountItems []*VendorDiscountItem `json:"vendorDiscountItems,omitempty" bson:"vendor_discount_items,omitempty"` // danh sách Chiêt khấu theo giá

	// client query
	Search          string                 `json:"search,omitempty" bson:"-"`
	TimeFrom        *time.Time             `json:"timeFrom,omitempty" bson:"-"`
	TimeTo          *time.Time             `json:"timeTo,omitempty" bson:"-"`
	TimeIn          *time.Time             `json:"timeIn,omitempty" bson:"-"`
	WarehouseCodeIn []string               `json:"warehouseCodeIn,omitempty" bson:"-"`
	ActiveStatus    enum.ActiveStatusValue `json:"activeStatus,omitempty" bson:"-"`

	GiftProductCodeQuery   string `json:"giftProductCodeQuery,omitempty" bson:"-"`
	ConditionItemCodeQuery string `json:"conditionItemCodeQuery,omitempty" bson:"-"`

	// internal query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// ========================================================

type VendorPromotionItemQuery struct {
	VendorPromotionCodeIn []string                      `json:"vendorPromotionCodeIn,omitempty" bson:"-"`
	PromotionType         enum.VendorPromotionTypeValue `json:"promotionType,omitempty" bson:"-"` // loại chiết khấu

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// ====================== Chương trình promotion mua X tặng Y ============================
type VendorGiftItem struct {
	// basic
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// info
	VendorPromotionItemID int64 `json:"vendorPromotionItemID,omitempty" bson:"vendor_promotion_item_id,omitempty"`
	// reference main
	VendorPromotionCode string `json:"vendorPromotionCode,omitempty" bson:"vendor_promotion_code,omitempty"`

	VendorCode     string   `json:"-" bson:"vendor_code,omitempty"`
	WarehouseCodes []string `json:"-" bson:"warehouse_codes,omitempty"` // khu vực áp dụng

	// condition
	Scope              enum.ScopePromotionValue          `json:"scope,omitempty" bson:"scope,omitempty"`                              // ALL || PRODUCT
	GiftConditions     []*GiftCondition                  `json:"giftConditions,omitempty" bson:"gift_conditions,omitempty"`           // product điều kiện
	GiftConditionType  enum.VendorGiftConditionTypeValue `json:"giftConditionType,omitempty" bson:"gift_condition_type,omitempty"`    // đơn vị khuyến mãi chiết khấu dựa vào
	TotalPerStepVendor int64                             `json:"totalPerStepVendor,omitempty" bson:"total_per_step_vendor,omitempty"` // quy cách target cho chiết khấu

	VATIncluded              bool  `json:"vatIncluded,omitempty" bson:"vat_included,omitempty"` // có chiết khấu có VAT hay không
	IsCalculatePurchasePrice *bool `json:"isCalculatePurchasePrice,omitempty" bson:"is_calculate_purchase_price,omitempty"`
	// reward
	GiftRewards []*GiftReward `json:"giftRewards,omitempty" bson:"gift_rewards,omitempty"` // product tặng

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// Product condition gift
type GiftCondition struct {
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`

	// PerStepVendor_ int64 `json:"-,omitempty" bson:"per_step_vendor,omitempty"` // quy cách target cho chiết khấu
}

// Product gift
type GiftReward struct {
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`

	Quantity int64 `json:"quantity,omitempty" bson:"quantity,omitempty"` // số lượng tặng
}

// ====================== Chương trình promotion chiết khấu giảm giá Product ============================
type VendorDiscountItem struct {
	// basic
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// info
	VendorPromotionItemID int64 `json:"vendorPromotionItemID,omitempty" bson:"vendor_promotion_item_id,omitempty"`
	// reference main
	VendorPromotionCode string `json:"vendorPromotionCode,omitempty" bson:"vendor_promotion_code,omitempty"`

	VendorCode     string   `json:"-" bson:"vendor_code,omitempty"`
	WarehouseCodes []string `json:"-" bson:"warehouse_codes,omitempty"` // khu vực áp dụng

	DiscountRangeType  enum.VendorDiscountRangeTypeValue  `json:"discountRangeType,omitempty" bson:"discount_range_type,omitempty"`   // đơn vị khuyến mãi chiết khấu dựa vào
	DiscountRewardType enum.VendorDiscountRewardTypeValue `json:"discountRewardType,omitempty" bson:"discount_reward_type,omitempty"` // đơn vị thưởng chiết khấu cấp độ

	// condition
	Scope                enum.ScopePromotionValue `json:"scope,omitempty" bson:"scope,omitempty"`                                 // ALL || PRODUCT
	VATIncluded          bool                     `json:"vatIncluded,omitempty" bson:"vat_included,omitempty"`                    // có chiết khấu có VAT hay không
	DiscountRangeRewards []*DiscountRangeReward   `json:"discountRangeRewards,omitempty" bson:"discount_range_rewards,omitempty"` // các range cho unit chiết khấu giá

	// reward
	DiscountRewardProducts []*DiscountProduct `json:"discountRewardProducts,omitempty" bson:"discount_reward_product,omitempty"` // product được chiết khấu

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// Product condition discount price
type DiscountProduct struct {
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`

	MinAmount   float64 `json:"minAmount,omitempty" bson:"min_amount,omitempty"`     // minimum value
	MinQuantity int64   `json:"minQuantity,omitempty" bson:"min_quantity,omitempty"` // minimum quantity

	DealTicketCodes []string `json:"dealTicketCodes,omitempty" bson:"deal_ticket_codes,omitempty"`
}

type DiscountRangeReward struct {
	From float64 `json:"from,omitempty" bson:"from"` //lượng mua tối thiểu                                                         // unit có thể lả (AMOUNT, QUANTITY)
	To   float64 `json:"to,omitempty" bson:"to"`     //lượng mua tối đa của level này -> nếu SL tối đa == 0 thì thì max đến vô cực // unit có thể lả (AMOUNT, QUANTITY)

	Discount                 float64 `json:"discount,omitempty" bson:"discount"` // chiết khấu unit của level này có thể  là (PRICE, PERCENT)
	IsCalculatePurchasePrice *bool   `json:"isCalculatePurchasePrice,omitempty" bson:"is_calculate_purchase_price,omitempty"`
}

type PromotionItem struct {
	VendorGiftItem     *VendorGiftItem     `json:"vendorGiftItem,omitempty" bson:"-"`
	VendorDiscountItem *VendorDiscountItem `json:"vendorDiscountItem,omitempty" bson:"-"`
}
