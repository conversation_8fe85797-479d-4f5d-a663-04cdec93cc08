package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetSellerPurchaseOrderItemList retrieves a list of purchase order items based on the provided query parameters.
// It supports pagination, sorting, and filtering by various fields.
//
// Parameters:
//   - query: A model.PurchaseOrderItem struct containing the query parameters for filtering the purchase order items.
//   - offset: The number of items to skip before starting to collect the result set.
//   - limit: The maximum number of items to return.
//   - getTotal: A boolean indicating whether to include the total count of items matching the query.
//   - sort: A string specifying the field and order to sort the results by.
//
// Returns:
//   - *common.APIResponse: An APIResponse struct containing the status, data, and total count (if requested) of the query.
//
// The function performs the following steps:
//  1. Sets the default sort field to "_id" in ascending order.
//  2. Adjusts the sort field based on the provided sort parameter.
//  3. Adds additional query conditions based on the fields in the query parameter.
//  4. Queries the database for purchase order items matching the query conditions.
//  5. For each purchase order item, retrieves vendor information and calculates the price after rebate.
//  6. If getTotal is true, counts the total number of items matching the query conditions.
//  7. Returns the API response containing the purchase order items and total count (if requested).
func GetSellerPurchaseOrderItemList(query model.PurchaseOrderItem, offset, limit int64, getTotal bool, sort string) *common.APIResponse {

	sortField := &primitive.M{"_id": 1}
	switch sort {
	case "-_id":
		sortField = &primitive.M{"_id": -1}
	case "createdTime":
		sortField = &primitive.M{"created_time": 1}
	case "-createdTime":
		sortField = &primitive.M{"created_time": -1}
	case "id":
		sortField = &primitive.M{"_id": 1}
	case "-id":
		sortField = &primitive.M{"_id": -1}
	}

	// Add additional query conditions with WrongTicketCodeIn
	if len(query.WrongTicketCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"wrong_ticket_codes": bson.M{
				"$in": query.WrongTicketCodeIn,
			},
		})
	}

	// Add additional query conditions with CreatedFrom and CreatedTo
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}

	// Add additional query conditions with CreatedTo
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTo,
			},
		})
	}

	// Add additional query conditions with IDFrom and IDTo
	if query.IDFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$gt": query.IDFrom,
			},
		})
	}

	// Add additional query conditions with IDTo
	if query.IDTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$lt": query.IDTo,
			},
		})
	}

	// Add additional query conditions with POCodeIn
	poResp := model.PurchaseOrderItemDB.Query(query, offset, limit, sortField)
	if poResp.Status != common.APIStatus.Ok {
		return poResp
	}

	// Get vendor info and calculate price after rebate
	poItems := poResp.Data.([]*model.PurchaseOrderItem)
	for _, poItem := range poItems {
		// Get vendor info
		vendorInfoResp := GetVendorInfo(poItem.VendorCode)
		if vendorInfoResp.Status == common.APIStatus.Ok {
			vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]
			vendorCode := vendorInfo.Code
			if len(vendorInfo.LineManager) > 0 {
				vendorCode = vendorInfo.LineManager
			}

			// Calculate price after rebate
			version := utils.ConvertTimeToStringYYYYMMDD(poItem.CreatedTime, "")
			otps := client.APIOption{
				Params: map[string]string{
					"version":       version,
					"warehouseCode": poItem.WarehouseCode,
					"vendorCode":    vendorCode,
					"productID":     fmt.Sprint(poItem.ProductID),
				},
			}

			// Get price after rebate
			getPriceAfterRebateResp := seller.GetPriceAfterRebateLastest(otps)
			if getPriceAfterRebateResp.Status == common.APIStatus.Ok {
				poItem.PriceAfterRebate = getPriceAfterRebateResp.Data.([]*model.PriceAfterRebate)[0].PriceAfterRebateWithAdhocPromotion
			}
		}
	}

	// Count total number of items matching the query conditions
	if getTotal {
		countResp := model.PurchaseOrderItemDB.Count(query)
		poResp.Total = countResp.Total
	}

	// Return API response
	return poResp
}

// HandleRefillBillQuantity handles the refill of bill quantities for a given purchase order code (poCode).
// It first validates the poCode, then forces an update to set the bill quantity to zero for all items
// associated with the given poCode. After that, it queries the vendor bills associated with the poCode
// and refills the bill quantities based on the status of the vendor bills and their items.
//
// Parameters:
//   - poCode: A string representing the purchase order code.
//
// Returns:
//   - *common.APIResponse: An API response indicating the status of the operation.
func HandleRefillBillQuantity(poCode string) *common.APIResponse {
	var ZERO int64 = 0
	// Validate poCode
	if poCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCode",
		}
	}

	// Force update bill quantity
	model.PurchaseOrderItemDB.UpdateMany(
		model.PurchaseOrderItem{
			POCode: poCode,
		},
		model.PurchaseOrderItem{
			BillQuantity: &ZERO,
		},
	)

	// Refill bill quantity
	vendorBillResp := model.VendorBillDB.Query(model.VendorBill{POCode: poCode}, 0, 1000, nil)
	if vendorBillResp.Status == common.APIStatus.Ok {

		vendorBills := vendorBillResp.Data.([]*model.VendorBill)

		// Execute update bill quantity PO
		for _, vendorBill := range vendorBills {

			// Only update bill quantity for approved, wait to paid, and paid vendor bills
			if vendorBill.Status != enum.VendorBillStatus.APPROVED && vendorBill.Status != enum.VendorBillStatus.WAIT_TO_PAID && vendorBill.Status != enum.VendorBillStatus.PAID {
				continue
			}

			// Get all vendor bill items
			vendorBillItemResp := model.VendorBillItemDB.Query(model.VendorBillItem{
				VendorBillCode: vendorBill.VendorBillCode,
			}, 0, 1000, nil)
			if vendorBillItemResp.Status == common.APIStatus.Ok {
				// Get all vendor bill items
				vendorBillItems := vendorBillItemResp.Data.([]*model.VendorBillItem)
				for _, item := range vendorBillItems {
					vendorBill.Items = append(vendorBill.Items, *item)
				}

				// Execute update bill quantity PO
				excuteUpdateBillQuantityPO(*vendorBill)
			}
		}
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

func HandleUpdatePOItem(poItem model.PurchaseOrderItem, acc *model.Account) *common.APIResponse {

	// validate
	if poItem.POItemID == 0 || poItem.POCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "POItemID, POCode is required",
		}
	}

	// get PO
	poCheckResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poItem.POCode})
	if poCheckResp.Status != common.APIStatus.Ok {
		return poCheckResp
	}
	poCheck := poCheckResp.Data.([]*model.PurchaseOrder)[0]

	if poCheck.Status != enum.PurchaseOrderStatus.PARTIALLY_RECEIVED {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PO status must be PARTIALLY_RECEIVED",
		}
	}

	// update
	resp := model.PurchaseOrderItemDB.UpdateOne(
		model.PurchaseOrderItem{POItemID: poItem.POItemID},
		model.PurchaseOrderItem{
			IsNearExpiration: poItem.IsNearExpiration,
		},
	)

	// #4 Check status, update receipt info & sync data warehouse
	if poCheck.Status == enum.PurchaseOrderStatus.CONFIRMED || poCheck.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
		// poCheck.Status == enum.PurchaseOrderStatus.PROCESSING ||
		poCheck.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || poCheck.Status == enum.PurchaseOrderStatus.RECEIVED ||
		// poCheck.Status == enum.PurchaseOrderStatus.SENT_PO ||
		poCheck.Status == enum.PurchaseOrderStatus.COMPLETED ||
		poCheck.Status == enum.PurchaseOrderStatus.AWAITING_BILLED || poCheck.Status == enum.PurchaseOrderStatus.BILLED {

		// Tạo receipt qua wh
		createReceptResp := CreateReceptToWarehouse(poCheck)
		if createReceptResp.Status != common.APIStatus.Ok {
			return createReceptResp
		}
	}

	return resp
}
