package enum

type (
	VendorPromotionStatusValue string

	vendorPromotionStatus struct {
		DRAFT    VendorPromotionStatusValue
		APPROVED VendorPromotionStatusValue
		CANCELED VendorPromotionStatusValue
	}
)

var VendorPromotionStatus = &vendorPromotionStatus{
	DRAFT:    "DRAFT",
	APPROVED: "APPROVED",
	CANCELED: "CANCELED",
}

// ============================================================

type (
	VendorPromotionTypeValue string

	vendorPromotionType struct {
		DISCOUNT      VendorPromotionTypeValue
		GIFT          VendorPromotionTypeValue
		GIFT_BY_LEVEL VendorPromotionTypeValue
	}
)

var VendorPromotionType = &vendorPromotionType{
	GIFT:          "GIFT",     // tặng sản phẩm
	DISCOUNT:      "DISCOUNT", // giảm giá
	GIFT_BY_LEVEL: "GIFT_BY_LEVEL",
}

// ============================================================

type (
	VendorDiscountRangeTypeValue string

	vendorDiscountRangeType struct {
		AMOUNT   VendorDiscountRangeTypeValue
		QUANTITY VendorDiscountRangeTypeValue
	}
)

var VendorDiscountRangeType = &vendorDiscountRangeType{
	AMOUNT:   "AMOUNT",   // giá trị
	QUANTITY: "QUANTITY", // số lượng
}

// ============================================================

type (
	VendorDiscountRewardTypeValue string
	vendorDiscountRewardType      struct {
		ABSOLUTE           VendorDiscountRewardTypeValue
		PERCENTAGE_ROOT    VendorDiscountRewardTypeValue
		PERCENTAGE_CURRENT VendorDiscountRewardTypeValue
	}
)

var VendorDiscountRewardType = &vendorDiscountRewardType{
	ABSOLUTE:           "ABSOLUTE",           // giảm giá tuyệt đối
	PERCENTAGE_ROOT:    "PERCENTAGE_ROOT",    // giảm % theo giá gốc
	PERCENTAGE_CURRENT: "PERCENTAGE_CURRENT", // giảm % bậc giá hiện tại
}

// ===============================================
type (
	ActiveStatusValue string
	activeStatus      struct {
		UPCOMING  ActiveStatusValue
		OCCURRING ActiveStatusValue
		FINISHED  ActiveStatusValue
	}
)

var ActiveStatus = &activeStatus{
	UPCOMING:  "UPCOMING",
	OCCURRING: "OCCURRING",
	FINISHED:  "FINISHED",
}

// ===============================================
type (
	ScopePromotionValue string
	scopePromotion      struct {
		ALL     ScopePromotionValue
		PRODUCT ScopePromotionValue
	}
)

var ScopePromotion = &scopePromotion{
	ALL:     "ALL",
	PRODUCT: "PRODUCT",
}

// ============================================================

type (
	VendorGiftConditionTypeValue string

	vendorGiftConditionType struct {
		AMOUNT   VendorGiftConditionTypeValue
		QUANTITY VendorGiftConditionTypeValue
	}
)

var VendorGiftConditionType = &vendorGiftConditionType{
	AMOUNT:   "AMOUNT",   // giá trị
	QUANTITY: "QUANTITY", // số lượng
}
