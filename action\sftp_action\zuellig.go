package sftp_action

import (
	"bytes"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/core"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/zuellig"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// zuelig template
// from : is start byte index at;
// to : is end byte index at;
type zuelligTemplate struct {
	OrderQuantity                     int64   `from:"29" to:"34"`     // SL đặt
	OrderDate                         string  `from:"56" to:"63"`     // ngày đặt YYYYMMDD
	RequestDeliveryDate               string  `from:"73" to:"80"`     // ngày giao dự kiến YYYYMMDD
	ShipToCode                        int64   `from:"81" to:"100"`    // Ship to code Zuellig cung cấp //
	CustomerPONumber                  string  `from:"101" to:"135"`   // mã đầu gửi KH MEDXPO000001
	SAPMaterial                       int64   `from:"136" to:"153"`   // mã sản phẩm nội bộ Zuellig -> product-mapping// BISMT
	MaterialPriceFromCustomer         int64   `from:"1490" to:"1498"` // giá trên đơn vị //optional
	DiscPercentageOverride            float64 `from:"1499" to:"1504"` //  phần trăm chiết khấu // optional
	ShipToCodeFromCustomer            string  `from:"1505" to:"1524"` // Zueligg sẽ cung cấp value này cho Medx điền trên PO // optional
	MaterialCodeCustomer              int64   `from:"1525" to:"1542"` // MEDX productID //optional
	ShortExpiryAcceptanceFromCustomer string  `from:"1543" to:"1543"` // Cận date //(Y | N)
	CustomerEmailAddress              string  `from:"1544" to:"2043"` // email
	// ControlFlag                       string  `from:"10000" to:"10000"` // Value (Y | N)
}

const (
	ZUELLIG     = "ZUELLIG"
	LINE_LENGHT = 2043
)

// gen template for file write
func genTemplateBuffer(c string, len int) *bytes.Buffer {
	var template = bytes.Buffer{}
	template.Grow(len)
	for i := 0; i < len; i++ {
		template.WriteString(c)
	}

	return &template
}

// create standard zuellig file
func createZuelligFile(poCode string, confirmDate string) string {
	return fmt.Sprintf("%s/MedX_%s_%s.txt", conf.Config.SFTPIntegration.Zuellig.Dir, poCode, confirmDate)
}

func processTemplate(buffer []byte, template *zuelligTemplate) ([]byte, error) {
	if template == nil {
		return nil, errors.New("nil template")
	}

	// template processing
	structVal := reflect.ValueOf(*template)
	structTyp := reflect.TypeOf(*template)
	numField := structTyp.NumField()

	bufferLen := len(buffer)
	prevTo := 0
	for i := 0; i < numField; i++ {
		var (
			currentTyp = structTyp.Field(i)
			currentVal = structVal.Field(i)
			currentTag = currentTyp.Tag
			fieldName  = currentTyp.Name
			fromString = currentTag.Get("from")
			toString   = currentTag.Get("to")
			from       int // index
			to         int // max index
			max        int // max byte for this field
			err        error
		)
		// validation
		if from, err = strconv.Atoi(fromString); err != nil {
			return nil, errors.New("from and to tag must be a number")
		}
		if to, err = strconv.Atoi(toString); err != nil {
			return nil, errors.New("from and to tag must be a number")
		}
		if from <= prevTo {
			return nil, fmt.Errorf("prevTo : %d >= currentFrom: %d", prevTo, from)
		}
		if from > to {
			return nil, fmt.Errorf("currentFrom : %d >= currentTo: %d", from, to)
		}
		if to > bufferLen {
			return nil, fmt.Errorf("currentTo : %d exceed max buffer length : %d", to, bufferLen)
		}

		max = to - from + 1
		// do replace
		value := fmt.Sprint(currentVal.Interface())
		if len(value) > max {
			return nil, fmt.Errorf("at %q value: %s exceed max field length: %d", fieldName, value, max)
		}
		if len(value) == 0 {
			continue
		}
		// append slice to middle of slice
		truncatedHead := append(buffer[:from-1], []byte(value)...)
		buffer = append(truncatedHead, buffer[from+len(value)-1:]...)

		// increase prev
		prevTo = to
	}

	return buffer, nil
}

func SendPOZuellig(poCode string) *common.APIResponse {

	// getPO
	getPOResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if getPOResp.Status != common.APIStatus.Ok {
		return getPOResp
	}
	getPOItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, nil)
	if getPOItemResp.Status != common.APIStatus.Ok {
		return getPOItemResp
	}
	getSettingResp := model.SettingDB.QueryOne(nil)
	if getSettingResp.Status != common.APIStatus.Ok {
		return getSettingResp
	}

	var (
		setting             = getSettingResp.Data.([]*model.Setting)[0]
		mapZuelligWarehouse = map[string]int64{}
		zuelligEmails       = []string{}
		po                  = getPOResp.Data.([]*model.PurchaseOrder)[0]
		poItems             = getPOItemResp.Data.([]*model.PurchaseOrderItem)
		poItemsLastIdx      = len(poItems) - 1
		content             []byte
		now                 = utils.GetVietnamTimeNow()
	)

	if setting.ZuelligIntegation == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Zuellig integration is not configured",
		}
	}

	zuelligEmails = setting.ZuelligIntegation.Emails
	mapZuelligWarehouse = setting.ZuelligIntegation.MapWarehouseCodeToZuelligShipcode

	// Call get info vendor
	option := client.APIOption{
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  po.VendorCode,
		},
	}
	vendorResp := seller.GetSellers(option)
	if vendorResp.Status != common.APIStatus.Ok {
		return vendorResp
	}
	vendor := vendorResp.Data.([]*model.Seller)[0]

	//check vendor is config for zuellig
	if !utils.IsContains(vendor.SystemIntegrateSFTP, ZUELLIG) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PO Vendor is not config for ZUELLIG",
		}
	}

	for i, item := range poItems {
		// skip gift
		if item.IsGift != nil && *item.IsGift == true {
			continue
		}
		// bỏ qua item không có SL
		if item.ExpectQuantity == 0 {
			continue
		}

		buffer := genTemplateBuffer(" ", LINE_LENGHT).Bytes()
		template := zuelligTemplate{
			OrderQuantity:                     item.ExpectQuantity,              //
			OrderDate:                         "",                               //
			RequestDeliveryDate:               "",                               //
			ShipToCode:                        0,                                //
			CustomerPONumber:                  fmt.Sprintf("MEDX_%s", poCode),   //
			SAPMaterial:                       0,                                //
			MaterialPriceFromCustomer:         int64(item.UnitPrice),            //
			DiscPercentageOverride:            0,                                //
			ShipToCodeFromCustomer:            "",                               // optional : 1.62
			MaterialCodeCustomer:              item.ProductID,                   //
			ShortExpiryAcceptanceFromCustomer: "",                               //
			CustomerEmailAddress:              strings.Join(zuelligEmails, " "), //
			// ControlFlag:                       "",                                                                  //
		}

		if po.ConfirmedTime != nil {
			template.OrderDate = po.ConfirmedTime.Format(utils.YYYYMMDD)
		} else {
			template.OrderDate = now.Format(utils.YYYYMMDD)
		}
		if po.EstimatedDeliveryTime != nil {
			template.RequestDeliveryDate = po.EstimatedDeliveryTime.Format(utils.YYYYMMDD)
		} else {
			template.RequestDeliveryDate = now.Format(utils.YYYYMMDD)
		}
		if item.DiscountPercent != nil {
			template.DiscPercentageOverride = *item.DiscountPercent
		}
		if item.IsNearExpiration != nil && *item.IsNearExpiration {
			template.ShortExpiryAcceptanceFromCustomer = "Y"
		} else {
			template.ShortExpiryAcceptanceFromCustomer = "N"
		}
		if zuelligShipToCode, ok := mapZuelligWarehouse[po.DeliveryWarehouseCode]; ok {
			template.ShipToCode = zuelligShipToCode
		} else {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "ZUELLIG ship to code is not config yet",
			}
		}

		// product-mapping
		getProductMappingResp := model.ProductMappingDB.QueryOne(model.ProductMapping{ProductID: item.ProductID})
		if getProductMappingResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: fmt.Sprintf("cannot get product mapping for %s, productID : %d", ZUELLIG, item.ProductID),
			}
		}
		productMapping := getProductMappingResp.Data.([]*model.ProductMapping)[0]
		if productMapping.ProductVendors == nil || len(*productMapping.ProductVendors) == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: fmt.Sprintf("product mapping vendor has not config for %s, productID : %d", ZUELLIG, item.ProductID),
			}
		}

		isHasProductMapping := false
	loopProductVendors:
		for _, productVendors := range *productMapping.ProductVendors {
			if productVendors.VendorCode == po.VendorCode {
				for _, mappingName := range productVendors.ProductVendorNames {
					upperMappingName := strings.ToUpper(mappingName)
					// ưu tiên 1
					if strings.HasPrefix(upperMappingName, fmt.Sprintf("%s-%s:", ZUELLIG, po.DeliveryWarehouseCode)) { // ZUELLIG-HN:12345
						code, err := genProductMappingName(upperMappingName, item.ProductID, mappingName)
						if err != nil {
							return &common.APIResponse{
								Status:  common.APIStatus.Error,
								Message: err.Error(),
							}
						}
						template.SAPMaterial = code
						isHasProductMapping = true
						break loopProductVendors

						// ưu tiên 2
					} else if strings.HasPrefix(upperMappingName, ZUELLIG+":") { // ZUELLIG:123456
						code, err := genProductMappingName(upperMappingName, item.ProductID, mappingName)
						if err != nil {
							return &common.APIResponse{
								Status:  common.APIStatus.Error,
								Message: err.Error(),
							}
						}
						template.SAPMaterial = code
						isHasProductMapping = true
					}
				}
			}
		}

		if !isHasProductMapping {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: fmt.Sprintf("productID : %d has not product mapping with %s", item.ProductID, ZUELLIG),
			}
		}

		// gen content
		newBuffcontent, err := processTemplate(buffer, &template)

		if err != nil {
			return &common.APIResponse{
				ErrorCode: common.APIStatus.Error,
				Message:   err.Error(),
			}
		}
		content = append(content, newBuffcontent...)

		if poItemsLastIdx != i {
			content = append(content, 0xA)
		}
	}

	err := zuellig.UploadContent(createZuelligFile(poCode, now.Format(utils.YYYYMMDD)), bytes.NewBuffer(content))
	if err != nil {
		return &common.APIResponse{
			Message: err.Error(),
			Status:  common.APIStatus.Error,
		}
	}

	genTokenResp := core.GenToken()
	if genTokenResp.Status != common.APIStatus.Ok {
		return genTokenResp
	}

	token := genTokenResp.Message
	var base64Encoding string
	base64Encoding += "data:text/plain;base64,"
	base64Encoding += utils.ToBase64(content)
	// Excute upload file
	option = client.APIOption{
		Body: model.UploadFile{
			FileName:   fmt.Sprintf("%v", poCode),
			RefType:    "ZUELLIG",
			RefCode:    poCode,
			Token:      token,
			Data:       base64Encoding,
			Mime:       "text/plain",
			FileExtend: "txt",
		},
	}
	uploadDocResp := core.UploadDocument(option)
	return uploadDocResp
}

func genProductMappingName(name string, id int64, mappingName string) (int64, error) {
	sl := strings.Split(name, ":")
	errResp := fmt.Errorf("product : %d mapping wrong to %q", id, mappingName)

	if len(sl) < 2 {
		return 0, errResp
	}

	code, err := strconv.Atoi(sl[1])
	if err != nil {
		return 0, errResp
	}

	return int64(code), nil
}
