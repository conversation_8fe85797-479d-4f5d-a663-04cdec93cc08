package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type LogQuotationExport struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	Version         string              `json:"version,omitempty" bson:"version,omitempty"`

	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`

	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	ProductName    string                 `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode    string                 `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID      int64                  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU            string                 `json:"sku,omitempty" bson:"sku,omitempty"`
	LogFormulaData map[string]interface{} `json:"logFormulaData,omitempty" bson:"log_formula_data,omitempty"`
}

var LogQuotationExportDB = &db.Instance{
	ColName:        "log_quotation_export",
	TemplateObject: "log_quotation_export",
}

func InitLogQuotationExport(s *mongo.Database) {
	LogQuotationExportDB.ApplyDatabase(s)

	t := true
	_ = LogQuotationExportDB.CreateIndex(bson.D{
		primitive.E{Key: "version", Value: 1},
		primitive.E{Key: "seller_code", Value: 1},
		primitive.E{Key: "warehouse_code", Value: 1},
		primitive.E{Key: "product_id", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
		Unique:     &t,
	})

	expire := int32((1 * 24 * time.Hour).Seconds())
	_ = LogQuotationExportDB.CreateIndex(bson.D{
		primitive.E{Key: "created_time", Value: 1},
	}, &options.IndexOptions{
		Background:         &t,
		ExpireAfterSeconds: &expire,
	})
}
