package action

import (
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetAdminPushingGroup retrieves a list of admin pushing groups based on the provided query parameters.
// It supports pagination through the offset and limit parameters, and can optionally return the total count of matching records.
//
// Parameters:
//   - query: A pointer to a model.AdminPushingGroup struct containing the query parameters.
//   - offset: An int64 representing the number of records to skip for pagination.
//   - limit: An int64 representing the maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of matching records in the response.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the query results and optionally the total count of matching records.
func GetAdminPushingGroup(query *model.AdminPushingGroup, offset, limit int64, getTotal bool) *common.APIResponse {

	// add CreatedFrom to filter
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}
	// add CreatedTo to filter
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTo,
			},
		})
	}

	// query data
	result := model.AdminPushingGroupDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if result.Status == common.APIStatus.Ok && getTotal {
		result.Total = model.AdminPushingGroupDB.Count(query).Total
	}

	return result
}

// ConfirmedPushingGroup ...
// ConfirmedPushingGroup confirms an admin pushing group based on the provided input.
// It performs several validation checks and updates the group status if all checks pass.
// 
// Parameters:
// - input: A pointer to model.AdminPushingGroup containing the details of the group to be confirmed.
//
// Returns:
// - *common.APIResponse: The response object containing the status and message of the operation.
//
// Validation checks:
// - Ensures the input is not nil and contains a valid GroupCode.
// - Validates the ConfirmType against a list of allowed values.
// - Checks if the group is already confirmed.
// - Compares the group's creation date with the current date to ensure it is valid for confirmation.
// - Verifies the version of the admin group to ensure it is the latest.
//
// If all checks pass, the group's IsConfirmed status is updated, and an ImportQuotationJob is pushed to the queue.
func ConfirmedPushingGroup(input *model.AdminPushingGroup) *common.APIResponse {
	if input == nil || len(input.GroupCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}


	// validConfirmType is a slice of strings representing the valid confirmation types
	// for admin push group actions. The possible values are:
	// - "OVERWRIDE": Overwrite the existing group.
	// - "REMOVE_AND_WRITE": Remove the existing group and write a new one.
	validConfirmType := []string{
		// string(enum.AdminPushGroupConfirmTypeValue.ADD),
		string(enum.AdminPushGroupConfirmTypeValue.OVERWRIDE),
		string(enum.AdminPushGroupConfirmTypeValue.REMOVE_AND_WRITE)}

	if !utils.IsContains(validConfirmType, string(input.ConfirmType)) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid confirm type",
		}
	}

	var (
		now       = time.Now()
		groupCode = input.GroupCode
	)

	// get AdminPushingGroupDB
	adminPushingGroupResp := model.AdminPushingGroupDB.QueryOne(model.AdminPushingGroup{GroupCode: groupCode, WarehouseCodes: input.WarehouseCodes})
	if adminPushingGroupResp.Status != common.APIStatus.Ok {
		return adminPushingGroupResp
	}
	adminPushingGroup := adminPushingGroupResp.Data.([]*model.AdminPushingGroup)[0]
	if adminPushingGroup.IsConfirmed == true {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Group is confirmed",
			ErrorCode: string(enum.ErrorCodeInvalid.GroupConfirmed),
		}
	}

	// check time confirm
	adminPushingGroupYYYYMMDD := adminPushingGroup.CreatedTime.In(utils.TimeZoneVN).Format(utils.YYYYMMDD_ENDASH)
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)
	if adminPushingGroupYYYYMMDD < nowYYYYMMDD {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid time confirm",
			ErrorCode: string(enum.ErrorCodeInvalid.TimeConfirmAdminGroup),
		}
	}

	// get lasted admin group
	lastedAdminPushingGroupResp := model.AdminPushingGroupDB.Query(model.AdminPushingGroup{SellerCode: adminPushingGroup.SellerCode, WarehouseCodes: adminPushingGroup.WarehouseCodes}, 0, 1, &primitive.M{"_id": -1})
	if lastedAdminPushingGroupResp.Status != common.APIStatus.Ok {
		return lastedAdminPushingGroupResp
	}
	lastedAdminPushingGroup := lastedAdminPushingGroupResp.Data.([]*model.AdminPushingGroup)[0]
	if lastedAdminPushingGroup.GroupCode != groupCode {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid version of admin group",
			ErrorCode: string(enum.ErrorCodeInvalid.VersionAdminGroup),
		}
	}

	// update IsConfirmed
	// by default update return new document
	updateResp := model.AdminPushingGroupDB.UpdateOne(
		model.AdminPushingGroup{ID: adminPushingGroup.ID},
		model.AdminPushingGroup{
			IsConfirmed:   true,
			ConfirmedTime: &now,
			SKUVendorType: input.SKUVendorType,
			ConfirmType:   input.ConfirmType,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	adminPushingGroup = updateResp.Data.([]*model.AdminPushingGroup)[0]

	key := append(adminPushingGroup.WarehouseCodes, adminPushingGroup.SellerCode)

	// push to queue ImportQuotationJob
	err := model.ImportQuotationJob.Push(
		adminPushingGroup,
		&job.JobItemMetadata{
			// keyToFind, workerCountingVariable.Version,
			Keys:      key,
			SortedKey: strings.Join(key, "_"),
			UniqueKey: adminPushingGroup.Version,
			Topic:     "IMPORT_QUOTATION",
			ReadyTime: &now,
		},
	)
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	return updateResp
}
