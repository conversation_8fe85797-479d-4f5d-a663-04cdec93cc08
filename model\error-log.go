package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ErrorLog struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Data string   `json:"data,omitempty" bson:"data,omitempty"`
	Keys []string `json:"keys,omitempty" bson:"keys,omitempty"`
	Type string   `json:"type,omitempty" bson:"type,omitempty"`
}

// ErrorLogDB ...
var ErrorLogDB = &db.Instance{
	ColName:        "error_log",
	TemplateObject: &ErrorLog{},
}

// InitErrorLogkModel ...
func InitErrorLogModel(s *mongo.Database) {
	ErrorLogDB.ApplyDatabase(s)
}
