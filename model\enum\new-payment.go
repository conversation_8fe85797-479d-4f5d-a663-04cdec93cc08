package enum

/**
 * Status type
 */
type (
	PaymentType string
	paymentType struct {
		RECEIPT_VOUCHER PaymentType // phiếu thu
		PAYMENT_VOUCHER PaymentType // phiếu chi
	}
)

var PayType = &paymentType{
	RECEIPT_VOUCHER: "RECEIPT_VOUCHER",
	PAYMENT_VOUCHER: "PAYMENT_VOUCHER",
}

/**
 * Purpose type
 */
type (
	PaymentPurposeType string
	paymentPurposeType struct {
		ADVANCE_MONEY         PaymentPurposeType // chi tạm ứng (ADVANCE_PAYMENT)
		BILL                  PaymentPurposeType // chi trả nợ (DEBT_PAYMENT)
		PAY_DIRECT            PaymentPurposeType // thu trực tiếp (DIRECT_RECEIPT)
		DEBT                  PaymentPurposeType // cấn trừ hoá đơn (VB_CLEARING_DEBT)
		PAYMENT_CLEARING_DEBT PaymentPurposeType // cấn trừ thu chi
	}
)

var PaymentPurpose = &paymentPurposeType{
	PAY_DIRECT:            "PAY_DIRECT",
	DEBT:                  "DEBT",
	ADVANCE_MONEY:         "ADVANCE_MONEY",
	BILL:                  "BILL",
	PAYMENT_CLEARING_DEBT: "PAYMENT_CLEARING_DEBT",
}
