package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetQuotationHedging retrieves a list of quotation hedging records from the database based on the provided input criteria.
// It supports pagination through the offset and limit parameters, and can optionally include the total count of records.
//
// Parameters:
//   - input: The criteria for querying quotation hedging records.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return for pagination.
//   - getTotal: A boolean flag indicating whether to include the total count of records in the response.
//
// Returns:
//   - *common.APIResponse: The API response containing the queried quotation hedging records and optionally the total count.
func GetQuotationHedging(input model.Quotation, offset, limit int64, getTotal bool) *common.APIResponse {
	resp := model.QuotationHedgingDB.Query(input, offset, limit, &bson.M{"_id": -1})

	if getTotal {
		countResp := model.QuotationHedgingDB.Count(input)
		resp.Total = countResp.Total
	}

	return resp
}

// Delete all quotation hedging
func DeleteQuotationHedging(input *model.Quotation) *common.APIResponse {
	return model.QuotationHedgingDB.Delete(input)
}

// CreateHedgingQuotation creates a hedging quotation based on the provided input.
// It validates the input, checks for existing quotations, and either updates or creates a new quotation.
//
// Parameters:
//   - input: CreateQuotationsInput containing the details for the quotations to be created.
//
// Returns:
//   - *common.APIResponse: The response containing the status and message of the operation.
//
// The function performs the following steps:
//  1. Validates the input to ensure it contains items and a mode.
//  2. Iterates through each item in the input to validate individual fields.
//  3. Checks if a quotation already exists for the given SKU, vendor codes, and warehouse code.
//  4. If a quotation exists and the mode is "OVERWRITE", it updates the existing quotation.
//  5. If no quotation exists, it retrieves product details, SKU configuration, and creates a new quotation.
//  6. Returns an appropriate API response based on the success or failure of the operations.
func CreateHedgingQuotation(input CreateQuotationsInput) *common.APIResponse {
	if len(input.Items) == 0 || len(input.Mode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid input",
		}
	}

	// validate
	// check if all fields are valid
	for _, quotaInput := range input.Items {
		if utils.HasZero(
			quotaInput.ProductCode, quotaInput.SKU,
			quotaInput.WarehouseCode, quotaInput.ProductID,
			quotaInput.SellerCode) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid input",
			}
		}

		// check if vendor codes is not empty
		if quotaInput.VendorCodes == nil || len(*quotaInput.VendorCodes) != 1 || (*quotaInput.VendorCodes)[0] == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid input",
			}
		}

		// check if quantity expect is greater than 0
		if quotaInput.QuantityExpect == nil || *quotaInput.QuantityExpect <= 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Quantity expect must be greater than 0",
			}
		}

	}

	adminstrativeTime := utils.GetAdminstrativeTime()
LOOP_QUOTATION_HEDGING:
	// loop through each item in the input
	for _, quotaInput := range input.Items {

		if purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(quotaInput.WarehouseCode); ok {
			quotaInput.PurchaserCode = purchaserCode
		} else {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Do not create for " + quotaInput.WarehouseCode,
			}
		}

		// check if quotation exists
		quotationCheck := model.QuotationHedgingDB.QueryOne(
			model.Quotation{
				SKU:                    quotaInput.SKU,
				VendorCodes:            quotaInput.VendorCodes,
				WarehouseCode:          quotaInput.WarehouseCode,
				InboundRequestItemCode: quotaInput.InboundRequestItemCode,
			},
		)

		// if quotation exists and mode is "OVERWRITE", update the quotation
		if quotationCheck.Status == common.APIStatus.Ok {
			if input.Mode == "OVERWRITE" {
				// update quotation
				updateQuotationResp := model.QuotationHedgingDB.UpdateOne(
					model.Quotation{
						ProductID:              quotaInput.ProductID,
						SellerCode:             quotaInput.SellerCode,
						WarehouseCode:          quotaInput.WarehouseCode,
						VendorCodes:            quotaInput.VendorCodes,
						InboundRequestItemCode: quotaInput.InboundRequestItemCode,
					},
					model.Quotation{
						Unit:           quotaInput.Unit,
						QuantityExpect: quotaInput.QuantityExpect,
					},
				)

				if updateQuotationResp.Status != common.APIStatus.Ok {
					return &common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Update quotation hedging failed",
					}
				}
			}
			continue LOOP_QUOTATION_HEDGING
		}

		// create quotation

		// get product details
		opts := client.APIOption{
			Q:     model.Product{ProductID: quotaInput.ProductID},
			Limit: utils.Pointer.WithInt(1),
		}
		productResp := marketplace.GetSingleProduct(opts)

		if productResp.Status != common.APIStatus.Ok {
			return productResp
		}

		product := productResp.Data.([]*model.Product)[0]
		quotaInput.Origin = product.Origin
		if quotaInput.Origin != "VN" {
			quotaInput.Origin = "FOREIGN"
		}
		quotaInput.ProductName = product.Name
		quotaInput.Unit = product.Unit
		quotaInput.ProductCode = product.Code

		// get sku config
		getSKUConf := model.SkuConfigDB.QueryOne(model.SkuConfig{
			SKU:           quotaInput.SKU,
			PurchaserCode: quotaInput.PurchaserCode,
		})
		if getSKUConf.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: "Get sku config failed",
			}
		}

		// check if sku is active
		skuConf := getSKUConf.Data.([]*model.SkuConfig)[0]
		if skuConf.IsActive != nil && *skuConf.IsActive == false {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: fmt.Sprintf("SKU %q, Purchaser %q is not active", quotaInput.SKU, skuConf.PurchaserCode),
			}
		}

		// check if sku has vendors
		if skuConf.Vendors == nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Error,
				Message: fmt.Sprintf("SKU %q, Purchaser %q has no vendor", quotaInput.SKU, skuConf.PurchaserCode),
			}
		}

		quotaInput.QuotationStatus = enum.QuotationStatus.STOP_PUBLISH
		quotaInput.Hashtag = genQuotationHashtag(quotaInput)
		quotaInput.AssignVendorTime = adminstrativeTime
		now := time.Now()
		quotaInput.LastUpdatedTime = &now

		// generate quotation info
		genQuotationInfo(&quotaInput, skuConf)

		if quotaInput.QuantityExpect == nil || *quotaInput.QuantityExpect <= 0 {
			continue LOOP_QUOTATION_HEDGING
		}
		
		createQuotationResp := model.QuotationHedgingDB.Create(quotaInput)
		if createQuotationResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Create quotation hedging failed",
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create quotation hedging success",
	}
}

func genQuotationHashtag(quotation model.Quotation) string {
	return strings.Replace(utils.NormalizeString(fmt.Sprintf("%d-%v", quotation.ProductID, quotation.ProductName)), " ", "-", -1)
}
