package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetVendorBidding ...
func GetVendorBidding(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	var query model.Bidding
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	if len(query.WarehouseCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetVendorBidding(query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// GetSellerBidding ...
func GetSellerBidding(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	var query model.Bidding
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// if sellerCode != "" {
	// 	query.SellerCodes = nil
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"seller_codes": sellerCode,
	// 	})
	// }

	if query.SelectedBy != "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"selected_bys": query.SelectedBy,
		})
	}
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}
	if len(query.VendorPrioritys) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"vendor_priority": bson.M{
				"$in": query.VendorPrioritys,
			},
		})
	}

	// do action
	return resp.Respond(action.GetSellerBidding(query, offset, limit, getTotal))
}

// BiddingQuotation
func BiddingQuotation(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.BiddingQuotationInput
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		input.Vendor = vendors[0]

		// do action
		return resp.Respond(action.BiddingQuotation(&input))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// SellerCreateBidding
func SellerUpdateBidding(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.Bidding

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Required: VendorCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
		})
	}

	// validate input
	if input.ProductID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Required: ProductID",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}

	// do action
	return resp.Respond(action.SellerUpdateBidding(input))
}

// GetVendorBiddingHistory
func GetVendorBiddingHistory(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// parse query
	var query model.BiddingHistory
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		if acc.Type == "EMPLOYEE" {
			return resp.Respond(action.GetVendorBiddingHistory(query, offset, limit, getTotal))
		}

		// acc.Type == "VENDOR"
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code

		// check version
		if query.Version == "ALL" {
			query.Version = ""
			query.CreatedFrom = nil
			query.CreatedTo = nil
		} else {
			// check version
			if query.CreatedFrom != nil || query.CreatedTo != nil {
				if query.CreatedFrom != nil {
					parseZoneFrom := query.CreatedFrom.In(utils.TimeZoneVN).Format("********")
					query.ComplexQuery = append(query.ComplexQuery, &bson.M{
						"version": bson.M{
							"$gte": parseZoneFrom,
						},
					})
				}
				if query.CreatedTo != nil {
					parseZoneTo := query.CreatedTo.In(utils.TimeZoneVN).Format("********")
					query.ComplexQuery = append(query.ComplexQuery, &bson.M{
						"version": bson.M{
							"$lt": parseZoneTo,
						},
					})
				}
			} else {
				// default version
				query.Version = utils.GetCurrentVersionDDMMYYYY()
			}
		}

		return resp.Respond(action.GetVendorBiddingHistory(query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CreatePOFromBidding
func CreatePOFromBidding(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")

		input model.CreatePOFromBiddingInput
	)

	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	input.PO.SellerCode = sellerCode
	input.PO.PurchaserCode = purchaserCode

	// check permission
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreatePOFromBidding(input, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}
