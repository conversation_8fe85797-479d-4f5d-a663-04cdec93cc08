package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Reason struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// unique
	ReasonID int64 `json:"reasonID" bson:"reason_id,omitempty"`

	ReasonCode     string `json:"reasonCode" bson:"reason_code,omitempty"`
	ReasonTypeCode string `json:"reasonTypeCode" bson:"reason_type_code,omitempty"`
	Description    string `json:"description,omitempty" bson:"description,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []*bson.M `json:"-" bson:"$or,omitempty"`

	ReasonCodeIn []string `json:"reasonCodeIn,omitempty" bson:"-"`
}

// ReasonDB ...
var ReasonDB = &db.Instance{
	ColName:        "reason",
	TemplateObject: &Reason{},
}

// InitReasonMappingModel ...
func InitReasonMappingModel(s *mongo.Database) {
	ReasonDB.ApplyDatabase(s)
}
