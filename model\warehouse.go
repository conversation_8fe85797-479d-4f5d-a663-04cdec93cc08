package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type InboundCallback struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ReceiptCode          string                `json:"receiptCode,omitempty" bson:"receipt_code,omitempty"`
	RequestID            int64                 `json:"requestId" bson:"request_id,omitempty"`
	POCode               string                `json:"poCode" bson:"po_code,omitempty"`
	InboundCallbackItems []InboundCallbackItem `json:"inboundTicketItems" bson:"inbound_ticket_items,omitempty"`
	CheckInTime          *time.Time            `json:"checkInTime,omitempty" bson:"check_in_time,omitempty"`
	Status               string                `json:"status,omitempty" bson:"status,omitempty"`
}

type InboundCallbackItem struct {
	LineID       int64 `json:"lineId" bson:"line-id,omitempty"`
	ProductID    int64 `json:"productId" bson:"product_id,omitempty"`
	DoneQuantity int64 `json:"doneQuantity" bson:"done_quantity,omitempty"`

	Lot     string `json:"lot,omitempty" bson:"lot,omitempty"`
	ExpDate string `json:"expDate,omitempty" bson:"exp_date,omitempty"`
	Vat     int64  `json:"vat" bson:"vat,omitempty"`
}

// InboundCallbacLogkDB ...
var InboundCallbacLogkDB = &db.Instance{
	ColName:        "inbound_callback_log",
	TemplateObject: &InboundCallback{},
}

// InitSkuModel ...
func InitInboundCallbackModel(s *mongo.Database) {
	InboundCallbacLogkDB.ApplyDatabase(s)

	// t := true
	// _ = InboundCallbacLogkDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "request_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// // s.Collection("inbound_callback_log").Indexes().DropOne(context.TODO(), "created_time_1")
	// var second int32 = 60 * 60 * 24 * 90 // 90 ngày
	// _ = InboundCallbacLogkDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &second,
	// })
}
