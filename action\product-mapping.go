package action

import (
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetProductMapping ...
func GetProductMapping(query *model.ProductMapping, offset, limit int64, getTotal bool) *common.APIResponse {

	nameMappingResp := model.ProductMappingDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.ProductMappingDB.Count(query)
		nameMappingResp.Total = countResp.Total
	}
	return nameMappingResp
}

// UpdateProductMapping ...
func UpdateProductMapping(inputs []*model.ProductMapping) *common.APIResponse {

	var errResp []*common.APIResponse

	// Insert/update data
	for _, input := range inputs {
		productMappingResp := model.ProductMappingDB.QueryOne(
			model.ProductMapping{
				ProductID: input.ProductID,
			})

		// Insert data if not found
		if productMappingResp.Status == common.APIStatus.NotFound {
			// Update product vendor
			if input.ProductVendors != nil {
				// Normalize product vendor name
				for index := range *input.ProductVendors {
					productVendor := (*input.ProductVendors)[index]
					// Normalize product vendor name
					for i := range productVendor.ProductVendorNames {
						productVendor.ProductVendorNames[i] = strings.Trim(productVendor.ProductVendorNames[i], " ")
						productVendor.ProductVendorNames[i] = strings.ReplaceAll(productVendor.ProductVendorNames[i], "\r\n", " ")

						productVendorSlug := utils.NormalizeString(productVendor.ProductVendorNames[i])
						if productVendorSlug != "" && !utils.IsContains(productVendor.ProductVendorSlugs, productVendorSlug) {
							productVendor.ProductVendorSlugs = append(productVendor.ProductVendorSlugs, productVendorSlug)
							(*input.ProductVendors)[index] = productVendor
						}
					}
				}
			}

			// Update unit
			if input.UnitVendors != nil {
				for index := range *input.UnitVendors {
					unitVendor := (*input.UnitVendors)[index]
					for _, name := range unitVendor.Names {
						name = strings.Trim(name, " ")
						name = strings.ReplaceAll(name, "\r\n", " ")
						slug := utils.NormalizeString(name)
						if slug != "" && !utils.IsContains(unitVendor.Slugs, slug) {
							unitVendor.Slugs = append(unitVendor.Slugs, slug)
						}
					}
					(*input.UnitVendors)[index] = unitVendor
				}
			}

			// Create data
			createResp := model.ProductMappingDB.Create(input)
			if createResp.Status != common.APIStatus.Ok {
				errResp = append(errResp, createResp)
			}
			continue
		}

		// Update data
		productMapping := productMappingResp.Data.([]*model.ProductMapping)[0]
		// Normalize product vendor name
		if input.ProductVendors != nil {
			// Normalize product vendor name
			for index := range *input.ProductVendors {
				productVendor := (*input.ProductVendors)[index]
				productVendorSlugs := []string{}
				// Normalize product vendor name
				for i := range productVendor.ProductVendorNames {
					productVendor.ProductVendorNames[i] = strings.Trim(productVendor.ProductVendorNames[i], " ")
					productVendor.ProductVendorNames[i] = strings.ReplaceAll(productVendor.ProductVendorNames[i], "\r\n", " ")
					productVendorSlug := utils.NormalizeString(productVendor.ProductVendorNames[i])
					if productVendorSlug != "" && !utils.IsContains(productVendorSlugs, productVendorSlug) {
						productVendorSlugs = append(productVendorSlugs, productVendorSlug)
					}
				}
				// Update product vendor
				productVendor.ProductVendorSlugs = productVendorSlugs
				(*input.ProductVendors)[index] = productVendor
			}
		}

		// Update unit
		if input.UnitVendors != nil {
			for index := range *input.UnitVendors {
				unitVendor := (*input.UnitVendors)[index]
				slugs := []string{}
				for _, name := range unitVendor.Names {
					name = strings.Trim(name, " ")
					name = strings.ReplaceAll(name, "\r\n", " ")
					slug := utils.NormalizeString(name)
					if slug != "" && !utils.IsContains(slugs, slug) {
						slugs = append(slugs, slug)
					}
				}
				unitVendor.Slugs = slugs
				(*input.UnitVendors)[index] = unitVendor
			}
		}

		// Update data
		updateResp := model.ProductMappingDB.UpdateOne(
			model.ProductMapping{ID: productMapping.ID},
			model.ProductMapping{
				ProductVendors: input.ProductVendors,
				UnitVendors:    input.UnitVendors,
			},
		)
		if updateResp.Status != common.APIStatus.Ok {
			errResp = append(errResp, updateResp)
		}
	}
	// Return error response
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   errResp,
	}
}

// DeleteProductMapping ...
func DeleteProductMapping(productID int64) *common.APIResponse {
	return model.ProductMappingDB.Delete(&model.ProductMapping{ProductID: productID})
}
