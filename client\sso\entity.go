package sso

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/client"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/constant"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetEntityMemberForLegacy = "/entity/member/legacy"
)

type entityClient struct {
	entityClient *client.RestClient
	headers      map[string]string
}

var (
	EntityClient *entityClient
)

func InitEntityClient(database *mongo.Database) {
	EntityClient = &entityClient{
		entityClient: client.NewRESTClient(
			conf.Config.SSOHost+"/iam/core/v1",
			"l_entity_client",
			constant.DEFAULT_TIMEOUT,
			constant.DEFAULT_RETRY_TIME,
			constant.DEFAULT_WAIT_TIME,
		),
		headers: map[string]string{
			"Authorization": conf.Config.SSOToken,
		},
	}

	if database != nil {
		EntityClient.entityClient.SetDBLog(database)
	}
}

func (cli *entityClient) GetEntityMemberForLegacy(entityID int64) *EntityMemberResponse {
	res, err := cli.entityClient.MakeHTTPRequestWithKey(client.HTTPMethods.Get, cli.headers, map[string]string{
		"entityID":        fmt.Sprintf("%d", entityID),
		"ignoreSupporter": "true",
	}, nil, pathGetEntityMemberForLegacy, &[]string{fmt.Sprintf("%d", entityID)})

	if err != nil {
		return &EntityMemberResponse{
			Status:    common.APIStatus.Error,
			Message:   "SSO server maintenance, please try again later. Error: " + err.Error(),
			ErrorCode: "ENTITY_GET_MEMBER_FAILED",
		}
	}

	var result *EntityMemberResponse
	err = json.Unmarshal([]byte(res.Body), &result)
	if err != nil {
		return &EntityMemberResponse{
			Status:    common.APIStatus.Error,
			Message:   "SSO server maintenance, please try again later. Error: " + err.Error(),
			ErrorCode: "ENTITY_GET_MEMBER_FAILED",
		}
	}

	return result
}
