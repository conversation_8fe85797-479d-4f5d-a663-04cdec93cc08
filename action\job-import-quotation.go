package action

import (
	"errors"
	"log"
	"sync"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ProcessingImportQuotation processes the import of a quotation based on the provided job item.
// It performs several checks and actions, including:
// - Checking if the job item has failed or been repushed too many times.
// - Marshalling and unmarshalling the job item data.
// - Validating the SellerCode and Version fields.
// - Creating a quotation from the export data.
// - Updating the warehouse index if necessary.
// - Creating hedging quotations.
// - Updating the status of the AdminPushingGroup.
//
// Parameters:
// - item: A pointer to the job.JobItem containing the data to be processed.
//
// Returns:
// - An error if any step in the process fails, otherwise nil.
func ProcessingImportQuotation(item *job.JobItem) error {
	if item.FailCount >= 3 || item.RepushCount >= 3 {
		return nil
	}

	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.AdminPushingGroup

	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	// input.SKU empty
	if input.SellerCode == "" || input.Version == "" {
		return errors.New("SellerCode/Version empty")
	}
	// do action
	soCodes, err := SellerCreateQuotationFromExport(input)

	if err != nil {
		log.Println(err)
	}

	// chỉ loại all -> bắn index so qua kho để giảm sl mua hàng
	if err == nil && len(soCodes) > 0 && input.SKUVendorType == enum.SKUVendorValue.ALL {
		// call to warehouse
		option := client.APIOption{
			Body: map[string]interface{}{
				"saleOrderCodes": soCodes,
			},
			Keys: soCodes,
		}

		warehouse.UpdateIndexSO(option)
	}

	// non fav hedging quotation
	// CreateHedgingNonFavVendor(input)
	// create hedging quotation
	CreateHedging(input)

	// if len(errorList) > 0 {
	// 	messages := []string{}
	// 	for _, quota := range errorList {
	// 		messages = append(messages, fmt.Sprint(quota.ProductID))
	// 	}
	// 	if item.Log == nil {
	// 		item.Log = &[]string{}
	// 	}
	// 	*item.Log = append(*item.Log, strings.Join(messages, ", "))
	// 	return nil
	// }
	// if resp.Status != common.APIStatus.Ok {
	// 	return errors.New(resp.Message)
	// }

	// update AdminPushingGroup status
	model.AdminPushingGroupDB.UpdateOne(model.AdminPushingGroup{
		Version: input.Version,
	}, model.AdminPushingGroup{
		IsImported: true,
	})

	return nil
}

// SellerCreateQuotationFromExport processes the creation of quotations from an export input.
// It performs the following steps:
// 1. Deletes existing export data of the same version.
// 2. Depending on the admin confirmation type, it may delete all existing quotations and bidding rates.
// 3. Queries the draft quotations and filters them based on SKU vendor type and warehouse code.
// 4. Collects the SOCodes from the valid quotations.
// 5. Executes the creation of quotations concurrently.
//
// Parameters:
// - input: model.AdminPushingGroup containing the details for creating quotations.
//
// Returns:
// - A slice of strings containing SOCodes.
// - An error if any operation fails.
func SellerCreateQuotationFromExport(input model.AdminPushingGroup) ([]string, error) {
	var (
		sellerCode       = input.SellerCode
		version          = input.Version
		skuVendorType    = input.SKUVendorType
		adminConfirmType = input.ConfirmType
		soCodes          = []string{}

		quotationGenMode string

		quotaionErr = []*model.QuotationExport{}
		inputItems  = []model.QuotationExport{}
	)

	// ==========================================

	// Xóa dữ liệu export cùng version: xóa cho chắc
	deleteDataVersionResp := model.QuotationExportDB.Delete(model.QuotationExport{
		Version: version,
	})

	if deleteDataVersionResp.Status == common.APIStatus.Error {
		return nil, errors.New(deleteDataVersionResp.Message)
	}

	// ==========================================

	switch adminConfirmType {
	// case enum.AdminPushGroupConfirmTypeValue.ADD:
	// 	quotationGenMode = "ADD"
	case enum.AdminPushGroupConfirmTypeValue.OVERWRIDE:
		quotationGenMode = "OVERRITE"
	case enum.AdminPushGroupConfirmTypeValue.REMOVE_AND_WRITE:
		// Delete all quotation
		for _, warehouseCode := range input.WarehouseCodes {
			deleteQuotationResp := DeleteAllQuotations(true, sellerCode, []string{}, []string{}, warehouseCode)
			if deleteQuotationResp.Status != common.APIStatus.Ok {
				return nil, errors.New(deleteQuotationResp.Message)
			}

			DeleteAllBiddingRates(true, sellerCode, []string{}, []string{}, warehouseCode)

		}
		quotationGenMode = "OVERRITE"

		// delete all bibding rate
	default:
		return nil, errors.New("admin confirm type is invalid")
	}

	// ==========================================

	_id := primitive.NilObjectID
	for {

		// get quotation export draft
		quotationResp := model.QuotationExportDraftDB.Query(model.QuotationExport{
			Version: version,
			ComplexQuery: []*bson.M{
				{"_id": bson.M{"$gt": _id}},
				{"quantity_expect": bson.M{"$gt": 0}},
			},
		}, 0, 100, &primitive.M{"_id": 1})

		// check response
		// if not found, break
		if quotationResp.Status == common.APIStatus.NotFound {
			break
		}
		if quotationResp.Status != common.APIStatus.Ok {
			continue
		}

		// get data
		quotations := quotationResp.Data.([]*model.QuotationExport)

		// loop data
		for _, quotation := range quotations {
			_id = *quotation.ID

			// filter by sku vendor type
			if skuVendorType != "" && skuVendorType != enum.SKUVendorValue.ALL && quotation.SKUVendorType != skuVendorType {
				continue
			}

			// filter by warehouse code
			if quotation.WarehouseCode == "" {
				quotaionErr = append(quotaionErr, quotation)
				continue
			}

			inputItems = append(inputItems, *quotation)

			// collect SOCode
			for _, so := range quotation.SOCodes {
				if !utils.IsContains(soCodes, so) {
					soCodes = append(soCodes, so)
				}
			}
		}
	}

	// --------------

	// limit 4 goroutine by using wait group
	wg := &sync.WaitGroup{}
	step := 0
	for i := range inputItems {
		if step == 4 {
			wg.Wait()
			step = 0
		}
		step++
		wg.Add(1)

		// do action
		go excuteCreateQuotations(inputItems[i], sellerCode, wg, quotationGenMode)
	}
	wg.Wait()

	if len(quotaionErr) > 0 {
		log.Println("Quotation error: ", quotaionErr)
	}

	return soCodes, nil
}

// excuteCreateQuotations processes the creation of quotations based on the provided quotation export data.
// It updates the InboundRequestItem status, checks SKU configurations, and creates a new quotation.
//
// Parameters:
//   - quotationExport: The data structure containing the details of the quotation to be exported.
//   - sellerCode: The code identifying the seller.
//   - wg: A WaitGroup to synchronize the completion of the goroutine.
//   - quotationGenMode: The mode in which the quotation is generated.
//
// The function performs the following steps:
//  1. Updates the InboundRequestItem status to inactive if there are applied items.
//  2. Checks if the SKU is blocked for favorite quotations or bidding sessions.
//  3. Creates a new QuotationExport record.
//  4. Constructs a new Quotation object and sets its properties.
//  5. If the expected quantity is valid, it calls SellerCreateQuotations to create the quotation.
func excuteCreateQuotations(quotationExport model.QuotationExport, sellerCode string, wg *sync.WaitGroup, quotationGenMode string) {
	defer wg.Done()

	// do action
	// update InboundRequestItem IsApplied
	if len(quotationExport.InboundRequestItemApplieds) > 0 {
		updateResp := model.InboundRequestItemDB.UpdateMany(
			model.InboundRequestItem{
				// SKU: quotation.SKU,
				// WarehouseCode: quotation.WarehouseCode,
				InboundRequestType: enum.InboundRequestType.OTHER,
				ComplexQuery: []*bson.M{
					{"inbound_request_item_code": bson.M{
						"$in": quotationExport.InboundRequestItemApplieds,
					}},
				},
			},
			model.InboundRequestItem{
				IsActive: utils.Pointer.WithBool(false),
			},
		)
		if updateResp.Status == common.APIStatus.NotFound {
		} else if updateResp.Status != common.APIStatus.Ok {
			return
		}
	}

	// get sku config
	blockFavQuotation := isBlockFavQuotation(quotationExport.SKU, quotationExport.WarehouseCode)
	blockBiddingSession := isBlockBidding(quotationExport.SKU, quotationExport.WarehouseCode)

	quotationExport.ID = &primitive.NilObjectID
	quotationExport.CreatedTime = nil
	quotationExport.Type = enum.QuotationTypeVal.NORMAL
	quotationExport.BlockReleaseToFavSession = &blockFavQuotation
	quotationExport.BlockReleaseToBiddingSession = &blockBiddingSession
	model.QuotationExportDB.Create(quotationExport)

	// Tạo quotation
	quota := model.Quotation{
		SellerCode:    sellerCode,
		WarehouseCode: quotationExport.WarehouseCode,

		ProductID:   quotationExport.ProductID,
		ProductCode: quotationExport.ProductCode,
		ProductName: quotationExport.ProductName,
		SKU:         quotationExport.SKU,

		QuantityExpect:       quotationExport.QuantityExpect,
		VendorExpectQuantity: quotationExport.VendorExpectQuantity,

		Unit: quotationExport.Unit,
		Type: quotationExport.Type,
		// UnitPrice: quotation.UnitPrice,
		// VAT:       quotation.VAT,

		BlockReleaseToFavSession:     quotationExport.BlockReleaseToFavSession,
		BlockReleaseToBiddingSession: quotationExport.BlockReleaseToBiddingSession,
	}
	// if quotationExport.VendorExpectQuantity != nil && *quotationExport.VendorExpectQuantity > 0 {
	// 	quota.QuantityExpect = quotationExport.VendorExpectQuantity
	// }
	if quota.QuantityExpect == nil || *quota.QuantityExpect <= 0 {
		return
	}

	quotaInput := CreateQuotationsInput{
		Mode: quotationGenMode,
		Items: []model.Quotation{
			quota,
		},
	}

	// create quotation
	SellerCreateQuotations(quotaInput, sellerCode)
}

// không còn dùng
func CreateHedgingNonFavVendor(input model.AdminPushingGroup) {
	if input.Version == "" {
		return
	}
	var createType = "OVERRITE"
	switch input.ConfirmType {
	case enum.AdminPushGroupConfirmTypeValue.OVERWRIDE:
	case enum.AdminPushGroupConfirmTypeValue.REMOVE_AND_WRITE:
		// Delete all quotation
		deleteQuotationResp := DeleteQuotationHedging(&model.Quotation{WarehouseCode: input.WarehouseCodes[0], SellerCode: input.SellerCode})
		if deleteQuotationResp.Status != common.APIStatus.Ok {
			return
		}
	default:
		return
	}

	_id_offset := primitive.NilObjectID
	listQuotationExport := []*model.QuotationExport{}
	for {
		getQuotationExportResp := model.QuotationExportHedgingDraftDB.Query(
			model.QuotationExport{
				Version: input.Version,
				ComplexQuery: []*bson.M{{
					"_id": bson.M{"$gt": _id_offset},
				}},
			},
			0, 500, &bson.M{"_id": 1})
		if getQuotationExportResp.Status != common.APIStatus.Ok {
			break
		}

		quotationExports := getQuotationExportResp.Data.([]*model.QuotationExport)

		for _, qta := range quotationExports {
			// id offset
			_id_offset = *qta.ID

			listQuotationExport = append(listQuotationExport, qta)
		}
	}

	sema := utils.NewSemaphore(10)
	for _, quotaExport := range listQuotationExport {
		sema.Acquire()

		go func(quotaExport model.QuotationExport) {
			defer sema.Release()

			execCreateQuotationHedging(quotaExport, createType)
		}(*quotaExport)
	}

	sema.Wait()
}

func CreateHedging(input model.AdminPushingGroup) {
	if input.Version == "" {
		return
	}
	// start create hedging quotation from draft
	var createType = "OVERRITE"
	switch input.ConfirmType {
	case enum.AdminPushGroupConfirmTypeValue.OVERWRIDE:
	case enum.AdminPushGroupConfirmTypeValue.REMOVE_AND_WRITE:
		// Delete all quotation
		deleteQuotationResp := DeleteQuotationHedging(&model.Quotation{WarehouseCode: input.WarehouseCodes[0], SellerCode: input.SellerCode})
		if deleteQuotationResp.Status != common.APIStatus.Ok {
			return
		}
	default:
		return
	}

	_id_offset := primitive.NilObjectID
	listQuotationExport := []*model.QuotationExport{}
	for {
		getQuotationExportResp := model.QuotationExportHedgingDraftDB.Query(
			model.QuotationExport{
				Version: input.Version,
				ComplexQuery: []*bson.M{{
					"_id": bson.M{"$gt": _id_offset},
				}},
			},
			0, 500, &bson.M{"_id": 1})
		if getQuotationExportResp.Status != common.APIStatus.Ok {
			break
		}

		quotationExports := getQuotationExportResp.Data.([]*model.QuotationExport)

		for _, qta := range quotationExports {
			// id offset
			_id_offset = *qta.ID

			listQuotationExport = append(listQuotationExport, qta)
		}
	}

	sema := utils.NewSemaphore(10)
	for _, quotaExport := range listQuotationExport {
		sema.Acquire()

		go func(quotaExport model.QuotationExport) {
			defer sema.Release()

			execCreateQuotationHedging(quotaExport, createType)
		}(*quotaExport)
	}

	sema.Wait()
}

// execCreateQuotationHedging processes the creation of a hedging quotation based on the provided quotation export data.
// It performs the following steps:
// 1. Updates the inbound request items to mark them as inactive.
// 2. Checks if the SKU and warehouse code should block bidding sessions.
// 3. Creates a new hedging quotation export record.
// 4. Adds the inbound request item ID to the hedging quotation.
// 5. Creates the hedging quotation and updates the expected quantity in the bidding rate if applicable.
//
// Parameters:
// - quotationExport: The data of the quotation export to be processed.
// - createMode: The mode in which the quotation should be created.
func execCreateQuotationHedging(quotationExport model.QuotationExport, createMode string) {
	// if len(quotationExport.InboundRequestItemApplieds) == 0 {
	// 	return
	// }

	// cập nhật lại để không tính quotation cho lần sau
	updateResp := model.InboundRequestItemDB.UpdateMany(
		model.InboundRequestItem{
			// SKU: quotation.SKU,
			// WarehouseCode: quotation.WarehouseCode,
			VendorCode:             utils.Pointer.WithString(quotationExport.VendorCode),
			InboundRequestType:     enum.InboundRequestType.OTHER,
			InboundRequestItemCode: quotationExport.InboundRequestItemCode,
			// ComplexQuery: []*bson.M{
			// 	{"inbound_request_item_code": bson.M{
			// 		"$in": quotationExport.InboundRequestItemApplieds,
			// 	}},
			// },
		},
		model.InboundRequestItem{
			IsActive: utils.Pointer.WithBool(false),
		},
	)
	if updateResp.Status == common.APIStatus.NotFound {
	} else if updateResp.Status != common.APIStatus.Ok {
		return
	}
	// tách hedging ra khỏi fav quotation
	// blockFavQuotation := isBlockFavQuotation(quotationExport.SKU, quotationExport.WarehouseCode)
	blockBiddingSession := isBlockBidding(quotationExport.SKU, quotationExport.WarehouseCode)

	quotationExport.ID = &primitive.NilObjectID
	quotationExport.CreatedTime = nil
	quotationExport.Type = enum.QuotationTypeVal.HEDGING
	// quotationExport.BlockReleaseToFavSession = &blockFavQuotation
	quotationExport.BlockReleaseToBiddingSession = &blockBiddingSession

	model.QuotationExportDB.Create(quotationExport)

	// Thêm Inbound request item ID vào quotation hedging
	quotation := model.Quotation{
		SellerCode:                   quotationExport.SellerCode,
		WarehouseCode:                quotationExport.WarehouseCode,
		ProductCode:                  quotationExport.ProductCode,
		ProductID:                    quotationExport.ProductID,
		ProductName:                  quotationExport.ProductName,
		SKU:                          quotationExport.SKU,
		QuantityExpect:               quotationExport.QuantityExpect,
		Unit:                         quotationExport.Unit,
		VendorCodes:                  &[]string{quotationExport.VendorCode},
		Type:                         quotationExport.Type,
		BlockReleaseToFavSession:     utils.Pointer.WithBool(false),
		BlockReleaseToBiddingSession: &blockBiddingSession,
		InboundRequestCode:           quotationExport.InboundRequestCode,
		InboundRequestID:             quotationExport.InboundRequestID,
		InboundRequestItemCode:       quotationExport.InboundRequestItemCode,
	}

	// nếu có vendor expect quantity không có hoặc <= 0 thì không tạo
	if quotation.QuantityExpect == nil || *quotation.QuantityExpect <= 0 {
		return
	}

	// nếu VendorExpectQuantity > 0 thì set lại QuantityExpect
	if quotationExport.VendorExpectQuantity != nil && *quotationExport.VendorExpectQuantity > 0 {
		quotation.QuantityExpect = quotationExport.VendorExpectQuantity
	}

	// create quotation
	createResp := CreateHedgingQuotation(
		CreateQuotationsInput{
			Items: []model.Quotation{quotation},
			Mode:  createMode,
		})

	// update expected quantity in bidding rate
	if createResp.Status == common.APIStatus.Ok {
		biddingRateResp := model.BiddingRateDB.QueryOne(model.BiddingRate{
			SKU: quotation.SKU, WarehouseCode: quotation.WarehouseCode,
			DaySpan: utils.GetCurrentVersionDDMMYYYY(),
		})
		if biddingRateResp.Status == common.APIStatus.Ok {
			biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]
			if biddingRate.ExpectedQuantity != nil && quotation.QuantityExpect != nil {
				newData := *biddingRate.ExpectedQuantity + *quotation.QuantityExpect
				model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{ExpectedQuantity: &newData})
			}

		}
	}

}

// isBlockFavQuotation checks if a given SKU code and warehouse code combination
// should block favorite quotations based on the SKU configuration and the current day of the week.
//
// Parameters:
// - skuCode: The SKU code to check.
// - warehouseCode: The warehouse code to check.
//
// Returns:
// - bool: Returns true if the favorite quotation should be blocked, false otherwise.
func isBlockFavQuotation(skuCode, warehouseCode string) bool {
	purchaseCode, _ := utils.WarehouseCodeToPurchaserCode(warehouseCode)

	if purchaseCode == "" {
		return false
	}

	skuConfigRes := model.SkuConfigReadDB.QueryOne(model.SkuConfig{
		SKU:           skuCode,
		PurchaserCode: purchaseCode,
	})

	// check response
	if skuConfigRes.Status != common.APIStatus.Ok {
		return false
	}

	skuConfig := skuConfigRes.Data.([]*model.SkuConfig)[0]
	if skuConfig.StopFavQuotation == nil || !*skuConfig.StopFavQuotation {
		return false
	}

	if skuConfig.StopFavDays == nil {
		return false
	}

	// get current day in week in Vietnam
	currentDate := time.Now().In(utils.TimeZoneVN)
	dayInWeek := currentDate.Weekday()

	// check if the current day is blocked
	switch dayInWeek {
	case time.Monday:
		return skuConfig.StopFavDays.Monday != nil && *skuConfig.StopFavDays.Monday
	case time.Tuesday:
		return skuConfig.StopFavDays.Tuesday != nil && *skuConfig.StopFavDays.Tuesday
	case time.Wednesday:
		return skuConfig.StopFavDays.Wednesday != nil && *skuConfig.StopFavDays.Wednesday
	case time.Thursday:
		return skuConfig.StopFavDays.Thursday != nil && *skuConfig.StopFavDays.Thursday
	case time.Friday:
		return skuConfig.StopFavDays.Friday != nil && *skuConfig.StopFavDays.Friday
	case time.Saturday:
		return skuConfig.StopFavDays.Saturday != nil && *skuConfig.StopFavDays.Saturday
	case time.Sunday:
		return skuConfig.StopFavDays.Sunday != nil && *skuConfig.StopFavDays.Sunday
	}

	return false
}

// isBlockBidding checks if bidding is blocked for a given SKU and warehouse code.
// It converts the warehouse code to a purchaser code and retrieves the SKU configuration.
// If the SKU configuration indicates that bidding should be stopped, the function returns true.
// Otherwise, it returns false.
//
// Parameters:
//   - skuCode: The SKU code to check.
//   - warehouseCode: The warehouse code to convert to a purchaser code.
//
// Returns:
//   - bool: True if bidding is blocked, false otherwise.
func isBlockBidding(skuCode, warehouseCode string) bool {
	purchaseCode, _ := utils.WarehouseCodeToPurchaserCode(warehouseCode)

	if purchaseCode == "" {
		return false
	}

	skuConfigRes := model.SkuConfigReadDB.QueryOne(model.SkuConfig{
		SKU:           skuCode,
		PurchaserCode: purchaseCode,
	})

	if skuConfigRes.Status != common.APIStatus.Ok {
		return false
	}

	skuConfig := skuConfigRes.Data.([]*model.SkuConfig)[0]

	return skuConfig.StopPublishBidding != nil && *skuConfig.StopPublishBidding
}
