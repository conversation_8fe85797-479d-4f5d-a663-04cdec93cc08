package api

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetPOList
func GetInboundRequestItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		qStr       = req.GetParam("q")
	)

	var query model.InboundRequestItem
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	query.SellerCode = strings.ToUpper(sellerCode)

	// do action
	return resp.Respond(action.GetInboundRequestItemList(query, offset, limit, getTotal))
}

// GetPOList
func GetInboundRequestList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		search     = req.GetParam("search")
		qStr       = req.GetParam("q")
	)

	var query model.InboundRequest
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	query.SellerCode = strings.ToUpper(sellerCode)

	// if want to search
	if len(search) > 0 {
		search := utils.ParserQ(search)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: search},
		})
	}

	if query.TagIn != nil && len(query.TagIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"tags": bson.M{"$in": query.TagIn}})
	}
	if len(query.InboundRequestCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"inbound_request_code": bson.M{
				"$in": query.InboundRequestCodeIn,
			},
		})
	}
	// filter product
	if len(query.POCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_codes": bson.M{"$in": query.POCodeIn}})
	}
	// filter product
	if len(query.SkuCodeIn) > 0 {
		queryItem := model.InboundRequestItem{
			SellerCode:         query.SellerCode,
			WarehouseCode:      query.WarehouseCode,
			InboundRequestType: query.InboundRequestType,
			ComplexQuery:       []*bson.M{{"sku": bson.M{"$in": query.SkuCodeIn}}},
		}
		mainCodeIn := action.GetListInboundRequestMainCodeFromItem(&queryItem, 0, 200, &bson.M{"_id": -1})

		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"inbound_request_code": bson.M{"$in": mainCodeIn}})
	}

	if query.ApplyTimeFrom != "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"apply_time": bson.M{
				"$gte": query.ApplyTimeFrom,
			},
		})
	}

	if query.ApplyTimeTo != "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"apply_time": bson.M{
				"$lte": query.ApplyTimeTo,
			},
		})
	}
	// do action
	return resp.Respond(action.GetInboundRequestList(query, offset, limit, getTotal))
}

// CreateInboundRequest ...
func CreateInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
	)
	var input model.InboundRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	input.SellerCode = strings.ToUpper(sellerCode)

	if input.WarehouseCode == "" || input.Name == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid WarehouseCode, Name",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}
	if input.InboundRequestType == enum.InboundRequestType.OTHER {
		if input.VendorCode == nil || *input.VendorCode == "" {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "VendorCode isn't empty",
				ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
			})
		}
		if input.Reason == "" {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Reason isn't empty",
				ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
			})
		}
		if _, exists := enum.InboundRequestReasonToOtherMap[input.Reason]; !exists {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid Reason",
			})
		}
	}

	if acc := getActionSource(req); acc != nil {
		// do action
		input.CreatedByID = acc.AccountID
		input.CreatedByName = acc.Fullname
		return resp.Respond(action.CreateInboundRequest(&input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// SwitchStatusInboundRequest ...
func SwitchStatusInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode = req.GetVar("sellerCode")
	)

	var input model.InboundRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	input.SellerCode = strings.ToUpper(sellerCode)

	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	if input.IsActive != nil {
		return resp.Respond(action.SwitchActiveInboundRequest(input.InboundRequestCode, input.IsActive))
	}

	// switch status
	switch input.Status {
	case enum.InboundRequestStatus.CONFIRMED:
		return resp.Respond(action.ConfirmInboundRequest(input.InboundRequestCode))
	case enum.InboundRequestStatus.CANCELED:
		return resp.Respond(action.CancelInboundRequest(input.InboundRequestCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Invalid status",
		ErrorCode: string(enum.ErrorCodeInvalid.Status),
	})
}

// UpdateInboundRequest
func UpdateInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequest

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// validate input
	if input.InboundRequestType == enum.InboundRequestType.OTHER {
		if input.VendorCode == nil || *input.VendorCode == "" {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "VendorCode isn't empty",
				ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
			})
		}
		if input.Reason == "" {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Reason isn't empty",
				ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
			})
		}
		if _, exists := enum.InboundRequestReasonToOtherMap[input.Reason]; !exists {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid Reason",
			})
		}
	}

	input.SellerCode = ""
	input.Status = ""
	return resp.Respond(action.UpdateInboundRequest(input))
}

// CloneInboundRequest
func CloneInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var inboundRequestCode = req.GetParam("inboundRequestCode")

	if inboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid inboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CloneInboundRequest(inboundRequestCode, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// GetInboundRequestItemContractPrice
func GetInboundRequestItemContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		warehouseCode  = req.GetParam("warehouseCode")
		vendorCode     = req.GetParam("vendorCode")
		requestType    = req.GetParam("requestType")
		applyTime      = req.GetParam("applyTime")
		productID      = sdk.ParseInt64(req.GetParam("productID"), 0)
		kind           = req.GetParam("kind")
		expectQuantity = sdk.ParseInt64(req.GetParam("expectQuantity"), 0)
		numDayInStock  = sdk.ParseInt64(req.GetParam("numDayInStock"), 0)
		sellerCode     = req.GetParam("sellerCode")
	)

	// validate input
	if productID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid productID",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}

	// validate input
	if vendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid vendorCode",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
		})
	}

	// validate input
	if requestType == "" || enum.InboundRequestTypeValue(requestType) != enum.InboundRequestType.OTHER {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid requestType",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestItemRequestType),
		})
	}

	// validate input
	if applyTime == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid applyTime",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestItemApplyTime),
		})
	}

	// validate input
	if warehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouseCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestWarehouseCode),
		})
	}

	applyTimeConverted, _ := time.Parse("2006-01-02", applyTime)

	if sellerCode == "" {
		sellerCode = "MEDX"
	}
	// do action
	return resp.Respond(action.GetInboundRequestItemContractPrice(sellerCode, warehouseCode, vendorCode, requestType, productID, applyTimeConverted, &kind, expectQuantity, numDayInStock))
}

// RefreshInboundRequestContractPrice
func RefreshInboundRequestContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// do action
	return resp.Respond(action.RefreshInboundRequestContractPrice(input))
}

// GetInboundRequestDeals
func GetInboundRequestDeal(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestDealRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// validate input
	if input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid WarehouseCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidInboundRequestWarehouseCode),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid VendorCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidInboundRequestVendorCode),
		})
	}

	// validate input
	if len(input.SKUs) == 0 || len(input.SKUs) > 5 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "SKU list must be between 1 and 5 items and not empty",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidInboundRequestSKUs),
		})
	}

	// do action
	return resp.Respond(action.GetInboundRequestDeal(input))
}

// UpdateInboundRequestItemDeal
func UpdateInboundRequestItemDeal(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestItemDealRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestItemCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestItemCode),
		})
	}

	// validate input
	if input.DealTicketCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid DealTicketCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidInboundRequestDealTicketCode),
		})
	}

	// validate input
	if input.SKU == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SKU",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidInboundRequestSKU),
		})
	}

	// do action
	return resp.Respond(action.UpdateInboundRequestItemDeal(input))
}

// CreateInboundRequestPurchaseOrder
func CreateInboundRequestPurchaseOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestPurchaseOrderRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateInboundRequestPurchaseOrder(input, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// ToolInboundRequest
func ToolInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequest
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestCode",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestCode),
		})
	}

	// do action
	return resp.Respond(action.ToolInboundRequest(input))
}

// InboundRequestItemValidateImport
func InboundRequestItemValidateImport(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestItemValidateImportReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ApplyTime == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid applyTime",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestItemApplyTime),
		})
	}

	// validate input
	if input.VendorID == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "VendorID isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if input.PromotionID == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "PromotionID isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	if input.SellerCode == "" {
		input.SellerCode = "MEDX"
	}
	// do action
	return resp.Respond(action.InboundRequestItemValidateImport(input))
}

// InboundRequestItemImport
func InboundRequestItemImport(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestItemImportReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ApplyTime == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ApplyTime isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.InboundRequestItemApplyTime),
		})
	}

	// validate input
	if input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if input.InboundRequestType == "" {
		input.InboundRequestType = enum.InboundRequestType.OTHER
	}

	// validate input
	if input.InboundRequestType != enum.InboundRequestType.OTHER {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid InboundRequestType",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if input.VendorID == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "VendorID isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if input.Reason == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if _, exists := enum.InboundRequestReasonToOtherMap[input.Reason]; !exists {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid Reason",
		})
	}

	// validate input
	if input.PromotionID == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "PromotionID isn't empty",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		// do action
		input.CreatedByID = acc.AccountID
		input.CreatedByName = acc.Fullname
		return resp.Respond(action.InboundRequestItemImport(input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// InboundRequestItemManualUpdate
func InboundRequestItemManualUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestItemManualUpdateReq
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.InboundRequestItemManualUpdate(input))
}
