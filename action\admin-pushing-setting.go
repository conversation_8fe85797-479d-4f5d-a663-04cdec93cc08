package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetAdminPushingSetting retrieves a list of AdminPushingSetting records from the database
// based on the provided query, offset, and limit. It also optionally retrieves the total
// count of records matching the query.
//
// Parameters:
//   - query: A pointer to an AdminPushingSetting model containing the query criteria.
//   - offset: The number of records to skip before starting to collect the result set.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to retrieve the total count of matching records.
//
// Returns:
//   - A pointer to an APIResponse containing the result set and optionally the total count.
func GetAdminPushingSetting(query *model.AdminPushingSetting, offset, limit int64, getTotal bool) *common.APIResponse {

	result := model.AdminPushingSettingDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if result.Status == common.APIStatus.Ok && getTotal {
		result.Total = model.AdminPushingSettingDB.Count(query).Total
	}

	return result
}

// UpdateAdminPushingSetting updates an existing admin pushing setting in the database.
// It first checks if the setting exists by querying with the provided AdminPushingSettingCode.
// If the setting does not exist, it returns the response from the query.
// If the setting exists, it updates the setting with the provided updater data.
//
// Parameters:
// - updater: A pointer to model.AdminPushingSetting containing the updated data.
//
// Returns:
// - *common.APIResponse: The response from the update operation, indicating success or failure.
func UpdateAdminPushingSetting(updater *model.AdminPushingSetting) *common.APIResponse {
	checkSettingResp := model.AdminPushingSettingDB.QueryOne(&model.AdminPushingSetting{
		AdminPushingSettingCode: updater.AdminPushingSettingCode,
	})

	if checkSettingResp.Status != common.APIStatus.Ok {
		return checkSettingResp
	}
	pushingSetting := checkSettingResp.Data.([]*model.AdminPushingSetting)[0]

	return model.AdminPushingSettingDB.UpdateOne(
		model.AdminPushingSetting{
			AdminPushingSettingCode: pushingSetting.AdminPushingSettingCode,
		},
		updater)
}

// CreateAdminPushingSetting creates a new admin pushing setting.
// If the RunTimeHHMM is earlier than the current time, it marks the setting as run today
// and schedules it to run the next day. If LastDayRun is empty, it sets it to the current date.
// It generates a new AdminPushingSettingID and AdminPushingSettingCode for the input setting,
// and then saves the setting to the database.
//
// Parameters:
//   - input: A pointer to an AdminPushingSetting model containing the setting details.
//
// Returns:
//   - *common.APIResponse: The response from the database after attempting to create the setting.
func CreateAdminPushingSetting(input *model.AdminPushingSetting) *common.APIResponse {
	// nếu RunTimeHHMM < hiện tại thì:
	// đánh dấu hôm nay chạy rồi
	// => setting này ngày mai mới chạy
	if input.LastDayRun == "" {
		now := time.Now().In(utils.TimeZoneVN)
		nowHHMM := now.Format("15:04")
		if nowHHMM > input.RunTimeHHMM {
			input.LastDayRun = now.Format("2006/01/02")
		}
	}

	input.AdminPushingSettingID, input.AdminPushingSettingCode = model.GetAdminPushingSettingID()
	checkSettingResp := model.AdminPushingSettingDB.Create(input)
	return checkSettingResp
}

// DeleteAdminPushingSetting ...
// DeleteAdminPushingSetting deletes an admin pushing setting from the database
// based on the provided adminPushingSettingID. It returns an APIResponse
// indicating the success or failure of the operation.
//
// Parameters:
// - adminPushingSettingID: The ID of the admin pushing setting to be deleted.
//
// Returns:
// - *common.APIResponse: The response from the database operation, containing
//   information about the success or failure of the deletion.
func DeleteAdminPushingSetting(adminPushingSettingID int64) *common.APIResponse {
	return model.AdminPushingSettingDB.Delete(&model.AdminPushingSetting{AdminPushingSettingID: adminPushingSettingID})
}
