package utils

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

var (
	INTERNAL_SELLERS            = []string{"MEDX", "MEDX-HN", "MARKETING", "MEDX_E", "BUYDENTAL", "DENX", "MED_NT", "TAM_GIA", "MEDC", "TENDER", "TAYAU", "CIRCA_INT", "BML_INT"}
	WAREHOUSES                  = []Warehouse{}
	WAREHOUSES_MODEL            = []*model.Warehouse{}
	DEFAULT_SELLER_CODE         = "MEDX"
	LIMIT_PRIORITY      int     = 3
	MAX_PRIORITY        int64   = 99999
	FAV_PRIORITY        []int64 = []int64{0}
	LOW_PRIORITY        []int64 = []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	ALL_PRIORITY        []int64 = []int64{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10} // vendor priority
	DEFAULT_PRIORITY    int64   = 1
	MAX_AUTO_PRIORITY   int64   = 10

	TimeZoneVN      *time.Location = time.FixedZone("UTC+7", +7*60*60)
	YYYYMMDD_ENDASH string         = "2006-01-02"
	YYYYMMDD        string         = "20060102"

	MAX_ACCEPT_UPDATE_PUBLISHED_PRICE_FROM_BIDDING_PERCENT = 50.0
)
