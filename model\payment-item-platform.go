package model

import (
	"fmt"
	"reflect"
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PaymentItemPlatform struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	OrgID           int64  `json:"orgID,omitempty"`
	PaymentCode     string `json:"paymentCode,omitempty" bson:"payment_code,omitempty"`
	PaymentItemCode string `json:"paymentItemCode,omitempty" bson:"payment_item_code,omitempty"`

	ObjectType    enum.PaymentPlatformObjectTypeValue `json:"objectType,omitempty" bson:"object_type,omitempty"`
	ObjectID      int64                               `json:"objectID,omitempty" bson:"object_id,omitempty"`
	ObjectCode    string                              `json:"objectCode,omitempty" bson:"object_code,omitempty"`
	ItemExtraData []PaymentItemPlatformRef            `json:"itemExtraData,omitempty" bson:"item_extra_data,omitempty"` // cho partner custom data, trường hợp cần lưu thêm thông tin
	Amount        *float64                            `json:"amount,omitempty" bson:"amount,omitempty"`

	Note   string                          `json:"note,omitempty" bson:"note,omitempty"`
	Source enum.PaymentCreatedBySourceEnum `json:"source,omitempty" bson:"source,omitempty"`
	// query
	ValidatePartnerCode string   `json:"validatePartnerCode,omitempty" bson:"-"`
	ExtraDataKeyValIn   []string `json:"extraDataKeyValIn,omitempty" bson:"-"`

	//support field thanh toán cho
	PaidTime           *time.Time           `json:"paidTime,omitempty" bson:""`
	VendorBillID       int64                `json:"vendorBillID,omitempty" bson:"-"`
	VendorBillCode     string               `json:"vendorBillCode,omitempty" bson:"-"`
	AdjustmentBillID   int64                `json:"adjustmentBillID,omitempty" bson:"-"`
	AdjustmentBillCode string               `json:"adjustmentBillCode,omitempty" bson:"-"`
	POCode             string               `json:"poCode,omitempty" bson:"-"`
	InvoiceNumber      string               `json:"invoiceNumber,omitempty" bson:"-"`
	PaymentVoucherCode string               `json:"paymentVoucherCode,omitempty" bson:"-"`
	ReceiptVoucherCode string               `json:"receiptVoucherCode,omitempty" bson:"-"`
	TransactionType    enum.TransactionType `json:"transactionType,omitempty" bson:"-"`

	LegacyPaymentItemCode string `json:"legacyPaymentItemCode,omitempty" bson:"legacy_payment_item_code,omitempty"`
	LegacyPaymentCode     string `json:"legacyPaymentCode,omitempty"`

	// suport field metadata
	PartnerCode   string `json:"partnerCode,omitempty" bson:"partner_code,omitempty"`
	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdById,omitempty" bson:"created_by_id,omitempty"`

	PartnerCodeIn        []string `json:"partnerCodeIn,omitempty" bson:"-"`
	VendorBillCodeIn     []string `json:"vendorBillCodeIn,omitempty" bson:"-"`
	AdjustmentBillCodeIn []string `json:"adjustmentBillCodeIn,omitempty" bson:"-"`

	PaymentCodeIn []string `json:"paymentCodeIn,omitempty" bson:"-"` // forward query
}

type PaymentItemPlatformRef struct {
	Key      string `json:"key,omitempty" bson:"key,omitempty"`
	Value    any    `json:"value,omitempty" bson:"value,omitempty"`
	KeyValue string `json:"keyValue,omitempty" bson:"key_value,omitempty"`
}

func (pi *PaymentItemPlatform) FillData(option ...OptionFillDataPaymentPlatform) {
	pi.Source = enum.PaymentCreatedBySource.THUOCSIVN_INTERNAL_SELLER
	pi.OrgID = conf.Config.OrgID
	opt := mergeOptionFillDataPaymentPlatform(option)
	if opt.ExtraData {
		if pi.VendorBillID > 0 {
			ref := PaymentItemPlatformRef{
				Key:      "VENDOR_BILL_ID",
				Value:    pi.VendorBillID,
				KeyValue: fmt.Sprintf("VENDOR_BILL_ID:%v", pi.VendorBillID),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.VendorBillCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "VENDOR_BILL_CODE",
				Value:    pi.VendorBillCode,
				KeyValue: fmt.Sprintf("VENDOR_BILL_CODE:%v", pi.VendorBillCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.AdjustmentBillID > 0 {
			ref := PaymentItemPlatformRef{
				Key:      "ADJUSTMENT_BILL_ID",
				Value:    pi.AdjustmentBillID,
				KeyValue: fmt.Sprintf("ADJUSTMENT_BILL_ID:%v", pi.AdjustmentBillID),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.AdjustmentBillCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "ADJUSTMENT_BILL_CODE",
				Value:    pi.AdjustmentBillCode,
				KeyValue: fmt.Sprintf("ADJUSTMENT_BILL_CODE:%v", pi.AdjustmentBillCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.POCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "PO_CODE",
				Value:    pi.POCode,
				KeyValue: fmt.Sprintf("PO_CODE:%v", pi.POCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.InvoiceNumber != "" {
			ref := PaymentItemPlatformRef{
				Key:      "INVOICE_NUMBER",
				Value:    pi.InvoiceNumber,
				KeyValue: fmt.Sprintf("INVOICE_NUMBER:%v", pi.InvoiceNumber),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.PaymentVoucherCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "PAYMENT_VOUCHER_CODE",
				Value:    pi.PaymentVoucherCode,
				KeyValue: fmt.Sprintf("PAYMENT_VOUCHER_CODE:%v", pi.PaymentVoucherCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.ReceiptVoucherCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "RECEIPT_VOUCHER_CODE",
				Value:    pi.ReceiptVoucherCode,
				KeyValue: fmt.Sprintf("RECEIPT_VOUCHER_CODE:%v", pi.ReceiptVoucherCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.TransactionType != "" {
			ref := PaymentItemPlatformRef{
				Key:      "TRANSACTION_TYPE",
				Value:    pi.TransactionType,
				KeyValue: fmt.Sprintf("TRANSACTION_TYPE:%v", pi.TransactionType),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.PaidTime != nil {
			ref := PaymentItemPlatformRef{
				Key:      "PAID_TIME",
				Value:    *pi.PaidTime,
				KeyValue: fmt.Sprintf("PAID_TIME:%v", *pi.PaidTime),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.CreatedByID > 0 {
			ref := PaymentItemPlatformRef{
				Key:      "CREATED_BY_ID",
				Value:    pi.CreatedByID,
				KeyValue: fmt.Sprintf("CREATED_BY_ID:%v", pi.CreatedByID),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.CreatedByName != "" {
			ref := PaymentItemPlatformRef{
				Key:      "CREATED_BY_NAME",
				Value:    pi.CreatedByName,
				KeyValue: fmt.Sprintf("CREATED_BY_NAME:%v", pi.CreatedByName),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.PartnerCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "PARTNER_CODE",
				Value:    pi.PartnerCode,
				KeyValue: fmt.Sprintf("PARTNER_CODE:%v", pi.PartnerCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

		if pi.LegacyPaymentItemCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "LEGACY_PAYMENT_ITEM_CODE",
				Value:    pi.LegacyPaymentItemCode,
				KeyValue: fmt.Sprintf("LEGACY_PAYMENT_ITEM_CODE:%v", pi.LegacyPaymentItemCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}
		if pi.LegacyPaymentCode != "" {
			ref := PaymentItemPlatformRef{
				Key:      "LEGACY_PAYMENT_CODE",
				Value:    pi.LegacyPaymentCode,
				KeyValue: fmt.Sprintf("LEGACY_PAYMENT_CODE:%v", pi.LegacyPaymentCode),
			}
			pi.ItemExtraData = append(pi.ItemExtraData, ref)
		}

	}
}

func (pi *PaymentItemPlatform) DecodeVendorBillID() int64 {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "VENDOR_BILL_ID" {
			value, _ := convertToNumber(ref.Value)
			return int64(value)
		}
	}
	return 0
}

func (pi *PaymentItemPlatform) DecodeVendorBillCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "VENDOR_BILL_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodeAdjustmentBillID() int64 {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "ADJUSTMENT_BILL_ID" {
			value, _ := convertToNumber(ref.Value)
			return int64(value)
		}
	}
	return 0
}

func (pi *PaymentItemPlatform) DecodeAdjustmentBillCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "ADJUSTMENT_BILL_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodePOCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "PO_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodeInvoiceNumber() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "INVOICE_NUMBER" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodePaymentVoucherCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "PAYMENT_VOUCHER_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodeReceiptVoucherCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "RECEIPT_VOUCHER_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodeTransactionType() enum.TransactionType {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "TRANSACTION_TYPE" {
			v, _ := ref.Value.(string)
			return enum.TransactionType(v)
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodePaidTime() *time.Time {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "PAID_TIME" {
			v, _ := ref.Value.(time.Time)
			return &v
		}
	}
	return nil
}

func (pi *PaymentItemPlatform) DecodeCreatedByID() int64 {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "CREATED_BY_ID" {
			value, _ := convertToNumber(ref.Value)
			return int64(value)
		}
	}
	return 0
}
func (pi *PaymentItemPlatform) DecodeCreatedByName() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "CREATED_BY_NAME" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) DecodeLegacyPaymentItemCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "LEGACY_PAYMENT_ITEM_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}
func (pi *PaymentItemPlatform) DecodeLegacyPaymentCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "LEGACY_PAYMENT_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}
func (pi *PaymentItemPlatform) DecodePartnerCode() string {
	for _, ref := range pi.ItemExtraData {
		if ref.Key == "PARTNER_CODE" {
			v, _ := ref.Value.(string)
			return v
		}
	}
	return ""
}

func (pi *PaymentItemPlatform) GenQueryAdjustmentBillCode([]string) []string {
	ouput := []string{}
	for _, refCode := range pi.AdjustmentBillCodeIn {
		ouput = append(ouput, "ADJUSTMENT_BILL_CODE:"+refCode)
	}
	return ouput

}

func (pi *PaymentItemPlatform) GenQueryVendorBillCode([]string) []string {
	ouput := []string{}
	for _, refCode := range pi.VendorBillCodeIn {
		ouput = append(ouput, "VENDOR_BILL_CODE:"+refCode)
	}
	return ouput
}

func (pi *PaymentItemPlatform) GenQueryPartnerCode([]string) []string {
	ouput := []string{}
	for _, refCode := range pi.PartnerCodeIn {
		ouput = append(ouput, "PARTNER_CODE:"+refCode)
	}
	return ouput
}

func (pi *PaymentItemPlatform) FillSuportField() {
	pi.VendorBillCode = pi.DecodeVendorBillCode()
	pi.VendorBillID = pi.DecodeVendorBillID()
	pi.AdjustmentBillCode = pi.DecodeAdjustmentBillCode()
	pi.AdjustmentBillID = pi.DecodeAdjustmentBillID()
	pi.POCode = pi.DecodePOCode()
	pi.InvoiceNumber = pi.DecodeInvoiceNumber()
	pi.PaymentVoucherCode = pi.DecodePaymentVoucherCode()
	pi.ReceiptVoucherCode = pi.DecodeReceiptVoucherCode()
	pi.PartnerCode = pi.DecodePartnerCode()
	pi.TransactionType = pi.DecodeTransactionType()
	pi.PaidTime = pi.DecodePaidTime()
	pi.CreatedByID = pi.DecodeCreatedByID()
	pi.CreatedByName = pi.DecodeCreatedByName()
	pi.LegacyPaymentCode = pi.DecodeLegacyPaymentCode()
	pi.LegacyPaymentItemCode = pi.DecodeLegacyPaymentItemCode()
	pi.TransactionType = pi.DecodeTransactionType()
}

func convertToNumber(i any) (float64, error) {
	v := reflect.ValueOf(i)
	switch v.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return float64(v.Int()), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return float64(v.Uint()), nil
	case reflect.Float32, reflect.Float64:
		return v.Float(), nil
	default:
		return 0, fmt.Errorf("unsupported type: %T", i)
	}
}
