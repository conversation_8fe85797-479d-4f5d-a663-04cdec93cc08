package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Setting struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"-" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"-" bson:"last_updated_time,omitempty"`

	// // time
	// TimeDelAfterLastInbound float64 `json:"timeDelAfterLastInbound,omitempty" bson:"time_del_after_last_inbound,omitempty"`
	// TimeDelDraft            float64 `json:"timeDelDraft,omitempty" bson:"time_del_draft,omitempty"`

	TimeAdminstration       float64 `json:"timeAdminstration,omitempty" bson:"time_adminstration,omitempty"`
	TimeUnassignQuotation   float64 `json:"timeUnassignQuotation,omitempty" bson:"time_unassign_quotation,omitempty"`
	TimeCreatePOFromBidding float64 `json:"timeCreatePOFromBidding,omitempty" bson:"time_create_po_from_bidding,omitempty"`
	TimeConfirmPOVendor     float64 `json:"timeConfirmPOVendor,omitempty" bson:"time_confirm_po_vendor,omitempty"`

	ValidHubMap             map[string]string                                                 `json:"validHubMap,omitempty" bson:"valid_hub_map,omitempty"`
	VaidPOStatusMap         map[enum.PurchaseOrderStatusValue][]enum.PurchaseOrderStatusValue `json:"vaidPOStatusMap,omitempty" bson:"vaid_po_status_map,omitempty"`
	VaidVendorBillStatusMap map[enum.VendorBillStatusValue][]enum.VendorBillStatusValue       `json:"vaidVendorBillStatusMap,omitempty" bson:"vaid_vendor_bill_status_map,omitempty"`

	// VendorErpMap map[string]int64 `json:"vendorErpMap,omitempty" bson:"vendor_erp_map,omitempty"` // vendorCode-warehouseCode

	DayDemands    map[string]int64 `json:"dayDemands,omitempty" bson:"day_demands,omitempty"`
	ListDaySkipSO map[string]bool  `json:"listDaySkipSO,omitempty" bson:"list_day_skip_so,omitempty"` // day skip calc demand
	POArrivedDay  int              `json:"poArrivedDay,omitempty" bson:"po_arrived_day,omitempty"`

	NumDayQueryNearExpiredDate int64 `json:"numDayQueryNearExpiredDate,omitempty" bson:"num_day_query_near_expired_date,omitempty"`

	SettingUpdatePrice *SettingUpdatePrice `json:"settingUpdatePrice,omitempty" bson:"setting_update_price,omitempty"`

	ZuelligIntegation *ZuelligIntegation `json:"zuelligIntegration,omitempty" bson:"zuellig_integration,omitempty"`

	StockDays *[]*StockDay `json:"stockDays,omitempty" bson:"stock_days,omitempty"`

	LayoutValidateUserRoles *map[string][]string `json:"layoutValidateUserRoles,omitempty" bson:"layout_validate_user_roles,omitempty"`

	BiddingRoundStep *map[string][]int64 `json:"biddingRoundStep,omitempty" bson:"bidding_round_step,omitempty"`
	// formula
	CalcPOExpectFormula string `json:"calcPOExpectFormula,omitempty" bson:"calc_po_expect_formula,omitempty"`
	//priority bidding
	BiddingPriority *BiddingPriority `json:"biddingPriority,omitempty" bson:"bidding_priority,omitempty"`

	EmailNotifyVendorChange *[]string `json:"emailNotifyVendorChange,omitempty" bson:"email_notify_vendor_change,omitempty"`

	TradingPriceConfig          *[]*TradingPriceConfig `json:"tradingPriceConfig,omitempty" bson:"trading_price_config,omitempty"`   //Hệ số tính giá trading
	NumberOfRepetitions         *int                   `json:"numberOfRepetitions,omitempty" bson:"number_of_repetitions,omitempty"` //Số lần lặp khi tính giá bán setting
	CalcPriceAfterRebateFormula string                 `json:"calcPriceAfterRebateFormula,omitempty" bson:"calc_price_after_rebate_formula,omitempty"`
	TimeCrawlSKUInterval        int64                  `json:"timeCrawlSKUInterval,omitempty" bson:"time_crawl_sku_interval,omitempty"` // minute
	TimeReservePriceAfterRebate int64                  `json:"timeReservePriceAfterRebate,omitempty" bson:"time_reserve_price_after_rebate,omitempty"`

	//sourceNumber
	SourceNumber                string              `json:"sourceNumber,omitempty" bson:"source_number,omitempty"`
	MapBankVP                   map[string]string   `json:"mapBankVP,omitempty" bson:"map_bank_vp,omitempty"`
	MapTradingNonTradingSellers map[string][]string `json:"mapTradingNonTradingSellers,omitempty" bson:"map_trading_non_trading_sellers,omitempty"`
	SettingVAT                  *SettingVAT         `json:"settingVAT,omitempty" bson:"setting_vat,omitempty"`

	// Auto update auto rebate price
	UpdateAfterRebatePrice *SettingUpdateAfterRebatePrice `json:"updateAfterRebatePrice,omitempty" bson:"update_after_rebate_price,omitempty"`

	UpperPercentile float64  `json:"upperPercentile,omitempty" bson:"upper_percentile,omitempty"`
	LowerPercentile float64  `json:"lowerPercentile,omitempty" bson:"lower_percentile,omitempty"`
	DistanceRatio   *float64 `json:"distanceRatio,omitempty" bson:"distance_ratio,omitempty"`

	SkipLineVBDictionary    []string `json:"skipLineVBDictionary,omitempty" bson:"skip_line_vb_dictionary,omitempty"`
	PendingLineVBDictionary []string `json:"pendingLineVBDictionary,omitempty" bson:"pending_line_vb_dictionary,omitempty"`

	// GroupMap
	// Key: hour (Ex: 00, 01, 02), Value: groupCode (Ex: 00-02, 02-04)
	HourGroupRangeMap map[string]string `json:"hourGroupRangeMap,omitempty" bson:"hour_group_range_map,omitempty"`
	DebugForDemand    *bool             `json:"debugForDemand,omitempty" bson:"debug_for_demand,omitempty"`

	SettingWarningValue              *SettingWarningValue              `json:"settingWarningValue,omitempty" bson:"setting_warning_value,omitempty"`
	TimeUpdateSKUStatusByBiddingRate *TimeUpdateSKUStatusByBiddingRate `json:"timeUpdateSKUStatusByBiddingRate,omitempty" bson:"time_update_sku_status_by_bidding_rate,omitempty"`

	AlertParseErrorBuymedAiInvoiceEmail []string  `json:"alertParseErrorBuymedAiInvoiceEmail,omitempty" bson:"alert_parse_error_buymed_ai_invoice_email,omitempty"`
	ConfigXMLByVendorSignatures         *[]string `json:"configXMLByVendorSignatures,omitempty" bson:"config_xml_by_vendor_signatures,omitempty"`

	// ConfigMatchInvoiceParse             *ConfigMatchInvoiceParse `json:"configMatchInvoiceParse,omitempty" bson:"config_match_invoice_parse,omitempty"`
	ConfigAprroveVBPPriceThreshold *ConfigAprroveVBPPriceThreshold `json:"configAprroveVBPPriceThreshold,omitempty" bson:"config_aprrove_vbp_price_threshold,omitempty"`
	VendorFillLotPdf               []string                        `json:"vendorFillLotPdf,omitempty" bson:"vendor_fill_lot_pdf,omitempty"`

	StopAutoApproveVB    bool `json:"stopAutoApproveVB,omitempty" bson:"stop_auto_approve_vb,omitempty"`
	StopComparePriceVbPo bool `json:"stopComparePriceVbPo,omitempty" bson:"stop_compare_price_vb_po,omitempty"`

	// StopStopRetryUnit bool                `json:"stopStopRetryUnit,omitempty" bson:"stop_stop_retry_unit,omitempty"` // remove in future
	MapSameUnits map[string][]string `json:"mapSameUnits,omitempty" bson:"map_same_units,omitempty"`

	ConfigXMLRuleBaseVendor *[]string `json:"configXMLRuleBaseVendor,omitempty" bson:"config_xml_rule_base_vendor,omitempty"`

	// LevenshteinScore *float64 `json:"levenshteinScore,omitempty" bson:"levenshtein_score,omitempty"`
	TransformerScore *float64 `json:"transformerScore,omitempty" bson:"transformer_score,omitempty"`

	UsernameBypassDuplicateValidate *BypassDuplicateValidate    `json:"userBypassDuplicateValidate,omitempty" bson:"user_bypass_duplicate_validate,omitempty"`
	SaveRebatePercentDefault        []*SaveRebatePercentDefault `json:"saveRebatePercentDefault,omitempty" bson:"save_rebate_percent_default,omitempty"`
	// auto update status PO
	QuantityCloseTimes map[string]int `json:"quantityCloseTimes,omitempty" bson:"quantity_close_times,omitempty"`
	IssueTicketTime    int            `json:"issueTicketTime,omitempty" bson:"issue_ticket_time,omitempty"`

	StopReportSearchProductVB *bool `json:"stopReportSearchProductVB,omitempty" bson:"stop_report_search_product_vb,omitempty"`
	// VB
	ReasonTagOption *[]*ReasonOption `json:"reasonTagOptions,omitempty" bson:"reason_tag_options,omitempty"`

	StopUseParseLotDate *bool `json:"stopUseParseLotDate,omitempty" bson:"stop_use_parse_lot_date,omitempty"`
}

type SaveRebatePercentDefault struct {
	WarehouseCode string  `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	LocationCode  string  `json:"locationCode,omitempty" bson:"location_code,omitempty"`
	RebatePercent float64 `json:"rebatePercent,omitempty" bson:"rebate_percent,omitempty"`
}

type BypassDuplicateValidate struct {
	Rebate []string `json:"rebate,omitempty" bson:"rebate,omitempty"`
}
type ConfigAprroveVBPPriceThreshold struct {
	PriceItem        *float64 `json:"priceItem,omitempty" bson:"price_item,omitempty"`
	PriceMain        *float64 `json:"priceMain,omitempty" bson:"price_main,omitempty"`
	PricePerUnitPoVb *float64 `json:"pricePerUnitPoVb,omitempty" bson:"price_per_unit_po_vb,omitempty"`
	PriceAllUnitPoVb *float64 `json:"priceAllUnitPoVb,omitempty" bson:"price_all_unit_po_vb,omitempty"`
}

//	type ConfigMatchInvoiceParse struct {
//		Buymed []string `json:"buymed,omitempty" bson:"buymed,omitempty"`
//		Growth []string `json:"growth,omitempty" bson:"growth,omitempty"`
//	}
type SettingVAT struct {
	DefaultTrading    *int  `json:"defaultTrading,omitempty" bson:"default_trading,omitempty"`
	DefaultNonTrading *int  `json:"defaultNonTrading,omitempty" bson:"default_non_trading,omitempty"`
	TradingVAT        []int `json:"tradingVAT,omitempty" bson:"trading_vat,omitempty"`
	NonTradingVAT     []int `json:"nonTradingVAT,omitempty" bson:"non_trading_vat,omitempty"`
}

type SettingUpdatePrice struct {
	RoleCanUpdatePriceOverMax []string `json:"roleCanUpdatePriceOverMax,omitempty" bson:"role_can_update_price_over_max,omitempty"`
	IncreasePercentPriceMax   float64  `json:"increasePercentPriceMax,omitempty" bson:"increase_percent_price_max,omitempty"`
	DecreasePercentPriceMax   float64  `json:"decreasePercentPriceMax,omitempty" bson:"decrease_percent_price_max,omitempty"`
}

type BiddingPriority struct {
	UnitPriceWeight       float64 `json:"unitPriceWeight,omitempty" bson:"unit_price_weight,omitempty"`
	PaymentTermWeight     float64 `json:"paymentTermWeight,omitempty" bson:"payment_term_weight,omitempty"`
	FulfillmentRateWeight float64 `json:"fulfillmentRateWeight,omitempty" bson:"fulfillment_rate_weight,omitempty"`
	BiddingTimeWeight     float64 `json:"biddingTimeWeight,omitempty" bson:"bidding_time_weight,omitempty"`
	DeviantWeight         float64 `json:"deviantWeight,omitempty" bson:"deviant_weight,omitempty"`
}

type StockDay struct {
	WarehouseCode         string  `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	MaxStockDay           float64 `json:"maxStockDay,omitempty" bson:"max_stock_day,omitempty"`
	UseMinSuggestQuantity *bool   `json:"useMinSuggestQuantity,omitempty" bson:"use_min_suggest_quantity,omitempty"`
	VendorCode            *string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	ReceiverCode          *string `json:"receiverCode,omitempty" bson:"receiver_code,omitempty"`
}

type ZuelligIntegation struct {
	MapWarehouseCodeToZuelligShipcode map[string]int64 `json:"mapWarehouseCodeZuelligShipcode,omitempty" bson:"map_warehouse_code_zuellig_shipcode,omitempty"`
	Emails                            []string         `json:"emails,omitempty" bson:"emails,omitempty"`
}

type TradingPriceConfig struct {
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	ProvinceCode  string `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	CustomerLevel string `json:"customerLevel,omitempty" bson:"customer_level,omitempty"`
}

type SettingUpdateAfterRebatePrice struct {
	Min *float64 `json:"min,omitempty" bson:"min,omitempty"`
	Max *float64 `json:"max,omitempty" bson:"max,omitempty"`

	MaxFixedPrice *float64 `json:"maxFixedPrice,omitempty" bson:"max_fixed_price,omitempty"`
}
type SettingWarningValue struct {
	ContactPrice *int64 `json:"contactPrice,omitempty" bson:"contact_price,omitempty"`
	Rebate       *int64 `json:"rebate,omitempty" bson:"rebate,omitempty"`
	Promotion    *int64 `json:"promotion,omitempty" bson:"promotion,omitempty"`
}

type TimeUpdateSKUStatusByBiddingRate struct {
	StartTime *int `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *int `json:"endTime,omitempty" bson:"end_time,omitempty"`
}

type ReasonOption struct {
	Value  string `json:"value,omitempty" bson:"value,omitempty"`
	En     string `json:"en,omitempty" bson:"en,omitempty"`
	Vi     string `json:"vi,omitempty" bson:"vi,omitempty"`
	Ticket string `json:"ticket,omitempty" bson:"ticket,omitempty"`
}

// SettingDB ...
var SettingDB = &db.Instance{
	ColName:        "setting",
	TemplateObject: &Setting{},
}

// InitSettingModel ...
func InitSettingModel(s *mongo.Database) {
	SettingDB.ApplyDatabase(s)
}
