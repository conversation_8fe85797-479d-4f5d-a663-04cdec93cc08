package enum

type FirstMileTicketStatusTypeValue string

type firstMileTicketStatusType struct {
	PACKING             FirstMileTicketStatusTypeValue
	WAITING_FOR_PICKING FirstMileTicketStatusTypeValue
	TRANSPORTING        FirstMileTicketStatusTypeValue
	DELIVERED           FirstMileTicketStatusTypeValue
	COMPLETED           FirstMileTicketStatusTypeValue
	CANCELLED           FirstMileTicketStatusTypeValue
}

var FirstMileTicketStatusType = &firstMileTicketStatusType{
	"PACKING",
	"WAITING_FOR_PICKING",
	"TRANSPORTING",
	"DELIVERED",
	"COMPLETED",
	"CANCELLED",
}
