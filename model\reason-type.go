package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ReasonType struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// unique
	ReasonTypeID int64 `json:"reasonTypeID" bson:"reason_type_id,omitempty"`

	Code string `json:"code" bson:"code,omitempty"`
	Name string `json:"name,omitempty" bson:"name,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []*bson.M `json:"-" bson:"$or,omitempty"`

	CodeIn []string `json:"codeIn,omitempty" bson:"-"`
}

// ReasonDB ...
var ReasonTypeDB = &db.Instance{
	ColName:        "reason_type",
	TemplateObject: &ReasonType{},
}

// InitReasonMappingModel ...
func InitReasonTypeMappingModel(s *mongo.Database) {
	ReasonTypeDB.ApplyDatabase(s)
}
