package action

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	// Hard code

	// map warehouse code to purchaser code
	warehousePurchaserMap = map[string]string{
		"DN": "PUR_DN",
		"BD": "PUR_HCM",
		"HN": "PUR_HN",
	}

	warehousesDefault = []string{"BD", "HN", "DN"}

	// map purchaser code to warehouse code
	purchaserToWarehouseMap = map[string]string{
		"PUR_DN":  "DN",
		"PUR_HCM": "BD",
		"PUR_HN":  "HN",
	}

	// map location code to region code
	warehouseToLocationCodeMap = map[string]string{
		"DN": "MIENTRUNG",
		"BD": "MIENNAM",
		"HN": "MIENBAC",
	}
)

// getLocationsToWarehouseDelivery takes a map of regions to their respective location codes,
// a slice of location codes, and a slice of warehouse data, and returns a slice of warehouse codes
// that can deliver to the given locations.
//
// Parameters:
// - regionMap: A map where the key is a region code and the value is a slice of location codes within that region.
// - locationCodes: A slice of location codes to be checked for warehouse delivery.
// - warehouseData: A slice of Warehouse objects containing information about warehouses and their delivery areas.
//
// Returns:
// - A slice of warehouse codes that can deliver to the given locations.
func getLocationsToWarehouseDelivery(regionMap map[string][]string, locationCodes []string, warehouseData []*model.Warehouse) []string {
	locationFlatten := []string{}
	for _, location := range locationCodes {
		if regionMap[location] != nil {
			locationFlatten = append(locationFlatten, regionMap[location]...)
		} else {
			locationFlatten = append(locationFlatten, location)
		}
	}

	var warehouseCodes []string
	// Check if the location is in the delivery area of the warehouse
	for _, location := range locationFlatten {
		for _, wh := range warehouseData {
			if utils.IsContains(wh.Areas, location) && !utils.IsContains(warehouseCodes, wh.Code) {
				warehouseCodes = append(warehouseCodes, wh.Code)
			}
		}
	}
	return warehouseCodes
}

// GetValidationDeal processes a DealRequest and returns an APIResponse containing the validation results.
// It performs the following steps:
// 1. Sets default values for the input if necessary.
// 2. Retrieves valid warehouses.
// 3. Gets SKU items and filters them by valid locations.
// 4. Retrieves initial stock drafts.
// 5. Gets SKU configurations and sellers.
// 6. Retrieves a map of seller codes.
// 7. Retrieves promotion items according to the client's seller/promotion code.
// 8. Constructs a deal map based on the input, SKU list, and vendor promotion.
// 9. Generates deal responses based on the gathered data.
//
// Parameters:
// - input: model.DealRequest containing the details of the deal to be validated.
//
// Returns:
// - *common.APIResponse: The API response containing the status and data of the validation process.
func GetValidationDeal(input model.DealRequest) *common.APIResponse {
	// Set default values if necessary
	setDefaultValues(&input)

	// Get valid warehouses
	dataWarehouses := getValidWarehouses()

	// Get SKU items and filter by valid locationsq
	locationItemCodesMap, skuList := getLocationItemCodesMap(input, dataWarehouses)

	// Get initial stock drafts
	initialStockDraftsMap := getInitialStockDraftsMap(input, skuList)

	// Get SKU configs and sellers
	skuConfigWarehouseHasVendorMap, skuConfigWarehouseWithoutVendorMap, sellerCodes := getSkuConfigs(input, skuList)

	// Get seller codes map
	sellerCodesMap := getSellerCodesMap(sellerCodes)

	// get promotion items accroding client seller/promotion
	vendorPromotion, _, _, _ := getVendorPromotionItems(input.VendorPromotionCode)

	dealMap := mapDealFromVendorPromotion(input, skuList, vendorPromotion)

	// Get deal responses
	data := getDealResponses(locationItemCodesMap, initialStockDraftsMap, skuConfigWarehouseHasVendorMap, skuConfigWarehouseWithoutVendorMap, sellerCodesMap, dealMap, vendorPromotion, input)

	if len(data) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No valid sku to create deal",
		}
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   data,
	}
}

// Helper functions
func mapDealFromVendorPromotion(input model.DealRequest, skuList []string, vendorPromotion *model.VendorPromotion) map[string][]*model.MarketplaceDeal {

	if vendorPromotion == nil {
		return nil
	}
	if len(skuList) == 0 {
		return nil
	}

	startTimeTemp := vendorPromotion.StartTime
	// format lai start time.
	// startime sẽ là trước 1 ngày diễn ra sản phẩm và có giờ là 8h
	// startTime = startTime.AddDate(0, 0, -1)
	startTime8AM_Before1D := time.Date(startTimeTemp.Year(), startTimeTemp.Month(), startTimeTemp.Day(), 8, 0, 0, 0, startTimeTemp.Location()).AddDate(0, 0, -1)

	skuLists := strings.Join(skuList, ",")

	// lấy tất cả deal:
	// + endTimeFrom: startTime8AM_Before1D
	// + forSku: skuLists
	// + status: ACTIVE
	// + owner: MARKETPLACE
	// + sellerCode: MEDX
	dealResp := marketplace.GetListDeal(client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]interface{}{
			"endTimeFrom": startTime8AM_Before1D,
			"forSku":      skuLists,
			"status":      "ACTIVE",
			"owner":       "MARKETPLACE",
			"sellerCode":  "MEDX",
		},
		Keys: []string{"GET_EXIST_DEAL"},
	})

	if dealResp.Status != common.APIStatus.Ok {
		return nil
	}

	dealInfos := dealResp.Data.([]*model.MarketplaceDeal)
	mapDeal := make(map[string][]*model.MarketplaceDeal)
	// duyệt qua tất cả deal, nếu không thỏa điều kiện sẽ bỏ qua
	// 1. nguồn tạo đơn từ CTKM_ID. source đang có dạng "CTKM_ID : 1234"
	// 2. VendorPromoInfo phải có dữ liệu và VendorPromoInfo.vendorPromotionCode != ""
	for _, dealInfo := range dealInfos {

		if dealInfo.Source == "" || !strings.Contains(dealInfo.Source, "CTKM_ID") {
			continue
		}
		if dealInfo.VendorPromoInfo == nil {
			continue
		}

		if dealInfo.VendorPromoInfo.PromoCode == vendorPromotion.VendorPromotionCode {
			dealInfo.VendorPromotionCode = vendorPromotion.VendorPromotionCode
		}
		// Lấy sku đầu tiên của deal
		if len(dealInfo.SKUs) == 0 {
			continue
		}
		firstSKU := dealInfo.SKUs[0]
		if len(firstSKU.ItemCodes) == 0 {
			continue
		}

		firstItemCode := firstSKU.ItemCodes[0]
		// key map deal theo item code
		mapDeal[firstItemCode] = append(mapDeal[firstItemCode], dealInfo)
	}

	return mapDeal
}

// setDefaultValues sets default values for the fields in the DealRequest struct.
// If the WarehouseCodes field contains "00", it replaces it with a default set of warehouse codes.
// If the SellerCode field is empty, it sets it to "MEDX".
func setDefaultValues(input *model.DealRequest) {
	if utils.IsContains(input.WarehouseCodes, "00") {
		input.WarehouseCodes = warehousesDefault
	}
	if input.SellerCode == "" {
		input.SellerCode = "MEDX"
	}
}

// getValidWarehouses retrieves a list of valid warehouses from the warehouse management system (WMS).
// It fetches warehouses with a status of "ACTIVE" and a group code of "BUYMED".
// The function returns a slice of pointers to model.Warehouse objects that are considered valid based on predefined criteria.
//
// Returns:
//
//	[]*model.Warehouse: A slice of pointers to valid Warehouse objects, or nil if the API call fails.
func getValidWarehouses() []*model.Warehouse {
	option := client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]string{
			"status":    "ACTIVE",
			"groupCode": "BUYMED",
		},
	}
	// Call the GetWarehouse function from the warehouse client package
	getWMSResp := warehouse.GetWarehouse(option)

	// Check if the API call was successful
	// If not, return nil
	if getWMSResp.Status != common.APIStatus.Ok {
		return nil
	}

	warehouses := getWMSResp.Data.([]*model.Warehouse)
	var dataWarehouses []*model.Warehouse
	for _, warehouse := range warehouses {
		if utils.IsContains(warehousesDefault, warehouse.Code) {
			dataWarehouses = append(dataWarehouses, warehouse)
		}
	}
	return dataWarehouses
}

// getRegionMap returns a map of region codes to their respective location codes.
func GetValidWarehouses() []*model.Warehouse {
	option := client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]string{
			"status": "ACTIVE",
		},
	}
	// Call the GetWarehouse function from the warehouse client package
	getWMSResp := warehouse.GetWarehouse(option)

	// Check if the API call was successful
	// If not, return nil
	if getWMSResp.Status != common.APIStatus.Ok {
		return nil
	}

	// Filter out warehouses that are not in the default list
	warehouses := getWMSResp.Data.([]*model.Warehouse)
	var dataWarehouses []*model.Warehouse
	for _, warehouse := range warehouses {
		if utils.IsContains(warehousesDefault, warehouse.Code) {
			dataWarehouses = append(dataWarehouses, warehouse)
		}
	}
	return dataWarehouses
}

// getValidWarehouseByCode retrieves a list of valid warehouses by their code.
// It fetches the warehouses from the warehouse management system (WMS) using
// the provided API options, which include a limit of 100 and a query for active
// warehouses. If the API response status is not OK, it returns nil. Otherwise,
// it filters the warehouses to include only those whose codes are present in
// the predefined list of default warehouses and returns the filtered list.
//
// Returns:
//
//	[]*model.Warehouse: A slice of pointers to valid Warehouse objects.
func getValidWarehouseByCode() []*model.Warehouse {
	option := client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]string{
			"status": "ACTIVE",
		},
	}

	// Call the GetWarehouse function from the warehouse client package
	getWMSResp := warehouse.GetWarehouse(option)
	if getWMSResp.Status != common.APIStatus.Ok {
		return nil
	}

	// Filter out warehouses that are not in the default list
	warehouses := getWMSResp.Data.([]*model.Warehouse)
	var dataWarehouses []*model.Warehouse
	// Filter out warehouses that are not in the default list
	for _, warehouse := range warehouses {
		if utils.IsContains(warehousesDefault, warehouse.Code) {
			dataWarehouses = append(dataWarehouses, warehouse)
		}
	}
	return dataWarehouses
}

// getLocationItemCodesMap processes a DealRequest and a list of Warehouses to generate a map of SKU items
// associated with their respective location codes and a list of SKU strings.
//
// Parameters:
// - input: model.DealRequest containing the product IDs and seller code.
// - dataWarehouses: A slice of pointers to model.Warehouse representing the available warehouses.
//
// Returns:
// - A map where the key is a string combining SKU and location code, and the value is a pointer to model.SKUItem.
// - A slice of strings containing the SKUs of the processed items.
func getLocationItemCodesMap(input model.DealRequest, dataWarehouses []*model.Warehouse) (map[string]*model.SKUItem, []string) {
	regionMap := getRegionMap()
	locationItemCodesMap := make(map[string]*model.SKUItem)
	skuList := make([]string, 0)
	// Get SKU items and filter by valid locations
	for _, productID := range input.ProductIDs {
		option := client.APIOption{
			Keys:  []string{fmt.Sprint(productID), input.SellerCode, model.CLASS_INTERNAL},
			Limit: utils.Pointer.WithInt(1000),
			Q: model.SKUItem{
				ProductID:   productID,
				SellerCode:  input.SellerCode,
				SellerClass: model.CLASS_INTERNAL,
			},
		}
		// Call the GetListSKUItem function from the marketplace client package
		skusResp := marketplace.GetListSKUItem(option)
		if skusResp.Status != common.APIStatus.Ok {
			continue
		}

		skus := skusResp.Data.([]*model.SKUItem)
		// Get the first SKU item from the response
		skuList = append(skuList, skusResp.Data.([]*model.SKUItem)[0].SKU)

		// Filter out SKU items that are not in the default list
		for _, skuItem := range skus {
			if skuItem == nil || skuItem.LocationCodes == nil {
				continue
			}

			location := getLocationsToWarehouseDelivery(regionMap, *skuItem.LocationCodes, dataWarehouses)
			if len(location) == 0 {
				continue
			}
			skuItem.LocationWarehouse = location[0]
			locationItemCodesMap[fmt.Sprintf("%s_%s", skuItem.SKU, location[0])] = skuItem
		}
	}
	return locationItemCodesMap, skuList
}

// getInitialStockDraftsMap retrieves a map of initial stock drafts based on the provided deal request and SKU list.
// It queries the InitialStockDraftDB with the given SKU list and warehouse codes from the input DealRequest.
// If the query is successful, it returns a map where the key is a combination of SKU and warehouse code, and the value is the corresponding InitialStockDraft.
// If the query fails, it returns nil.
//
// Parameters:
//   - input: model.DealRequest containing the warehouse codes to filter the initial stock drafts.
//   - skuList: []string containing the SKUs to filter the initial stock drafts.
//
// Returns:
//   - map[string]*model.InitialStockDraft: A map of initial stock drafts keyed by a combination of SKU and warehouse code.
//   - nil: If the query fails or no initial stock drafts are found.
func getInitialStockDraftsMap(input model.DealRequest, skuList []string) map[string]*model.InitialStockDraft {
	// Query the InitialStockDraftDB with the given SKU list and warehouse codes
	initialStockDraftResp := model.InitialStockDraftDB.Query(model.InitialStockDraft{
		ComplexQuery: []*bson.M{
			{
				"sku": bson.M{
					"$in": skuList,
				},
			},
			{
				"warehouse_code": bson.M{
					"$in": input.WarehouseCodes,
				},
			},
		},
	}, 0, 1000, nil)

	if initialStockDraftResp.Status != common.APIStatus.Ok {
		return nil
	}

	// Create a map of initial stock drafts keyed by a combination of SKU and warehouse code
	initialStockDrafts := initialStockDraftResp.Data.([]*model.InitialStockDraft)
	initialStockDraftsMap := make(map[string]*model.InitialStockDraft)
	for _, initialStockDraft := range initialStockDrafts {
		initialStockDraftsMap[fmt.Sprintf("%s_%s", initialStockDraft.SKU, initialStockDraft.WarehouseCode)] = initialStockDraft
	}
	return initialStockDraftsMap
}

// getSkuConfigs retrieves SKU configurations based on the provided DealRequest and SKU list.
// It returns three values:
// 1. A map of SKU configurations that have vendors, keyed by a combination of SKU and warehouse code.
// 2. A map of SKU configurations that do not have vendors, keyed by a combination of SKU and warehouse code.
// 3. A slice of seller codes associated with the SKU configurations.
//
// Parameters:
// - input: A DealRequest object containing the details of the deal.
// - skuList: A slice of strings representing the list of SKUs to query.
//
// Returns:
// - map[string]*model.SkuConfig: A map of SKU configurations that have vendors.
// - map[string]*model.SkuConfig: A map of SKU configurations that do not have vendors.
// - []string: A slice of seller codes associated with the SKU configurations.
func getSkuConfigs(input model.DealRequest, skuList []string) (map[string]*model.SkuConfig, map[string]*model.SkuConfig, []string) {
	skuConfigWarehouseHasVendorMap := make(map[string]*model.SkuConfig)
	skuConfigWarehouseWithoutVendorMap := make(map[string]*model.SkuConfig)
	sellerCodes := make([]string, 0)

	// Get the purchaser code for the warehouse
	for _, warehouseCode := range input.WarehouseCodes {
		purchaserCode, exists := warehousePurchaserMap[warehouseCode]
		if !exists {
			continue
		}

		skuConfigsResp := model.SkuConfigDB.Query(model.SkuConfig{
			PurchaserCode: purchaserCode,
			ComplexQuery: []*bson.M{
				{
					"sku": bson.M{
						"$in": skuList,
					},
				},
			},
		}, 0, 1000, nil)

		// Check if the API call was successful
		if skuConfigsResp.Status != common.APIStatus.Ok {
			continue
		}
		// Filter out SKU configurations that do not have vendors
		skuConfigs := skuConfigsResp.Data.([]*model.SkuConfig)

		for _, skuConfig := range skuConfigs {
			if skuConfig.Vendors == nil || len(*skuConfig.Vendors) == 0 {
				continue
			}

			hasVendor := false
			var favVendorCode = ""
			// Check if the SKU configuration has a vendor with priority 0
			for _, vendor := range *skuConfig.Vendors {
				if vendor.Priority != nil && *vendor.Priority == 0 {
					favVendorCode = vendor.VendorCode
					if vendor.VendorCode == input.VendorCode {
						skuConfigWarehouseHasVendorMap[fmt.Sprintf("%s_%s", skuConfig.SKU, warehouseCode)] = skuConfig
						sellerCodes = append(sellerCodes, vendor.VendorCode)
						hasVendor = true
					} else {
						skuConfigWarehouseWithoutVendorMap[fmt.Sprintf("%s_%s", skuConfig.SKU, warehouseCode)] = skuConfig
						sellerCodes = append(sellerCodes, vendor.VendorCode)
					}
				}
			}

			// if sku config have fav vendor, do not get child vendor
			// get vendor in line-manager
			if !hasVendor && favVendorCode != "" {
				opt := client.APIOption{
					Keys: []string{input.SellerCode, model.CLASS_VENDOR},
					Q: map[string]string{
						"sellerClass": model.CLASS_VENDOR,
						"lineManager": input.VendorCode,
					},
					Offset: utils.Pointer.WithInt(0),
					Limit:  utils.Pointer.WithInt(1000),
				}

				// Call the GetSellers function from the seller client package
				getSellersResp := seller.GetSellers(opt)
				if getSellersResp.Status == common.APIStatus.Ok {
					childVendors := getSellersResp.Data.([]*model.Seller)
					for _, vendor := range childVendors {
						// nếu fav vendor là vendor con
						if favVendorCode == vendor.Code {
							skuConfigWarehouseHasVendorMap[fmt.Sprintf("%s_%s", skuConfig.SKU, warehouseCode)] = skuConfig
							sellerCodes = append(sellerCodes, vendor.Code)
						}
					}
				}
			}
		}
	}

	// Return the SKU configurations that have vendors, the SKU configurations that do not have vendors, and the seller codes
	return skuConfigWarehouseHasVendorMap, skuConfigWarehouseWithoutVendorMap, sellerCodes
}

// getSellerCodesMap retrieves a map of seller codes to Seller objects.
// It takes a slice of seller codes as input and returns a map where the keys
// are seller codes and the values are pointers to Seller objects.
//
// Parameters:
//
//	sellerCodes []string: A slice of seller codes to be retrieved.
//
// Returns:
//
//	map[string]*model.Seller: A map where the keys are seller codes and the values
//	are pointers to Seller objects.
//
// The function makes an API call to retrieve sellers based on the provided seller codes
// and a predefined seller class. It then constructs and returns a map of seller codes
// to Seller objects if the API call is successful.
func getSellerCodesMap(sellerCodes []string) map[string]*model.Seller {
	sellersResp := seller.GetSellers(client.APIOption{
		Params: map[string]string{
			"sellerCodes": strings.Join(sellerCodes, ","),
			"sellerClass": model.CLASS_VENDOR,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1000),
	})

	// Check if the API call was successful
	sellerCodesMap := make(map[string]*model.Seller)
	if sellersResp.Status == common.APIStatus.Ok {
		sellers := sellersResp.Data.([]*model.Seller)
		for _, seller := range sellers {
			sellerCodesMap[seller.Code] = seller
		}
	}
	return sellerCodesMap
}

// getDealResponses processes deal responses based on various input maps and configurations.
// It generates a list of DealResponse objects by iterating through the locationItemCodesMap
// and applying various business logic and conditions.
//
// Parameters:
// - locationItemCodesMap: A map of SKUItem objects keyed by location item codes.
// - initialStockDraftsMap: A map of InitialStockDraft objects keyed by SKU+warehouse.
// - skuConfigWarehouseHasVendorMap: A map of SkuConfig objects for warehouses with vendors.
// - skuConfigWarehouseWithoutVendorMap: A map of SkuConfig objects for warehouses without vendors.
// - sellerCodesMap: A map of Seller objects keyed by seller codes.
// - dealMap: A map of MarketplaceDeal slices keyed by item codes.
// - vendorPromotion: A VendorPromotion object containing promotion details.
// - input: A DealRequest object containing the input request details.
//
// Returns:
// - A slice of DealResponse objects containing the processed deal responses.
func getDealResponses(locationItemCodesMap map[string]*model.SKUItem, initialStockDraftsMap map[string]*model.InitialStockDraft, skuConfigWarehouseHasVendorMap map[string]*model.SkuConfig, skuConfigWarehouseWithoutVendorMap map[string]*model.SkuConfig, sellerCodesMap map[string]*model.Seller, dealMap map[string][]*model.MarketplaceDeal, vendorPromotion *model.VendorPromotion, input model.DealRequest) []*model.DealResponse {
	var (
		data                       []*model.DealResponse
		tempStartTime, tempEndTime time.Time
	)

	if vendorPromotion != nil && vendorPromotion.StartTime != nil && vendorPromotion.EndTime != nil {
		vpStart := *vendorPromotion.StartTime
		tempStartTime = time.Date(vpStart.Year(), vpStart.Month(), vpStart.Day(), 8, 0, 0, 0, utils.TimeZoneVN)

		vpEnd := *vendorPromotion.EndTime
		tempEndTime = time.Date(vpEnd.Year(), vpEnd.Month(), vpEnd.Day(), 23, 59, 0, 0, utils.TimeZoneVN)

	}

	// Get deal responses
	for key, value := range locationItemCodesMap {
		if !utils.IsContains(input.WarehouseCodes, value.LocationWarehouse) {
			continue
		}

		dealResp := &model.DealResponse{}
		// get initial stock draft by sku+warehouse
		if initialStockDraft, exists := initialStockDraftsMap[key]; exists {
			dealResp.SellerCode = initialStockDraft.SellerCode
			dealResp.WarehouseCode = initialStockDraft.WarehouseCode
			dealResp.SKU = initialStockDraft.SKU
			dealResp.AVGDemand = initialStockDraft.AVGDemand
			dealResp.MaxQuantity = initialStockDraft.MaxQuantity
		} else {
			dealResp.SKU = value.SKU
			dealResp.WarehouseCode = value.LocationWarehouse
		}

		dealResp.ProductCode = value.ProductCode
		dealResp.ProductID = value.ProductID
		dealResp.SellerCode = value.SellerCode
		dealResp.Status = value.Status
		dealResp.SellerClass = value.SellerClass
		dealResp.RetailPriceType = value.RetailPriceType
		dealResp.RetailPriceValue = value.RetailPriceValue
		dealResp.ItemCode = value.ItemCode
		dealResp.IsValid = utils.Pointer.WithBool(false)
		dealResp.IsShow = utils.Pointer.WithBool(false)
		// check sku config have vendor or not
		if skuConfigWarehous, exists := skuConfigWarehouseHasVendorMap[key]; exists {
			if value.RetailPriceType == "FIXED_REVENUE" {
				dealResp.IsValid = utils.Pointer.WithBool(true)
				dealResp.IsShow = utils.Pointer.WithBool(true)
			}

			dealResp.Seller = getSeller(sellerCodesMap, skuConfigWarehous.Vendors)
		} else if skuConfigWarehouseWithoutVendor, exists := skuConfigWarehouseWithoutVendorMap[key]; exists {
			dealResp.Seller = getSeller(sellerCodesMap, skuConfigWarehouseWithoutVendor.Vendors)
		}

		// get price after rebate
		calcPriceAfterRebate := getCalcPriceAfterRebateByVendorPromotion(dealResp.ProductID, dealResp.WarehouseCode, dealResp.Seller, input.VendorPromotionCode, input.VendorCode)
		if calcPriceAfterRebate != nil {
			dealResp.PriceAfterRebateWithAdhocPromotion = calcPriceAfterRebate.PriceAfterRebateWithAdhocPromotion
		}

		// đã có deal đang tạo với sku này chưa
		if dealInfos, isOk := dealMap[value.ItemCode]; isOk && len(dealInfos) > 0 {

			for _, dealInfo := range dealInfos {
				if dealInfo == nil {
					continue
				}

				// nếu deal đã taọ từ chính CTKM này thi trả về
				if vendorPromotion.VendorPromotionCode == dealInfo.VendorPromotionCode {
					dealResp.IsShow = utils.Pointer.WithBool(true)
					dealResp.DealCode = dealInfo.Code
					dealResp.DealTicketCode = dealInfo.TicketCode
					break
				}

				if tempStartTime.Before(dealInfo.EndTime) && tempEndTime.After(dealInfo.EndTime) {
					tempStartTime = dealInfo.EndTime.Add(time.Minute * 1)
				}

				if tempEndTime.After(dealInfo.StartTime) && tempStartTime.Before(dealInfo.StartTime) {
					tempEndTime = dealInfo.StartTime.Add(time.Minute * -1)
				}

			}
			if tempStartTime.Before(tempEndTime) {
				start := tempStartTime.UTC()
				end := tempEndTime.UTC()
				dealResp.StartTime = &start
				dealResp.EndTime = &end
			} else {
				dealResp.IsValid = utils.Pointer.WithBool(false)
			}

		}

		if dealResp.IsShow != nil && *dealResp.IsShow {
			data = append(data, dealResp)
		}

		// nếu seller tồn tại và có line manager thì trả về thêm line manager
		if dealResp.Seller != nil && dealResp.Seller.LineManager != "" {
			lineManagerCode := dealResp.Seller.LineManager

			// get vendor
			getVendorResp := seller.GetSellers(client.APIOption{
				Q: model.Seller{
					SellerClass: model.CLASS_VENDOR,
				},
				Limit: utils.Pointer.WithInt(1),
				Params: map[string]string{
					"sellerCode": lineManagerCode,
					// "getPaymentInfo": "true",
				},
			})

			if getVendorResp.Status != common.APIStatus.Ok {
				continue
			}

			dealResp.SellerLineManager = getVendorResp.Data.([]*model.Seller)[0]
		}
	}

	// sort data by product id
	sort.Slice(data, func(i, j int) bool {
		return data[i].ProductID < data[j].ProductID
	})

	return data
}

// getSeller returns a pointer to a Seller from the sellerCodesMap based on the provided vendors list.
// It iterates through the vendors and checks if the vendor's Priority is set to 0.
// If a matching vendor is found in the sellerCodesMap, the corresponding Seller is returned.
// If vendors is nil or no matching vendor is found, it returns nil.
//
// Parameters:
//   - sellerCodesMap: A map where the key is a string representing the vendor code and the value is a pointer to a Seller.
//   - vendors: A pointer to a slice of VendorConfig.
//
// Returns:
//   - A pointer to a Seller if a matching vendor with Priority 0 is found in the sellerCodesMap, otherwise nil.
func getSeller(sellerCodesMap map[string]*model.Seller, vendors *[]model.VendorConfig) *model.Seller {
	if vendors == nil {
		return nil
	}

	for _, vendor := range *vendors {
		if vendor.Priority != nil && *vendor.Priority == 0 {
			if seller, exists := sellerCodesMap[vendor.VendorCode]; exists {
				return seller
			}
		}
	}
	return nil
}

// GetPriceForDeal retrieves the price for a given product deal based on the provided product ID, SKU code, and location codes.
// It performs the following steps:
// 1. Splits the location codes into an array and validates them.
// 2. Retrieves valid warehouses and maps regions to warehouse deliveries.
// 3. Validates the locations and ensures there is exactly one location.
// 4. Retrieves SKU information from the marketplace.
// 5. Checks if the SKU is a combo product.
// 6. If the product is not a combo, it retrieves the price for the deal by product ID.
// 7. If the product is a combo, it retrieves the prices for each child product SKU and calculates the total combo price.
//
// Parameters:
// - productID: The ID of the product.
// - skuCode: The SKU code of the product.
// - locationCodes: A comma-separated string of location codes.
//
// Returns:
// - A pointer to a common.APIResponse containing the status, message, and data (price information) of the deal.
func GetPriceForDeal(productID int64, skuCode string, locationCodes string) *common.APIResponse {
	// Split the location codes into an array
	arrLocationCodes := strings.Split(locationCodes, ",")
	if len(arrLocationCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "locationCodes is required",
		}
	}

	// Get valid warehouses
	warehouses := getValidWarehouses()
	if len(warehouses) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Warehouses not found",
		}
	}

	// Map regions to warehouse deliveries
	regionMap := getRegionMap()

	// Get locations to warehouse delivery
	locations := getLocationsToWarehouseDelivery(regionMap, arrLocationCodes, warehouses)
	if len(locations) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Locations not found",
		}
	}
	if len(locations) > 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Locations must be 1",
		}
	}

	// Get purchaser code for the warehouse
	purchaserCode := warehousePurchaserMap[locations[0]]

	// get setting price
	skusResp := marketplace.GetListSKUItem(client.APIOption{
		Limit: utils.Pointer.WithInt(1),
		Q: model.SKUItem{
			SKU: skuCode,
			LocationCodeIn: []string{
				warehouseToLocationCodeMap[locations[0]],
			},
		},
	})

	// Check if the API call was successful
	if skusResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "SKU not found",
		}
	}

	sku := skusResp.Data.([]*model.SKUItem)[0]

	// check item is combo or not
	var isCombo bool
	if sku.IsCombo != nil && *sku.IsCombo {
		isCombo = true
	}

	// if product is not combo
	if !isCombo {
		resp := GetPriceForDealByProductID(productID, skuCode, locations[0], purchaserCode)
		if resp.Status != common.APIStatus.Ok {
			return resp
		}
		// get price after rebate
		data := resp.Data.([]interface{})
		priceForDeal, ok := data[0].(*model.PriceForDeal)
		if !ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid data type",
			}
		}

		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []interface{}{priceForDeal},
		}
	}

	// if product is combo
	// get SKU of child product
	subsSku := *sku.SKUs
	if len(subsSku) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "SKU not found",
		}
	}

	var priceOfCombo float64
	var SubPriceForDeals []model.SubPriceForDeal
	for _, subSku := range subsSku {
		// var subPriceForDeal model.SubPriceForDeal
		// subPriceForDeal.Quantity = subSku.Quantity
		resp := GetPriceForDealByProductID(subSku.ProductID, subSku.SKU, locations[0], purchaserCode)
		if resp.Status != common.APIStatus.Ok {
			return resp
		}
		// get price after rebate
		data := resp.Data.([]interface{})
		priceForDeal, ok := data[0].(*model.PriceForDeal)
		if !ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid data type",
			}
		}
		// calculate total price of combo
		tempTotalPrice := *priceForDeal.Price * float64(subSku.Quantity)
		subSkuForDeal := model.SubPriceForDeal{
			Quantity:   subSku.Quantity,
			Price:      priceForDeal.Price,
			Source:     priceForDeal.Source,
			TotalPrice: &tempTotalPrice,
			ProductID:  subSku.ProductID,
			ItemCode:   subSku.ItemCode,
			SKU:        subSku.SKU,
		}
		SubPriceForDeals = append(SubPriceForDeals, subSkuForDeal)
		priceOfCombo += tempTotalPrice
	}

	// return total price of combo
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data: []interface{}{model.PriceForDeal{
			Price:   &priceOfCombo,
			Source:  fmt.Sprintf("%v|%v|%v|%v|%v", "COMBO_PRICE", purchaserCode, skuCode, locations[0], priceOfCombo),
			SubSku:  SubPriceForDeals,
			IsCombo: isCombo,
		}},
	}
}

// GetPriceForDealByProductID retrieves the price for a deal based on the provided product ID, SKU code, location code, and purchaser code.
// It first attempts to fetch SKU configuration from the database. If the configuration is not found, it retrieves the SKU item from the marketplace
// and uses its retail price. If the SKU configuration is found, it checks for a favorite vendor with priority 0. If no favorite vendor is found,
// it falls back to getting the price by bill, PO item, and setting price. If a favorite vendor is found, it retrieves vendor information and
// calculates the price after rebate.
//
// Parameters:
// - productID: The ID of the product.
// - skuCode: The SKU code of the product.
// - locationCode: The location code where the product is stored.
// - purchaserCode: The code of the purchaser.
//
// Returns:
// - *common.APIResponse: The API response containing the price for the deal or an error message.

/*
1. Get price after rebate
2. If (1) is not exists then get vendor bill lastest
3. If (2) is not exists then get contract price lastest
4. If (3) is not exists then get setting price
*/
func GetPriceForDealByProductID(productID int64, skuCode string, locationCode, purchaserCode string) *common.APIResponse {
	resp := &model.PriceForDeal{}

	// get sku config
	skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
		ProductID:     productID,
		SellerCode:    "MEDX",
		PurchaserCode: purchaserCode,
	})
	// Check if the API call was successful
	if skuConfigResp.Status != common.APIStatus.Ok {
		// if sku config not found, get price by bill, po item and setting price
		skusResp := marketplace.GetListSKUItem(client.APIOption{
			Limit: utils.Pointer.WithInt(1),
			Q: model.SKUItem{
				SKU: skuCode,
				LocationCodeIn: []string{
					warehouseToLocationCodeMap[locationCode],
				},
			},
		})

		// Check if the API call was successful
		if skusResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "SKU not found",
			}
		}

		// get price by setting price
		sku := skusResp.Data.([]*model.SKUItem)[0]

		resp.Price = utils.Pointer.WithFloat64(float64(sku.RetailPriceValue))
		resp.Source = fmt.Sprintf("%v|%v|%v|%v|%v", "SETTING_PRICE", warehouseToLocationCodeMap[locationCode], sku.SKU, sku.RetailPriceType, sku.RetailPriceValue)
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []interface{}{resp},
		}
	}

	// get sku config
	skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]

	// get favorite vendor
	favoriteVendorCode := ""
	if skuConfig.Vendors != nil {
		for _, vendor := range *skuConfig.Vendors {
			if vendor.Priority != nil && *vendor.Priority == 0 {
				favoriteVendorCode = vendor.VendorCode
				break
			}
		}
	}

	// if sku config have favorite vendor
	if favoriteVendorCode == "" {
		return GetPriceByBillAndPOItemAndSettingPrice(skuConfig, resp, locationCode)
	}

	// get vendor info
	vendorInfoResp := GetVendorInfo(favoriteVendorCode)
	if vendorInfoResp.Status != common.APIStatus.Ok {
		return vendorInfoResp
	}

	// get vendor code
	vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]
	vendorCode := vendorInfo.Code
	if len(vendorInfo.LineManager) > 0 {
		vendorCode = vendorInfo.LineManager
	}

	warehouseCode := purchaserToWarehouseMap[skuConfig.PurchaserCode]
	version := utils.GetCurrentVersionYYYYMMDD()

	otps := client.APIOption{
		Params: map[string]string{
			"version":       version,
			"warehouseCode": warehouseCode,
			"vendorCode":    vendorCode,
			"productID":     fmt.Sprint(productID),
		},
	}

	// get price after rebate
	getPriceAfterRebateResp := seller.GetPriceAfterRebateForDeal(otps)
	if getPriceAfterRebateResp.Status != common.APIStatus.Ok {
		return GetPriceByBillAndPOItemAndSettingPrice(skuConfig, resp, locationCode)
	}

	resp.Price = getPriceAfterRebateResp.Data.([]*model.PriceForDeal)[0].Price
	resp.Source = fmt.Sprintf("%v|%v|%v|%v|%v", "PRICE_AFTER_REBATE", skuConfig.PurchaserCode, skuConfig.SKU, version, favoriteVendorCode)

	// return price after rebate
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{resp},
	}
}

// GetPriceVendorBillByProductCode retrieves the latest VendorBillItem for a given product code and purchaser code.
// It first queries the VendorBill collection to find the most recent VendorBill that matches the provided product code and purchaser code.
// If a matching VendorBill is found, it then queries the VendorBillItem collection to find the corresponding VendorBillItem for the given product code.
// If both queries are successful, it returns the VendorBillItem; otherwise, it returns nil.
//
// Parameters:
//   - productCode: The code of the product to search for.
//   - purchaserCode: The code of the purchaser to search for.
//
// Returns:
//   - *model.VendorBillItem: The latest VendorBillItem that matches the given product code and purchaser code, or nil if no matching item is found.
func GetPriceVendorBillByProductCode(productCode, purchaserCode string) *model.VendorBillItem {
	// get vendor bill
	getVBResp := model.VendorBillDB.Query(model.VendorBill{
		ComplexQuery: []*bson.M{
			{
				"product_codes": bson.M{
					"$in": []string{productCode},
				},
			},
		},
		SellerCode:    "MEDX",
		PurchaserCode: purchaserCode,
	}, 0, 1, &primitive.M{"_id": -1})
	if getVBResp.Status != common.APIStatus.Ok {
		return nil
	}

	// get vendor bill item
	vendorBill := getVBResp.Data.([]*model.VendorBill)[0]
	// get vendor bill item
	getVBItem := model.VendorBillItemDB.Query(model.VendorBillItem{
		VendorBillCode: vendorBill.VendorBillCode,
		ProductCode:    productCode,
	}, 0, 1, &primitive.M{"_id": -1})
	if getVBItem.Status != common.APIStatus.Ok {
		return nil
	}

	vbItem := getVBItem.Data.([]*model.VendorBillItem)[0]
	return vbItem
}

// GetPOItemByProductCode retrieves a PurchaseOrderItem based on the provided product code and purchaser code.
// It first queries the PurchaseOrder database to find a purchase order that matches the given product code and purchaser code.
// If a matching purchase order is found, it then queries the PurchaseOrderItem database to find the corresponding purchase order item.
// If either query fails or no matching items are found, it returns nil.
//
// Parameters:
//   - productCode: The code of the product to search for.
//   - purchaserCode: The code of the purchaser to search for.
//
// Returns:
//   - *model.PurchaseOrderItem: A pointer to the matching PurchaseOrderItem, or nil if no match is found.
func GetPOItemByProductCode(productCode, purchaserCode string) *model.PurchaseOrderItem {
	// get po
	getPOResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
		ComplexQuery: []*bson.M{
			{
				"skus": bson.M{
					"$in": []string{fmt.Sprintf("%s.%s", "MEDX", productCode)},
				},
			},
		},
		SellerCode:    "MEDX",
		PurchaserCode: purchaserCode,
	}, 0, 1, &primitive.M{"_id": -1})

	if getPOResp.Status != common.APIStatus.Ok {
		return nil
	}

	po := getPOResp.Data.([]*model.PurchaseOrder)[0]

	// get po item
	getPOItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode:      po.POCode,
		ProductCode: productCode,
	}, 0, 1, &primitive.M{"_id": -1})
	if getPOItemResp.Status != common.APIStatus.Ok {
		return nil
	}

	poItem := getPOItemResp.Data.([]*model.PurchaseOrderItem)[0]
	return poItem
}

// GetPriceByBillAndPOItemAndSettingPrice retrieves the price for a given SKU configuration by checking vendor bills, purchase orders, and setting prices.
// It returns an API response containing the price and its source.
//
// Parameters:
// - skuConfig: A pointer to the SKU configuration model.
// - resp: A pointer to the PriceForDeal model where the price and source will be stored.
// - locationCode: A string representing the location code.
//
// Returns:
// - A pointer to an APIResponse containing the status, message, and data (if any).
//
// The function follows these steps:
// 1. If the SKU configuration is nil, it returns a NotFound status.
// 2. It attempts to get the price from vendor bills using the product code and purchaser code.
// 3. If no vendor bill price is found, it attempts to get the price from purchase orders using the product code and purchaser code.
// 4. If no purchase order price is found, it attempts to get the setting price from the marketplace using the SKU and location code.
// 5. The price and its source are stored in the resp parameter and returned in the API response.

// Logic: get price according: Bill -> POItem -> Setting Price
func GetPriceByBillAndPOItemAndSettingPrice(skuConfig *model.SkuConfig, resp *model.PriceForDeal, locationCode string) *common.APIResponse {
	if skuConfig == nil {
		return &common.APIResponse{
			Status: common.APIStatus.NotFound,
		}
	}

	// get price by bill
	getPrice := GetPriceVendorBillByProductCode(skuConfig.ProductCode, skuConfig.PurchaserCode)
	if getPrice == nil {
		// get PO
		getPOItem := GetPOItemByProductCode(skuConfig.ProductCode, skuConfig.PurchaserCode)
		if getPOItem == nil {

			// get setting price
			skusResp := marketplace.GetListSKUItem(client.APIOption{
				Limit: utils.Pointer.WithInt(1),
				Q: model.SKUItem{
					SKU: skuConfig.SKU,
					LocationCodeIn: []string{
						warehouseToLocationCodeMap[locationCode],
					},
				},
			})
			if skusResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:  common.APIStatus.NotFound,
					Message: "SKU not found",
				}
			}

			// get price by setting price
			sku := skusResp.Data.([]*model.SKUItem)[0]
			resp.Price = utils.Pointer.WithFloat64(float64(sku.RetailPriceValue))
			resp.Source = fmt.Sprintf("%v|%v|%v|%v|%v", "SETTING_PRICE", skuConfig.PurchaserCode, sku.SKU, sku.RetailPriceType, sku.RetailPriceValue)
			// return price by setting price
			return &common.APIResponse{
				Status: common.APIStatus.Ok,
				Data:   []interface{}{resp},
			}
		}

		// get price by POItem
		resp.Price = &getPOItem.UnitPrice
		resp.Source = fmt.Sprintf("%v|%v|%v|%v|%v", "PURCHASE_ORDER", skuConfig.PurchaserCode, getPOItem.ProductCode, getPOItem.UnitPrice, getPOItem.POCode)
		// return price by POItem
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []interface{}{resp},
		}
	}

	// get price by bill
	resp.Price = getPrice.Price
	resp.Source = fmt.Sprintf("%v|%v|%v|%v|%v", "VENDOR_BILL", skuConfig.PurchaserCode, getPrice.ProductCode, getPrice.Price, getPrice.VendorBillCode)
	// return price by bill
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{resp},
	}
}

// getCalcPriceAfterRebateByVendorPromotion calculates the price after rebate based on vendor promotion.
// It takes the following parameters:
// - productID: the ID of the product.
// - warehouseCode: the code of the warehouse.
// - sellerResp: the seller response containing seller information.
// - vendorPromotionCode: the code of the vendor promotion.
// - vendorCode: the code of the vendor.
//
// It returns a pointer to a CalcPriceAfterRebateResp struct containing the calculated price after rebate.
// If the seller response is nil or if there is an error during the calculation, it returns nil.
func getCalcPriceAfterRebateByVendorPromotion(productID int64, warehouseCode string, sellerResp *model.Seller, vendorPromotionCode string, vendorCode string) *model.CalcPriceAfterRebateResp {
	if sellerResp == nil {
		return nil
	}

	// get vendor info
	vendorInfoResp := GetVendorInfo(vendorCode)
	if vendorInfoResp.Status == common.APIStatus.Ok {
		vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]
		if len(vendorInfo.LineManager) > 0 {
			vendorCode = vendorInfo.LineManager
		}
	}
	// get contract price and contract price item
	applyTime := time.Now().In(utils.TimeZoneVN).Format("2006-01-02")
	timeNow := time.Now().In(utils.TimeZoneVN)
	queryCalcPriceAfterRebateRequest := model.CalcPriceAfterRebateRequest{
		ProductID:     productID,
		ApplyTime:     strings.Replace(applyTime, "-", "", -1),
		WarehouseCode: warehouseCode,
		VendorCode:    vendorCode,
		PromotionCode: &vendorPromotionCode,
	}

	// get contract price and contract price item
	contractPrice, contractPriceItem, err := getLastestContractPrice(queryCalcPriceAfterRebateRequest.VendorCode, timeNow, productID, []string{queryCalcPriceAfterRebateRequest.WarehouseCode})
	if err == nil {
		if contractPrice != nil {
			queryCalcPriceAfterRebateRequest.ContractPriceCode = &contractPrice.ContractPriceCode
		}
		if contractPriceItem != nil {
			queryCalcPriceAfterRebateRequest.Discount = contractPriceItem.Discount
			queryCalcPriceAfterRebateRequest.VAT = contractPriceItem.VAT
			queryCalcPriceAfterRebateRequest.UnitPrice = contractPriceItem.UnitPrice
		}
	}

	// get price after rebate
	keys := []string{
		fmt.Sprintf("%v", queryCalcPriceAfterRebateRequest.ProductID),
		queryCalcPriceAfterRebateRequest.WarehouseCode,
		queryCalcPriceAfterRebateRequest.ApplyTime,
		queryCalcPriceAfterRebateRequest.WarehouseCode,
	}
	if queryCalcPriceAfterRebateRequest.ContractPriceCode != nil {
		keys = append(keys, *queryCalcPriceAfterRebateRequest.ContractPriceCode)
	}
	if queryCalcPriceAfterRebateRequest.PromotionCode != nil {
		keys = append(keys, *queryCalcPriceAfterRebateRequest.PromotionCode)
	}

	opts := client.APIOption{
		Keys: keys,
		Body: queryCalcPriceAfterRebateRequest,
	}

	// Call the PostCalcPriceAfterRebate function from the seller client package
	calcPriceAfterRebateResp := seller.PostCalcPriceAfterRebate(opts)
	if calcPriceAfterRebateResp.Status == common.APIStatus.Ok {
		calcPriceAfterRebate := calcPriceAfterRebateResp.Data.([]*model.CalcPriceAfterRebateResp)[0]
		return calcPriceAfterRebate
	}

	return nil
}

func GetDealCodesCreatedByVendor(sku, vendorCode string) []string {
	var dealCodes []string
	dealResp := marketplace.GetListDeal(client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]interface{}{
			"endTimeFrom": utils.GetVietnamTimeNow(),
			"forSku":      sku,
			"status":      "ACTIVE",
			"owner":       "MARKETPLACE",
			"sellerCode":  "MEDX",
		},
		Keys: []string{"GET_EXIST_DEAL"},
	})

	if dealResp.Status != common.APIStatus.Ok {
		return nil
	}

	dealInfos := dealResp.Data.([]*model.MarketplaceDeal)

	// duyệt qua tất cả deal, nếu không thỏa điều kiện sẽ bỏ qua
	// 1. nguồn tạo đơn từ CTKM_ID. source đang có dạng "CTKM_ID : 1234"
	// 2. VendorPromoInfo phải có dữ liệu và VendorPromoInfo.vendorPromotionCode != ""
	for _, dealInfo := range dealInfos {

		if dealInfo.Source == "" || !strings.Contains(dealInfo.Source, "CTKM_ID") {
			continue
		}
		if dealInfo.VendorPromoInfo == nil || dealInfo.VendorPromoInfo.Source == "" {
			continue
		}
		// Lấy sku đầu tiên của deal
		if len(dealInfo.SKUs) == 0 {
			continue
		}

		// data mẫu của source gồm
		// "source": "PRICE_AFTER_REBATE|PUR_DN|MEDX.DMC-TAY-001|20250320|MXX7STYACB"
		// LOẠI_LẤY GIÁ | PURCHASER  | SKU CODE | NGÀY ĐỔI | VENDOR CODE
		// arraySource := strings.Split(dealInfo.Source, "|")
		// if vendorCode != arraySource[len(arraySource)-1] {
		// 	continue
		// }

		dealCodes = append(dealCodes, dealInfo.Code)
	}

	return dealCodes

}
