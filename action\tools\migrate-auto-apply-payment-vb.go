package tools

import (
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MigrateAutoApplyPaymentVBStruct struct {
	VendorBillIDIn []int64 `json:"vendorBillIDIn,omitempty"`
}

func MigrateAutoApplyPaymentVB(req sdk.APIRequest, resp sdk.APIResponder) error {
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "not allow",
	})
	input := MigrateAutoApplyPaymentVBStruct{}
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	if len(input.VendorBillIDIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "vendorBillIDIn is required",
		})
	}
	migrateAutoApplyPaymentVB(input.VendorBillIDIn)
	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
	})
}

func migrateAutoApplyPaymentVB(vendorBillIDIn []int64) {
	return
	log.Println("START::migrateAutoApplyPaymentVB")
	defer log.Println("END::migrateAutoApplyPaymentVB")

	_id := primitive.NilObjectID

	count := 0
	for {
		resp := model.VendorBillDB.Query(model.VendorBill{
			Status: enum.VendorBillStatus.WAIT_TO_PAID,
			ComplexQuery: []*bson.M{
				{
					"_id": bson.M{"$gt": _id},
				},
				{
					"vendor_bill_id": bson.M{"$in": vendorBillIDIn},
				},
			},
		}, 0, 1000, &bson.M{"_id": 1})
		if resp.Status != common.APIStatus.Ok {
			break
		}

		vendorBills := resp.Data.([]*model.VendorBill)
		for i := range vendorBills {
			vendorBill := vendorBills[i]
			_id = *vendorBill.ID

			// get payment
			paymentResp := action.GetNewPayment(
				model.PaymentPlatform{
					PartnerCode:     vendorBill.VendorCode,
					Type:            enum.PaymentPlatformType.PAYMENT,
					PrepaidPOCodeIn: []string{vendorBill.POCode},
				},
				0, 1000, model.QueryOption{Items: true}, "-_id",
			)
			if paymentResp.Status != common.APIStatus.Ok {
				continue
			}

			// check đã có phiếu thanh toán cho VB này
			payments := paymentResp.Data.([]*model.PaymentPlatform)
			isHasPayment := false
			for j := range payments {
				payment := payments[j]
				for k := range payment.Items {
					item := payment.Items[k]
					if item.ObjectCode == vendorBill.VendorBillCode {
						isHasPayment = true
						break
					}
				}
			}
			if isHasPayment {
				continue
			}

			resp := action.AutoApplyPaymentVoucherToVB(vendorBill.VendorBillCode)
			if resp.Status != common.APIStatus.Ok {
				log.Println("ERROR::migrateAutoApplyPaymentVB::", vendorBill.VendorBillCode, resp.Message)
				continue
			}
			count++

		}
	}

	log.Println("DONE::migrateAutoApplyPaymentVB::totalUpdate:", count)
}
