package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotationProductVendor struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	// PrWhHashtag string `json:"prWhHashtag,omitempty" bson:"pr_wh_hashtag,omitempty"`

	IsLowPriority *bool `json:"isLowPriority" bson:"is_low_priority,omitempty"`

	// For query
	ComplexQuery    []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductIDIn     []int64   `json:"productIDIn,omitempty" bson:"-"`
	WarehouseCodes  []string  `json:"warehouseCodes,omitempty" bson:"-"`
	ProductIDNotIn  []int64   `json:"productIDNotIn,omitempty" bson:"-"`
	PurchaserCodeIn []string  `json:"purchaserCodeIn,omitempty" bson:"-"`
	// PrWhHashtagIn    []string  `json:"prWhHashtagIn,omitempty" bson:"-"`
	// PrWhHashtagNotIn []string  `json:"prWhHashtagNotIn,omitempty" bson:"-"`
}

// QuotationProductVendorDB ...
var QuotationProductVendorDB = &db.Instance{
	ColName:        "quotation_product_vendor",
	TemplateObject: &QuotationProductVendor{},
}

func InitQuotationProductVendorModel(s *mongo.Database) {
	QuotationProductVendorDB.ApplyDatabase(s)

	// t := true
	// _ = QuotationProductVendorDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
