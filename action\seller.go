package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

func GetVendorInfo(vendorCode string) *common.APIResponse {

	if vendorCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "No vendor information found",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
		}
	}
	// Call get info vendor
	option := client.APIOption{
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  vendorCode,
		},
	}
	vendorResp := seller.GetSellers(option)

	if vendorResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "No vendor information found",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
		}
	}

	// Check if vendor is active
	vendors := vendorResp.Data.([]*model.Seller)

	if len(vendors) > 1 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Many vendor is same info.",
		}
	}
	if len(vendors) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "No vendor information found",
		}
	}

	// Check if vendor is active
	vendor := vendors[0]
	if vendor.Code != vendorCode {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "No vendor information found",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
		}
	}

	// Check if vendor is active
	if vendor.Status != "ACTIVE" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor inactivated",
			ErrorCode: string(enum.ErrorCodeInvalid.VendorInactivated),
		}
	}

	// Check if vendor is active
	return &common.APIResponse{
		Status:  vendorResp.Status,
		Message: vendorResp.Message,
		Data:    []*model.Seller{vendor},
	}
}
