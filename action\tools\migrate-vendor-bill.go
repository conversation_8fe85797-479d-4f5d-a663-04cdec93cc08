package tools

import (
	"log"
	"math"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateCancelVendorBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	vendor_bill_ids := []int64{}
	if err := req.GetContent(&vendor_bill_ids); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	for _, vbId := range vendor_bill_ids {
		resp := model.VendorBillDB.QueryOne(&model.VendorBill{VendorBillID: vbId})
		if resp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill not found", vbId)
			continue
		}

		vb := resp.Data.([]*model.VendorBill)[0]

		if vb.Status != enum.VendorBillStatus.DRAFT {
			log.Println(vb.VendorBillID, vb.Status, "is not in draft status")
			continue
		}

		respSwitchStatus := action.CancelVendorBill(
			model.VendorBill{VendorBillCode: vb.VendorBillCode, Status: enum.VendorBillStatus.CANCEL},
		)

		if respSwitchStatus.Status != common.APIStatus.Ok {
			log.Println("Can not switch status vendor bill", vbId, respSwitchStatus.Message)
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cancel vendor bill done",
	})
}

func MigrateVendorBillItemRecalcPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	vendor_bill_ids := []int64{}
	if err := req.GetContent(&vendor_bill_ids); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	for _, vbId := range vendor_bill_ids {
		vbResp := model.VendorBillDB.QueryOne(&model.VendorBill{VendorBillID: vbId})
		if vbResp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill not found", vbId)
			continue
		}

		vb := vbResp.Data.([]*model.VendorBill)[0]
		vbItemResp := model.VendorBillItemDB.Query(&model.VendorBillItem{VendorBillID: vb.VendorBillID}, 0, 1000, nil)
		if vbItemResp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill item not found", vbId)
			continue
		}

		vbItems := vbItemResp.Data.([]*model.VendorBillItem)
		for i := range vbItems {
			item := vbItems[i]
			if item.Price == nil || item.Quantity == nil {
				continue
			}

			{
				totalUnitPrice := (*item.Price) * float64(*item.Quantity)

				// skip line km
				if totalUnitPrice <= 0 {
					continue
				}
				if item.DiscountPercent == nil {
					*item.DiscountPercent = 0
				}
				if item.DiscountPrice == nil {
					*item.DiscountPrice = 0
				}
				if item.PriceAfterDiscount == nil {
					*item.PriceAfterDiscount = 0
				}

				if *item.DiscountPercent > 0 {
					// discount_price = unit_price / 100 * discount_percent
					(*item.TotalDiscountPrice) = totalUnitPrice / 100 * (*item.DiscountPercent)
				} else if *item.TotalDiscountPrice > 0 {
					// discount_percent = discount_price / unit_price * 100
					(*item.TotalDiscountPrice) = totalUnitPrice / math.Max(1, totalUnitPrice) * 100
				}

				// discount price of single unit
				discountPrice := (*item.TotalDiscountPrice) / float64(*item.Quantity)
				item.DiscountPrice = &discountPrice

				totalPriceAfterDiscount := totalUnitPrice - *item.TotalDiscountPrice

				// PriceAfterDiscount
				if item.Price != nil && item.Quantity != nil && item.TotalDiscountPrice != nil {
					if *item.Quantity != 0 {
						priceAfterDiscount := ((totalUnitPrice) - (*item.TotalDiscountPrice)) / float64(*item.Quantity)
						item.PriceAfterDiscount = &priceAfterDiscount
					}
				}

				vatPrice := 0.0
				if item.Vat != nil || *item.Vat > 0 {
					// vat_price = price_after_discount / 100 * vat_percent
					vatPrice = (totalPriceAfterDiscount) / 100 * (*item.Vat)
					item.VatPrice = &vatPrice
				}

				// total_price = price_after_discount + vat_price
				totalPrice := (totalPriceAfterDiscount) + vatPrice
				item.TotalPrice = &totalPrice

				resp := model.VendorBillItemDB.UpdateOne(
					model.VendorBillItem{
						ID: item.ID,
					}, item)
				if resp.Status != common.APIStatus.Ok {
					log.Println("Can not update vendor bill item", item.VendorBillItemCode, resp.Message)
				}
			}
		}
	}
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate vendor bill done",
	})
}

func MigrateVendorBillTax(req sdk.APIRequest, resp sdk.APIResponder) error {
	vendor_bill_ids := []int64{
		725372,
		725373,
		725376,
		746613,
		746614,
		746615,
		746616,
		746617,
		746618,
		746626,
		746627,
		746628,
		746629,
		746630,
		746631,
		746632,
		746633,
		746634,
		746635,
		746636,
		746637,
	}
	for _, vbId := range vendor_bill_ids {
		resp := model.VendorBillDB.UpdateOne(&model.VendorBill{VendorBillID: vbId},
			model.VendorBill{
				VendorTaxCode:         "0302166964",
				VendorLegalName:       "Công Ty TNHH Dược Phẩm Việt - Thái",
				VendorLegalNameOrigin: "Công Ty TNHH Dược Phẩm Việt - Thái",
				VendorCode:            "R3LXPWOQEM",
			})
		if resp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill not found", vbId)
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate vendor bill 0302166964 done",
	})
}

func MigrateVendorBillEnsureTemplateCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	_id := primitive.NilObjectID
	allowTemplateCodes := []string{"1", "2", "3", "4", "5", "6"}
	totalUpdate := 0
	for {
		resp := model.VendorBillDB.Query(model.VendorBill{
			ComplexQuery: []*primitive.M{
				{"_id": primitive.M{"$gt": _id}},
				{"buymed_ai_invoice_id": bson.M{"$ne": nil}},
			},
		}, 0, 1000, &bson.M{"_id": 1})
		if resp.Status != common.APIStatus.Ok {
			break
		}

		vendorBills := resp.Data.([]*model.VendorBill)
		for i := range vendorBills {
			vb := vendorBills[i]
			_id = *vb.ID

			if vb.TemplateCode == "" {
				continue
			}

			if utils.IsContains(allowTemplateCodes, vb.TemplateCode) {
				continue
			}

			resp := model.VendorBillDB.UpdateOne(model.VendorBill{ID: vb.ID}, bson.M{"template_code": ""})
			if resp.Status != common.APIStatus.Ok {
				log.Println("Can not update vendor bill", vb.VendorBillCode, resp.Message)
				continue
			}

			totalUpdate++
		}
	}

	log.Println("Total update:", totalUpdate)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Cancel vendor bill done",
	})
}

func MigrateUpdateVBpriceWithoutVat(req sdk.APIRequest, resp sdk.APIResponder) error {
	log.Println("Start:: MigrateUpdateVBpriceWithoutVat")
	defer log.Println("End:: MigrateUpdateVBpriceWithoutVat")
	vendor_bill_ids := []int64{}
	if err := req.GetContent(&vendor_bill_ids); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	for _, vbId := range vendor_bill_ids {
		vbItemResp := model.VendorBillItemDB.Query(&model.VendorBillItem{VendorBillID: vbId}, 0, 1000, nil)
		if vbItemResp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill item not found", vbId)
			continue
		}

		totalPriceWithoutVat := float64(0)
		vbItems := vbItemResp.Data.([]*model.VendorBillItem)
		for i := range vbItems {
			item := vbItems[i]
			priceAfterDiscountUnit := float64(0)
			quantity := int64(0)
			if item.PriceAfterDiscount != nil {
				priceAfterDiscountUnit = *item.PriceAfterDiscount
			}
			if item.Quantity != nil {
				quantity = *item.Quantity
			}

			totalPriceWithoutVat += (priceAfterDiscountUnit) * float64(quantity)
		}

		resp := model.VendorBillDB.UpdateOne(&model.VendorBill{VendorBillID: vbId},
			model.VendorBill{
				TotalPriceWithoutVat: &totalPriceWithoutVat,
			})
		if resp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill not found", vbId)
		}

	}
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate vendor bill done",
	})
}

func MigrateUpdateVBpriceTotalVatPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	log.Println("Start:: MigrateUpdateVBpriceWithoutVat")
	defer log.Println("End:: MigrateUpdateVBpriceWithoutVat")
	vendor_bill_ids := []int64{}
	if err := req.GetContent(&vendor_bill_ids); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data." + err.Error(),
		})
	}

	for _, vbId := range vendor_bill_ids {
		vbItemResp := model.VendorBillItemDB.Query(&model.VendorBillItem{VendorBillID: vbId}, 0, 1000, nil)
		if vbItemResp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill item not found", vbId)
			continue
		}

		vbItems := vbItemResp.Data.([]*model.VendorBillItem)
		totalVatPrice := float64(0)
		for i := range vbItems {
			vbi := vbItems[i]
			if vbi.VatPrice != nil {
				totalVatPrice += *vbi.VatPrice
			}
		}

		resp := model.VendorBillDB.UpdateOne(&model.VendorBill{VendorBillID: vbId},
			model.VendorBill{
				TotalVatPrice: &totalVatPrice,
			})
		if resp.Status != common.APIStatus.Ok {
			log.Println("Vendor bill not found", resp)
		}

	}
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate vendor bill done",
	})
}
