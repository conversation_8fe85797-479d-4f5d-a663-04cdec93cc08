package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// CheckValidPOStatus checks if the transition from the current purchase order status
// to the new purchase order status is valid based on the settings in the database.
// It returns true if the transition is valid, otherwise false.
//
// Parameters:
//   - currentStatus: The current status of the purchase order.
//   - newStatus: The new status to which the purchase order is being transitioned.
//
// Returns:
//   - bool: true if the transition is valid, false otherwise.
func CheckValidPOStatus(currentStatus, newStatus enum.PurchaseOrderStatusValue) bool {
	settingResp := model.SettingDB.QueryOne(model.Setting{})
	if settingResp.Status != common.APIStatus.Ok {
		return false
	}
	setting := settingResp.Data.([]*model.Setting)[0]

	// Get the valid status transitions from the settings.
	mapStatus := setting.VaidPOStatusMap
	if mapStatus == nil {
		return false
	}

	// Check if the new status is a valid transition from the current status.
	arrayNextValid := mapStatus[currentStatus]
	for _, status := range arrayNextValid {
		if status == newStatus {
			return true
		}
	}

	return false
}

// CheckValidVendorBillStatus checks if the transition from the current vendor bill status
// to the new vendor bill status is valid based on the settings in the database.
// It retrieves the settings from the database and uses the valid status map to determine
// if the new status is a valid next status for the current status.
//
// Parameters:
//   - currentStatus: The current status of the vendor bill.
//   - newStatus: The new status to which the vendor bill is being transitioned.
//
// Returns:
//   - bool: Returns true if the transition to the new status is valid, otherwise false.
func CheckValidVendorBillStatus(currentStatus, newStatus enum.VendorBillStatusValue) bool {
	settingResp := model.SettingDB.QueryOne(model.Setting{})
	if settingResp.Status != common.APIStatus.Ok {
		return false
	}
	setting := settingResp.Data.([]*model.Setting)[0]

	mapStatus := setting.VaidVendorBillStatusMap
	if mapStatus == nil {
		return false
	}

	// Check if the new status is a valid transition from the current status.
	arrayNextValid := mapStatus[currentStatus]
	for _, status := range arrayNextValid {
		if status == newStatus {
			return true
		}
	}

	return false
}

// combinePrWhHashtag generates a hashtag by combining a product ID and a warehouse code.
// The resulting hashtag is in the format "productID-warehouseCode".
//
// Parameters:
//   - productID: an int64 representing the product ID.
//   - warehouseCode: a string representing the warehouse code.
//
// Returns:
//   A string representing the combined hashtag.
func combinePrWhHashtag(productID int64, warehouseCode string) string {
	hashTag := fmt.Sprintf("%v-%v", productID, warehouseCode)
	return hashTag
}

// getVendorInfo retrieves vendor information based on the provided vendor code.
// It returns an APIResponse containing the vendor details or an error message.
//
// Parameters:
//   - vendorCode: A string representing the vendor code.
//
// Returns:
//   - *common.APIResponse: A pointer to an APIResponse struct containing the status, error code, message, and data.
//
// Possible error responses:
//   - If the vendor code is empty, the function returns an error response with the error code BUYER_CODE_EMPTY and the message "empty vendor code".
//   - If the seller is not found, the function returns an error response with the corresponding error code and the message "seller not found".
func getVendorInfo(vendorCode string) *common.APIResponse {
	if vendorCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: BUYER_CODE_EMPTY,
			Message:   "empty vendor code",
		}
	}
	option := client.APIOption{
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCodes": vendorCode,
		},
	}
	sellerResp := seller.GetSellers(option)

	if sellerResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: sellerResp.ErrorCode,
			Message:   "seller not found",
		}
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   sellerResp.Data,
	}
}

// CheckValidPOItems validates a list of PurchaseOrderItem objects.
// It returns false if the list is empty or if any item in the list
// has a SellerCode of "MEDX" and either has no VAT or a VAT value
// less than or equal to 0.
//
// Parameters:
// - items: A slice of pointers to PurchaseOrderItem objects.
//
// Returns:
// - bool: true if all items are valid, false otherwise.
func CheckValidPOItems(items []*model.PurchaseOrderItem) bool {
	if len(items) == 0 {
		return false
	}

	for _, item := range items {
		// không cho phép po item không có VAT hoặc VAT < 0
		if item.SellerCode == "MEDX" && (item.VAT == nil || *item.VAT <= 0) {
			return false
		}
	}
	return true
}
