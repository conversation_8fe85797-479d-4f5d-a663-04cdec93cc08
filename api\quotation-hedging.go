package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetQuotationHedgingByVenodor ...
func GetQuotationHedgingByVenodor(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		qStr       = req.GetParam("q")
	)

	// validate input
	var query model.Quotation
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// add more ComplexQuery 
	query.SellerCode = strings.ToUpper(sellerCode)
	if len(query.SellerCodeIn) > 0 {
		query.SellerCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"seller_code": bson.M{
				"$in": query.SellerCodeIn,
			},
		})
	}

	// add more ComplexQuery 
	if len(query.Search) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hashtag": primitive.Regex{Pattern: query.Search},
		})
	}

	// add more ComplexQuery 
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// add more ComplexQuery 
	if len(query.WarehouseCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	// add more ComplexQuery 
	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}

	// do action
	return resp.Respond(action.GetQuotationHedging(query, offset, limit, getTotal))
}

// CreateQuotationHedging ...
func CreateQuotationHedging(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input action.CreateQuotationsInput
	)

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.CreateHedgingQuotation(input))
}

// DeleteQuotationHedging
func DeleteQuotationHedging(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.Quotation
	)

	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.DeleteQuotationHedging(&query))
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Unauthorized",
	})
}
