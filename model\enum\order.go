package enum

// OrderStateValue ...
type OrderStateValue string

type orderStateValue struct {
	WaitConfirm OrderStateValue
	Confirmed   OrderStateValue
	Canceled    OrderStateValue
	Processing  OrderStateValue
	Completed   OrderStateValue
	Delivering  OrderStateValue
}

// OrderState ...
var OrderState = &orderStateValue{
	"WAIT_TO_CONFIRM",
	"CONFIRMED",
	"CANCEL",
	"PROCESSING",
	"COMPLETED",
	"DELIVERING",
}
