package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// Create Reason ...
func CreateReason(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Reason

	// parse input
	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ReasonCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Description == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Description is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ReasonTypeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}
	// do action
	return resp.Respond(action.CreateReason(input))
}

// Update Reason ...
func UpdateReason(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Reason

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ReasonCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Description == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Description is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ReasonTypeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.UpdateReason(input))
}

// Delete Reason ...
func DeleteReason(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		reasonCode = req.GetParam("reasonCode")
	)

	// validate input
	if reasonCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Reason Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.DeleteReason(reasonCode))
}

// Get Reason ...
func GetReasonByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	reasonCode := req.GetParam("reasonCode")

	if reasonCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Reason Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.GetReasonByCode(reasonCode))
}

// Get Reason List ...
func GetReasonList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.Reason
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetReasonList(&query, offset, limit, getTotal))
}
