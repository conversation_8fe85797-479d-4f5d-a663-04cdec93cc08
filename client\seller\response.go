package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

/*
Seller Core response
*/
type (
	SellerResponse struct {
		Data []*model.Seller `json:"data"`
		common.APIResponse
	}
)

/**
 * Promotion response
 */
type (
	PriceAfterRebateResponse struct {
		Data []*model.PriceAfterRebate `json:"data"`
		common.APIResponse
	}

	VendorPromotionResponse struct {
		Data []*model.VendorPromotion `json:"data"`
		common.APIResponse
	}

	CalcPriceAfterRebateReponse struct {
		Data []*model.CalcPriceAfterRebateResp `json:"data"`
		common.APIResponse
	}

	PriceAfterRebateForDealResponse struct {
		Data []*model.PriceForDeal `json:"data"`
		common.APIResponse
	}
	BasicResponse struct {
		Data []interface{} `json:"data"`
		common.APIResponse
	}
)

/**
 * WIS response
 */
type (
	FirstMileTicketResponse struct {
		Data []*model.FirstMileTicket `json:"data"`
		common.APIResponse
	}
)

type BankResponse struct {
	Data []*model.Bank `json:"data"`
	common.APIResponse
}

type WrongTicketItemResponse struct {
	Data []*model.WrongTicketItem `json:"data"`
	common.APIResponse
}
