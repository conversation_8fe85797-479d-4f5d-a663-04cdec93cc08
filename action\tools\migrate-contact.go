package tools

import (
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateAddressWarehouseCode(_ sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(migrateAddressWarehouseCode)
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func migrateAddressWarehouseCode() {
	log.Println("Start migrateAddressWarehouseCode")
	defer log.Println("End migrateAddressWarehouseCode")

	listAddress := []*model.Address{}
	_id := primitive.NilObjectID
	for {
		addressResp := model.AddressDB.Query(
			model.Address{ComplexQuery: []*bson.M{{"_id": bson.M{"$gt": _id}}}},
			0, 100, &primitive.M{"_id": 1},
		)
		if addressResp.Status != common.APIStatus.Ok {
			break
		}

		addressData := addressResp.Data.([]*model.Address)
		for _, address := range addressData {
			// increase offset
			_id = *address.ID
			// add to list
			listAddress = append(listAddress, address)
		}
	}

	updateErr := []string{}
	option := client.APIOption{}
	warehouseInfoResp := warehouse.GetWarehouse(option)
	if warehouseInfoResp.Status != common.APIStatus.Ok {
		updateErr = append(updateErr, fmt.Sprintf("Cannot get warehouse info: %s", warehouseInfoResp.Message))
	}
	warehouseData := warehouseInfoResp.Data.([]*model.Warehouse)

	log.Println("Total Address: ", len(listAddress))
	for _, address := range listAddress {
		locations := []string{
			address.ProvinceCode,
		}
		// get warehouse
		warehouseCodes := action.ParseLocationsToWarehouseDelivery(locations, warehouseData)

		if len(warehouseCodes) == 0 {
			updateErr = append(updateErr, fmt.Sprintf("Cannot get warehouse info: %s", warehouseInfoResp.Message))
		}

		address.WarehouseCode = &warehouseCodes[0]

		// update
		updateResp := model.AddressDB.UpdateOne(model.Address{ID: address.ID}, address)
		if updateResp.Status != common.APIStatus.Ok {
			updateErr = append(updateErr, fmt.Sprintf("ID :%s, warehouseCode: %s, purposeCode: %s -- ", address.ID, *address.WarehouseCode, address.PurposeCode))
		}
	}

	log.Println("Total Update Error: ", len(updateErr))
}

func MigrateContactWarehouseCode(_ sdk.APIRequest, resp sdk.APIResponder) error {
	go sdk.Execute(migrateContactWarehouseCode)
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func migrateContactWarehouseCode() {
	log.Println("Start migrateContactWarehouseCode")
	defer log.Println("End migrateContactWarehouseCode")

	listContact := []*model.Contact{}
	_id := primitive.NilObjectID
	for {
		contactResp := model.ContactDB.Query(
			model.Contact{ComplexQuery: []*bson.M{{"_id": bson.M{"$gt": _id}}}},
			0, 100, &primitive.M{"_id": 1},
		)
		if contactResp.Status != common.APIStatus.Ok {
			break
		}

		contactData := contactResp.Data.([]*model.Contact)
		for _, contact := range contactData {
			// increase offset
			_id = *contact.ID
			// add to list
			listContact = append(listContact, contact)
		}
	}

	updateErr := []string{}

	log.Println("Total Contact: ", len(listContact))
	for _, contact := range listContact {

		warehouseCode := "BD"
		contact.WarehouseCode = &warehouseCode // currently, all contact is belong to BD warehouse - 18apr2023

		// update
		updateResp := model.ContactDB.UpdateOne(model.Contact{ID: contact.ID}, contact)
		if updateResp.Status != common.APIStatus.Ok {
			updateErr = append(updateErr, fmt.Sprintf("ID :%s, warehouseCode: %s,  type: %s, purposeCode: %s -- ", contact.ID, *contact.WarehouseCode, contact.Type, contact.PurposeCode))
		}
	}

	log.Println("Total Update Error: ", len(updateErr))
}
