package api

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetInboundCallbackStore ...
func GetInboundCallbackStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.InboundCallbackStore
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// validate input
	if len(query.POCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_code": bson.M{
			"$in": query.POCodeIn,
		}})
	}

	// do action
	return resp.Respond(action.GetInboundCallbackStore(&query, offset, limit, getTotal))
}

// DeleteInboundCallbackStore...
func DeleteInboundCallbackStore(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr = req.GetParam("q")
	)

	// validate input
	var query model.InboundCallbackStore
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// do action
	return resp.Respond(action.DeleteInboundCallbackStore(&query))
}

// WarehouseCallbackLotDate ...
func WarehouseCallbackLotDate(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.InboundCallback
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Required POCode",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		})
	}

	// validate input
	if input.RequestID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Required RequestID",
			ErrorCode: string(enum.ErrorCodeInvalid.RequestID),
		})
	}

	// validate input
	if len(input.InboundCallbackItems) > 0 && len(input.ReceiptCode) > 0 && input.Status != "CHECKIN" {
		// không được trùng receipt_code + PO
		getInboundCallbackStoreResp := model.InboundCallbacLogkDB.QueryOne(model.InboundCallback{
			ReceiptCode: input.ReceiptCode,
			POCode:      input.POCode,
		})

		if getInboundCallbackStoreResp.Status == common.APIStatus.Ok {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: fmt.Sprintf("ReceiptCode: %s is existed in POCode: %s", input.ReceiptCode, input.POCode),
			})
		}
	}

	// #1 ghi log InboundCallbacLog, uniqe theo po_code + request_id
	createResp := model.InboundCallbacLogkDB.Create(input)
	if createResp.Status != common.APIStatus.Ok {
		if strings.Contains(createResp.Message, "duplicate") {
			createResp.Status = common.APIStatus.Ok
			createResp.ErrorCode = common.APIStatus.Existed
		}
		return resp.Respond(createResp)
	}

	// do action
	action.WarehouseCallbackLotDate(input)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Callback successful.",
	})
}
