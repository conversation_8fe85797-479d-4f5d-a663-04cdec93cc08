package env

import "os"

var version string
var env string
var group string
var hostname string
var app string

func init() {
	env = os.Getenv("group")
	if env == "" {
		env = "unknown"
	}

	group = os.Getenv("group")
	if group == "" {
		group = "unknown"
	}

	_hostname, err := os.Hostname()
	if err != nil {
		hostname = "undefined"
	} else {
		hostname = _hostname
	}

	app = group + "/" + hostname + " " + env
}

func GetAppName() string {
	return app
}
