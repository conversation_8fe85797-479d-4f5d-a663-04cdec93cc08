package utils

import (
	"encoding/base64"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsonrw"
)

func IsInt64Contains(arr []int64, key int64) bool {

	if len(arr) == 0 {
		return false
	}

	for i := range arr {
		if key == arr[i] {
			return true
		}
	}

	return false
}

func GetMinOfArray(arr []int64) int64 {
	if len(arr) == 0 {
		return 0
	}
	min := arr[0]
	for _, number := range arr {
		if min > number {
			min = number
		}
	}
	return min
}

func GetMaxOfArray(arr []int64) int64 {
	if len(arr) == 0 {
		return 0
	}
	max := arr[0]
	for _, number := range arr {
		if max < number {
			max = number
		}
	}
	return max
}

func IsEqualTime(time1, time2 *time.Time) bool {
	if time1 == nil || time2 == nil {
		return false
	}
	return time1.Equal(*time2)
}

func ToBase64(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}

func GetPriceFromPriceAfterVAT(priceAfterVAT, discountPercent, vat float64) float64 {
	if priceAfterVAT <= 0 {
		return 0
	}
	price := priceAfterVAT
	if vat > 0 {
		price = priceAfterVAT / (1 + vat/100.0)
	}

	if discountPercent > 0 {
		if discountPercent == 100 {
			price = 0
		} else {
			price = price / (1 - discountPercent/100)
		}
	}
	return math.Round(price*100) / 100
}

func GetPriceAfterVATFromPrice(unitPrice, discountPercent, vat float64) float64 {
	var priceAfterVAT float64 = 0
	if unitPrice == 0 {
		return priceAfterVAT
	}
	priceAfterVAT = unitPrice
	if discountPercent > 0 {
		priceAfterVAT *= (1 - discountPercent/100)
	}
	if vat > 0 {
		priceAfterVAT *= (1 + vat/100)
	}
	return priceAfterVAT
}

func DecodeBsonDocument(input any) (any, error) {
	// The BSON data: https://pkg.go.dev/go.mongodb.org/mongo-driver@v1.15.0/bson#Decoder.DefaultDocumentM
	_, bsonData, _ := bson.MarshalValue(input)
	reader := bsonrw.NewBSONValueReader(bson.TypeArray, bsonData)
	dec, err := bson.NewDecoder(reader)
	if err == nil {
		dec.DefaultDocumentM()
		dec.Decode(input)
	}

	return input, err
}

