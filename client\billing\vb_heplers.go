package billing

import (
	"fmt"
	"log"
	"runtime/debug"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

const (
	PaidVB     = "PaidVB"
	RollbackVB = "RollbackVB"
)

type VendorBill struct {
	OrgUid     int64  `bson:"orgUid,omitempty" json:"orgUid,omitempty"`
	EntityUid  int64  `bson:"entityUid,omitempty" json:"entityUid,omitempty"`
	ServiceUid int64  `bson:"serviceUid,omitempty" json:"serviceUid,omitempty"`
	Type       string `bson:"type" json:"type"`

	Code        string `json:"code" bson:"code"`
	PoCode      string `json:"poCode" bson:"poCode"`
	PaymentCode string `json:"paymentCode" bson:"paymentCode"`

	BuyerAddressLine string `json:"buyerAddressLine" bson:"buyerAddressLine"`
	BuyerTaxCode     string `json:"buyerTaxCode" bson:"buyerTaxCode"`
	BuyerLegalName   string `json:"buyerLegalName" bson:"buyerLegalName"`

	VendorCode            string `json:"vendorCode" bson:"vendorCode"`
	VendorLegalNameOrigin string `json:"vendorLegalNameOrigin" bson:"vendorLegalNameOrigin"`
	VendorTaxCode         string `json:"vendorTaxCode" bson:"vendorTaxCode"`
	VendorLegalName       string `json:"vendorLegalName" bson:"vendorLegalName"`

	InvoiceNumber string `json:"invoiceNumber" bson:"invoiceNumber"`
	InvoiceSeries string `json:"invoiceSeries" bson:"invoiceSeries"`

	Vat           *float64 `json:"vat" bson:"vat"`
	TotalVatPrice *float64 `json:"totalVatPrice" bson:"totalVatPrice"`

	DifferencePrice *float64 `json:"differencePrice" bson:"differencePrice"`

	TotalDiscountPrice    *float64 `json:"totalDiscountPrice" bson:"totalDiscountPrice"`
	TotalDiscountAfterVat *float64 `json:"totalDiscountAfterVat" bson:"totalDiscountAfterVat"`

	IsWrongQuantity *bool `json:"isWrongQuantity,omitempty" bson:"is_wrong_quantity,omitempty"`
	IsWrongLotDate  *bool `json:"isWrongLotDate,omitempty" bson:"is_wrong_lot_date,omitempty"`

	TotalPrice           *float64 `json:"totalPrice" bson:"totalPrice"`
	TotalPriceWithoutVat *float64 `json:"totalPriceWithoutVat" bson:"totalPriceWithoutVat"`

	Items []VendorBillItem `json:"items" bson:"items"`

	CollectionDate time.Time `json:"collectionDate" bson:"collectionDate"`
}

func (vb *VendorBill) Bind(m model.VendorBill) *VendorBill {
	vb.Code = m.VendorBillCode
	vb.PoCode = m.POCode
	vb.BuyerAddressLine = m.BuyerAddressLine
	vb.BuyerTaxCode = m.BuyerTaxCode
	vb.BuyerLegalName = m.BuyerLegalName
	vb.VendorCode = m.VendorCode
	vb.VendorLegalNameOrigin = m.VendorLegalNameOrigin
	vb.VendorTaxCode = m.VendorTaxCode
	vb.VendorLegalName = m.VendorLegalName
	vb.InvoiceNumber = m.InvoiceNumber
	vb.InvoiceSeries = m.InvoiceSeries
	vb.Vat = m.Vat
	vb.TotalVatPrice = m.TotalVatPrice
	vb.DifferencePrice = m.DifferencePrice
	vb.TotalDiscountPrice = m.TotalDiscountPrice
	vb.TotalDiscountAfterVat = m.TotalDiscountAfterVat
	vb.TotalPrice = m.TotalPrice
	vb.TotalPriceWithoutVat = m.TotalPriceWithoutVat
	vb.IsWrongQuantity = m.IsWrongQuantity
	vb.IsWrongLotDate = m.IsWrongLotDate
	return vb
}

type VendorBillItem struct {
	ItemName  string  `json:"itemName" bson:"itemName"`
	ItemCode  string  `json:"itemCode" bson:"itemCode"`
	Unit      string  `json:"unit" bson:"unit"`
	UnitPrice float64 `json:"unitPrice" bson:"unitPrice"`

	ExpectQuantity int64  `json:"expectQuantity" bson:"expectQuantity"`
	BillQuantity   *int64 `json:"billQuantity" bson:"billQuantity"`

	ProductName string `json:"productName" bson:"productName"`
	ProductId   int64  `json:"productId" bson:"productId"`
	ProductCode string `json:"productCode" bson:"productCode"`
	Sku         string `json:"sku" bson:"sku"`

	Lot     string `json:"lot" bson:"lot,omitempty"`
	ExpDate string `json:"expDate" bson:"exp_date,omitempty"`

	Vat                *float64 `json:"vat" bson:"vat"`
	Price              *float64 `json:"price" bson:"price"`
	PriceAfterDiscount *float64 `json:"priceAfterDiscount" bson:"priceAfterDiscount"`
	VatPrice           *float64 `json:"vatPrice" bson:"vatPrice"`
	DiscountPrice      *float64 `json:"discountPrice" bson:"discountPrice"`
	DiscountPercent    *float64 `json:"discountPercent" bson:"discountPercent"`
	TotalDiscountPrice *float64 `json:"totalDiscountPrice" bson:"totalDiscountPrice"`
	TotalOrderPrice    *float64 `json:"totalOrderPrice" bson:"totalOrderPrice"`
	TotalPrice         *float64 `json:"totalPrice" bson:"totalPrice"`
}

func (vbi *VendorBillItem) Bind(item model.VendorBillItem, poItem model.PurchaseOrderItem) *VendorBillItem {
	vbi.ItemName = item.ItemName
	vbi.ItemCode = item.ItemCode
	vbi.Unit = item.Unit
	vbi.UnitPrice = poItem.UnitPrice
	vbi.ExpectQuantity = poItem.ExpectQuantity
	vbi.BillQuantity = poItem.BillQuantity
	vbi.ProductName = item.ProductName
	vbi.ProductId = item.ProductID
	vbi.ProductCode = item.ProductCode
	vbi.Sku = item.SKU
	vbi.Vat = item.Vat
	vbi.Price = item.Price
	vbi.PriceAfterDiscount = item.PriceAfterDiscount
	vbi.VatPrice = item.VatPrice
	vbi.DiscountPrice = item.DiscountPrice
	vbi.DiscountPercent = item.DiscountPercent
	vbi.TotalDiscountPrice = item.TotalDiscountPrice
	vbi.TotalOrderPrice = poItem.TotalPrice
	vbi.TotalPrice = item.TotalPrice
	vbi.Lot = item.Lot
	vbi.ExpDate = item.ExpDate
	return vbi
}

func recoverFunc() {
	if r := recover(); r != nil {
		log.Print(fmt.Sprintf("panic: %s - %s", r, string(debug.Stack())))
	}
}

func BuildVendorBillAnalyze(billCode, poCode string) {
	// defer recoverFunc()
	// bcSvc := NewBillingCenterSvcClient()
	// if bcSvc == nil {
	// 	panic("Init Billing Center client fail")
	// }
	// var bill *model.VendorBill
	// var billItems []*model.VendorBillItem
	// var payment *model.PaymentItemPlatform
	// var entity EntityTf
	// POItems := make(map[string]model.PurchaseOrderItem)
	// wg := NewWgGroup()
	// wg.Go(func() error {
	// 	billRs := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: billCode})
	// 	if billRs.Status != common.APIStatus.Ok {
	// 		return errors.New(billRs.Message)
	// 	}
	// 	bill = billRs.Data.([]*model.VendorBill)[0]
	// 	seller, err := bcSvc.GetVendor(bill.VendorCode)
	// 	if err != nil {
	// 		return err
	// 	}
	// 	entity, err = bcSvc.GetEntity(seller.EntityID)
	// 	if err != nil {
	// 		return err
	// 	}
	// 	return nil
	// })
	// wg.Go(func() error {
	// 	billItemRs := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillCode: billCode}, 0, 1000, nil)
	// 	if billItemRs.Status != common.APIStatus.Ok {
	// 		return errors.New(billItemRs.Message)
	// 	}
	// 	billItems = billItemRs.Data.([]*model.VendorBillItem)
	// 	return nil
	// })
	// wg.Go(func() error {
	// 	POItemsRs := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, nil)
	// 	if POItemsRs.Status != common.APIStatus.Ok {
	// 		return errors.New(POItemsRs.Message)
	// 	}
	// 	items := POItemsRs.Data.([]*model.PurchaseOrderItem)
	// 	for _, item := range items {
	// 		POItems[item.SKU] = *item
	// 	}
	// 	return nil
	// })
	// wg.Go(func() error {
	// 	PaymentRs := model.PaymentItemDB.QueryOne(bson.D{{"po_code", poCode}})
	// 	if PaymentRs.Status == common.APIStatus.NotFound {
	// 		payment = nil
	// 		return nil
	// 	}
	// 	if PaymentRs.Status != common.APIStatus.Ok {
	// 		return errors.New(PaymentRs.Message)
	// 	}
	// 	payment = PaymentRs.Data.([]*model.PaymentItem)[0]
	// 	return nil
	// })
	// err := wg.Wait()
	// if err != nil {
	// 	panic(err)
	// }
	// rs := new(VendorBill).Bind(*bill)
	// rs.Type = PaidVB
	// rs.OrgUid = entity.OrgID
	// rs.EntityUid = entity.EntityID
	// rs.ServiceUid = 2
	// var items []VendorBillItem
	// for _, item := range billItems {
	// 	poItem := POItems[item.SKU]
	// 	vbItem := new(VendorBillItem).Bind(*item, poItem)
	// 	items = append(items, *vbItem)
	// }
	// rs.Items = items
	// if payment != nil {
	// 	rs.PaymentCode = payment.PaymentCode
	// 	if payment.PaidTime != nil {
	// 		rs.CollectionDate = *payment.PaidTime
	// 	} else {
	// 		rs.CollectionDate = time.Now()
	// 	}
	// }
	// err = bcSvc.PutVendorBill(rs)
	// if err != nil {
	// 	panic(err)
	// }
}

func BuildRollbackVBAnalyze(billCode string) {
	defer recoverFunc()
	bcSvc := NewBillingCenterSvcClient()
	if bcSvc == nil {
		panic("Init Billing Center client fail")
	}

	billRs := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: billCode})
	if billRs.Status != common.APIStatus.Ok {
		panic(billRs.Message)
	}
	bill := billRs.Data.([]*model.VendorBill)[0]
	seller, err := bcSvc.GetVendor(bill.VendorCode)
	if err != nil {
		panic(err)
	}
	entity, err := bcSvc.GetEntity(seller.EntityID)
	if err != nil {
		panic(err)
	}
	rs := VendorBill{
		OrgUid:     entity.OrgID,
		EntityUid:  entity.EntityID,
		ServiceUid: 2,
		Type:       RollbackVB,
		Code:       billCode,
		VendorCode: bill.VendorBillCode,
	}
	err = bcSvc.PutVendorBill(rs)
	if err != nil {
		panic(err)
	}
}
