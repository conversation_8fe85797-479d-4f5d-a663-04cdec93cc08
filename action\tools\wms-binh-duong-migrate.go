package tools

import (
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigrateBinhDuongWMS(req sdk.APIRequest, res sdk.APIResponder) error {
	db := req.GetVar("db")
	switch db {
	case "inbound_request":
		go inboundRequest()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "inbound_request_item":
		go inboundRequestItem()
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	case "po":
		var input []string
		err := req.GetContent(&input)
		if err != nil {
			return res.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: err.Error()})
		}
		go poForWMSBinhDuong(input)
		return res.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: fmt.Sprintf("Request migrate %q", db)})
	}

	return res.Respond(&common.APIResponse{Status: common.APIStatus.Invalid, Message: "Invalid 'db' params"})
}

func inboundRequest() {
	log.Println("Migrate All inbound request")
	defer log.Println("Migrate All inbound request done")

	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		inboundRequestResp := model.InboundRequestDB.Query(
			&model.InboundRequest{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}}}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if inboundRequestResp.Status == common.APIStatus.Ok {
			inboundRequests := inboundRequestResp.Data.([]*model.InboundRequest)
			// select last _id
			_id = *inboundRequests[len(inboundRequests)-1].ID

			model.InboundRequestDB.UpdateMany(
				// filter
				&model.InboundRequest{
					WarehouseCode: WAREHOUSE_HCM, ComplexQuery: []*primitive.M{
						{"_id": bson.M{"$gt": _id_offset}},
						{"_id": bson.M{"$lte": _id}},
					}},
				// updater
				&model.InboundRequest{WarehouseCode: WAREHOUSE_BD})
			_id_offset = _id
		} else if inboundRequestResp.Status == common.APIStatus.NotFound {
			break
		} else {
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func inboundRequestItem() {
	log.Println("Migrate purchase order")
	defer log.Println("Migrate purchase order done")

	var (
		_id        = primitive.NilObjectID
		_id_offset = _id
	)
	for {
		inboundRequestItemResp := model.InboundRequestItemDB.Query(
			&model.InboundRequestItem{
				WarehouseCode: WAREHOUSE_HCM,
				ComplexQuery: []*primitive.M{
					{"_id": bson.M{"$gt": _id_offset}}}},
			0,
			DEFAULT_LIMIT,
			&primitive.M{"_id": 1},
		)

		if inboundRequestItemResp.Status == common.APIStatus.Ok {
			inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
			// select last _id
			_id = *inboundRequestItems[len(inboundRequestItems)-1].ID

			model.InboundRequestItemDB.UpdateMany(
				// filter
				&model.InboundRequestItem{
					WarehouseCode: WAREHOUSE_HCM, ComplexQuery: []*primitive.M{
						{"_id": bson.M{"$gt": _id_offset}},
						{"_id": bson.M{"$lte": _id}},
					}},
				// updater
				&model.InboundRequestItem{WarehouseCode: WAREHOUSE_BD})

			_id_offset = _id
		} else if inboundRequestItemResp.Status == common.APIStatus.NotFound {
			break
		} else {
			time.Sleep(time.Duration(DEFAULT_SLEEP) * time.Millisecond)
		}
	}
}

func poForWMSBinhDuong(listPOCodes []string) {
	log.Println("Migrate PO")
	defer log.Println("Migrate PO done")

	for _, poCode := range listPOCodes {
		model.PurchaseOrderDB.UpdateMany(
			//filter
			&model.PurchaseOrder{POCode: poCode},
			//updater
			&model.PurchaseOrder{WarehouseCode: WAREHOUSE_BD},
		)

	}

	// po Item
	poItemForWMSBinhDuong(listPOCodes)
}

func poItemForWMSBinhDuong(listPOCodes []string) {
	log.Println("Migrate PO item")
	defer log.Println("Migrate PO item done")

	for _, poCode := range listPOCodes {
		model.PurchaseOrderItemDB.UpdateMany(
			//filter
			&model.PurchaseOrderItem{POCode: poCode},
			//updater
			&model.PurchaseOrderItem{WarehouseCode: WAREHOUSE_BD},
		)

	}

}

func UpdateWMSBinhDuongPurchaser(req sdk.APIRequest, res sdk.APIResponder) {

}
