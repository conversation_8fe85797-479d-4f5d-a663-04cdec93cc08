package tools

import (
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PaymentMigrate struct {
	Codes []string `json:"codes"`
}

func MigratePaymentPlatform(req sdk.APIRequest, resp sdk.APIResponder) error {
	input := &PaymentMigrate{}
	err := req.GetContent(input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	go migratePaymentPlatform(input.Codes)
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}
func migratePaymentPlatform(pmCodes []string) {
	log.Println("Start migratePaymentPlatform")
	defer log.Println("End migratePaymentPlatform")

	mapReasonCode := getMapPaymentReason()
	if len(mapReasonCode) == 0 {
		log.Println("Cannot get reason code")
		return
	}

	mapVendorInfo := genMapVendorInfo()
	if len(mapVendorInfo) == 0 {
		log.Println("Cannot get vendor info")
		return
	}
	mapPmLegacyTypePmPlatformType := map[enum.PaymentType]enum.PaymentPlatfromType{
		enum.PayType.PAYMENT_VOUCHER: enum.PaymentPlatformType.PAYMENT,
		enum.PayType.RECEIPT_VOUCHER: enum.PaymentPlatformType.RECEIPT,
	}

	_id := primitive.NilObjectID
	count := 0
	reMigrateCodes := []string{}
	for {
		q := model.PaymentLegacy{ComplexQuery: []*bson.M{{"_id": bson.M{"$gt": _id}}}}
		if len(pmCodes) > 0 {
			q.ComplexQuery = append(q.ComplexQuery, &bson.M{"payment_code": bson.M{"$in": pmCodes}})
		}
		resp := model.PaymentLegacyDB.Query(
			q,
			0, 1000,
			&primitive.M{"_id": 1},
		)
		if resp.Status != common.APIStatus.Ok {
			break
		}
		if count%1000 == 0 {
			log.Printf("Processing: %v\n", count)
		}
		data := resp.Data.([]*model.PaymentLegacy)
		for i := range data {
			pmMainLegacy := data[i]
			_id = *pmMainLegacy.ID
			count++

			// get payment platform
			paymentPlatformQ := model.PaymentPlatform{
				LegacyPaymentCodeIn: []string{pmMainLegacy.PaymentCode},
			}

			platformResp := action.GetSingleNewPayment(
				paymentPlatformQ,
				model.QueryOption{Items: true})

			if platformResp.Status == common.APIStatus.Ok && len(pmCodes) > 0 {
				// xoá cũ
				pmPlatformCode := platformResp.Data.([]*model.PaymentPlatform)[0].PaymentCode
				resp := action.SwitchStatusPayment(pmPlatformCode, enum.PaymentPlatformStatus.CANCELLED)
				if resp.Status != common.APIStatus.Ok {
					log.Printf("Cannot cancel payment platform: %v, msg: %v\n", pmMainLegacy.PaymentCode, resp.Message)
					continue
				}
			} else if platformResp.Status == common.APIStatus.Ok {
				log.Println("Payment platform existed: ", pmMainLegacy.PaymentCode)
				// đã được migrate
				continue
			}

			if pmMainLegacy.TotalPrice == nil {
				pmMainLegacy.TotalPrice = utils.Pointer.WithFloat64(0)
			}

			pmPlatformNew := model.PaymentPlatform{
				LegacyPaymentCode:  pmMainLegacy.PaymentCode,                                    //
				LegacyPaymendID:    pmMainLegacy.PaymentID,                                      //
				CreatedTime:        pmMainLegacy.CreatedTime,                                    //
				LastUpdatedTime:    pmMainLegacy.LastUpdatedTime,                                //
				PaidTime:           pmMainLegacy.PaidTime,                                       //
				CreatedByID:        pmMainLegacy.CreatedByID,                                    //
				PartnerType:        enum.PaymentPlatformPartnerType.THUOCSIVN_VENDOR,            //
				PartnerCode:        pmMainLegacy.VendorCode,                                     //
				PartnerName:        mapVendorInfo[pmMainLegacy.VendorCode].Name,                 //
				Amount:             *pmMainLegacy.TotalPrice,                                    //
				Note:               pmMainLegacy.Note,                                           //
				PrepaidPOCodes:     pmMainLegacy.PrePaidPOCodes,                                 //
				RefundBillCode:     pmMainLegacy.RefundBillCode,                                 //
				AdjustmentBillCode: pmMainLegacy.AdjustmentBillCode,                             //
				Type:               mapPmLegacyTypePmPlatformType[pmMainLegacy.Type],            //
				PaymentMethod:      enum.PaymentPlatformMethodValue(pmMainLegacy.PaymentMethod), //
				PORelatedCodes:     pmMainLegacy.POCodes,                                        //
			}

			rsCode, ok := mapReasonCode[pmMainLegacy.Purpose]
			if !ok {
				log.Printf("Cannot get reason code:%v for: %v", pmMainLegacy.PaymentCode, pmMainLegacy.Purpose)
				continue
			}
			pmPlatformNew.ReasonCode = rsCode
			itemLegacyResp := model.PaymentItemLegacyDB.Query(
				model.PaymentItemLegacy{PaymentCode: pmMainLegacy.PaymentCode},
				0, 1000, nil)

			if pmMainLegacy.Purpose == enum.PaymentPurpose.PAY_DIRECT {
				// loại này balance = 0 -> tạo item để cập nhật balance
				item := &model.PaymentItemPlatform{
					Amount:          &pmPlatformNew.Amount,
					ObjectType:      enum.PaymentPlatformObjectType.OTHER,
					ObjectCode:      "OTHER",
					Note:            "Payment Direct System auto create item",
					TransactionType: "DECREASE_BALANCE",
					PartnerCode:     pmMainLegacy.VendorCode,
				}
				item.FillData(model.OptionFillDataPaymentPlatform{
					ExtraData: true,
				})
				pmPlatformNew.Items = append(pmPlatformNew.Items, item)
			} else if itemLegacyResp.Status == common.APIStatus.Ok {
				// get payment item
				itemLegacyList := itemLegacyResp.Data.([]*model.PaymentItemLegacy)
				for i := range itemLegacyList {
					pmItemLegacy := itemLegacyList[i]
					pmItemNew := &model.PaymentItemPlatform{
						LegacyPaymentItemCode: pmItemLegacy.PaymentItemCode,
						LegacyPaymentCode:     pmItemLegacy.PaymentCode,
						PartnerCode:           pmItemLegacy.VendorCode,
						// Amount:                pmItemLegacy.TotalPrice,
						VendorBillID:       pmItemLegacy.VendorBillID,
						VendorBillCode:     pmItemLegacy.VendorBillCode,
						AdjustmentBillID:   pmItemLegacy.AdjustmentBillID,
						AdjustmentBillCode: pmItemLegacy.AdjustmentBillCode,
						POCode:             pmItemLegacy.POCode,
						InvoiceNumber:      pmItemLegacy.InvoiceNumber,
						TransactionType:    enum.TransactionType(pmItemLegacy.TransactionType),
						CreatedTime:        pmItemLegacy.CreatedTime,
						LastUpdatedTime:    pmItemLegacy.LastUpdatedTime,
						PaidTime:           pmItemLegacy.PaidTime,
					}
					if pmItemLegacy.TotalPrice == nil {
						pmItemLegacy.TotalPrice = utils.Pointer.WithFloat64(0)
					}

					switch {
					case len(pmItemLegacy.VendorBillCode) > 0:
						pmItemNew.ObjectType = enum.PaymentPlatformObjectType.VENDOR_BILL
						pmItemNew.ObjectCode = pmItemLegacy.VendorBillCode
						pmItemNew.Amount = pmItemLegacy.TotalPrice
					case len(pmItemLegacy.AdjustmentBillCode) > 0:
						pmItemNew.ObjectType = enum.PaymentPlatformObjectType.ADJUSMENT_BILL
						pmItemNew.ObjectCode = pmItemLegacy.AdjustmentBillCode
						pmItemNew.Amount = pmItemLegacy.TotalPrice
					case pmItemLegacy.TransactionType == string(enum.Transaction.PAYMENT_VOUCHER):
						{
							if pmItemLegacy.PaymentVoucherCode == "" {
								log.Printf("PaymentVoucherCode is empty:['%v'] '%v'\n", pmMainLegacy.PaymentCode, pmItemLegacy.PaymentItemCode)
								continue
							}

							// get payment platform
							paymentPlatformQ := model.PaymentPlatform{
								LegacyPaymentCodeIn: []string{pmItemLegacy.PaymentVoucherCode},
							}

							linkedPaymentResp := action.GetSingleNewPayment(paymentPlatformQ, model.QueryOption{Items: true})
							if linkedPaymentResp.Status != common.APIStatus.Ok {
								//TODO: migrate lại payment từ log này
								if !utils.IsContains(reMigrateCodes, pmItemLegacy.PaymentCode) {
									reMigrateCodes = append(reMigrateCodes, pmItemLegacy.PaymentCode)
								}
								// log.Printf("Cannot get linked payment platform: [%v], %s\n", pmItemLegacy.PaymentVoucherCode, linkedPaymentResp.Message)
								// continue
								pmItemNew.ObjectCode = string(enum.Transaction.PAYMENT_VOUCHER)
								pmItemNew.ObjectType = enum.PaymentPlatformObjectType.OTHER
								pmItemNew.Amount = pmItemLegacy.TotalPrice
							} else {
								pmItemNew.PaymentVoucherCode = linkedPaymentResp.Data.([]*model.PaymentPlatform)[0].PaymentCode
								pmItemNew.ObjectCode = pmItemNew.PaymentVoucherCode
								pmItemNew.ObjectType = enum.PaymentPlatformObjectType.CLEARING_PAYMENT
								pmItemNew.Amount = pmItemLegacy.TotalPrice
							}
						}
					case pmItemLegacy.TransactionType == string(enum.Transaction.RECEIPT_VOUCHER):
						{
							if pmItemLegacy.ReceiptVoucherCode == "" {
								log.Printf("ReceiptVoucherCode is empty:['%v'] '%v'\n", pmMainLegacy.PaymentCode, pmItemLegacy.PaymentItemCode)
								continue
							}

							// get payment platform
							paymentPlatformQ := model.PaymentPlatform{
								LegacyPaymentCodeIn: []string{pmItemLegacy.ReceiptVoucherCode},
							}

							linkedPaymentResp := action.GetSingleNewPayment(paymentPlatformQ, model.QueryOption{Items: true})
							if linkedPaymentResp.Status != common.APIStatus.Ok {
								//TODO: migrate lại payment từ log này
								if !utils.IsContains(reMigrateCodes, pmItemLegacy.PaymentCode) {
									reMigrateCodes = append(reMigrateCodes, pmItemLegacy.PaymentCode)
								}
								// log.Printf("Cannot get linked payment platform: [%v], %s\n", pmItemLegacy.ReceiptVoucherCode, linkedPaymentResp.Message)
								// continue
								pmItemNew.ObjectCode = string(enum.Transaction.RECEIPT_VOUCHER)
								pmItemNew.ObjectType = enum.PaymentPlatformObjectType.OTHER
								pmItemNew.Amount = pmItemLegacy.TotalPrice
							} else {
								pmItemNew.ReceiptVoucherCode = linkedPaymentResp.Data.([]*model.PaymentPlatform)[0].PaymentCode
								pmItemNew.ObjectCode = pmItemNew.ReceiptVoucherCode
								pmItemNew.ObjectType = enum.PaymentPlatformObjectType.CLEARING_PAYMENT
								pmItemNew.Amount = pmItemLegacy.TotalPrice
							}
						}

					default:
						log.Printf("undefinedType of item: %v\n", pmItemLegacy.PaymentItemCode)
						continue
					}
					pmItemNew.FillData(model.OptionFillDataPaymentPlatform{
						ExtraData: true,
					})
					pmPlatformNew.Items = append(pmPlatformNew.Items, pmItemNew)
				}
			}

			pmPlatformNew.FillData(model.OptionFillDataPaymentPlatform{
				ExtraData: true,
			})

			// tạo
			option := client.APIOption{
				Body:    pmPlatformNew,
				SaveLog: utils.Pointer.WithBool(true),
			}
			createResp := payment_platform.CreatePayment(option)
			if createResp.Status != common.APIStatus.Ok {
				log.Printf("Cannot create payment platform: %v, msg: %v\n: ", pmMainLegacy.PaymentCode, createResp.Message)
			}

		}
	}

	log.Println("need to remigrate: ", reMigrateCodes)
}

func getMapPaymentReason() map[enum.PaymentPurposeType]string {
	output := make(map[enum.PaymentPurposeType]string)
	reasonResp := action.GetPaymentReason(model.ReasonSetting{})
	if reasonResp.Status != common.APIStatus.Ok {
		return output
	}

	reasons := reasonResp.Data.([]*model.ReasonSetting)
	reasonCodes := []string{}
	for _, reason := range reasons {
		reasonCodes = append(reasonCodes, reason.ReasonCode)
	}
	reasonSettings, err := action.ParsePaymentReasonData(reasonCodes)
	if err != nil {
		return output
	}

	for i := range reasonSettings {
		reasonSetting := reasonSettings[i]
		output[enum.PaymentPurposeType(reasonSetting.Data)] = reasonSetting.Code
	}

	return output

}

func genMapVendorInfo() map[string]model.Seller {
	mapVendorInfo := make(map[string]model.Seller)
	for offset, limit := 0, 500; ; offset += limit {
		option := client.APIOption{
			Params: map[string]string{
				"sellerClass": model.CLASS_VENDOR,
			},
			Offset: utils.Pointer.WithInt(offset),
			Limit:  utils.Pointer.WithInt(limit),
		}
		sellerResp := seller.GetSellers(option)

		if sellerResp.Status != common.APIStatus.Ok {
			break
		}

		sellers := sellerResp.Data.([]*model.Seller)
		for i := range sellers {
			seller := sellers[i]
			mapVendorInfo[seller.Code] = *seller
		}
	}

	return mapVendorInfo
}
