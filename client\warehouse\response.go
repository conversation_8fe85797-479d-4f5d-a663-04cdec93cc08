package warehouse

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

/*
Warehouse Core response
*/
type (
	WarehouseResponse struct {
		Data []*model.Warehouse `json:"data,omitempty"`
		common.APIResponse
	}
)

/*
Warehouse Inbound response
*/
type (
	InboundTicketResponse struct {
		Data []*model.InboundTicket `json:"data"`
		common.APIResponse
	}
)

/*
Warehouse Inventory response
*/
type (
	InventorySkuLocationResponse struct {
		Data []*model.InventorySkuLocation `json:"data"`
		common.APIResponse
	}

	InventorySkuResponse struct {
		Data []*model.InventorySku `json:"data"`
		common.APIResponse
	}
)
