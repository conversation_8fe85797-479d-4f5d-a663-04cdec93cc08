package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSellerPurchaseOrderItemList ...
func GetSellerPurchaseOrderItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal      = req.GetParam("getTotal") == strconv.FormatBool(true)
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
		qStr          = req.GetParam("q")
		sort          = req.GetParam("sort")
	)

	// validate input
	var query model.PurchaseOrderItem
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}
	query.SellerCode = sellerCode
	query.PurchaserCode = purchaserCode
	// do action
	return resp.Respond(action.GetSellerPurchaseOrderItemList(query, offset, limit, getTotal, sort))
}

// GetPurchaseOrderItemList
func GetPurchaseOrderItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.PurchaseOrderItem
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"po_code": bson.M{
				"$in": query.POCodeIn,
			}},
		)
	}

	// do action
	return resp.Respond(action.GetSellerPurchaseOrderItemList(query, offset, limit, getTotal, ""))
}

// GetVendorPurchaseOrderItemList
func GetVendorPurchaseOrderItemList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.PurchaseOrderItem
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// add complex query
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}

	// add complex query
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	// add complex query
	if len(query.ProductCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_code": bson.M{
				"$in": query.ProductCodeIn,
			},
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)

		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetSellerPurchaseOrderItemList(query, offset, limit, getTotal, ""))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// GetPurchaseOrderItemList
func UpdatePurchaseOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PurchaseOrderItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {

		// do action
		return resp.Respond(action.HandleUpdatePOItem(input, acc))
	}

	// do action
	return resp.Respond(
		&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Your account cannot perform this action",
			ErrorCode: string(enum.ErrorCodeInvalid.Action),
		},
	)
}
