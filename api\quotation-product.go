package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetQuotationProduct ...
func GetQuotationProduct(req sdk.APIRequest, resp sdk.APIResponder) error {
	// var (
	// 	offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
	// 	limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
	// 	getTotal = req.GetParam("getTotal") == "true"
	// 	qStr     = req.GetParam("q")
	// sort     = req.GetParam("sort")
	// )

	// var query model.QuotationProduct
	// if qStr != "" {
	// 	err := json.Unmarshal([]byte(qStr), &query)
	// 	if err != nil {
	// 		return resp.Respond(&common.APIResponse{
	// 			Status:    common.APIStatus.Invalid,
	// 			Message:   "Can not parse input data. " + err.Error(),
	// 			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
	// 		})

	// 	}
	// }

	// validate input
	var payload model.GetQuotationProductPayload
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	query := payload.Query

	// add more ComplexQuery
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}
	// add more ComplexQuery
	if len(query.ProductIDNotIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$nin": query.ProductIDNotIn,
			},
		})
	}
	// add more ComplexQuery
	if len(query.PrWhHashtagIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$in": query.PrWhHashtagIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.PrWhHashtagNotIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"pr_wh_hashtag": bson.M{
				"$nin": query.PrWhHashtagNotIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.WarehouseCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	// if len(query.ManufacturerCodeIn) > 0 {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"manufacturer_code": bson.M{
	// 			"$in": query.ManufacturerCodeIn,
	// 		},
	// 	})
	// }
	// add more ComplexQuery
	if len(query.OriginIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"origin": bson.M{
				"$in": query.OriginIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.SellerCodeIn) > 0 {
		query.SellerCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"seller_code": bson.M{
				"$in": query.SellerCodeIn,
			},
		})
	}

	// If have marked as block release to fav session -> not show in both fav and publish bidding
	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
		"block_release_to_fav_session": &bson.M{
			"$ne": true,
		},
		"block_release_to_bidding_session": &bson.M{
			"$ne": true,
		},
	})

	// do action
	return resp.Respond(action.GetQuotationProduct(query, payload.Offset, payload.Limit, payload.GetTotal, payload.Sort))
}

// GetQuotationProductVendor
func GetQuotationProductVendor(req sdk.APIRequest, resp sdk.APIResponder) error {
	// var (
	// 	offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
	// 	limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
	// 	getTotal = req.GetParam("getTotal") == "true"
	// 	qStr     = req.GetParam("q")
	// )

	// var query model.QuotationProductVendor
	// if qStr != "" {
	// 	err := json.Unmarshal([]byte(qStr), &query)
	// 	if err != nil {
	// 		return resp.Respond(&common.APIResponse{
	// 			Status:    common.APIStatus.Invalid,
	// 			Message:   "Can not parse input data. " + err.Error(),
	// 			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
	// 		})

	// 	}
	// }

	// validate input
	var payload model.GetQuotationProductVendorPayload
	err := req.GetContent(&payload)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	query := payload.Query

	// add more ComplexQuery
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.ProductIDNotIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$nin": query.ProductIDNotIn,
			},
		})
	}

	// add more ComplexQuery
	if len(query.PurchaserCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"purchaser_code": bson.M{
				"$in": query.PurchaserCodeIn,
			},
		})
	}

	// if len(query.PrWhHashtagIn) > 0 {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"pr_wh_hashtag": bson.M{
	// 			"$in": query.PrWhHashtagIn,
	// 		},
	// 	})
	// }

	// if len(query.PrWhHashtagNotIn) > 0 {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"pr_wh_hashtag": bson.M{
	// 			"$nin": query.PrWhHashtagNotIn,
	// 		},
	// 	})
	// }

	// add more ComplexQuery
	if len(query.WarehouseCodes) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{
				"$in": query.WarehouseCodes,
			},
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetQuotationProductVendor(query, payload.Offset, payload.Limit, payload.GetTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// DeleteQuotationProduct	
func DeleteQuotationProduct(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		productCode = req.GetParam("productCode")
	)

	// validate input
	if productCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid productCode",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductCode),
		})
	}

	// do action
	return resp.Respond(action.DeleteQuotationProduct(productCode))
}
