package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// CreateAdminPushingGroup ...
func GetAdminPushingGroup(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == strconv.FormatBool(true)
		sellerCode = strings.ToUpper(req.GetVar("sellerCode"))
		qStr       = req.GetParam("q")
	)

	var query model.AdminPushingGroup
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}
	query.SellerCode = sellerCode

	// do action
	return resp.Respond(action.GetAdminPushingGroup(&query, offset, limit, getTotal))
}

// ConfirmedPushingGroup ...
func ConfirmedPushingGroup(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.AdminPushingGroup
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.GroupCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "GroupCode invalid.",
			ErrorCode: string(enum.ErrorCodeInvalid.GroupCode),
		})
	}

	// do action
	return resp.Respond(action.ConfirmedPushingGroup(&input))
}
