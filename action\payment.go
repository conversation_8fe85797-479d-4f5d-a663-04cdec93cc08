package action

import (
	"errors"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/core"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

func GetSingleNewPayment(query model.PaymentPlatform, option model.QueryOption) *common.APIResponse {
	return GetNewPayment(query, 0, 1, option, "")
}

// GetNewPayment retrieves a list of new payments based on the provided query parameters.
// It processes various query fields to generate additional query conditions and then
// fetches the payment list from the payment platform.
//
// Parameters:
//   - query: model.PaymentPlatform - The query parameters for filtering payments.
//   - offset: int64 - The offset for pagination.
//   - limit: int64 - The limit for pagination.
//   - option: model.QueryOption - Additional query options.
//   - sort: string - The sorting criteria.
//
// Returns:
//   - *common.APIResponse - The API response containing the list of payments or an error status.
func GetNewPayment(query model.PaymentPlatform, offset, limit int64, option model.QueryOption, sort string) *common.APIResponse {

	// tìm po liên quan
	if len(query.POCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryPORelatedCode(query.POCodeIn)...)
		query.POCodeIn = nil
	}

	// set query by RefundBillCodeIn
	if len(query.RefundBillCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryRefundBillCode(query.RefundBillCodeIn)...)
		query.RefundBillCodeIn = nil
	}
	if len(query.RefundBillCode) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryRefundBillCode([]string{query.RefundBillCode})...)
		query.RefundBillCode = ""
	}

	// set query by PrepaidPOCodeIn
	if len(query.PrepaidPOCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryPrepaidPOCode(query.PrepaidPOCodeIn)...)
		query.PrepaidPOCodeIn = nil
	}

	// set query by AdjustmentBillCodeIn
	if len(query.AdjustmentBillCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryAdjustmentBillCode(query.AdjustmentBillCodeIn)...)
		query.AdjustmentBillCodeIn = nil
	}

	// set query by LegacyPaymentCodeIn
	if len(query.LegacyPaymentCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryLegacyPaymentCode(query.LegacyPaymentCodeIn)...)
		query.LegacyPaymentCodeIn = nil
	}

	// set query by CreatedByID
	if query.CreatedByID != 0 {
		query.ExtraDataKeyValIn = append(query.ExtraDataKeyValIn, query.GenQueryCreatedByAccountID(query.CreatedByID)...)
		query.CreatedByID = 0
	}

	query.FillData()
	cliOption := client.APIOption{
		Body: model.PaymentPlatformQuery{
			Query:  query,
			Offset: offset,
			Limit:  limit,
			Sort:   sort,
			Option: option,
		},
		SaveLog: utils.Pointer.WithBool(false),
	}

	// get payment list from client
	resp := payment_platform.GetPaymentList(cliOption)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	payments := resp.Data.([]*model.PaymentPlatform)
	for i := range payments {
		payments[i].FillSuportField()
	}
	// return response
	resp.Data = payments
	return resp
}

// UpdatePayment
func UpdateNewPayment(input *model.PaymentPlatform) *common.APIResponse {
	checkResp := GetSingleNewPayment(model.PaymentPlatform{PaymentCode: input.PaymentCode}, model.QueryOption{})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	payment := checkResp.Data.([]*model.PaymentPlatform)[0]

	// Create token for upload file
	genTokenResp := core.GenToken()
	if genTokenResp.Status == common.APIStatus.Ok {
		payment.Token = genTokenResp.Message
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: checkResp.Message,
		Data:    []*model.PaymentPlatform{payment},
	}
}

func UpdatePaymentInfo(input model.PaymentPlatform) *common.APIResponse {

	// validate PaymentCode  and PartnerCode
	if len(input.PaymentCode) == 0 || len(input.PartnerCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment code or vendor code is empty",
		}
	}

	queryCheck := model.PaymentPlatform{
		PaymentCode: input.PaymentCode,
	}

	// query check data
	checkResp := GetNewPayment(queryCheck,
		0, 1, model.QueryOption{}, "")
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}

	paymentCheck := checkResp.Data.([]*model.PaymentPlatform)[0]

	// updater data
	updater := model.PaymentPlatform{
		PaymentCode: input.PaymentCode,
	}

	// if paymentCheck.PartnerCode != input.PartnerCode , get vendor child
	if paymentCheck.PartnerCode != input.PartnerCode {
		// check new vendor in line-manager
		opt := client.APIOption{
			Keys: []string{paymentCheck.PartnerCode, input.PartnerCode},
			Params: map[string]string{
				"sellerClass": model.CLASS_VENDOR,
				"sellerCode":  paymentCheck.PartnerCode,
			},
			Offset: utils.Pointer.WithInt(0),
			Limit:  utils.Pointer.WithInt(1),
		}

		// get old vendor
		getOldVendor := seller.GetSellers(opt)
		if getOldVendor.Status != common.APIStatus.Ok {
			return getOldVendor
		}

		oldVendor := getOldVendor.Data.([]*model.Seller)[0]
		if oldVendor.LineManager != input.PartnerCode {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "New vendor is not your line-manager",
			}
		}

		// check payment item
		paymentItemQuery := model.PaymentItemPlatform{
			PaymentCode: input.PaymentCode,
		}

		// get payment item
		paymentItemResp := GetSingleNewPaymentItem(paymentItemQuery, model.QueryOption{})

		// check paymentItemResp status
		if paymentItemResp.Status == common.APIStatus.Error {
			return paymentItemResp
		}

		// check paymentItemResp status
		if paymentItemResp.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Payment has been paid, cannot be change. Please cancel all payments related to PO or bill",
				ErrorCode: string(enum.ErrorCodeInvalid.CancelPaymentPaid),
			}
		}

		// get new vendor
		updater.PartnerCode = input.PartnerCode
		updater.PartnerName = input.PartnerName
	}

	if input.Note != paymentCheck.Note {
		updater.Note = input.Note
	}

	updater.FillData()
	option := client.APIOption{
		Body:    updater,
		SaveLog: utils.Pointer.WithBool(true),
	}

	// cal client to update payment
	return payment_platform.UpdatePayment(option)
}

// DeletePayment
func DeleteNewPayment(paymentCode string) *common.APIResponse {
	// check paymentCode
	if paymentCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PaymentCode",
			ErrorCode: string(enum.ErrorCodeInvalid.PaymentCode),
		}
	}
	// check
	queryCheck := model.PaymentPlatform{
		PaymentCode: paymentCode,
	}
	// get payment
	checkResp := GetNewPayment(queryCheck, 0, 1, model.QueryOption{
		Items: true,
	}, "")
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	payment := checkResp.Data.([]*model.PaymentPlatform)[0]
	paymentItems := payment.Items
	if payment.ReasonCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid ReasonCode",
		}
	}

	// check reason
	reasonData, err := ParsePaymentReasonData([]string{payment.ReasonCode})
	if err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}
	reason := reasonData[0]

	// check payment item
	if len(paymentItems) > 0 {
		switch reason.Data {
		case enum.PaymentPlatformReasonCore.PAYMENT_CLEARING_DEBT:
			// for _, paymentItem := range paymentItems {
			// 	cancelItemResp := CancelPaymentItem(paymentItem.PaymentItemCode)
			// 	if cancelItemResp.Status != common.APIStatus.Ok {
			// 		return cancelItemResp
			// 	}
			// }

			// pass
		default:
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Payment has been paid, cannot be canceled. Please cancel all payments related to PO or bill",
				ErrorCode: string(enum.ErrorCodeInvalid.CancelPaymentPaid),
			}
		}
	}

	// SwitchStatusPayment to CANCELLED
	cancelPaymentResp := SwitchStatusPayment(paymentCode, enum.PaymentPlatformStatus.CANCELLED)
	if cancelPaymentResp.Status != common.APIStatus.Ok {
		return cancelPaymentResp
	}

	// get AdjustmentBillCode, RefundBillCode
	ABcode := payment.DecodeAdjustmentBillCode()
	RBcode := payment.DecodeRefundBillCode()
	switch {
	case ABcode != "": // update AdjustmentBill về WAIT_TO_PAID
		model.AdjustmentBillDB.UpdateOne(
			model.AdjustmentBill{AdjustmentBillCode: payment.AdjustmentBillCode},
			model.AdjustmentBill{Status: enum.AdjustmentBillStatus.WAIT_TO_PAID},
		)
	case RBcode != "": // udpate refund bill
		option := client.APIOption{
			Body: model.RefundBill{
				RefundBillCode: payment.RefundBillCode,
				Status:         enum.RefundBillStatus.DRAFT,
			},
		}
		seller.SwitchStatusRefundBill(option)
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Delete payment success",
	}
}

func CreateNewPayment(input *model.PaymentPlatform) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment is empty",
		}
	}

	// tạo mới thì ko có po liên quan
	input.PORelatedCodes = []string{}

	// for default sort query
	input.LastUpdatedTime = utils.Pointer.WithTime(time.Now())
	if len(input.Purpose) > 0 && len(input.ReasonCode) == 0 {
		input.ReasonCode = input.Purpose
	}

	if input.ReasonCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid ReasonCode",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	// optionField := model.OptionFillDataPaymentPlatform{
	// 	ExtraData: true,
	// }
	// input.FillData(optionField)
	reasonResp := GetPaymentReason(model.ReasonSetting{
		ReasonCode: input.ReasonCode,
	})
	if reasonResp.Status != common.APIStatus.Ok {
		return reasonResp
	}

	// logic by reason
	{
		reason, err := ParsePaymentReasonData([]string{input.ReasonCode})
		if err != nil {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: err.Error(),
			}
		}
		input.Reason = *reason[0]
	}

	// validate
	if input.PartnerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PartnerCode (VendorCode)",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	// validate
	if input.Amount == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Amount",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	// do action by input.Type
	switch input.Type {
	case enum.PaymentPlatformType.PAYMENT:
		return CreateSpendingPayment(input)
	case enum.PaymentPlatformType.RECEIPT:
		return CreateCollectingPayment(input)
	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment type is invalid",
		}
	}
}

// CreateSpendingPayment
func CreateSpendingPayment(input *model.PaymentPlatform) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment is empty",
		}
	}

	// validate AdjustmentBillCode and RefundBillCode
	if len(input.AdjustmentBillCode) > 0 || len(input.RefundBillCode) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Not support adjustment bill, refund bill this payment type",
		}
	}

	// validate balance
	if input.Balance <= 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Balance must be greater than 0",
		}
	}

	// hỗ trợ loại trả trước, trả trực tiếp
	switch input.Reason.Data {
	case enum.PaymentPlatformReasonCore.ADVANCE_MONEY:
		// loại trả trước chỉ hỗ trợ cho PO
		if len(input.PrepaidPOCodes) == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Pre-paid PO code is empty",
			}
		}

	case enum.PaymentPlatformReasonCore.BILL:
		// loại trả trực tiếp chỉ hỗ trợ cho bill

	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment purpose is invalid",
		}
	}

	// call action
	return genPaymentInfo(input)
}

func CreateCollectingPayment(input *model.PaymentPlatform) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment is empty",
		}
	}

	// hỗ trợ loại tăng dư nợ, trả trực tiếp
	switch input.Reason.Data {
	case enum.PaymentPlatformReasonCore.DEBT:
		// tăng công nợ buộc phải có số dư (tạo trước xài sau)
		if input.Balance <= 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Balance must be greater than 0",
			}
		}
	case enum.PaymentPlatformReasonCore.PAY_DIRECT:
		// loại này chỉ lưu như log, force không xài như voucher dư nợ
		item := &model.PaymentItemPlatform{
			Amount:          &input.Amount,
			ObjectType:      enum.PaymentPlatformObjectType.OTHER,
			ObjectCode:      "OTHER",
			Note:            "Payment Direct System auto create item",
			TransactionType: "DECREASE_BALANCE",
			PartnerCode:     input.PartnerCode,
		}
		item.FillData(model.OptionFillDataPaymentPlatform{
			ExtraData: true,
		})
		input.Items = append(input.Items, item)
	case enum.PaymentPlatformReasonCore.PAYMENT_CLEARING_DEBT:
		if input.PaidTime == nil || input.PaidTime.After(time.Now().Add(24*time.Hour)) {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid Paid Time",
			}
		}
	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment purpose is invalid",
		}
	}

	// check if ADJUSTMENT_BILL exist another payment
	if len(input.AdjustmentBillCode) > 0 {
		paymentQuery := model.PaymentPlatform{AdjustmentBillCodeIn: []string{input.AdjustmentBillCode}}
		checkPaymentkResp := GetSingleNewPayment(paymentQuery, model.QueryOption{})
		if checkPaymentkResp.Status == common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Adjustment bill has been paid",
			}
		}
		// switch ADJUSTMENT_BILL status
		switchABResp := SwitchStatusAdjustmentBill(input.AdjustmentBillCode, enum.AdjustmentBillStatus.PAID)
		if switchABResp.Status != common.APIStatus.Ok {
			return switchABResp
		}
	}

	return genPaymentInfo(input)
}

func genPaymentInfo(input *model.PaymentPlatform) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Payment is empty",
		}
	}

	// create Payment
	now := utils.GetVietnamTimeNow()

	if input.PaidTime == nil {
		input.PaidTime = &now
	}

	// get vendor info
	vendorResp := getVendorInfo(input.PartnerCode)
	if vendorResp.Status != common.APIStatus.Ok {
		return vendorResp
	}
	vendor := vendorResp.Data.([]*model.Seller)[0]

	// set data
	input.PartnerName = vendor.Name
	input.PartnerType = enum.PaymentPlatformPartnerType.THUOCSIVN_VENDOR
	input.BranchCode = conf.Config.BranchCode
	input.Source = enum.PaymentCreatedBySource.THUOCSIVN_INTERNAL_SELLER
	input.CurrencyCode = conf.Config.CurrencyCode
	input.FillData(model.OptionFillDataPaymentPlatform{
		ExtraData: true,
	})
	option := client.APIOption{
		Body:    input,
		SaveLog: utils.Pointer.WithBool(true),
	}

	// create payment by client
	createResp := payment_platform.CreatePayment(option)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}
	payment := createResp.Data.([]*model.PaymentPlatform)[0]
	payment.FillSuportField()

	// Create token for upload file
	genTokenResp := core.GenToken()
	if genTokenResp.Status == common.APIStatus.Ok {
		payment.Token = genTokenResp.Message
	}

	// chi tạm ứng thành công -> tự động áp dụng cho các VB đang chờ thanh toán
	if payment.Type == enum.PaymentPlatformType.PAYMENT && input.Reason.Data == enum.PaymentPlatformReasonCore.ADVANCE_MONEY {
		for _, poCode := range payment.DecodePrepaidPOs() {
			vbResp := model.VendorBillDB.QueryOne(model.VendorBill{POCode: poCode})
			if vbResp.Status != common.APIStatus.Ok {
				continue
			}

			vb := vbResp.Data.([]*model.VendorBill)[0]
			if vb.Status == enum.VendorBillStatus.WAIT_TO_PAID {
				AutoApplyPaymentVoucherToVB(vb.VendorBillCode)
			}
		}
	}

	// return response
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: createResp.Message,
		Data:    []*model.PaymentPlatform{payment},
	}
}

// DeletePayment
// func DeletePayment(paymentCode string) *common.APIResponse {

// 	checkResp := model.PaymentDB.QueryOne(model.Payment{PaymentCode: paymentCode})
// 	if checkResp.Status != common.APIStatus.Ok {
// 		return checkResp
// 	}

// 	paymentItemResp := model.PaymentItemDB.QueryOne(model.PaymentItem{
// 		PaymentCode: paymentCode,
// 	})
// 	if paymentItemResp.Status == common.APIStatus.Error {
// 		return paymentItemResp
// 	}
// 	if paymentItemResp.Status == common.APIStatus.Ok {
// 		return &common.APIResponse{
// 			Status:    common.APIStatus.Invalid,
// 			Message:   "Payment has been paid, cannot be canceled. Please cancel all payments related to PO or vendor bill",
// 			ErrorCode: string(enum.ErrorCodeInvalid.CancelPaymentPaid),
// 		}
// 	}

// 	// if payment.RefundBillCode != "" {
// 	// 	updateResp := model.RefundBillDB.UpdateOne(
// 	// 		model.RefundBill{RefundBillCode: payment.RefundBillCode},
// 	// 		model.RefundBill{Status: enum.RefundBillStatus.DRAFT},
// 	// 	)
// 	// 	if updateResp.Status != common.APIStatus.Ok {
// 	// 		return updateResp
// 	// 	}
// 	// }

// 	return model.PaymentDB.Delete(model.Payment{PaymentCode: paymentCode})
// }

// forward
// GetPaymentReason retrieves payment reasons based on the provided ReasonSetting input.
// It sets the CompanyCode, Status, and ReasonType fields of the input to predefined values,
// constructs an APIOption with the input query, and calls the GetReasonSetting function
// from the payment_platform package to fetch the reasons.
//
// Parameters:
//   - input: model.ReasonSetting - The input settings for querying payment reasons.
//
// Returns:
//   - *common.APIResponse - The API response containing the payment reasons.
func GetPaymentReason(input model.ReasonSetting) *common.APIResponse {
	input.CompanyCode = conf.Config.CompanyCode
	input.Status = "ACTIVE"
	input.ReasonType = "PAYMENT"
	option := client.APIOption{
		Body: model.ReasonSettingQuery{
			Query:  input,
			Offset: 0,
			Limit:  1000,
		},
	}

	return payment_platform.GetReasonSetting(option)
}

// ParsePaymentReasonData parses a list of reason codes and returns a list of ReasonPaymentParse objects.
// It fetches the payment reasons using the provided reason codes and maps the extra data to the corresponding fields.
//
// Parameters:
//   - reasonCode: A slice of reason codes to be parsed.
//
// Returns:
//   - A slice of pointers to ReasonPaymentParse objects containing the parsed data.
//   - An error if the reason codes are empty, if the API response status is not OK, or if no valid reason codes are found.
func ParsePaymentReasonData(reasonCode []string) ([]*model.ReasonPaymentParse, error) {
	if len(reasonCode) == 0 {
		return nil, errors.New("ReasonCode is empty")
	}
	// check reason
	reasonResp := GetPaymentReason(model.ReasonSetting{
		ReasonCodeIn: reasonCode,
	})

	if reasonResp.Status != common.APIStatus.Ok {
		return nil, errors.New(reasonResp.Message)
	}

	// parse reason
	reasons := reasonResp.Data.([]*model.ReasonSetting)
	output := []*model.ReasonPaymentParse{}
	for i := range reasons {
		r := reasons[i]
		mapKeyValue := map[string]any{}
		for _, obj := range r.ExtraData {
			mapKeyValue[obj.Key] = obj.Value
		}

		coreI, ok := mapKeyValue["CORE"]
		if !ok {
			continue
		}

		coreValue, ok := coreI.(string)
		if !ok {
			continue
		}

		// check coreValue
		data := &model.ReasonPaymentParse{
			Data: enum.PaymentPlatformReasonType(coreValue),
			Code: r.ReasonCode,
		}
		output = append(output, data)
	}

	// check output
	if len(output) == 0 {
		return nil, errors.New("ReasonCode not found")
	}

	return output, nil
}

// SwitchStatusPayment updates the status of a payment platform based on the provided payment code and status.
// It first retrieves the payment platform using the provided payment code. If the payment platform is found,
// it updates its status and sends the updated payment platform to the payment platform service to switch the status.
//
// Parameters:
//   - paymentCode: A string representing the code of the payment platform to be updated.
//   - status: An enum value representing the new status to be set for the payment platform.
//
// Returns:
//   - *common.APIResponse: The response from the payment platform service after attempting to switch the status.
//     If the payment platform is not found, it returns the response from the GetNewPayment function.
func SwitchStatusPayment(paymentCode string, status enum.PaymentPlatformStatusValue) *common.APIResponse {
	checkQuery := model.PaymentPlatform{
		PaymentCode: paymentCode,
	}
	checkResp := GetNewPayment(checkQuery, 0, 1, model.QueryOption{}, "")
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}

	// get payment
	payment := checkResp.Data.([]*model.PaymentPlatform)[0]
	payment.Status = status
	cliOption := client.APIOption{
		Body: payment,
	}

	return payment_platform.SwitchStatusPayment(cliOption)
}

// updatePayment updates the payment information using the provided PaymentPlatform updater.
// It constructs an APIOption with the updater as the body and enables logging.
// The function returns an APIResponse indicating the result of the update operation.
//
// Parameters:
// - updater: model.PaymentPlatform - The payment platform updater containing the updated payment information.
//
// Returns:
// - *common.APIResponse - The response from the payment platform update operation.
func updatePayment(updater model.PaymentPlatform) *common.APIResponse {
	option := client.APIOption{
		Body:    updater,
		SaveLog: utils.Pointer.WithBool(true),
	}
	return payment_platform.UpdatePayment(option)
}
