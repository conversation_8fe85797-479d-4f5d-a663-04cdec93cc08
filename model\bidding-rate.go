package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BiddingRate struct {
	// basic info
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Key           string `json:"key,omitempty" bson:"key,omitempty"`
	ProductID     int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode   string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductName   string `json:"productName,omitempty" bson:"product_name,omitempty"`
	SKU           string `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode      string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	IsValid       bool   `json:"isValid,omitempty" bson:"is_valid,omitempty"`

	ExpectedQuantity *int64 `json:"expectedQuantity,omitempty" bson:"expected_quantity,omitempty"`
	POsQuantity      *int64 `json:"posQuantity,omitempty" bson:"pos_quantity,omitempty"`

	PurchaserCode  string     `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	ActionByName   string     `json:"actionByName" bson:"action_by_name,omitempty"`
	ActionByID     int64      `json:"actionByID" bson:"action_by_id,omitempty"`
	LastActionTime *time.Time `json:"lastActionTime,omitempty" bson:"last_action_time,omitempty"`

	VendorPIC           int64  `json:"vendorPIC,omitempty" bson:"vendor_pic,omitempty"`
	FavoriteVendorID    int64  `json:"favoriteVendorID,omitempty" bson:"favorite_vendor_id,omitempty"`
	FavoriteVendorName  string `json:"favoriteVendorName,omitempty" bson:"favorite_vendor_name,omitempty"`
	FavoriteVendorPhone string `json:"favoriteVendorPhone,omitempty" bson:"favorite_vendor_phone,omitempty"`

	// FE
	Notes              *[]string  `json:"notes,omitempty" bson:"note,omitempty"`
	BiddingRatePercent float32    `json:"biddingRatePercent" bson:"-"`
	SkuConfig          *SkuConfig `json:"skuConfig" bson:"-"`
	OldStatus          string     `json:"oldStatus,omitempty" bson:"oldStatus,omitempty"`

	// filter
	// format DD/MM/YYYY
	POCodes    *[]string `json:"poCodes,omitempty" bson:"po_codes,omitempty"`
	DaySpan    string    `json:"daySpan,omitempty" bson:"day_span,omitempty"`
	SellerCode string    `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SkuPIC     int64     `json:"skuPIC,omitempty" bson:"sku_pic,omitempty"`

	ComplexQuery    []*bson.M `json:"-" bson:"$and,omitempty"`
	WarehouseCodes  []string  `json:"warehouseCodes,omitempty" bson:"-"`
	PurchaserCodeIn []string  `json:"purchaserCodeIn,omitempty" bson:"-"`
	SellerCodes     []string  `json:"sellerCodes,omitempty" bson:"-"`
	POCode          string    `json:"poCode,omitempty" bson:"-"`
	Search          string    `json:"search,omitempty" bson:"-"`
}

// BiddingRateDB ...
var BiddingRateDB = &db.Instance{
	ColName:        "bidding_rate",
	TemplateObject: &BiddingRate{},
}

func InitBiddingRateModel(s *mongo.Database) {
	BiddingRateDB.ApplyDatabase(s)

	// t := true
	// _ = BiddingRateDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "day_span", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	// Unique:     &t,
	// })

	// t := true
	// _ = BiddingRateDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "key", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = BiddingRateDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "day_span", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "sku_pic", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// var second int32 = 60 * 60 * 24 * 60 // 60 ngày
	// _ = BiddingRateDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &second,
	// })
}
