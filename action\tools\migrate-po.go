package tools

import (
	"fmt"
	"log"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func MigratePO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var poCode = req.GetParam("poCode")

	go func(poCode string) {
		migratePO(poCode)
	}(poCode)

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func migratePO(poCode string) {
	log.Println("Start MigratePO")
	defer log.Println("End MigratePO")

	posMigrate := []*model.PurchaseOrder{}
	_id := primitive.NilObjectID
	// queryday lấy ngày 25/2/2025
	queryDay := time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC)

	for {

		query := model.PurchaseOrder{
			SellerCode: "MEDX",
			ComplexQuery: []*bson.M{
				{
					"_id": &bson.M{"$gt": _id},
				},
				{
					"created_time": &bson.M{"$gte": queryDay},
				},
				{
					"status": &bson.M{"$in": []string{"DRAFT", "CONFIRMED", "PROCESSING"}},
				},
			}}
		if poCode != "" {
			query.POCode = poCode
		}

		posResp := model.PurchaseOrderDB.Query(
			query,
			0, 100, &primitive.M{"_id": 1},
		)

		if posResp.Status != common.APIStatus.Ok {
			break
		}

		pos := posResp.Data.([]*model.PurchaseOrder)
		for _, po := range pos {
			// increase offset
			_id = *po.ID
			// if po.Type == "REPLENISHMENT" {
			// 	continue
			// }
			// add to list
			posMigrate = append(posMigrate, po)
		}

		total := len(posMigrate)
		for i, po := range posMigrate {

			// migrate po
			// get po items
			poItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, nil)
			if poItemsResp.Status != common.APIStatus.Ok {
				log.Printf("Get PO Items Error: %s\n", poItemsResp.Message)
				continue
			}
			poItems := poItemsResp.Data.([]*model.PurchaseOrderItem)

			newItems := []*model.PurchaseOrderItem{}
			for _, poItem := range poItems {

				// po cos gia bang khong thi khong chay
				if poItem.UnitPrice == 0 {
					continue
				}
				// nếu có BBG thì sẽ lấy thông tin BBG cài đặt ngược lại
				getContractPriceItemResp := action.GetContractPriceItemV2(poItem.ProductID, poItem.WarehouseCode, poItem.VendorCode)
				if getContractPriceItemResp.Status == common.APIStatus.Ok {
					if len(getContractPriceItemResp.Data.([]*model.ContractPriceItem)) == 0 {
						log.Printf("Get Contract Price Item Error: %s\n", "Not found")
						continue
					}
					contractPriceItem := getContractPriceItemResp.Data.([]*model.ContractPriceItem)[0]
					poItem.UnitPrice = contractPriceItem.UnitPrice
					poItem.VAT = &contractPriceItem.VAT
					poItem.DiscountPercent = &contractPriceItem.Discount
				}

				newItems = append(newItems, poItem)
			}

			po.Items = newItems

			result := CalculatePricePO(newItems, po)
			purchaseOrderResp := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: po.POCode}, model.PurchaseOrder{
				TotalPrice:           result.TotalPrice,
				TotalVatPrice:        result.TotalVatPrice,
				TotalWithoutVatPrice: result.TotalWithoutVatPrice,
			})

			if purchaseOrderResp.Status != common.APIStatus.Ok {
				log.Printf("Update PO Error: %s\n", purchaseOrderResp.Message)
				continue
			}

			po.TotalPrice = result.TotalPrice
			po.TotalVatPrice = result.TotalVatPrice
			po.TotalWithoutVatPrice = result.TotalWithoutVatPrice

			model.PurchaseOrderMigrateDB.Create(po)

			if len(result.Items) == 0 {
				continue
			}
			for _, itemUpdated := range result.Items {
				purchaseOrderItemResp := model.PurchaseOrderItemDB.UpdateOne(
					model.PurchaseOrderItem{POCode: po.POCode, POItemID: itemUpdated.POItemID},
					itemUpdated)

				if purchaseOrderItemResp.Status != common.APIStatus.Ok {
					log.Printf("Update PO Item Error: %s\n", purchaseOrderItemResp.Message)
					continue
				}
				model.PurchaseOrderItemMigrateDB.Create(itemUpdated)
			}
			if po.Status != "DRAFT" {
				opts := client.APIOption{
					Keys: []string{"recall_vendor_promotion", po.POCode},
					Params: map[string]string{
						"poCode":    po.POCode,
						"isMigrate": "true",
					},
				}

				seller.PutRecallVendorPromotion(opts)
			}
			fmt.Printf("Update %d/%d\n", i+1, total)
		}
	}

}

func MigrateCompletedPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var poCode = req.GetParam("poCode")

	go func(poCode string) {
		migrateCompletedPO(poCode)
	}(poCode)

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func migrateCompletedPO(poCode string) {
	log.Println("Start migrateCompletedPO")
	defer log.Println("End migrateCompletedPO")

	posMigrate := []*model.PurchaseOrder{}
	_id := primitive.NilObjectID
	// queryday lấy ngày 25/2/2025
	queryDay := time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC)

	for {

		query := model.PurchaseOrder{
			SellerCode: "MEDX",
			ComplexQuery: []*bson.M{
				{
					"_id": &bson.M{"$gt": _id},
				},
				{
					"created_time": &bson.M{"$gte": queryDay},
				},
				{
					"status": &bson.M{"$in": []string{"COMPLETED"}},
				},
			}}
		if poCode != "" {
			query.POCode = poCode
		}

		posResp := model.PurchaseOrderDB.Query(
			query,
			0, 100, &primitive.M{"_id": 1},
		)

		if posResp.Status != common.APIStatus.Ok {
			break
		}

		pos := posResp.Data.([]*model.PurchaseOrder)
		for _, po := range pos {
			// increase offset
			_id = *po.ID
			// if po.Type == "REPLENISHMENT" {
			// 	continue
			// }
			// add to list
			posMigrate = append(posMigrate, po)
		}

		total := len(posMigrate)
		for i, po := range posMigrate {

			// migrate po
			// get po items
			poItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, nil)
			if poItemsResp.Status != common.APIStatus.Ok {
				log.Printf("Get PO Items Error: %s\n", poItemsResp.Message)
				continue
			}
			poItems := poItemsResp.Data.([]*model.PurchaseOrderItem)

			newItems := []*model.PurchaseOrderItem{}
			for _, poItem := range poItems {

				// po cos gia bang khong thi khong chay
				if poItem.UnitPrice == 0 {
					continue
				}
				// nếu có BBG thì sẽ lấy thông tin BBG cài đặt ngược lại
				getContractPriceItemResp := action.GetContractPriceItemV2(poItem.ProductID, poItem.WarehouseCode, poItem.VendorCode)
				if getContractPriceItemResp.Status == common.APIStatus.Ok {
					if len(getContractPriceItemResp.Data.([]*model.ContractPriceItem)) == 0 {
						log.Printf("Get Contract Price Item Error: %s\n", "Not found")
						continue
					}
					contractPriceItem := getContractPriceItemResp.Data.([]*model.ContractPriceItem)[0]
					poItem.UnitPrice = contractPriceItem.UnitPrice
					poItem.VAT = &contractPriceItem.VAT
					poItem.DiscountPercent = &contractPriceItem.Discount
				}

				if poItem.Metadata != nil && len(poItem.Metadata.ApplyPromotions) > 0 && poItem.ExpectQuantity > 0 && poItem.UnitPrice > 0 {

					// Tổng tiền sau chiết khấu đối = A
					totalAmountAfterDiscount := poItem.UnitPrice * float64(poItem.ExpectQuantity)
					totalUnitPrice := poItem.UnitPrice * float64(poItem.ExpectQuantity)
					discountByBBG := 0.0
					if poItem.DiscountPercent != nil {
						discountByBBG = *poItem.DiscountPercent

					}
					if discountByBBG > 0 {
						totalAmountAfterDiscount *= (1 - discountByBBG/100)
					}

					var (
						discountPercentRoots    = poItem.Metadata.DiscountPercentRoots
						discountPercentCurrents = poItem.Metadata.DiscountPercentCurrents
						discountAbsolutes       = poItem.Metadata.DiscountAbsolutes
					)

					// PERCENTAGE_ROOT
					if len(discountPercentRoots) > 0 {
						totalDiscountPercent := 0.0
						for _, discountPercent := range discountPercentRoots {
							totalDiscountPercent += (discountPercent / 100)
						}
						totalAmountAfterDiscount *= (1 - totalDiscountPercent)

					}
					// PERCENTAGE_CURRENT
					for _, discountPercent := range discountPercentCurrents {
						totalAmountAfterDiscount *= (1 - (discountPercent / 100))
					}
					// ABSOLUTE
					for _, discountAmount := range discountAbsolutes {
						totalAmountAfterDiscount -= discountAmount
					}
					// discount percent
					// [chiết khấu = tổng ban đầu -  tổng sau chiết khấu]
					discountPrice := poItem.UnitPrice*float64(poItem.ExpectQuantity) - totalAmountAfterDiscount
					if discountPrice > 0 {
						poItem.DiscountPrice = &discountPrice
					}
					// phần trăm sẽ chiết khấu
					percentDiscount := utils.MaxFloat64(0, calcPercentFromAmount(totalUnitPrice, discountPrice))
					// safety percent discount
					if percentDiscount > 100 {
						percentDiscount = 100
					}
					// replace old po_item calculated price
					poItem.DiscountPercent = utils.Pointer.WithFloat64(percentDiscount)

				}
				newItems = append(newItems, poItem)
			}

			result := CalculatePricePO(newItems, po)
			purchaseOrderResp := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: po.POCode}, model.PurchaseOrder{
				TotalPrice:           result.TotalPrice,
				TotalVatPrice:        result.TotalVatPrice,
				TotalWithoutVatPrice: result.TotalWithoutVatPrice,
			})

			if purchaseOrderResp.Status != common.APIStatus.Ok {
				log.Printf("Update PO Error: %s\n", purchaseOrderResp.Message)
				continue
			}

			po.TotalPrice = result.TotalPrice
			po.TotalVatPrice = result.TotalVatPrice
			po.TotalWithoutVatPrice = result.TotalWithoutVatPrice

			model.PurchaseOrderMigrateDB.Create(po)

			if len(result.Items) == 0 {
				continue
			}
			for _, itemUpdated := range result.Items {
				purchaseOrderItemResp := model.PurchaseOrderItemDB.UpdateOne(
					model.PurchaseOrderItem{POCode: po.POCode, POItemID: itemUpdated.POItemID},
					itemUpdated)

				if purchaseOrderItemResp.Status != common.APIStatus.Ok {
					log.Printf("Update PO Item Error: %s\n", purchaseOrderItemResp.Message)
					continue
				}
				model.PurchaseOrderItemMigrateDB.Create(itemUpdated)
			}
			if po.Status != "DRAFT" {
				opts := client.APIOption{
					Keys: []string{"recall_vendor_promotion", po.POCode},
					Params: map[string]string{
						"poCode":    po.POCode,
						"isMigrate": "true",
					},
				}

				seller.PutRecallVendorPromotion(opts)
			}
			fmt.Printf("Update %d/%d\n", i+1, total)
		}
	}

}

type minCondition struct {
	totalQuantity int64
	totalAmount   float64
}

type productFullfillPromotion struct {
	rewardType  enum.VendorDiscountRewardTypeValue
	rewardValue model.DiscountRangeReward
}

// UnitPrice, DiscountPercent, VAT, ExpectQuantity
type productAmount struct {
	// track total
	TotalUnitPrice        float64
	TotalUnitPriceWithVAT float64

	// track product of po_item
	TotalQuantity int64

	// track for combo
	ComboQuantity int64

	// range discount to calculate
	DiscountAbsolutes       []float64
	DiscountPercentRoots    []float64
	DiscountPercentCurrents []float64

	DiscountContractPrice float64

	// gắn vào po_item
	AppliedPromotionCodes map[string]struct{} // key promotion_code  -> dùng map để tránh duplicate promotion_code
}

// tính toán promotion dựa vào Giá Trị tiền mua
func calcDiscountAmount(promotionItem *model.VendorDiscountItem, mapProductAmount map[int64]*productAmount, promotionCode string) {
	// tracking po fullfillment promotion
	// map<productID, struct{productFullfillPromotion}>
	var mapProductFullfill = make(map[int64]productFullfillPromotion, 8)

	// ============== Ràng buộc Min ==============
	{
		mapMinCondition := make(map[int64]minCondition, 8)
		// sum totalAmount, totalQuantity
		for productID, productAmount := range mapProductAmount {
			minCond := mapMinCondition[productID]
			// quantity
			minCond.totalQuantity += productAmount.TotalQuantity
			// amount
			if enum.VendorDiscountRangeType.AMOUNT == promotionItem.DiscountRangeType && promotionItem.VATIncluded {
				minCond.totalAmount += productAmount.TotalUnitPriceWithVAT
			} else {
				// mặc định nếu loại khác AMOUNT thì lấy unit price
				// hoặc cùng loại AMOUNT và chọn theo unit price
				minCond.totalAmount += productAmount.TotalUnitPrice
			}

			mapMinCondition[productID] = minCond
		}
		// check min condition
		for _, con := range promotionItem.DiscountRewardProducts {
			// xử scope all ở dưới
			// chưa hỗ trợ loại scope all & condition min tất cả
			if con.ProductID == 0 {
				continue
			}
			minCond := mapMinCondition[con.ProductID]
			if minCond.totalQuantity < con.MinQuantity || minCond.totalAmount < con.MinAmount {
				return
			}
		}
	}

	for _, discountRange := range promotionItem.DiscountRangeRewards {
		// scope ALL apply promotion for all mapProductAmount

		if promotionItem.Scope == enum.ScopePromotion.ALL {
			var totalInPO float64 = 0.0
			for _, productAmount := range mapProductAmount {
				// [Khuyến mãi dựa vào GIÁ TRỊ ĐI MUA]
				// kiểm tra điều kiện bao gồm thuế
				switch promotionItem.DiscountRangeType {
				case enum.VendorDiscountRangeType.AMOUNT:
					if promotionItem.VATIncluded {
						totalInPO += productAmount.TotalUnitPriceWithVAT
					} else {
						totalInPO += productAmount.TotalUnitPrice
					}
				case enum.VendorDiscountRangeType.QUANTITY:
					totalInPO += float64(productAmount.TotalQuantity)
				}
			}

			// Accepted range
			// (from < accept < to) || (from < accept  && to == 0)
			if checkCurrentWithRange(discountRange.From, discountRange.To, totalInPO) {
				for productID := range mapProductAmount {
					mapProductFullfill[productID] = productFullfillPromotion{
						rewardType:  promotionItem.DiscountRewardType,
						rewardValue: *discountRange,
					}
				}
			}
		} else if promotionItem.Scope == enum.ScopePromotion.PRODUCT {
			//loop condition
			var totalInPO float64 = 0.0

			for _, discountRewardProduct := range promotionItem.DiscountRewardProducts {
				productID := discountRewardProduct.ProductID
				productAmount, ok := mapProductAmount[productID]
				if !ok {
					continue
				}
				// [Khuyến mãi dựa vào GIÁ TRỊ ĐI MUA]
				// kiểm tra điều kiện bao gồm thuế
				switch promotionItem.DiscountRangeType {
				case enum.VendorDiscountRangeType.AMOUNT:
					if promotionItem.VATIncluded {
						totalInPO += productAmount.TotalUnitPriceWithVAT
					} else {
						totalInPO += productAmount.TotalUnitPrice
					}
				case enum.VendorDiscountRangeType.QUANTITY:
					totalInPO += float64(productAmount.TotalQuantity)
				}
			}

			if checkCurrentWithRange(discountRange.From, discountRange.To, totalInPO) {
				//fill rewards
				for _, discountRewardProduct := range promotionItem.DiscountRewardProducts {
					_, ok := mapProductAmount[discountRewardProduct.ProductID]
					if ok {
						mapProductFullfill[discountRewardProduct.ProductID] = productFullfillPromotion{
							rewardType:  promotionItem.DiscountRewardType,
							rewardValue: *discountRange,
						}
					}
				}
			}
		}
	}

	// DiscountAbsolutes, DiscountPercentRoots, DiscountPercentCurrents
	for productID, fullfillPromotion := range mapProductFullfill {
		switch fullfillPromotion.rewardType {
		case enum.VendorDiscountRewardType.ABSOLUTE:
			// giảm giá tiền
			mapProductAmount[productID].DiscountAbsolutes = append(mapProductAmount[productID].DiscountAbsolutes, fullfillPromotion.rewardValue.Discount)

		case enum.VendorDiscountRewardType.PERCENTAGE_ROOT:
			// giảm % dựa vào giá gốc
			mapProductAmount[productID].DiscountPercentRoots = append(mapProductAmount[productID].DiscountPercentRoots, fullfillPromotion.rewardValue.Discount)

		case enum.VendorDiscountRewardType.PERCENTAGE_CURRENT:
			// giảm % dựa vào giá hiện tại
			mapProductAmount[productID].DiscountPercentCurrents = append(mapProductAmount[productID].DiscountPercentCurrents, fullfillPromotion.rewardValue.Discount)
		}

		// track what promotion is applied for Product
		mapProductAmount[productID].AppliedPromotionCodes[promotionCode] = struct{}{}
	}
}

func checkCurrentWithRange(from, to, current float64) bool {
	return current > 0 &&
		from <= current &&
		(current < to || to == 0)
}

// tính kết quả từ nhiều tầng %
func calcAmountWithPercent(base float64, percents ...float64) float64 {
	var totalPercent float64 = 0
	for _, percent := range percents {
		totalPercent += (percent / 100)
	}
	return base * totalPercent
}

// tính % từ kết quả
func calcPercentFromAmount(base float64, val float64) float64 {
	return (100 * val) / base
}

func getPOItemPromotionVAT(sellerCode, purchaserCode, poCode, sku, vendorCode string) float64 {
	var vat float64
	var sellerType string

	// get vendor info for claiming seller class
	vendorResp := seller.GetSellers(client.APIOption{
		Keys: []string{vendorCode},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  vendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	})
	if vendorResp.Status == common.APIStatus.Ok {
		vendorList := vendorResp.Data.([]*model.Seller)
		for _, vendor := range vendorList {
			sellerType = string(vendor.SellerType)
		}
	}

	setting := &model.Setting{}

	settingResp := model.SettingDB.QueryOne(nil)
	if settingResp.Status == common.APIStatus.Ok {
		setting = settingResp.Data.([]*model.Setting)[0]
	}

	// get sku config and calculate VAT based on config
	switch sellerType {
	case "NON-TRADING":
		vat = -1

		if setting.SettingVAT != nil && setting.SettingVAT.DefaultNonTrading != nil {
			vat = float64(*setting.SettingVAT.DefaultNonTrading)
		}

	case "TRADING":
		vat = 5

		if setting.SettingVAT != nil && setting.SettingVAT.DefaultTrading != nil {
			vat = float64(*setting.SettingVAT.DefaultTrading)
		}

		skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
			SellerCode:    sellerCode,
			PurchaserCode: purchaserCode,
			SKU:           sku,
		})

		if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfig := skuConfigResp.Data.([]*model.SkuConfig)
			for _, config := range skuConfig {
				if config.Vendors != nil {
					for _, vendor := range *config.Vendors {
						if vendor.VendorCode == vendorCode {
							if vendor.VAT > 0 {
								vat = float64(vendor.VAT)
							}
						}
					}
				}
			}
		}
	}

	return vat
}

func validCalcPromotion(poItem *model.PurchaseOrderItem) bool {
	if poItem.Metadata != nil && len(poItem.Metadata.ApplyPromotions) > 0 || poItem.IsGift != nil && *poItem.IsGift == true {
		return true
	}
	return false
}

func makeMapProductAmount(items []*model.PurchaseOrderItem) map[int64]*productAmount {
	var (
		// map<product_id, productAmount>
		mapProductAmount = make(map[int64]*productAmount)
	)

	// merge product of po_item & price for later calculate , key :  product_ID
	for _, item := range items {
		if validCalcPromotion(item) {
			continue
		}

		accumulateProductAmount, ok := mapProductAmount[item.ProductID]
		if !ok {
			accumulateProductAmount = new(productAmount)
		}
		// track total unit price có thuế
		// trường hợp nhiều product line có thuế khác nhau
		// cộng tiền sau chiết khấu vào tổng tiền
		var vatPercent float64
		if item.VAT == nil || (*item.VAT) <= 0 {
			vatPercent = 0
		} else {
			vatPercent = (*item.VAT)
		}

		// track  total unit price không thuế
		accumulateProductAmount.TotalQuantity += item.ExpectQuantity

		if accumulateProductAmount.AppliedPromotionCodes == nil {
			accumulateProductAmount.AppliedPromotionCodes = map[string]struct{}{}
		}
		// tổng giá raw ban đầu
		accumulateProductAmount.TotalUnitPrice += item.UnitPrice * float64(item.ExpectQuantity)
		accumulateProductAmount.TotalUnitPriceWithVAT += calcAmountWithPercent(item.UnitPrice, 100+vatPercent) * float64(item.ExpectQuantity)

		mapProductAmount[item.ProductID] = accumulateProductAmount
	}
	return mapProductAmount
}
