package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type AdjustmentBill struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// basic
	AdjustmentBillCode string `json:"adjustmentBillCode,omitempty" bson:"adjustment_bill_code,omitempty"`
	AdjustmentBillID   int64  `json:"adjustmentBillID,omitempty" bson:"adjustment_bill_id,omitempty"`

	// info
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorName    string `json:"vendorName,omitempty" bson:"vendor_name,omitempty"`
	InvoiceNumber string `json:"invoiceNumber,omitempty" bson:"invoice_number,omitempty"`

	// date
	IssuedTime *time.Time `json:"issuedTime,omitempty" bson:"issued_time,omitempty"` // ngày phát hành
	DueTime    *time.Time `json:"dueTime,omitempty" bson:"due_time,omitempty"`       // hạn thanh toán

	// status type
	Status     enum.AdjustmentBillStatusType `json:"status,omitempty" bson:"status,omitempty"`
	AdjustType enum.AdjustmentBillType       `json:"adjustType,omitempty" bson:"adjust_type,omitempty"`

	// amount
	TotalAmountBeforeVAT *float64 `json:"totalAmountBeforeVAT,omitempty" bson:"total_amount_before_vat,omitempty"` // tổng tiền trước thuế
	TotalVAT             *float64 `json:"totalVAT,omitempty" bson:"total_vat,omitempty"`                           // tổng tiền thuế
	TotalAmountAfterVAT  *float64 `json:"totalAmountAfterVAT,omitempty" bson:"total_amount_after_vat,omitempty"`   // tổng tiền sau thuế
	TotalDiscountAmount  *float64 `json:"totalDiscountAmount,omitempty" bson:"total_discount_amount,omitempty"`    // tổng tiền chiết khấu

	// for payment
	RemainingMoney *float64 `json:"remainingMoney,omitempty" bson:"remaining_money,omitempty"` // số tiền còn lại

	//client
	Search                string                          `json:"search,omitempty" bson:"-"`
	Items                 []*AdjustmentBillItem           `json:"items,omitempty" bson:"-"`
	StatusIn              []enum.AdjustmentBillStatusType `json:"statusIn,omitempty" bson:"-"`
	AdjustTypeIn          []enum.AdjustmentBillType       `json:"adjustTypeIn,omitempty" bson:"-"`
	VendorCodeIn          []string                        `json:"vendorCodeIn,omitempty" bson:"-"`
	DueTimeFrom           *time.Time                      `json:"dueTimeFrom,omitempty" bson:"-"`
	DueTimeTo             *time.Time                      `json:"dueTimeTo,omitempty" bson:"-"`
	OriginInvoiceNumberIn []string                        `json:"originInvoiceNumberIn,omitempty" bson:"-"`
	ProductCodeIn         []string                        `json:"productCodeIn,omitempty" bson:"-"`
	InvoiceNumberIn       []string                        `json:"invoiceNumberIn,omitempty" bson:"-"`
	AdjustmentBillIDIn    []int64                         `json:"adjustmentBillIDIn,omitempty" bson:"-"`
	IssuedTimeFrom        *time.Time                      `json:"issuedTimeFrom,omitempty" bson:"-"`
	IssuedTimeTo          *time.Time                      `json:"issuedTimeTo,omitempty" bson:"-"`
	OriginIssuedTimeFrom  *time.Time                      `json:"originIssuedTimeFrom,omitempty" bson:"-"`
	OriginIssuedTimeTo    *time.Time                      `json:"originIssuedTimeTo,omitempty" bson:"-"`

	//other
	Hashtag string `json:"hastag,omitempty" bson:"hashtag,omitempty"`
	Note    string `json:"note,omitempty" bson:"note,omitempty"`

	// internal query
	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`
}

type AdjustmentBillItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// basic
	AdjustmentBillItemID int64  `json:"adjustmentBillItemID,omitempty" bson:"adjustment_bill_item_id,omitempty"`
	ProductCode          string `json:"productCode,omitempty" bson:"product_code,omitempty"` //require

	// ref
	AdjustmentBillCode string `json:"adjustmentBillCode,omitempty" bson:"adjustment_bill_code,omitempty"`

	// info
	VendorBillCode      []string `json:"vendorBillCodes,omitempty" bson:"vendor_bill_codes,omitempty"`
	OriginInvoiceNumber string   `json:"originInvoiceNumber,omitempty" bson:"origin_invoice_number,omitempty"` //require

	// date
	OriginIssuedTime *time.Time `json:"originIssuedTime,omitempty" bson:"origin_issued_time,omitempty"` // ngày phát hành hoá đơn gốc (required)

	// chưa nhân sl
	AdjustPrice   *float64 `json:"adjustPrice,omitempty" bson:"adjust_price,omitempty"` // required
	VatPrice      *float64 `json:"vatPrice,omitempty" bson:"vat_price,omitempty"`
	DiscountPrice *float64 `json:"discountPrice,omitempty" bson:"discount_price,omitempty"`

	// total
	TotalAmountAfterVAT *float64 `json:"totalAmountAfterVAT,omitempty" bson:"total_amount_after_vat,omitempty"` // tổng tiền sau thuế

	//percent
	Vat      *float64 `json:"vat,omitempty" bson:"vat,omitempty"`
	Discount *float64 `json:"discount,omitempty" bson:"discount,omitempty"`

	// qty
	Quantity int64 `json:"quantity,omitempty" bson:"quantity,omitempty"`

	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`
}

var AdjustmentBillDB = &db.Instance{
	ColName:        "adjustment_bill",
	TemplateObject: new(AdjustmentBill),
}

var AdjustmentBillItemDB = &db.Instance{
	ColName:        "adjustment_bill_item",
	TemplateObject: new(AdjustmentBillItem),
}

func InitAdjustmentBillModel(s *mongo.Database) {
	AdjustmentBillDB.ApplyDatabase(s)
	AdjustmentBillItemDB.ApplyDatabase(s)
}
