package action

import (
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/monitoring"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetInboundRequestItemList ...
// GetInboundRequestItemList retrieves a list of inbound request items based on the provided query parameters.
// It supports pagination through offset and limit, and can optionally return the total count of items.
//
// Parameters:
//   - query: model.InboundRequestItem - The query parameters to filter the inbound request items.
//   - offset: int64 - The number of items to skip before starting to collect the result set.
//   - limit: int64 - The maximum number of items to return.
//   - getTotal: bool - A flag indicating whether to return the total count of items.
//
// Returns:
//   - *common.APIResponse - The API response containing the list of inbound request items and optionally the total count.
//
// The function performs the following steps:
//  1. Queries the database for inbound request items based on the provided query, offset, and limit.
//  2. If the query is successful, it retrieves the first inbound request item.
//  3. Queries the database for the corresponding inbound request using the InboundRequestCode from the first item.
//  4. If the inbound request type is "OTHER", it iterates through the list of inbound request items and performs additional processing:
//     - Retrieves the purchaser code based on the warehouse code.
//     - Queries the database for SKU configurations using the product ID, seller code, and purchaser code.
//     - For each SKU configuration, retrieves the vendor with the highest priority and queries the database for the vendor details.
//     - Associates the vendor details with the corresponding inbound request item.
//  5. If getTotal is true, it queries the database for the total count of inbound request items matching the query.
//
// Note: The function assumes that the database queries and utility functions used are defined elsewhere in the codebase.
func GetInboundRequestItemList(query model.InboundRequestItem, offset, limit int64, getTotal bool) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}

	genResp := model.InboundRequestItemDB.Query(query, offset, limit, sortField)
	if genResp.Status == common.APIStatus.Ok {
		getFirstInboundRequestItem := genResp.Data.([]*model.InboundRequestItem)[0]

		// get inbound request
		inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
			InboundRequestCode: getFirstInboundRequestItem.InboundRequestCode,
		})
		// get inbound request
		if inboundRequestResp.Status == common.APIStatus.Ok {
			inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]
			// inbound request type is "OTHER"
			if inboundRequest.InboundRequestType == enum.InboundRequestType.OTHER {

				for _, item := range genResp.Data.([]*model.InboundRequestItem) {
					// get contract price item by product code & contract price code
					purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(item.WarehouseCode)
					if ok {
						skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
							ProductID:     item.ProductID,
							SellerCode:    query.SellerCode,
							PurchaserCode: purchaserCode,
						})

						// get sku config
						if skuConfigResp.Status == common.APIStatus.Ok {
							skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)
							for _, skuConfig := range skuConfigs {
								if skuConfig == nil || utils.IsNil(skuConfig.Vendors) || len(*skuConfig.Vendors) == 0 {
									continue
								}

								var vendorCode string
								for _, vendor := range *skuConfig.Vendors {
									if vendor.Priority != nil && *vendor.Priority == 0 {
										vendorCode = vendor.VendorCode
										break
									}
								}

								// get vendor
								if len(vendorCode) > 0 {
									// get vendor
									opts := client.APIOption{
										Q: model.Seller{
											SellerClass: model.CLASS_VENDOR,
										},
										Limit: utils.Pointer.WithInt(1),
										Params: map[string]string{
											"sellerCode": vendorCode,
										},
									}
									// get vendor
									getVendorResp := seller.GetSellers(opts)
									if getVendorResp.Status == common.APIStatus.Ok {
										vendor := getVendorResp.Data.([]*model.Seller)[0]
										item.Seller = vendor
									}
								}
							}
						}
					}
				}
			}
		}
	}
	// get total count
	if getTotal {
		countResp := model.InboundRequestItemDB.Count(query)
		genResp.Total = countResp.Total
	}

	return genResp
}

// GetInboundRequestList ...
// GetInboundRequestList retrieves a list of inbound requests based on the provided query parameters.
// It supports pagination through the offset and limit parameters and can optionally return the total count of matching records.
//
// Parameters:
//   - query: The query parameters to filter the inbound requests.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return for pagination.
//   - getTotal: A boolean flag indicating whether to include the total count of matching records.
//
// Returns:
//   - *common.APIResponse: The API response containing the list of inbound requests and optionally the total count.
func GetInboundRequestList(query model.InboundRequest, offset, limit int64, getTotal bool) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}

	genResp := model.InboundRequestDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.InboundRequestDB.Count(query)
		genResp.Total = countResp.Total
	}

	return genResp
}

// CreateInboundRequest
// CreateInboundRequest handles the creation of an inbound request.
// It validates the warehouse code, request type, and item count before proceeding with the creation.
// If the request type is PROMOTION, it ensures the start time is in the future.
// If the request type is OTHER, it ensures the apply time is in the future.
// It also checks that the number of items does not exceed 1000.
// Upon successful validation, it creates the inbound request and its associated items in the database.
//
// Parameters:
//   - input: A pointer to a model.InboundRequest containing the details of the inbound request.
//
// Returns:
//   - A pointer to a common.APIResponse indicating the result of the operation.
func CreateInboundRequest(input *model.InboundRequest) *common.APIResponse {
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	now := time.Now()
	// convert time to string
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)

	// input.InboundRequestType == PROMOTION
	if input.InboundRequestType == enum.InboundRequestType.PROMOTION {
		if nowYYYYMMDD >= input.StartTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the time start small more or by current day",
				ErrorCode: string(enum.ErrorCodeInvalid.NotCreateNowLTEStartTime),
			}
		}
	} else if input.InboundRequestType == enum.InboundRequestType.OTHER {
		// input.InboundRequestType == OTHER
		if nowYYYYMMDD >= input.ApplyTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the current time at large more or by apply time",
				ErrorCode: string(enum.ErrorCodeInvalid.NotCreateNowGTEApplyTime),
			}
		}
	}

	// check number of items
	if len(input.Items) > 1000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cannot create request with the number of product to more than 1000",
			ErrorCode: string(enum.ErrorCodeInvalid.NotAllowItemGT1000),
		}
	}

	// create InboundRequest
	inboundRequest := input

	// generate InboundRequestID and InboundRequestCode
	inboundRequest.InboundRequestID, inboundRequest.InboundRequestCode = model.GetInboundRequestID()
	inboundRequest.Status = enum.InboundRequestStatus.DRAFT
	inboundRequest.HashTag = utils.NormalizeString(input.Name)
	inboundRequest.IsActive = utils.Pointer.WithBool(false)

	// input.InboundRequestType == OTHER
	if input.InboundRequestType == enum.InboundRequestType.OTHER {
		inboundRequest.Reason = input.Reason
		inboundRequest.TotalRebate = input.TotalRebate
		if input.PromotionCode != nil {
			inboundRequest.PromotionCode = input.PromotionCode
		}
		if input.PurchaseOrderCode != nil {
			inboundRequest.PurchaseOrderCode = input.PurchaseOrderCode
		}
		if input.ContractPriceCode != nil {
			inboundRequest.ContractPriceCode = input.ContractPriceCode
		}
	}

	// create InboundRequest
	createResp := model.InboundRequestDB.Create(inboundRequest)

	// create InboundRequestItem
	for _, item := range input.Items {

		// create InboundRequestItem
		item.InboundRequestItemID, item.InboundRequestItemCode = model.GetInboundRequestItemID()
		item.InboundRequestID = inboundRequest.InboundRequestID
		item.InboundRequestCode = inboundRequest.InboundRequestCode
		item.Status = enum.InboundRequestStatus.DRAFT
		item.IsActive = utils.Pointer.WithBool(false)
		item.VendorCode = inboundRequest.VendorCode
		createResp := model.InboundRequestItemDB.Create(item)
		if createResp.Status != common.APIStatus.Ok {
			return createResp
		}
	}

	return createResp
}

// UpdateInboundRequest updates an existing inbound request based on the provided input.
// It performs various validations and updates the request in the database.
//
// Parameters:
//   - input: model.InboundRequest - The inbound request data to be updated.
//
// Returns:
//   - *common.APIResponse - The response containing the status and message of the update operation.
//
// Validations:
//   - Checks if the warehouse code is valid.
//   - Checks if the inbound request exists in the database.
//   - Validates the start time for promotion type requests.
//   - Validates the apply time for other type requests.
//   - Ensures the number of items does not exceed 1000.
//
// Updates:
//   - Updates the inbound request details.
//   - Updates the items associated with the inbound request if the status is DRAFT.
func UpdateInboundRequest(input model.InboundRequest) *common.APIResponse {
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// check inbound request
	checkInboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: input.InboundRequestCode,
		SellerCode:         input.SellerCode,
	})
	if checkInboundRequestResp.Status != common.APIStatus.Ok {
		return checkInboundRequestResp
	}

	// get inbound request
	checkInboundRequest := checkInboundRequestResp.Data.([]*model.InboundRequest)[0]

	now := time.Now()
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)

	// input.InboundRequestType == PROMOTION
	if input.InboundRequestType == enum.InboundRequestType.PROMOTION {
		if input.StartTime != checkInboundRequest.StartTime && nowYYYYMMDD >= input.StartTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the time start small more or by current day",
				ErrorCode: string(enum.ErrorCodeInvalid.NotCreateNowLTEStartTime),
			}
		}
	} else if input.InboundRequestType == enum.InboundRequestType.OTHER {
		// input.InboundRequestType == OTHER
		if input.ApplyTime != checkInboundRequest.ApplyTime && nowYYYYMMDD >= input.ApplyTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the current time at large more or by apply time",
				ErrorCode: string(enum.ErrorCodeInvalid.NotCreateNowGTEApplyTime),
			}
		}

	}

	// check number of items
	if len(input.Items) > 1000 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cannot create request with the number of product to more than 1000",
			ErrorCode: string(enum.ErrorCodeInvalid.NotAllowItemGT1000),
		}
	}

	// update InboundRequest
	updateInboundRequest := input
	// update InboundRequest
	if input.InboundRequestType != enum.InboundRequestType.OTHER {
		updateInboundRequest.Reason = input.Reason
		updateInboundRequest.TotalRebate = input.TotalRebate
		if input.PromotionCode != nil {
			updateInboundRequest.PromotionCode = input.PromotionCode
		}
		if input.PurchaseOrderCode != nil {
			updateInboundRequest.PurchaseOrderCode = input.PurchaseOrderCode
		}
		if input.ContractPriceCode != nil {
			updateInboundRequest.ContractPriceCode = input.ContractPriceCode
		}
	}

	// do action
	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			ID: checkInboundRequest.ID,
		},
		updateInboundRequest,
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	inboundRequest := updateResp.Data.([]*model.InboundRequest)[0]
	// Only update item when status DRAFT
	if inboundRequest.Status == enum.InboundRequestStatus.DRAFT {
		deleteItemResp := model.InboundRequestItemDB.Delete(model.InboundRequestItem{InboundRequestCode: inboundRequest.InboundRequestCode})
		if deleteItemResp.Status != common.APIStatus.Ok {
			return deleteItemResp
		}

		// create InboundRequestItem
		for _, item := range input.Items {
			item.InboundRequestID = inboundRequest.InboundRequestID
			item.InboundRequestCode = inboundRequest.InboundRequestCode
			item.Status = enum.InboundRequestStatus.DRAFT
			item.IsActive = utils.Pointer.WithBool(false)
			item.VendorCode = input.VendorCode
			if item.InboundRequestItemID == 0 {
				item.InboundRequestItemID, item.InboundRequestItemCode = model.GetInboundRequestItemID()
			}

			createResp := model.InboundRequestItemDB.Create(item)
			if createResp.Status != common.APIStatus.Ok {
				return createResp
			}
		}
	}

	return updateResp
}

func ConfirmInboundRequest(inboundRequestCode string) *common.APIResponse {

	now := time.Now()
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)

	inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: inboundRequestCode,
	})
	if inboundRequestResp.Status != common.APIStatus.Ok {
		return inboundRequestResp
	}

	inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.InboundRequestType == enum.InboundRequestType.PROMOTION {
		if nowYYYYMMDD >= inboundRequest.StartTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the time start small more or by current day",
				ErrorCode: string(enum.ErrorCodeInvalid.NotApproveNowLTEStartTime),
			}
		}
	} else if inboundRequest.InboundRequestType == enum.InboundRequestType.OTHER {
		if nowYYYYMMDD >= inboundRequest.ApplyTime {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Cannot create inbound request with the current time at large more or by apply time",
				ErrorCode: string(enum.ErrorCodeInvalid.NotApproveNowLTEApplyTime),
			}
		}

		// get inbound request items
		inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
			InboundRequestCode: inboundRequestCode,
		}, 0, 1000, nil)
		if inboundRequestItemResp.Status != common.APIStatus.Ok {
			return inboundRequestItemResp
		}

		inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
		if len(inboundRequestItems) == 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Not found inbound request items",
			}
		}

		for _, inboundRequestItem := range inboundRequestItems {
			if inboundRequestItem.UnitPrice == 0 {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Unit price is required",
				}
			}
		}
	}

	// update parent
	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			ID: inboundRequest.ID,
		},
		model.InboundRequest{
			ActionTime: &now,
			Status:     enum.InboundRequestStatus.CONFIRMED,
			IsActive:   utils.Pointer.WithBool(true),
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	if inboundRequest.InboundRequestType != enum.InboundRequestType.SAVE_INITIAL_STOCK {
		// update item
		updateItemResp := model.InboundRequestItemDB.UpdateMany(
			model.InboundRequestItem{
				InboundRequestCode: inboundRequestCode,
			},
			model.InboundRequestItem{
				Status:   enum.InboundRequestStatus.CONFIRMED,
				IsActive: utils.Pointer.WithBool(true),
			},
		)
		return updateItemResp
	}

	return updateResp
}

func CancelInboundRequest(inboundRequestCode string) *common.APIResponse {

	now := time.Now()
	// update parent
	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			InboundRequestCode: inboundRequestCode,
		},
		model.InboundRequest{
			ActionTime: &now,
			Status:     enum.InboundRequestStatus.CANCELED,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	inboundRequest := updateResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.InboundRequestType != enum.InboundRequestType.SAVE_INITIAL_STOCK {
		// update item
		updateItemResp := model.InboundRequestItemDB.UpdateMany(
			model.InboundRequestItem{
				InboundRequestCode: inboundRequestCode,
			},
			model.InboundRequestItem{
				Status: enum.InboundRequestStatus.CANCELED,
			},
		)
		return updateItemResp
	}
	return updateResp
}

func SwitchActiveInboundRequest(inboundRequestCode string, isActive *bool) *common.APIResponse {

	now := time.Now()

	// update parent
	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			InboundRequestCode: inboundRequestCode,
			InboundRequestType: enum.InboundRequestType.SAVE_INITIAL_STOCK,
		},
		model.InboundRequest{
			ActionTime: &now,
			IsActive:   isActive,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}
	inboundRequest := updateResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.InboundRequestType == enum.InboundRequestType.SAVE_INITIAL_STOCK {
		// update item
		updateItemResp := model.InboundRequestItemDB.UpdateMany(
			model.InboundRequestItem{
				InboundRequestCode: inboundRequestCode,
			},
			model.InboundRequestItem{
				IsActive: isActive,
			},
		)
		return updateItemResp
	}

	return updateResp
}

func CloneInboundRequest(inboundRequestCode string, acc *model.Account) *common.APIResponse {

	inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: inboundRequestCode,
	})

	if inboundRequestResp.Status != common.APIStatus.Ok {
		return inboundRequestResp
	}
	inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]

	// Clone inbound request
	inboundRequest.ID = nil
	inboundRequest.CreatedTime = nil
	inboundRequest.LastUpdatedTime = nil
	inboundRequest.CreatedByID = 0
	inboundRequest.CreatedByName = ""

	if inboundRequest.InboundRequestType != enum.InboundRequestType.OTHER {
		inboundRequest.ApplyTime = ""
	}

	inboundRequest.StartTime = ""
	inboundRequest.EndTime = ""
	inboundRequest.EndWeekday = 0
	inboundRequest.POCodes = nil

	inboundRequest.InboundRequestID, inboundRequest.InboundRequestCode = model.GetInboundRequestID()
	inboundRequest.Status = enum.InboundRequestStatus.DRAFT
	inboundRequest.HashTag = utils.NormalizeString(inboundRequest.Name)
	inboundRequest.IsActive = utils.Pointer.WithBool(false)
	if inboundRequest.InboundRequestType == enum.InboundRequestType.OTHER {
		inboundRequest.PurchaseOrderCode = nil
		inboundRequest.DealTicketCode = nil
	}

	if acc != nil {
		inboundRequest.CreatedByID = acc.AccountID
		inboundRequest.CreatedByName = acc.Fullname
	}
	createResp := model.InboundRequestDB.Create(inboundRequest)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}

	if inboundRequest.InboundRequestType == enum.InboundRequestType.SAVE_INITIAL_STOCK {
		return createResp
	}

	inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		InboundRequestCode: inboundRequestCode,
	}, 0, 1000, nil)
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
	var newInboundRequestItems []*model.InboundRequestItem
	for _, inboundRequestItem := range inboundRequestItems {

		// create InboundRequestItem
		inboundRequestItem.ID = nil
		inboundRequestItem.CreatedTime = nil
		inboundRequestItem.LastUpdatedTime = nil

		inboundRequestItem.ApplyTime = ""
		inboundRequestItem.StartTime = ""
		inboundRequestItem.EndTime = ""
		inboundRequestItem.POCode = ""

		inboundRequestItem.InboundRequestItemID, inboundRequestItem.InboundRequestItemCode = model.GetInboundRequestItemID()
		inboundRequestItem.InboundRequestID = inboundRequest.InboundRequestID
		inboundRequestItem.InboundRequestCode = inboundRequest.InboundRequestCode
		inboundRequestItem.Status = enum.InboundRequestStatus.DRAFT
		inboundRequestItem.IsActive = utils.Pointer.WithBool(false)
		inboundRequestItem.VendorCode = inboundRequest.VendorCode

		if inboundRequest.InboundRequestType == enum.InboundRequestType.OTHER {
			applyTime, _ := time.Parse("2006-01-02", inboundRequest.ApplyTime)
			result := GetInboundRequestItemContractPrice(inboundRequest.SellerCode, inboundRequestItem.WarehouseCode, *inboundRequestItem.VendorCode, string(inboundRequestItem.InboundRequestType), inboundRequestItem.ProductID, applyTime, nil, 0, 0)
			if result.Status == common.APIStatus.Ok {
				contractPriceItem := result.Data.([]interface{})[0].(model.ContractPriceItemInboundRequestResponse)
				inboundRequestItem.UnitPrice = contractPriceItem.UnitPrice
				inboundRequestItem.VAT = contractPriceItem.VAT
				inboundRequestItem.Discount = contractPriceItem.Discount
				inboundRequestItem.ContractPriceCode = contractPriceItem.ContractPriceCode
				inboundRequestItem.ContractPriceStartTime = contractPriceItem.ContractPriceStartTime
				inboundRequestItem.ContractPriceEndTime = contractPriceItem.ContractPriceEndTime
				inboundRequestItem.DealTicketCode = ""
			} else {
				inboundRequestItem.UnitPrice = 0
				inboundRequestItem.VAT = 0
				inboundRequestItem.Discount = 0
				inboundRequestItem.ContractPriceCode = ""
				inboundRequestItem.ContractPriceStartTime = nil
				inboundRequestItem.ContractPriceEndTime = nil
				inboundRequestItem.DealTicketCode = ""
			}
		}

		newInboundRequestItems = append(newInboundRequestItems, inboundRequestItem)
	}

	createItemResp := model.InboundRequestItemDB.CreateMany(newInboundRequestItems)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}
	return createResp
}

// get item
func GetListInboundRequestItem(query *model.InboundRequestItem, offset, limit int64, getTotal bool, sortFields *bson.M) *common.APIResponse {
	if sortFields == nil {
		sortFields = &bson.M{"_id": -1}
	}
	getResp := model.InboundRequestItemDB.Query(query, offset, limit, sortFields)
	if getTotal {
		getResp.Total = model.InboundRequestItemDB.Count(query).Total
	}
	return getResp
}

func GetListInboundRequestMainCodeFromItem(query *model.InboundRequestItem, offset, limit int64, sortFields *bson.M) []string {
	getItemResp := GetListInboundRequestItem(query, offset, limit, false, sortFields)
	if getItemResp.Status != common.APIStatus.Ok {
		return make([]string, 0)
	}

	var (
		items     = getItemResp.Data.([]*model.InboundRequestItem)
		mainCodes = make([]string, 0, len(items))
	)

	for _, item := range items {
		if utils.IsContains(mainCodes, item.InboundRequestCode) {
			continue
		}
		mainCodes = append(mainCodes, item.InboundRequestCode)
	}

	return mainCodes
}

func GetInboundRequestItemContractPrice(sellerCode, warehouseCode, vendorCode, requestType string, productID int64, applyTime time.Time, kind *string, expectQuantity, numDayInStock int64) *common.APIResponse {
	if sellerCode == "" {
		sellerCode = "MEDX"
	}
	option := client.APIOption{
		Keys: []string{vendorCode, model.CLASS_VENDOR, fmt.Sprintf("%v", productID), warehouseCode},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  vendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	getSellerResp := seller.GetSellers(option)
	if getSellerResp.Status != common.APIStatus.Ok {
		return getSellerResp
	}

	getSeller := getSellerResp.Data.([]*model.Seller)[0]
	if len(getSeller.LineManager) > 0 {
		vendorCode = getSeller.LineManager
	}

	// get contract price
	contractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		Status: enum.ContractPriceStatus.CONFIRMED,
		ComplexQuery: []bson.M{
			{
				"vendor_codes": bson.M{"$in": []string{vendorCode}},
			},
			{
				"start_time": bson.M{"$lte": applyTime},
			},
			{
				"end_time": bson.M{"$gte": applyTime},
			},
		},
	}, 0, 1000, nil)
	if contractPriceResp.Status != common.APIStatus.Ok {
		return contractPriceResp
	}

	contractPrices := contractPriceResp.Data.([]*model.ContractPrice)
	if len(contractPrices) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Not found contract price",
			ErrorCode: "NOT_FOUND_CONTRACT_PRICE",
		}
	}

	contractPriceCodes := make([]string, 0, len(contractPrices))
	for _, contractPrice := range contractPrices {
		contractPriceCodes = append(contractPriceCodes, contractPrice.ContractPriceCode)
	}

	// get contract price item by product code & contract price code
	contractPriceItemsResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{
		ProductID:    productID,
		ComplexQuery: []bson.M{{"contract_price_code": bson.M{"$in": contractPriceCodes}}},
	}, 0, 1000, nil)
	if contractPriceItemsResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Not found contract price item",
			ErrorCode: "NOT_FOUND_CONTRACT_PRICE_ITEM_BY_CONTRACT_PRICE_CODE",
		}
	}

	contractPriceItems := contractPriceItemsResp.Data.([]*model.ContractPriceItem)
	contractPriceCodeMap := make(map[string]*model.ContractPriceItem)
	for _, contractPriceItem := range contractPriceItems {
		contractPriceCodeMap[contractPriceItem.ContractPriceCode] = contractPriceItem
	}

	filterContractPrices := make([]*model.ContractPrice, 0, len(contractPrices))
	for _, contractPrice := range contractPrices {
		if _, ok := contractPriceCodeMap[contractPrice.ContractPriceCode]; !ok {
			continue
		}

		diffDays := math.Ceil(utils.GetVietnamTimeNow().Sub(*contractPrice.StartTime).Hours() / 24)
		contractPrice.DiffDay = int(diffDays)
		filterContractPrices = append(filterContractPrices, contractPrice)
	}

	getContractPrice := getContractPriceLasted(filterContractPrices)

	// get contract price item by product code & contract price code
	contractPriceItemResp := model.ContractPriceItemDB.QueryOne(model.ContractPriceItem{
		ProductID:         productID,
		ContractPriceCode: getContractPrice.ContractPriceCode,
	})
	if contractPriceItemResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Not found contract price item",
			ErrorCode: "NOT_FOUND_CONTRACT_PRICE_ITEM_BY_PRODUCT_ID",
		}
	}

	contractPriceItem := contractPriceItemResp.Data.([]*model.ContractPriceItem)[0]

	// init model
	contractPriceInboundRequestResp := model.ContractPriceItemInboundRequestResponse{
		ContractPriceCode:      getContractPrice.ContractPriceCode,
		ContractPriceID:        getContractPrice.ContractPriceID,
		UnitPrice:              contractPriceItem.UnitPrice,
		VAT:                    contractPriceItem.VAT,
		Discount:               contractPriceItem.Discount,
		ProductCode:            contractPriceItem.ProductCode,
		ProductID:              contractPriceItem.ProductID,
		ProductName:            contractPriceItem.ProductName,
		WarehouseCode:          warehouseCode,
		ContractPriceStartTime: getContractPrice.StartTime,
		ContractPriceEndTime:   getContractPrice.EndTime,
	}

	purchaserCode, _ := utils.WarehouseCodeToPurchaserCode(warehouseCode)
	skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
		ProductID:     contractPriceItem.ProductID,
		SellerCode:    sellerCode,
		PurchaserCode: purchaserCode,
	})

	if skuConfigResp.Status == common.APIStatus.Ok {
		skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]
		if skuConfig != nil && skuConfig.Vendors != nil && len(*skuConfig.Vendors) > 0 {
			contractPriceInboundRequestResp.SKU = skuConfig.SKU

			var vendorCode string
			for _, vendor := range *skuConfig.Vendors {
				if vendor.Priority != nil && *vendor.Priority == 0 {
					vendorCode = vendor.VendorCode
					break
				}
			}

			if len(vendorCode) > 0 {
				// get vendor
				opts := client.APIOption{
					Q: model.Seller{
						SellerClass: model.CLASS_VENDOR,
					},
					Limit: utils.Pointer.WithInt(1),
					Params: map[string]string{
						"sellerCode": vendorCode,
					},
				}
				getVendorResp := seller.GetSellers(opts)
				if getVendorResp.Status == common.APIStatus.Ok {
					vendor := getVendorResp.Data.([]*model.Seller)[0]
					contractPriceItem.Seller = vendor
				}
			}
		}
	}

	if contractPriceItem.Seller != nil {
		contractPriceInboundRequestResp.Seller = contractPriceItem.Seller
	}

	getAVGDemandResp := GetInboundRequestAvgDemand(sellerCode, warehouseCode, contractPriceInboundRequestResp.SKU)
	if getAVGDemandResp.Status == common.APIStatus.Ok {
		avgDemand := getAVGDemandResp.Data.([]interface{})[0].(float64)
		contractPriceInboundRequestResp.AvgDemand = avgDemand
	}
	getAVGDemandHedgingResp := GetInboundRequestDemandHedging(sellerCode, warehouseCode, contractPriceInboundRequestResp.SKU)
	if getAVGDemandHedgingResp.Status == common.APIStatus.Ok {
		demandHedging := getAVGDemandHedgingResp.Data.([]*model.DemandHedgingResponse)[0]
		contractPriceInboundRequestResp.AvgHedgingDemand = demandHedging.HedgingAVGDemand
		contractPriceInboundRequestResp.DemandClassificationCode = demandHedging.DemandClassificationCode
		contractPriceInboundRequestResp.Total30DaysDemand = demandHedging.Total30DaysDemand
	}
	if kind != nil {
		determineStockAvailability := &model.DetermineStockAvailabilityResponse{}
		if *kind == "" {
			determineStockAvailability = getInboundRequestAvgStorageDays(sellerCode, warehouseCode, contractPriceInboundRequestResp.SKU, expectQuantity, contractPriceInboundRequestResp.ProductID)
		} else {
			if *kind == "EXPECT_QUANTITY" {
				determineStockAvailability = getInboundRequestAvgStorageDays(sellerCode, warehouseCode, contractPriceInboundRequestResp.SKU, expectQuantity, contractPriceInboundRequestResp.ProductID)
			}
			if *kind == "STORAGE_DAYS" {
				determineStockAvailability = getInboundRequestExpectedQty(sellerCode, warehouseCode, contractPriceInboundRequestResp.SKU, numDayInStock, contractPriceInboundRequestResp.ProductID)
			}
		}
		if determineStockAvailability != nil {
			if determineStockAvailability.NumDayInStock < 0 {
				determineStockAvailability.NumDayInStock = 0
			}

			contractPriceInboundRequestResp.TotalAvailableQuantity = determineStockAvailability.TotalAvailableQuantity
			contractPriceInboundRequestResp.AvailableQuantityWHInbound = determineStockAvailability.AvailableQuantityWHInbound
			contractPriceInboundRequestResp.QuantityIncoming = determineStockAvailability.QuantityIncoming
			contractPriceInboundRequestResp.WaitingConfirmOrderQuantity = determineStockAvailability.WaitingConfirmOrderQuantity
			contractPriceInboundRequestResp.NumDayInStock = determineStockAvailability.NumDayInStock
			contractPriceInboundRequestResp.ExpectQuantity = determineStockAvailability.ExpectQuantity
			contractPriceInboundRequestResp.PoCodes = determineStockAvailability.PoCodes
		}
		contractPriceInboundRequestResp.Kind = *kind
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{contractPriceInboundRequestResp},
	}
}

func getContractPriceLasted(contractPrices []*model.ContractPrice) *model.ContractPrice {
	minDiffDay := math.MaxInt64
	maxContractPrice := int64(math.MinInt64)
	var result *model.ContractPrice
	for _, contractPrice := range contractPrices {
		if (contractPrice.DiffDay < minDiffDay) || (contractPrice.DiffDay == minDiffDay && contractPrice.ContractPriceID > maxContractPrice) {
			minDiffDay = contractPrice.DiffDay
			maxContractPrice = contractPrice.ContractPriceID
			result = contractPrice
		}
	}

	return result
}

func RefreshInboundRequestContractPrice(input model.InboundRequest) *common.APIResponse {
	// get inbound request
	inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: input.InboundRequestCode,
	})
	if inboundRequestResp.Status != common.APIStatus.Ok {
		return inboundRequestResp
	}

	inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.Status != enum.InboundRequestStatus.DRAFT {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cannot refresh contract price for this status",
		}
	}
	if inboundRequest.InboundRequestType != enum.InboundRequestType.OTHER {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Cannot refresh contract price for this type",
		}
	}

	// get inbound request item
	inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		InboundRequestCode: input.InboundRequestCode,
	}, 0, 1000, nil)
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
	for _, inboundRequestItem := range inboundRequestItems {
		// get contract price item

		applyTimeConverted, _ := time.Parse("2006-01-02", inboundRequest.ApplyTime)
		contractPriceItemResp := GetInboundRequestItemContractPrice(
			inboundRequest.SellerCode,
			inboundRequest.WarehouseCode, *inboundRequest.VendorCode,
			string(inboundRequest.InboundRequestType),
			inboundRequestItem.ProductID,
			applyTimeConverted,
			nil, 0, 0,
		)
		if contractPriceItemResp.Status != common.APIStatus.Ok {
			continue
		}

		contractPriceItem := contractPriceItemResp.Data.([]interface{})[0].(model.ContractPriceItemInboundRequestResponse)
		inboundRequestItem.UnitPrice = contractPriceItem.UnitPrice
		inboundRequestItem.VAT = contractPriceItem.VAT
		inboundRequestItem.Discount = contractPriceItem.Discount
		inboundRequestItem.ContractPriceCode = contractPriceItem.ContractPriceCode
		inboundRequestItem.ContractPriceStartTime = contractPriceItem.ContractPriceStartTime
		inboundRequestItem.ContractPriceEndTime = contractPriceItem.ContractPriceEndTime

		// update inbound request item
		updateResp := model.InboundRequestItemDB.UpdateOne(
			model.InboundRequestItem{
				ID: inboundRequestItem.ID,
			},
			inboundRequestItem,
		)
		if updateResp.Status != common.APIStatus.Ok {
			continue
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done",
	}
}

func GetInboundRequestDeal(input model.InboundRequestDealRequest) *common.APIResponse {
	if input.SellerCode == "" {
		input.SellerCode = "MEDX"
	}
	// get inbound request
	inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: input.InboundRequestCode,
	})
	if inboundRequestResp.Status != common.APIStatus.Ok {
		return inboundRequestResp
	}

	inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.InboundRequestType != enum.InboundRequestType.OTHER {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Cannot get deal for this request type: %s", inboundRequest.InboundRequestType),
		}
	}
	if inboundRequest.Status != enum.InboundRequestStatus.CONFIRMED {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Cannot get deal for this status: %s", inboundRequest.Status),
		}
	}
	if inboundRequest.Reason != enum.InboundRequestReasonToOther.PROMOTIONAL_STOCK &&
		inboundRequest.Reason != enum.InboundRequestReasonToOther.INCREASE_PRICE {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: fmt.Sprintf("Cannot get deal for this reason: %v", inboundRequest.Reason),
		}
	}

	// get contract price
	contractPriceResp := model.ContractPriceDB.QueryOne(model.ContractPrice{
		ContractPriceCode: input.ContractPriceCode,
	})
	if contractPriceResp.Status != common.APIStatus.Ok {
		return contractPriceResp
	}

	contractPrice := contractPriceResp.Data.([]*model.ContractPrice)[0]

	// get contract price item
	contractPriceItemsMap, err := getListContractPriceItemByContractPriceCodeAndWarehouseCode(input.ContractPriceCode, input.WarehouseCode)
	if err != nil {
		return err
	}

	// get promotion items accroding client seller/promotion
	vendorPromotion, vendorDiscountItemsMap, vendorGiftItemsMap, err := getVendorPromotionItems(input.PromotionCode)
	if err != nil {
		return err
	}

	// get init stock draf
	initStockDraftsMap, err := getListInitStockDraft(input.WarehouseCode, input.SKUs)
	if err != nil {
		return err
	}

	// get inventory sku
	inventorySkusMap, inventorySkuLocationsMap, err := getListInventory(input.SKUs, input.WarehouseCode)
	if err != nil {
		return err
	}

	// get sku config
	skuConfigWarehouseMap, _, _ := getSkuConfigsFromInboundRequest(input)

	// Get valid warehouses
	dataWarehouses := getValidWarehouses()

	// Get SKU items and filter by valid locations
	locationItemCodesMap := getLocationItemCodesMapByInboundRequestDeal(input, dataWarehouses)

	// Get orders
	nowTime := utils.GetVietnamTimeNow()
	pastTime := utils.GetVietnamTimeNow().AddDate(0, 0, -5)

	qtySKUOrderItemsMap := getOrdersFromInboundRequestDeal(input.SellerCode, pastTime, nowTime)

	// Get purchase order
	var purchaseOrder = make(map[string]*model.PurchaseOrder)
	if inboundRequest.POCodes != nil && len(*inboundRequest.POCodes) > 0 {
		purchaseOrderResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
			ComplexQuery: []*bson.M{
				{
					"po_code": bson.M{"$in": inboundRequest.POCodes},
				},
			},
			// POCode: *inboundRequest.PurchaseOrderCode,
		}, 0, 100, nil)
		if purchaseOrderResp.Status == common.APIStatus.Ok {
			purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
			for _, po := range purchaseOrders {
				purchaseOrder[po.POCode] = po
			}
		}
	}

	// get inbound request item
	inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		InboundRequestCode: input.InboundRequestCode,
		WarehouseCode:      input.WarehouseCode,
		ComplexQuery: []*bson.M{
			{
				"sku": bson.M{"$in": input.SKUs},
			},
		},
	}, 0, 1000, nil)
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	applyTimeConverted, _ := time.Parse("2006-01-02", inboundRequest.ApplyTime)
	now := utils.GetVietnamTimeNow()

	option := client.APIOption{
		Keys: []string{input.VendorCode, model.CLASS_VENDOR, input.WarehouseCode},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  input.VendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	getSellerResp := seller.GetSellers(option)
	if getSellerResp.Status != common.APIStatus.Ok {
		return getSellerResp
	}

	getSeller := getSellerResp.Data.([]*model.Seller)[0]

	inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)

	resps := []*model.InboundRequestItemResponse{}
	for _, inboundRequestItem := range inboundRequestItems {
		inboundRequestItemResponse := populateInboundRequestItem(inboundRequestItem)

		if initStockDraft, ok := initStockDraftsMap[inboundRequestItem.SKU]; ok {
			inboundRequestItemResponse.AVGDemand = initStockDraft.AVGDemand
		}
		if inventorySku, ok := inventorySkusMap[inboundRequestItem.SKU]; ok {
			inboundRequestItemResponse.AvailableQuantity = inventorySku.AvailableQuantity
			inboundRequestItemResponse.WaitToReserveQty = inventorySku.WaitingHoldQuantity
			inboundRequestItemResponse.IncomingQty = inventorySku.WaitingInboundQuantity
		}
		if inventorySkuLocation, ok := inventorySkuLocationsMap[inboundRequestItem.SKU]; ok {
			inboundRequestItemResponse.InboundQuantity = inventorySkuLocation.Quantity
		}

		if _, ok := skuConfigWarehouseMap[fmt.Sprintf("%s_%s", inboundRequestItem.SKU, inboundRequestItem.WarehouseCode)]; ok {
			inboundRequestItemResponse.SameWithHedgingVendor = utils.Pointer.WithBool(true)
		} else {
			inboundRequestItemResponse.SameWithHedgingVendor = utils.Pointer.WithBool(false)
		}
		if locationItemCode, ok := locationItemCodesMap[fmt.Sprintf("%s_%s", inboundRequestItem.SKU, inboundRequestItem.WarehouseCode)]; ok {
			if locationItemCode.RetailPriceType == "FIXED_REVENUE" {
				if _, ok := skuConfigWarehouseMap[fmt.Sprintf("%s_%s", inboundRequestItem.SKU, inboundRequestItem.WarehouseCode)]; ok {
					inboundRequestItemResponse.DealCreation = utils.Pointer.WithBool(true)
				} else {
					inboundRequestItemResponse.DealCreation = utils.Pointer.WithBool(false)
				}
			} else {
				inboundRequestItemResponse.DealCreation = utils.Pointer.WithBool(false)
			}
		}
		if qty, ok := qtySKUOrderItemsMap[inboundRequestItem.SKU]; ok {
			inboundRequestItemResponse.OrderQty = qty
		}

		// check inbound request reason
		if contractPriceItemsMap != nil && inboundRequest.Reason == enum.InboundRequestReasonToOther.INCREASE_PRICE {
			if contractPriceItem, ok := contractPriceItemsMap[inboundRequestItem.ProductID]; ok {
				inboundRequestItemResponse.NewUnitPrice = contractPriceItem.UnitPrice
				inboundRequestItemResponse.NewVAT = contractPriceItem.VAT
				inboundRequestItemResponse.NewDiscount = contractPriceItem.Discount
				inboundRequestItemResponse.NewContractPriceCode = contractPrice.ContractPriceCode
				inboundRequestItemResponse.NewContractPriceStartTime = contractPrice.StartTime
				inboundRequestItemResponse.NewContractPriceEndTime = contractPrice.EndTime
			}
		}

		// calculate sales qty per deal by reason INCREASE_PRICE
		if inboundRequest.Reason == enum.InboundRequestReasonToOther.INCREASE_PRICE {
			day := 0
			if contractPrice.StartTime != nil {
				contractPriceStartTime := contractPrice.StartTime.In(utils.TimeZoneVN)

				startDate := time.Date(contractPriceStartTime.Year(), contractPriceStartTime.Month(), contractPriceStartTime.Day(), 0, 0, 0, 0, utils.TimeZoneVN)
				nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, utils.TimeZoneVN)

				day = int(startDate.Sub(nowDate).Hours()/24) + 1
				if day < 0 {
					day = 0
				}
			}

			inboundRequestItemResponse.SalesQtyPerDeal = calculateSalesQtyPerDealByReasonIncreasePrice(inboundRequest, inboundRequestItemResponse, purchaseOrder, day, now)
		}

		if vendorPromotion != nil {
			inboundRequestItemResponse.PromotionID = vendorPromotion.VendorPromotionID
			inboundRequestItemResponse.PromotionCode = vendorPromotion.VendorPromotionCode
			inboundRequestItemResponse.PromotionName = vendorPromotion.PromotionName
			inboundRequestItemResponse.PromotionType = vendorPromotion.PromotionType
			inboundRequestItemResponse.PromotionStartDate = vendorPromotion.StartTime
			inboundRequestItemResponse.PromotionEndDate = vendorPromotion.EndTime
			if vendorPromotion.Groups != nil && len(*vendorPromotion.Groups) > 0 {
				group := *vendorPromotion.Groups
				inboundRequestItemResponse.PromotionGroup = group[0]
			}
			if inboundRequest.Reason == enum.InboundRequestReasonToOther.PROMOTIONAL_STOCK {
				if vendorPromotion.PromotionType == enum.VendorPromotionType.DISCOUNT {
					if vendorDiscountItem, ok := vendorDiscountItemsMap[inboundRequestItem.ProductID]; ok {
						for index, reward := range vendorDiscountItem.DiscountRangeRewards {
							if reward != nil && reward.IsCalculatePurchasePrice != nil && *reward.IsCalculatePurchasePrice {
								// get discount reward product
								inboundRequestItemResponse.PurchasedProductCode = vendorDiscountItem.DiscountRewardProducts[0].ProductCode
								inboundRequestItemResponse.PurchasedProductID = vendorDiscountItem.DiscountRewardProducts[0].ProductID

								// get discount range reward
								inboundRequestItemResponse.PurchaserFrom = vendorDiscountItem.DiscountRangeRewards[index].From
								inboundRequestItemResponse.PurchaserTo = vendorDiscountItem.DiscountRangeRewards[index].To
								inboundRequestItemResponse.Discount = &vendorDiscountItem.DiscountRangeRewards[index].Discount
								inboundRequestItemResponse.IsIntoCOGS = vendorDiscountItem.DiscountRangeRewards[index].IsCalculatePurchasePrice
							}
						}
					} else {
						inboundRequestItemResponse.Discount = nil
					}
				}
				if vendorPromotion.PromotionType == enum.VendorPromotionType.GIFT {
					if vendorGiftItem, ok := vendorGiftItemsMap[inboundRequestItem.ProductID]; ok {
						inboundRequestItemResponse.PurchasedProductCode = vendorGiftItem.GiftConditions[0].ProductCode
						inboundRequestItemResponse.PurchasedProductID = vendorGiftItem.GiftConditions[0].ProductID
						inboundRequestItemResponse.IsIntoCOGS = vendorGiftItem.IsCalculatePurchasePrice
						inboundRequestItemResponse.GiftProductCode = vendorGiftItem.GiftRewards[0].ProductCode
						inboundRequestItemResponse.GiftProductID = vendorGiftItem.GiftRewards[0].ProductID
						inboundRequestItemResponse.GiftQty = vendorGiftItem.GiftRewards[0].Quantity
						inboundRequestItemResponse.TotalPerStepVendor = vendorGiftItem.TotalPerStepVendor
					}
				}
			}

			day := 0
			if vendorPromotion.EndTime != nil {
				vendorPromotionEndTime := vendorPromotion.EndTime.In(utils.TimeZoneVN)

				endDate := time.Date(vendorPromotionEndTime.Year(), vendorPromotionEndTime.Month(), vendorPromotionEndTime.Day(), 0, 0, 0, 0, utils.TimeZoneVN)
				nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, utils.TimeZoneVN)

				day = int(endDate.Sub(nowDate).Hours()/24) + 1
				if day < 0 {
					day = 0
				}
			}

			if vendorPromotion.PromotionType == enum.VendorPromotionType.DISCOUNT {
				inboundRequestItemResponse.SalesQtyPerDeal = calculateSalesQtyPerDealByDiscount(inboundRequest, inboundRequestItemResponse, purchaseOrder, day, now)
			}
			if vendorPromotion.PromotionType == enum.VendorPromotionType.GIFT {
				inboundRequestItemResponse.SalesQtyPerDeal = calculateSalesQtyPerDealByGift(inboundRequest, inboundRequestItemResponse, purchaseOrder, day, now)
			}
		}
		if inboundRequestItemResponse.SalesQtyPerDeal < 0 {
			inboundRequestItemResponse.SalesQtyPerDeal = 0
		}

		calcPriceAfterRebate := getCalcPriceAfterRebateByInboundRequest(input, getSeller, inboundRequest, inboundRequestItem, applyTimeConverted)
		if calcPriceAfterRebate != nil {
			inboundRequestItemResponse.PriceAfterRebateWithAdhocPromotion = calcPriceAfterRebate.PriceAfterRebateWithAdhocPromotion
		}

		resps = append(resps, inboundRequestItemResponse)
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   resps,
	}
}

// get contract price
func getListContractPriceItemByContractPriceCodeAndWarehouseCode(contractPriceCode string, warehouseCode string) (map[int64]*model.ContractPriceItem, *common.APIResponse) {
	if len(contractPriceCode) == 0 {
		return nil, nil
	}
	contractPriceItemResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{
		ContractPriceCode: contractPriceCode,
	}, 0, 2000, nil)
	if contractPriceItemResp.Status != common.APIStatus.Ok {
		return nil, contractPriceItemResp
	}

	contractPriceItems := contractPriceItemResp.Data.([]*model.ContractPriceItem)
	contractPriceItemsMap := make(map[int64]*model.ContractPriceItem)
	for _, contractPriceItem := range contractPriceItems {
		if contractPriceItem.WarehouseCode != "00" && contractPriceItem.WarehouseCode != warehouseCode {
			continue
		}

		contractPriceItemsMap[contractPriceItem.ProductID] = contractPriceItem
	}

	return contractPriceItemsMap, nil
}

func getVendorPromotionItems(promotionCode string) (*model.VendorPromotion, map[int64]*model.VendorDiscountItem, map[int64]*model.VendorGiftItem, *common.APIResponse) {
	if len(promotionCode) == 0 {
		return nil, nil, nil, nil
	}

	opts := client.APIOption{
		Q: map[string]string{
			"vendorPromotionCode": promotionCode,
		},
	}
	getVendorPromotionResp := seller.GetVendorPromotion(opts)
	if getVendorPromotionResp.Status != common.APIStatus.Ok {
		return nil, nil, nil, getVendorPromotionResp
	}

	vendorPromotion := getVendorPromotionResp.Data.([]*model.VendorPromotion)[0]
	vendorPromotionType := vendorPromotion.PromotionType

	vendorDiscountItemsMap := make(map[int64]*model.VendorDiscountItem)
	if vendorPromotionType == enum.VendorPromotionType.DISCOUNT {
		for _, vendorDiscountItem := range vendorPromotion.VendorDiscountItems {
			if vendorDiscountItem.DiscountRewardType == enum.VendorDiscountRewardType.ABSOLUTE {
				continue
			}

			isCOGS := false
			for _, discountRangeReward := range vendorDiscountItem.DiscountRangeRewards {
				if discountRangeReward.IsCalculatePurchasePrice != nil && *discountRangeReward.IsCalculatePurchasePrice {
					isCOGS = true
					break
				}
			}

			if isCOGS {
				discountRewardProduct := vendorDiscountItem.DiscountRewardProducts[0]
				vendorDiscountItemsMap[discountRewardProduct.ProductID] = vendorDiscountItem
			}
		}
	}

	vendorGiftItemsMap := make(map[int64]*model.VendorGiftItem)
	if vendorPromotionType == enum.VendorPromotionType.GIFT {
		for _, vendorGiftItem := range vendorPromotion.VendorGiftItems {
			if len(vendorGiftItem.GiftConditions) > 1 || vendorGiftItem.IsCalculatePurchasePrice == nil || !*vendorGiftItem.IsCalculatePurchasePrice {
				continue
			}

			giftCondition := vendorGiftItem.GiftConditions[0]
			for _, giftReward := range vendorGiftItem.GiftRewards {
				if giftCondition.ProductID == giftReward.ProductID {
					vendorGiftItemsMap[giftCondition.ProductID] = vendorGiftItem
				}
			}
		}
	}

	return vendorPromotion, vendorDiscountItemsMap, vendorGiftItemsMap, nil
}

func getListInitStockDraft(warehouseCode string, skus []string) (map[string]*model.InitialStockDraft, *common.APIResponse) {
	initStockDraftResp := model.InitialStockDraftDB.Query(model.InitialStockDraft{
		WarehouseCode: warehouseCode,
		ComplexQuery: []*bson.M{
			{
				"sku": bson.M{"$in": skus},
			},
		},
	}, 0, 1000, nil)
	if initStockDraftResp.Status != common.APIStatus.Ok {
		return nil, initStockDraftResp
	}

	initStockDrafts := initStockDraftResp.Data.([]*model.InitialStockDraft)
	initStockDraftsMap := make(map[string]*model.InitialStockDraft)
	for _, initStockDraft := range initStockDrafts {
		initStockDraftsMap[initStockDraft.SKU] = initStockDraft
	}

	return initStockDraftsMap, nil
}

func getListInventory(skus []string, warehouseCode string) (map[string]*model.InventorySku, map[string]*model.InventorySkuLocation, *common.APIResponse) {
	optionInventorySKU := client.APIOption{
		Q: map[string]interface{}{
			"skus":                     skus,
			"warehouseCode":            warehouseCode,
			"isGetWaitingInboundItems": true,
		},
		Keys: []string{strings.Join(skus, ","), warehouseCode},
	}

	inventorySkuResp := warehouse.GetInventorySKU(optionInventorySKU)
	if inventorySkuResp.Status != common.APIStatus.Ok {
		return nil, nil, inventorySkuResp
	}

	inventorySkus := inventorySkuResp.Data.([]*model.InventorySku)
	inventorySkusMap := make(map[string]*model.InventorySku)
	for _, inventorySku := range inventorySkus {
		inventorySkusMap[inventorySku.Sku] = inventorySku
	}

	// get inventory sku location
	optionInventorySKULocation := client.APIOption{
		Q: map[string]interface{}{
			"skuList":       skus,
			"warehouseCode": warehouseCode,
			"locationCode":  model.INVENTORY_LOCATION_INBOUND_CODE,
		},
		Keys: []string{strings.Join(skus, ","), warehouseCode, model.INVENTORY_LOCATION_INBOUND_CODE},
	}

	inventorySkuLocationResp := warehouse.GetListInventorySKULocation(optionInventorySKULocation)
	if inventorySkuLocationResp.Status != common.APIStatus.Ok {
		return nil, nil, inventorySkuLocationResp
	}

	inventorySkuLocations := inventorySkuLocationResp.Data.([]*model.InventorySkuLocation)
	inventorySkuLocationsMap := make(map[string]*model.InventorySkuLocation)
	for _, inventorySkuLocation := range inventorySkuLocations {
		inventorySkuLocationsMap[inventorySkuLocation.SKU] = inventorySkuLocation
	}

	return inventorySkusMap, inventorySkuLocationsMap, nil
}

func getLocationItemCodesMapByInboundRequestDeal(input model.InboundRequestDealRequest, dataWarehouses []*model.Warehouse) map[string]*model.SKUItem {
	regionMap := getRegionMap()
	locationItemCodesMap := make(map[string]*model.SKUItem)

	option := client.APIOption{
		Keys:  []string{fmt.Sprint(input.SKUs), input.SellerCode, model.CLASS_INTERNAL},
		Limit: utils.Pointer.WithInt(1000),
		Q: model.SKUItem{
			SKU:         strings.Join(input.SKUs, ","),
			SellerCode:  input.SellerCode,
			SellerClass: model.CLASS_INTERNAL,
		},
	}
	skusResp := marketplace.GetListSKUItem(option)
	if skusResp.Status != common.APIStatus.Ok {
		return nil
	}

	skus := skusResp.Data.([]*model.SKUItem)

	for _, skuItem := range skus {
		if skuItem == nil || skuItem.LocationCodes == nil {
			continue
		}

		location := getLocationsToWarehouseDelivery(regionMap, *skuItem.LocationCodes, dataWarehouses)
		if len(location) == 0 {
			continue
		}

		skuItem.LocationWarehouse = location[0]
		locationItemCodesMap[fmt.Sprintf("%s_%s", skuItem.SKU, location[0])] = skuItem
	}
	return locationItemCodesMap
}

func getSkuConfigsFromInboundRequest(input model.InboundRequestDealRequest) (map[string]*model.SkuConfig, map[string]*model.SkuConfig, []string) {
	skuConfigWarehouseMap := make(map[string]*model.SkuConfig)
	skuConfigWarehouseWithoutVendorMap := make(map[string]*model.SkuConfig)
	sellerCodes := make([]string, 0)

	purchaserCode, exists := warehousePurchaserMap[input.WarehouseCode]
	if !exists {
		return nil, nil, []string{}
	}

	skuConfigsResp := model.SkuConfigDB.Query(model.SkuConfig{
		PurchaserCode: purchaserCode,
		ComplexQuery: []*bson.M{
			{
				"sku": bson.M{
					"$in": input.SKUs,
				},
			},
		},
	}, 0, 1000, nil)

	if skuConfigsResp.Status != common.APIStatus.Ok {
		return nil, nil, []string{}
	}
	skuConfigs := skuConfigsResp.Data.([]*model.SkuConfig)
	for _, skuConfig := range skuConfigs {
		if skuConfig.Vendors == nil || len(*skuConfig.Vendors) == 0 {
			continue
		}

		hasVendor := false
		for _, vendor := range *skuConfig.Vendors {
			if vendor.Priority != nil && *vendor.Priority == 0 {
				if vendor.VendorCode == input.VendorCode {
					skuConfigWarehouseMap[fmt.Sprintf("%s_%s", skuConfig.SKU, input.WarehouseCode)] = skuConfig
					sellerCodes = append(sellerCodes, vendor.VendorCode)
					hasVendor = true
				} else {
					skuConfigWarehouseWithoutVendorMap[fmt.Sprintf("%s_%s", skuConfig.SKU, input.WarehouseCode)] = skuConfig
					sellerCodes = append(sellerCodes, vendor.VendorCode)
				}
			}
		}

		// get vendor in line-manager
		if !hasVendor {
			opt := client.APIOption{
				Keys: []string{input.SellerCode, model.CLASS_VENDOR},
				Q: map[string]string{
					"sellerClass": model.CLASS_VENDOR,
					"lineManager": input.VendorCode,
				},
				Offset: utils.Pointer.WithInt(0),
				Limit:  utils.Pointer.WithInt(1000),
			}

			getSellersResp := seller.GetSellers(opt)
			if getSellersResp.Status == common.APIStatus.Ok {
				childVendors := getSellersResp.Data.([]*model.Seller)
				for _, vendor := range childVendors {
					if vendor.Code == input.VendorCode {
						skuConfigWarehouseMap[fmt.Sprintf("%s_%s", skuConfig.SKU, input.WarehouseCode)] = skuConfig
						sellerCodes = append(sellerCodes, vendor.Code)
					}
				}
			}
		}
	}

	return skuConfigWarehouseMap, skuConfigWarehouseWithoutVendorMap, sellerCodes
}

func getOrdersFromInboundRequestDeal(sellerCode string, pastTime, nowTime time.Time) map[string]int64 {
	qtySKUOrderItemsMap := make(map[string]int64)
	for offset, limit := 0, 100; ; offset += limit {
		option := client.APIOption{
			Offset: utils.Pointer.WithInt(offset),
			Limit:  utils.Pointer.WithInt(limit),
			Q: map[string]interface{}{
				"timeFrom": pastTime,
				"timeTo":   nowTime,
				"status":   "WAIT_TO_CONFIRM",
			},
		}
		getOrderResp := marketplace.GetInternalOrderList(option)
		if getOrderResp.Status != common.APIStatus.Ok {
			break
		}

		orders := getOrderResp.Data.([]*model.Order)
		for _, order := range orders {
			// Load order item
			option := client.APIOption{
				Offset: utils.Pointer.WithInt(0),
				Limit:  utils.Pointer.WithInt(1000),
				Keys:   []string{fmt.Sprint(order.OrderID)},
				Q: model.OrderItem{
					OrderID:    order.OrderID,
					SellerCode: sellerCode,
				},
			}
			getListOrderItemResp := marketplace.GetOrderItems(option)
			if getListOrderItemResp.Status != common.APIStatus.Ok {
				continue
			}
			orderItems := getListOrderItemResp.Data.([]*model.OrderItem)
			for _, orderItem := range orderItems {
				qtySKUOrderItemsMap[orderItem.Sku] += orderItem.Quantity
			}
		}
	}

	return qtySKUOrderItemsMap
}

// MAX [0, Available + Incoming + Inbound + Hedging - Order qty - Wait to reserve qty - Avg demand * MAX (0, Start date - Current date)]
func calculateSalesQtyPerDealByReasonIncreasePrice(inboundRequest *model.InboundRequest, inboundRequestItemResponse *model.InboundRequestItemResponse, mapPurchaseOrder map[string]*model.PurchaseOrder, day int, timeNow time.Time) int64 {
	if inboundRequest == nil || inboundRequestItemResponse == nil {
		return 0
	}

	calc := inboundRequestItemResponse.AvailableQuantity + inboundRequestItemResponse.IncomingQty + inboundRequestItemResponse.InboundQuantity - inboundRequestItemResponse.OrderQty - inboundRequestItemResponse.WaitToReserveQty - int64(math.Ceil(inboundRequestItemResponse.AVGDemand*float64(day)))
	calc += calcQtyHedging(mapPurchaseOrder, inboundRequestItemResponse)
	return calc
}

// MAX [0, Available + Incoming + Inbound + Hedging + Gift - Order qty - Wait to reserve qty - Avg demand * MAX (0, Start date - Current date)]
func calculateSalesQtyPerDealByGift(inboundRequest *model.InboundRequest, inboundRequestItemResponse *model.InboundRequestItemResponse, mapPurchaseOrder map[string]*model.PurchaseOrder, day int, timeNow time.Time) int64 {
	if inboundRequest == nil || inboundRequestItemResponse == nil {
		return 0
	}

	calc := inboundRequestItemResponse.AvailableQuantity + inboundRequestItemResponse.IncomingQty + inboundRequestItemResponse.InboundQuantity - inboundRequestItemResponse.OrderQty - inboundRequestItemResponse.WaitToReserveQty - int64(math.Ceil(inboundRequestItemResponse.AVGDemand*float64(day)))

	// ROUNDDOWN (Hedging / Quy cách mua,0) * Quy cách tặng
	// Quy cách mua = purchaseOrderItem.ExpectQuantity
	// Quy cách tặng (khuyến mãi) = inboundRequestItem.TotalPerStepVendor
	calc = calc + int64(math.Floor(float64(inboundRequestItemResponse.ExpectQuantity)/float64(inboundRequestItemResponse.TotalPerStepVendor)))*inboundRequestItemResponse.GiftQty
	calc += calcQtyHedging(mapPurchaseOrder, inboundRequestItemResponse)

	return calc
}

// MAX [0, Available + Incoming + Inbound + Hedging - Order qty - Wait to reserve qty - Avg demand * MAX (0, Start date - Current date)]
func calculateSalesQtyPerDealByDiscount(inboundRequest *model.InboundRequest, inboundRequestItemResponse *model.InboundRequestItemResponse, mapPurchaseOrder map[string]*model.PurchaseOrder, day int, timeNow time.Time) int64 {
	if inboundRequest == nil || inboundRequestItemResponse == nil {
		return 0
	}

	calc := inboundRequestItemResponse.AvailableQuantity + inboundRequestItemResponse.IncomingQty + inboundRequestItemResponse.InboundQuantity - inboundRequestItemResponse.OrderQty - inboundRequestItemResponse.WaitToReserveQty - int64(math.Ceil(inboundRequestItemResponse.AVGDemand*float64(day)))
	calc += calcQtyHedging(mapPurchaseOrder, inboundRequestItemResponse)
	return calc
}

func calcQtyHedging(purchaseOrder map[string]*model.PurchaseOrder, inboundRequestItemResponse *model.InboundRequestItemResponse) (hedgingQty int64) {

	// if purchaseOrder == nil {
	// 	hedgingQty = inboundRequestItemResponse.ExpectQuantity
	// } else {
	// 	if purchaseOrder.Status == enum.PurchaseOrderStatus.CONFIRMED || purchaseOrder.Status == enum.PurchaseOrderStatus.PROCESSING || purchaseOrder.Status == enum.PurchaseOrderStatus.COMPLETED {
	// 	} else {
	// 		hedgingQty = inboundRequestItemResponse.ExpectQuantity
	// 	}
	// }

	if inboundRequestItemResponse.PoCode == nil || *inboundRequestItemResponse.PoCode == "" {
		hedgingQty = inboundRequestItemResponse.ExpectQuantity
		return
	}

	if purchaseOrder, ok := purchaseOrder[*inboundRequestItemResponse.PoCode]; ok && purchaseOrder != nil {
		// Nếu PO đã CONFIRMED, PROCESSING, COMPLETED thì số lượng sản phẩm hedging đã được tính vào inbound quantity nên không cần thêm nữa.
		if purchaseOrder.Status == enum.PurchaseOrderStatus.CONFIRMED || purchaseOrder.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
			// purchaseOrder.Status == enum.PurchaseOrderStatus.PROCESSING
			purchaseOrder.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || purchaseOrder.Status == enum.PurchaseOrderStatus.RECEIVED ||
			purchaseOrder.Status == enum.PurchaseOrderStatus.COMPLETED ||
			purchaseOrder.Status == enum.PurchaseOrderStatus.AWAITING_BILLED || purchaseOrder.Status == enum.PurchaseOrderStatus.BILLED {
			return
		}
		hedgingQty = inboundRequestItemResponse.ExpectQuantity
	}

	return
}

func populateInboundRequestItem(inboundRequestItem *model.InboundRequestItem) *model.InboundRequestItemResponse {
	return &model.InboundRequestItemResponse{
		ApplyTime:              inboundRequestItem.ApplyTime,
		StartTime:              inboundRequestItem.StartTime,
		EndTime:                inboundRequestItem.EndTime,
		ProductCode:            inboundRequestItem.ProductCode,
		ProductID:              inboundRequestItem.ProductID,
		SKU:                    inboundRequestItem.SKU,
		SellerCode:             inboundRequestItem.SellerCode,
		WarehouseCode:          inboundRequestItem.WarehouseCode,
		InboundRequestID:       inboundRequestItem.InboundRequestID,
		InboundRequestCode:     inboundRequestItem.InboundRequestCode,
		InboundRequestItemID:   inboundRequestItem.InboundRequestItemID,
		InboundRequestItemCode: inboundRequestItem.InboundRequestItemCode,
		InboundRequestType:     inboundRequestItem.InboundRequestType,
		ExpectQuantity:         inboundRequestItem.ExpectQuantity,
		Status:                 inboundRequestItem.Status,
		IsActive:               inboundRequestItem.IsActive,
		VendorCode:             inboundRequestItem.VendorCode,
		UnitPrice:              inboundRequestItem.UnitPrice,
		VAT:                    inboundRequestItem.VAT,
		Discount:               &inboundRequestItem.Discount,
		ContractPriceCode:      inboundRequestItem.ContractPriceCode,
		ContractPriceStartTime: inboundRequestItem.ContractPriceStartTime,
		ContractPriceEndTime:   inboundRequestItem.ContractPriceEndTime,
		DealTicketCode:         inboundRequestItem.DealTicketCode,
		PoCode:                 &inboundRequestItem.POCode,
	}
}

func UpdateInboundRequestItemDeal(input model.InboundRequestItemDealRequest) *common.APIResponse {
	// get inbound request item
	inboundRequestItemResp := model.InboundRequestItemDB.QueryOne(model.InboundRequestItem{
		InboundRequestCode: input.InboundRequestCode,
		SKU:                input.SKU,
	})
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	inboundRequestItem := inboundRequestItemResp.Data.([]*model.InboundRequestItem)[0]

	// update inbound request item
	updateResp := model.InboundRequestItemDB.UpdateOne(
		model.InboundRequestItem{
			ID: inboundRequestItem.ID,
		},
		model.InboundRequestItem{
			DealTicketCode: input.DealTicketCode,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done",
	}
}

func CreateInboundRequestPurchaseOrder(input model.InboundRequestPurchaseOrderRequest, acc *model.Account) *common.APIResponse {
	// get inbound request
	inboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: input.InboundRequestCode,
	})
	if inboundRequestResp.Status != common.APIStatus.Ok {
		return inboundRequestResp
	}

	inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]
	if inboundRequest.PurchaseOrderCode != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Purchase order is existed",
		}
	}

	// get inbound request item
	inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		InboundRequestCode: inboundRequest.InboundRequestCode,
	}, 0, 2000, nil)
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
	paymentTerm := 4.0
	now := utils.GetVietnamTimeNow()
	purchaserOrder := model.PurchaseOrder{
		SellerCode:            inboundRequest.SellerCode,
		PurchaserCode:         warehousePurchaserMap[inboundRequest.WarehouseCode],
		Type:                  enum.POTypeVal.HEDGING,
		ApplyPromotion:        utils.Pointer.WithBool(true),
		CreatedBySystem:       string(enum.POCreateBySystem.INTERNAL_MEDX),
		PlanningTime:          &now,
		PaymentTerm:           &paymentTerm,
		WarehouseCode:         inboundRequest.WarehouseCode,
		DeliveryWarehouseCode: inboundRequest.WarehouseCode,
		VendorCode:            *inboundRequest.VendorCode,
		IssuedTime:            &now,
	}

	if inboundRequest.VendorCode != nil && *inboundRequest.VendorCode != "" {
		vendorResp := GetVendorInfo(*inboundRequest.VendorCode)
		if vendorResp.Status != common.APIStatus.Ok {
			return vendorResp
		}
		vendor := vendorResp.Data.([]*model.Seller)[0]
		purchaserOrder.VendorName = vendor.Name
	}

	productList := []int64{}
	for _, inboundRequestItem := range inboundRequestItems {
		if inboundRequestItem.ProductID == 0 {
			continue
		}
		// check product is existed và không nằm trong productList thì thêm vào array
		isExisted := false
		for _, productID := range productList {
			if productID == inboundRequestItem.ProductID {
				isExisted = true
				break
			}
		}
		if !isExisted && inboundRequestItem.ProductID != 0 {
			productList = append(productList, inboundRequestItem.ProductID)
		}
	}
	mapContractPrice := GetValidContractPriceItem(productList, inboundRequest.WarehouseCode, []string{*inboundRequest.VendorCode})

	for _, inboundRequestItem := range inboundRequestItems {
		purchaserOrderItem := model.PurchaseOrderItem{
			ProductID:              inboundRequestItem.ProductID,
			ProductCode:            inboundRequestItem.ProductCode,
			SKU:                    inboundRequestItem.SKU,
			UnitPrice:              inboundRequestItem.UnitPrice,
			VAT:                    &inboundRequestItem.VAT,
			DiscountPercent:        &inboundRequestItem.Discount,
			ExpectQuantity:         inboundRequestItem.ExpectQuantity,
			InboundRequestCode:     inboundRequest.InboundRequestCode,
			InboundRequestID:       inboundRequest.InboundRequestID,
			InboundRequestItemCode: inboundRequestItem.InboundRequestItemCode,
		}

		opts := client.APIOption{
			Q:     model.Product{ProductID: inboundRequestItem.ProductID},
			Limit: utils.Pointer.WithInt(1),
		}
		productResp := marketplace.GetSingleProduct(opts)

		if productResp.Status != common.APIStatus.Ok {
			continue
		}

		product := productResp.Data.([]*model.Product)[0]
		purchaserOrderItem.ProductName = product.Name

		query := model.PriceAfterRebate{
			WarehouseCode: inboundRequest.WarehouseCode,
			ProductID:     inboundRequestItem.ProductID,
			VendorCode:    *inboundRequest.VendorCode,
			Version:       utils.ConvertTimeToStringYYYYMMDD(&now, ""),
		}
		limit := 100
		if inboundRequest.ContractPriceCode != nil {
			query.ContractPriceCode = *inboundRequest.ContractPriceCode
			limit = 1
		}

		otps := client.APIOption{
			Q:     query,
			Limit: utils.Pointer.WithInt(limit),
		}

		getPriceAfterRebateResp := seller.GetPriceAfterRebate(otps)
		if getPriceAfterRebateResp.Status != common.APIStatus.Ok {
			purchaserOrderItem.PriceAfterRebate = utils.Pointer.WithFloat64(0)
		} else {
			priceAfterRebates := getPriceAfterRebateResp.Data.([]*model.PriceAfterRebate)
			if len(priceAfterRebates) == 1 {
				purchaserOrderItem.PriceAfterRebate = priceAfterRebates[0].PriceAfterRebate
			} else {
				getPriceAfterRebate := getPriceAfterRebateAccordingLastedContractID(priceAfterRebates)
				purchaserOrderItem.PriceAfterRebate = getPriceAfterRebate.PriceAfterRebate
			}
		}

		// get discount from contract price
		key := inboundRequestItem.ProductCode + "_" + inboundRequest.WarehouseCode
		var discount float64
		if contractPrice, ok := mapContractPrice[key]; ok {
			discount = contractPrice.Discount
		}
		inboundRequestItem.Discount = discount

		priceAfterDiscount := inboundRequestItem.UnitPrice - (inboundRequestItem.UnitPrice * inboundRequestItem.Discount / 100)
		purchaserOrderItem.PriceAfterDiscount = &priceAfterDiscount

		totalPrice := *purchaserOrderItem.PriceAfterDiscount * (1 + *purchaserOrderItem.VAT/100) * float64(purchaserOrderItem.ExpectQuantity)
		purchaserOrderItem.TotalPrice = &totalPrice

		purchaserOrderItem.InboundRequestCode = inboundRequest.InboundRequestCode
		purchaserOrder.Items = append(purchaserOrder.Items, &purchaserOrderItem)
	}

	// create purchase order
	purchaserOrderResp := CreatePurchaseOrder(purchaserOrder, acc)
	if purchaserOrderResp.Status != common.APIStatus.Ok {
		return purchaserOrderResp
	}

	dataPurchaserOrder := purchaserOrderResp.Data.([]*model.PurchaseOrder)[0]

	var newTags = []string{}
	if inboundRequest.Tags != nil && len(*inboundRequest.Tags) > 0 {
		isExisted := false
		newTags = *inboundRequest.Tags
		for _, tag := range newTags {
			if tag == "has_po" {
				isExisted = true
				continue
			}
		}
		if !isExisted {
			newTags = append(newTags, "has_po")
		}
	} else {
		newTags = []string{"has_po"}
	}

	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			ID: inboundRequest.ID,
		},
		model.InboundRequest{
			// PurchaseOrderCode: &dataPurchaserOrder.POCode,
			POCodes:     &[]string{dataPurchaserOrder.POCode},
			IsPurchased: utils.Pointer.WithBool(true),
			Tags:        &newTags,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	// update inbound request
	model.InboundRequestItemDB.UpdateMany(
		model.InboundRequestItem{
			InboundRequestCode: inboundRequest.InboundRequestCode,
		},
		model.InboundRequestItem{
			POCode: dataPurchaserOrder.POCode,
		},
	)

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   purchaserOrderResp.Data,
	}
}

func getPriceAfterRebateAccordingLastedContractID(priceAfterRebateContracts []*model.PriceAfterRebate) *model.PriceAfterRebate {
	for _, priceAfterRebate := range priceAfterRebateContracts {
		contractPriceResp := model.ContractPriceDB.QueryOne(model.ContractPrice{
			ContractPriceCode: priceAfterRebate.ContractPriceCode,
		})
		if contractPriceResp.Status != common.APIStatus.Ok {
			continue
		}

		contractPrice := contractPriceResp.Data.([]*model.ContractPrice)[0]
		if contractPrice.StartTime != nil {
			diffDays := math.Ceil(utils.GetVietnamTimeNow().Sub(*contractPrice.StartTime).Hours() / 24)
			priceAfterRebate.DiffDay = int(diffDays)
		}
	}

	minDiffDay := math.MaxInt64
	maxContractPrice := int64(math.MinInt64)
	var result *model.PriceAfterRebate
	for _, priceAfterRebate := range priceAfterRebateContracts {
		if (priceAfterRebate.DiffDay < minDiffDay) || (priceAfterRebate.DiffDay == minDiffDay && priceAfterRebate.ContractPriceID > maxContractPrice) {
			minDiffDay = priceAfterRebate.DiffDay
			maxContractPrice = priceAfterRebate.ContractPriceID
			result = priceAfterRebate
		}
	}

	return result
}

func ToolInboundRequest(input model.InboundRequest) *common.APIResponse {
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	checkInboundRequestResp := model.InboundRequestDB.QueryOne(model.InboundRequest{
		InboundRequestCode: input.InboundRequestCode,
		SellerCode:         input.SellerCode,
	})
	if checkInboundRequestResp.Status != common.APIStatus.Ok {
		return checkInboundRequestResp
	}

	checkInboundRequest := checkInboundRequestResp.Data.([]*model.InboundRequest)[0]

	// do action
	updateResp := model.InboundRequestDB.UpdateOne(
		model.InboundRequest{
			ID: checkInboundRequest.ID,
		},
		input,
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	if input.ApplyTime != "" {
		model.InboundRequestItemDB.UpdateMany(
			model.InboundRequestItem{
				InboundRequestCode: input.InboundRequestCode,
			},
			model.InboundRequestItem{
				ApplyTime: input.ApplyTime,
			},
		)
	}
	return updateResp
}

func getLastestContractPrice(vendorCode string, applyTime time.Time, productID int64, warehouseCodes []string) (*model.ContractPrice, *model.ContractPriceItem, *common.APIResponse) {
	if !utils.IsContainsString(warehouseCodes, "00") {
		warehouseCodes = append(warehouseCodes, "00")
	}
	// get contract price
	contractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		Status: enum.ContractPriceStatus.CONFIRMED,
		ComplexQuery: []bson.M{
			{
				"vendor_codes": bson.M{"$in": []string{vendorCode}},
			},
			{
				"start_time": bson.M{"$lte": applyTime},
			},
			{
				"end_time": bson.M{"$gte": applyTime},
			},
			{
				"warehouse_code": bson.M{"$in": warehouseCodes},
			},
		},
	}, 0, 1000, nil)
	if contractPriceResp.Status != common.APIStatus.Ok {
		return nil, nil, contractPriceResp
	}

	contractPrices := contractPriceResp.Data.([]*model.ContractPrice)
	if len(contractPrices) == 0 {
		return nil, nil, &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Not found contract price",
		}
	}

	contractPriceCodes := make([]string, 0, len(contractPrices))
	for _, contractPrice := range contractPrices {
		contractPriceCodes = append(contractPriceCodes, contractPrice.ContractPriceCode)
	}

	// get contract price item by product code & contract price code
	contractPriceItemsResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{
		ProductID:    productID,
		ComplexQuery: []bson.M{{"contract_price_code": bson.M{"$in": contractPriceCodes}}},
	}, 0, 1000, nil)
	if contractPriceItemsResp.Status != common.APIStatus.Ok {
		return nil, nil, contractPriceItemsResp
	}

	contractPriceItems := contractPriceItemsResp.Data.([]*model.ContractPriceItem)
	contractPriceCodeMap := make(map[string]*model.ContractPriceItem)
	for _, contractPriceItem := range contractPriceItems {
		contractPriceCodeMap[contractPriceItem.ContractPriceCode] = contractPriceItem
	}

	filterContractPrices := make([]*model.ContractPrice, 0, len(contractPrices))
	for _, contractPrice := range contractPrices {
		if _, ok := contractPriceCodeMap[contractPrice.ContractPriceCode]; !ok {
			continue
		}

		diffDays := math.Ceil(utils.GetVietnamTimeNow().Sub(*contractPrice.StartTime).Hours() / 24)
		contractPrice.DiffDay = int(diffDays)
		filterContractPrices = append(filterContractPrices, contractPrice)
	}

	getContractPrice := getContractPriceLasted(filterContractPrices)

	// get contract price item by product code & contract price code
	contractPriceItemResp := model.ContractPriceItemDB.QueryOne(model.ContractPriceItem{
		ProductID:         productID,
		ContractPriceCode: getContractPrice.ContractPriceCode,
	})
	if contractPriceItemResp.Status != common.APIStatus.Ok {
		return nil, nil, contractPriceItemResp
	}

	contractPriceItem := contractPriceItemResp.Data.([]*model.ContractPriceItem)[0]
	return getContractPrice, contractPriceItem, nil
}

func getCalcPriceAfterRebateByInboundRequest(input model.InboundRequestDealRequest, sellerResp *model.Seller, inboundRequest *model.InboundRequest, inboundRequestItem *model.InboundRequestItem, applyTimeConverted time.Time) *model.CalcPriceAfterRebateResp {
	if sellerResp == nil || inboundRequest == nil || inboundRequestItem == nil {
		return nil
	}

	queryCalcPriceAfterRebateRequest := model.CalcPriceAfterRebateRequest{
		ProductID:     inboundRequestItem.ProductID,
		ApplyTime:     strings.Replace(inboundRequest.ApplyTime, "-", "", -1),
		WarehouseCode: input.WarehouseCode,
		VendorCode:    input.VendorCode,
	}
	if len(sellerResp.LineManager) > 0 {
		queryCalcPriceAfterRebateRequest.VendorCode = sellerResp.LineManager
	}

	if input.ContractPriceCode != "" {
		queryCalcPriceAfterRebateRequest.ContractPriceCode = inboundRequest.ContractPriceCode

		contractPriceItemsResp := model.ContractPriceItemDB.QueryOne(model.ContractPriceItem{
			ProductID:         inboundRequestItem.ProductID,
			ContractPriceCode: *inboundRequest.ContractPriceCode,
		})
		if contractPriceItemsResp.Status == common.APIStatus.Ok {
			contractPriceItem := contractPriceItemsResp.Data.([]*model.ContractPriceItem)[0]
			queryCalcPriceAfterRebateRequest.UnitPrice = contractPriceItem.UnitPrice
			queryCalcPriceAfterRebateRequest.VAT = contractPriceItem.VAT
			queryCalcPriceAfterRebateRequest.Discount = contractPriceItem.Discount
		}
	} else {
		contractPrice, contractPriceItem, err := getLastestContractPrice(queryCalcPriceAfterRebateRequest.VendorCode, applyTimeConverted, inboundRequestItem.ProductID, []string{queryCalcPriceAfterRebateRequest.WarehouseCode})
		if err == nil {
			if contractPrice != nil {
				queryCalcPriceAfterRebateRequest.ContractPriceCode = &contractPrice.ContractPriceCode
			}
			if contractPriceItem != nil {
				queryCalcPriceAfterRebateRequest.Discount = contractPriceItem.Discount
				queryCalcPriceAfterRebateRequest.VAT = contractPriceItem.VAT
				queryCalcPriceAfterRebateRequest.UnitPrice = contractPriceItem.UnitPrice
			}
		}
	}
	if inboundRequest.PromotionCode != nil && *inboundRequest.PromotionCode != "" {
		queryCalcPriceAfterRebateRequest.PromotionCode = inboundRequest.PromotionCode
	}

	keys := []string{
		fmt.Sprintf("%v", queryCalcPriceAfterRebateRequest.ProductID),
		queryCalcPriceAfterRebateRequest.WarehouseCode,
		queryCalcPriceAfterRebateRequest.ApplyTime,
		queryCalcPriceAfterRebateRequest.WarehouseCode,
	}
	if queryCalcPriceAfterRebateRequest.ContractPriceCode != nil {
		keys = append(keys, *queryCalcPriceAfterRebateRequest.ContractPriceCode)
	}
	if queryCalcPriceAfterRebateRequest.PromotionCode != nil {
		keys = append(keys, *queryCalcPriceAfterRebateRequest.PromotionCode)
	}

	opts := client.APIOption{
		Keys: keys,
		Body: queryCalcPriceAfterRebateRequest,
	}

	calcPriceAfterRebateResp := seller.PostCalcPriceAfterRebate(opts)
	if calcPriceAfterRebateResp.Status == common.APIStatus.Ok {
		calcPriceAfterRebate := calcPriceAfterRebateResp.Data.([]*model.CalcPriceAfterRebateResp)[0]
		return calcPriceAfterRebate
	}

	return nil
}

func InboundRequestItemValidateImport(input model.InboundRequestItemValidateImportReq) *common.APIResponse {
	applyTimeConverted, _ := time.Parse("2006-01-02", input.ApplyTime)
	vendor, err := getVendorByVendorID(*input.VendorID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Can not find vendor",
			ErrorCode: "CAN_NOT_FIND_VENDOR",
		}
	}

	vendorCode := vendor.Code
	if len(vendor.LineManager) > 0 {
		vendorCode = vendor.LineManager
	}

	// OTHER is hedging type
	kind := input.Kind
	resp := GetInboundRequestItemContractPrice(input.SellerCode, input.WarehouseCode, vendorCode, "OTHER", input.ProductID, applyTimeConverted, &kind, input.ExpectQuantity, input.NumDayInStock)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	inboundRequestItem := resp.Data.([]interface{})[0].(model.ContractPriceItemInboundRequestResponse)
	data := model.InboundRequestItemValidateImportResponse{
		ContractPriceItemInboundRequestResponse: inboundRequestItem,
		AvgDemand:                               inboundRequestItem.AvgDemand,
		NumDayInStock:                           inboundRequestItem.NumDayInStock,
		ExpectQuantity:                          inboundRequestItem.ExpectQuantity,
		TotalAvailableQuantity:                  inboundRequestItem.TotalAvailableQuantity,
		AvailableQuantityWHInbound:              inboundRequestItem.AvailableQuantityWHInbound,
		QuantityIncoming:                        inboundRequestItem.QuantityIncoming,
		WaitingConfirmOrderQuantity:             inboundRequestItem.WaitingConfirmOrderQuantity,
		VendorCode:                              vendorCode,
	}

	// get vendor promotion
	var vendorPromotion *model.VendorPromotion
	if input.PromotionID != nil {
		opts := client.APIOption{
			Q: map[string]int64{
				"vendorPromotionID": *input.PromotionID,
			},
			Offset: utils.Pointer.WithInt(0),
			Limit:  utils.Pointer.WithInt(1),
		}
		getVendorPromotionResp := seller.GetVendorPromotion(opts)
		if getVendorPromotionResp.Status == common.APIStatus.Ok {
			vendorPromotion = getVendorPromotionResp.Data.([]*model.VendorPromotion)[0]
			data.PromotionCode = vendorPromotion.VendorPromotionCode
		}
	}

	if vendorPromotion == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Can not find vendor promotion",
			ErrorCode: "CAN_NOT_FIND_VENDOR_PROMOTION",
		}
	}

	// get inbound request
	inboundRequestResp := model.InboundRequestDB.Query(model.InboundRequest{
		PromotionCode:      &vendorPromotion.VendorPromotionCode,
		VendorCode:         &vendor.Code,
		ApplyTime:          input.ApplyTime,
		WarehouseCode:      input.WarehouseCode,
		Status:             enum.InboundRequestStatus.DRAFT,
		InboundRequestType: "OTHER",
	}, 0, 1, &primitive.M{"_id": -1})

	if inboundRequestResp.Status == common.APIStatus.Ok {
		inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]

		// get inbound request item
		inboundRequestItemResp := model.InboundRequestItemDB.QueryOne(model.InboundRequestItem{
			InboundRequestID: inboundRequest.InboundRequestID,
			ProductID:        input.ProductID,
		})
		if inboundRequestItemResp.Status == common.APIStatus.Ok {
			data.InboundRequestCode = inboundRequest.InboundRequestCode
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{data},
	}
}

func getVendorByVendorID(vendorID int64) (*model.Seller, error) {
	structTemp := struct {
		SellerClass string `json:"sellerClass"`
		SellerID    int64  `json:"sellerID"`
	}{
		SellerClass: model.CLASS_VENDOR,
		SellerID:    vendorID,
	}

	opt := client.APIOption{
		Q:      structTemp,
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	getVendor := seller.GetSellers(opt)
	if getVendor.Status != common.APIStatus.Ok {
		return nil, errors.New("get vendor failed")
	}

	return getVendor.Data.([]*model.Seller)[0], nil
}

func InboundRequestItemImport(input model.InboundRequestItemImportReq) *common.APIResponse {
	vendor, err := getVendorByVendorID(*input.VendorID)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Can not find vendor",
			ErrorCode: "CAN_NOT_FIND_VENDOR",
		}
	}

	// get vendor promotion
	var vendorPromotion *model.VendorPromotion
	if input.PromotionID != nil {
		opts := client.APIOption{
			Q: map[string]int64{
				"vendorPromotionID": *input.PromotionID,
			},
			Offset: utils.Pointer.WithInt(0),
			Limit:  utils.Pointer.WithInt(1),
		}
		getVendorPromotionResp := seller.GetVendorPromotion(opts)
		if getVendorPromotionResp.Status == common.APIStatus.Ok {
			vendorPromotion = getVendorPromotionResp.Data.([]*model.VendorPromotion)[0]
		}
	}
	if vendorPromotion == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "Can not find vendor promotion",
			ErrorCode: "CAN_NOT_FIND_VENDOR_PROMOTION",
		}
	}

	// get inbound request item
	filter := model.InboundRequest{
		InboundRequestType: "OTHER",
		ApplyTime:          input.ApplyTime,
		Status:             enum.InboundRequestStatus.DRAFT,
		WarehouseCode:      input.WarehouseCode,
		VendorCode:         &vendor.Code,
		PromotionCode:      &vendorPromotion.VendorPromotionCode,
	}

	inboundRequestResp := model.InboundRequestDB.Query(filter, 0, 1, &bson.M{"_id": -1})
	if inboundRequestResp.Status == common.APIStatus.Ok {
		inboundRequest := inboundRequestResp.Data.([]*model.InboundRequest)[0]

		// get inbound request item
		inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
			InboundRequestCode: inboundRequest.InboundRequestCode,
		}, 0, 4000, nil)
		if inboundRequestItemResp.Status == common.APIStatus.Ok {
			inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)

			inboundRequestItemProductIdMap := make(map[int64]*model.InboundRequestItem)
			for _, inboundRequestItem := range inboundRequestItems {
				inboundRequestItemProductIdMap[inboundRequestItem.ProductID] = inboundRequestItem
			}

			metadata := map[string]interface{}{
				"availableQuantityWHInbound": input.MetaData.AvailableQuantityWHInbound,
				"avgDemand":                  input.MetaData.AVGDemand,
				"numDayInStock":              input.MetaData.NumDayInStock,
				"quantityIncoming":           input.MetaData.QuantityIncoming,
				"totalAvailableQuantity":     input.MetaData.TotalAvailableQuantity,
				"demandClassificationCode":   input.MetaData.DemandClassificationCode,
				"total30DaysDemand":          input.MetaData.Total30DaysDemand,
				"hedgingAVGDemand":           input.MetaData.HedgingAVGDemand,
				// "avgDemand":                  input.MetaData.AVGDemand,
			}
			if inboundRequestItem, ok := inboundRequestItemProductIdMap[input.ProductID]; ok {

				// update inbound request item
				resp := model.InboundRequestItemDB.UpdateOne(
					model.InboundRequestItem{
						ID:                 inboundRequestItem.ID,
						InboundRequestCode: inboundRequestItem.InboundRequestCode,
					},
					model.InboundRequestItem{
						ExpectQuantity: input.ExpectQuantity,
						Metadata:       &metadata,
					},
				)

				if resp.Status == common.APIStatus.Ok {
					return resp
				}
			} else {
				// create inbound request item
				inboundRequestItem := model.InboundRequestItem{
					ApplyTime:          input.ApplyTime,
					ContractPriceCode:  input.ContractPriceCode,
					ExpectQuantity:     input.ExpectQuantity,
					ProductID:          input.ProductID,
					ProductCode:        input.ProductCode,
					SellerCode:         "MEDX",
					UnitPrice:          input.UnitPrice,
					VAT:                input.VAT,
					Discount:           input.Discount,
					SKU:                input.SKU,
					WarehouseCode:      input.WarehouseCode,
					Metadata:           &metadata,
					InboundRequestID:   inboundRequest.InboundRequestID,
					InboundRequestCode: inboundRequest.InboundRequestCode,
					Status:             enum.InboundRequestStatus.DRAFT,
					IsActive:           utils.Pointer.WithBool(false),
					VendorCode:         inboundRequest.VendorCode,
					InboundRequestType: enum.InboundRequestType.OTHER,
				}

				inboundRequestItem.InboundRequestItemID, inboundRequestItem.InboundRequestItemCode = model.GetInboundRequestItemID()

				createResp := model.InboundRequestItemDB.Create(inboundRequestItem)
				if createResp.Status == common.APIStatus.Ok {
					return createResp
				}
			}
		}
	} else {

		// Create inbound request
		metadata := map[string]interface{}{
			"availableQuantityWHInbound": input.MetaData.AvailableQuantityWHInbound,
			"avgDemand":                  input.MetaData.AVGDemand,
			"numDayInStock":              input.MetaData.NumDayInStock,
			"quantityIncoming":           input.MetaData.QuantityIncoming,
			"totalAvailableQuantity":     input.MetaData.TotalAvailableQuantity,

			"demandClassificationCode": input.MetaData.DemandClassificationCode,
			"total30DaysDemand":        input.MetaData.Total30DaysDemand,
			"hedgingAVGDemand":         input.MetaData.HedgingAVGDemand,
		}

		inboundRequest := model.InboundRequest{
			ApplyTime:          input.ApplyTime,
			InboundRequestType: input.InboundRequestType,
			CreatedByID:        input.CreatedByID,
			CreatedByName:      input.CreatedByName,
			Items: []*model.InboundRequestItem{
				{
					ApplyTime:          input.ApplyTime,
					ContractPriceCode:  input.ContractPriceCode,
					ExpectQuantity:     input.ExpectQuantity,
					ProductID:          input.ProductID,
					ProductCode:        input.ProductCode,
					SellerCode:         "MEDX",
					UnitPrice:          input.UnitPrice,
					VAT:                input.VAT,
					Discount:           input.Discount,
					SKU:                input.SKU,
					WarehouseCode:      input.WarehouseCode,
					InboundRequestType: enum.InboundRequestType.OTHER,
					Metadata:           &metadata,
				},
			},
			Name:          input.Name,
			Reason:        input.Reason,
			SellerCode:    "MEDX",
			TotalRebate:   input.TotalRebate,
			VendorCode:    &vendor.Code,
			WarehouseCode: input.WarehouseCode,
		}
		if vendorPromotion != nil {
			inboundRequest.PromotionCode = &vendorPromotion.VendorPromotionCode
		}

		resp := CreateInboundRequest(&inboundRequest)
		if resp.Status == common.APIStatus.Ok {
			return resp
		}
	}

	return &common.APIResponse{
		Status:    common.APIStatus.Error,
		Message:   "Create inbound request failed",
		ErrorCode: "CREATE_INBOUND_REQUEST_FAILED",
	}
}

func GetInboundRequestAvgDemand(sellerCode, warehouseCode, skuCode string) *common.APIResponse {
	avgDemand := 0.0
	// get initial stock draft
	initialStockDraftResp := model.InitialStockDraftDB.QueryOne(model.InitialStockDraft{
		WarehouseCode: warehouseCode,
		SellerCode:    sellerCode,
		SKU:           skuCode,
	})
	if initialStockDraftResp.Status != common.APIStatus.Ok {
		return initialStockDraftResp
	}

	initialStockDraft := initialStockDraftResp.Data.([]*model.InitialStockDraft)[0]
	if initialStockDraft.AVGDemand > 0.0 {
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []interface{}{initialStockDraft.AVGDemand},
		}
	}

	avgDemand = float64(initialStockDraft.OriginInitialStock) / float64(initialStockDraft.Leadtime)
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{avgDemand},
	}
}

func GetInboundRequestHedgingAvgDemand(sellerCode, warehouseCode, skuCode string) *common.APIResponse {
	avgDemand := 0.0
	// get initial stock draft
	initialStockDraftResp := model.InitialStockDraftDB.QueryOne(model.InitialStockDraft{
		WarehouseCode: warehouseCode,
		SellerCode:    sellerCode,
		SKU:           skuCode,
	})
	if initialStockDraftResp.Status != common.APIStatus.Ok {
		return initialStockDraftResp
	}

	initialStockDraft := initialStockDraftResp.Data.([]*model.InitialStockDraft)[0]
	if initialStockDraft.HedgingAVGDemand > 0.0 {
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   []interface{}{initialStockDraft.HedgingAVGDemand},
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{avgDemand},
	}
}

func GetInboundRequestDemandHedging(sellerCode, warehouseCode, skuCode string) *common.APIResponse {
	// get initial stock draft
	initialStockDraftResp := model.InitialStockDraftDB.QueryOne(model.InitialStockDraft{
		WarehouseCode: warehouseCode,
		SellerCode:    sellerCode,
		SKU:           skuCode,
	})
	if initialStockDraftResp.Status != common.APIStatus.Ok {
		return initialStockDraftResp
	}

	initialStockDraft := initialStockDraftResp.Data.([]*model.InitialStockDraft)[0]

	var demandHedgingResponse = &model.DemandHedgingResponse{
		SKU:                      skuCode,
		WarehouseCode:            warehouseCode,
		HedgingAVGDemand:         initialStockDraft.HedgingAVGDemand,
		Total30DaysDemand:        initialStockDraft.Total30DaysDemand,
		DemandClassificationCode: initialStockDraft.DemandClassificationCode,
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.DemandHedgingResponse{demandHedgingResponse},
	}
}

func getInboundRequestAvgStorageDays(sellerCode, warehouseCode, skuCode string, expectQuantity int64, productID int64) *model.DetermineStockAvailabilityResponse {
	avgDemand, availableQuantity, availableQuantityWHInbound, quantityIncoming, waitingConfirmOrderQuantity, poCodesResp, status, waitingHoldQuantity := CalculateAmountInInboundRequest(warehouseCode, skuCode, sellerCode, productID)
	if status != common.APIStatus.Ok {
		return nil
	}

	resp := &model.DetermineStockAvailabilityResponse{
		AvgDemand:                   math.Round(avgDemand*100) / 100,
		TotalAvailableQuantity:      availableQuantity,
		AvailableQuantityWHInbound:  availableQuantityWHInbound,
		QuantityIncoming:            quantityIncoming,
		WaitingConfirmOrderQuantity: waitingConfirmOrderQuantity,
		PoCodes:                     poCodesResp,
		ExpectQuantity:              expectQuantity,
		WaitingHoldQuantity:         waitingHoldQuantity,
		Status:                      common.APIStatus.Ok,
	}
	if avgDemand == 0 {
		return resp
	}

	calc := math.Round(float64(availableQuantity+availableQuantityWHInbound+quantityIncoming+expectQuantity-waitingConfirmOrderQuantity-waitingHoldQuantity) / avgDemand)
	resp.NumDayInStock = int64(calc)
	return resp
}

func getInboundRequestExpectedQty(sellerCode, warehouseCode, skuCode string, numDayInStock int64, productID int64) *model.DetermineStockAvailabilityResponse {
	avgDemand, totalAvailableQuantity, availableQuantityWHInbound, quantityIncoming, waitingConfirmOrderQuantity, poCodesResp, status, waitingHoldQuantity := CalculateAmountInInboundRequest(warehouseCode, skuCode, sellerCode, productID)
	if status != common.APIStatus.Ok {
		return nil
	}

	resp := &model.DetermineStockAvailabilityResponse{
		AvgDemand:                   avgDemand,
		TotalAvailableQuantity:      totalAvailableQuantity,
		AvailableQuantityWHInbound:  availableQuantityWHInbound,
		QuantityIncoming:            quantityIncoming,
		WaitingConfirmOrderQuantity: waitingConfirmOrderQuantity,
		PoCodes:                     poCodesResp,
		NumDayInStock:               numDayInStock,
		WaitingHoldQuantity:         waitingHoldQuantity,
		Status:                      common.APIStatus.Ok,
	}

	if avgDemand == 0 {
		resp.ExpectQuantity = int64(0)
		return resp
	}

	roundAmount := math.Round(avgDemand * float64(numDayInStock))
	resp.ExpectQuantity = int64(roundAmount) - totalAvailableQuantity - availableQuantityWHInbound - quantityIncoming + waitingConfirmOrderQuantity
	if resp.ExpectQuantity < 0 {
		resp.ExpectQuantity = 0
	}

	return resp
}

func CalculateAmountInInboundRequest(warehouseCode, skuCode, sellerCode string, productID int64) (avgDemand float64, totalAvailableQuantity, availableQuantityWHInbound, quantityIncoming int64, waitingConfirmOrderQuantity int64, poCodesResp []string, status string, waitingHoldQuantity int64) {
	purchaseCode, _ := utils.WarehouseCodeToPurchaserCode(warehouseCode)
	// get avg demand
	getAvgDemand := GetInboundRequestHedgingAvgDemand(sellerCode, warehouseCode, skuCode)
	if getAvgDemand.Status != common.APIStatus.Ok {
		status = getAvgDemand.Status
		return
	}

	avgDemand = getAvgDemand.Data.([]interface{})[0].(float64)

	// get available quantity
	totalAvailableQuantity = int64(0)
	optionInventorySKU := client.APIOption{
		Q: map[string]interface{}{
			"skus":                     []string{skuCode},
			"warehouseCode":            warehouseCode,
			"isGetWaitingInboundItems": true,
		},
		Keys: []string{skuCode, warehouseCode, "GetInboundRequestExpectedQty"},
	}

	inventorySkuResp := warehouse.GetInventorySKU(optionInventorySKU)
	if inventorySkuResp.Status == common.APIStatus.Ok {
		inventorySku := inventorySkuResp.Data.([]*model.InventorySku)[0]
		totalAvailableQuantity = inventorySku.AvailableQuantity
		waitingHoldQuantity = inventorySku.WaitingHoldQuantity
	}

	// get available quantity wh inbound
	availableQuantityWHInbound = int64(0)
	optionInventorySKULocation := client.APIOption{
		Q: map[string]interface{}{
			"skuList":       []string{skuCode},
			"warehouseCode": warehouseCode,
			"locationCode":  model.INVENTORY_LOCATION_INBOUND_CODE,
		},
		Keys: []string{skuCode, warehouseCode, model.INVENTORY_LOCATION_INBOUND_CODE, "GetInboundRequestExpectedQty"},
	}

	inventorySkuLocationResp := warehouse.GetListInventorySKULocation(optionInventorySKULocation)
	if inventorySkuLocationResp.Status == common.APIStatus.Ok {
		inventorySkuLocations := inventorySkuLocationResp.Data.([]*model.InventorySkuLocation)
		for _, item := range inventorySkuLocations {
			availableQuantityWHInbound += item.Quantity
		}
	}

	// get purchasing order item
	quantityIncoming = int64(0)
	purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		SKU:           skuCode,
		WarehouseCode: warehouseCode,
		SellerCode:    sellerCode,
		PurchaserCode: purchaseCode,
	}, 0, 100, &primitive.M{"_id": -1})
	if purchaseOrderItemResp.Status == common.APIStatus.Ok {
		purchasingOrderItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)
		poCodes := make([]string, 0, len(purchasingOrderItems))
		poCodesMap := make(map[string][]*model.PurchaseOrderItem)

		for _, purchasingOrderItem := range purchasingOrderItems {
			poCodes = append(poCodes, purchasingOrderItem.POCode)
			poCodesMap[purchasingOrderItem.POCode] = append(poCodesMap[purchasingOrderItem.POCode], purchasingOrderItem)
		}

		// get purchase order by poCodes
		purchasingOrdersResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
			ComplexQuery: []*bson.M{
				{"po_code": bson.M{"$in": poCodes}},
			},
		}, 0, 100, &primitive.M{"_id": -1})
		if purchasingOrdersResp.Status == common.APIStatus.Ok {
			purchasingOrders := purchasingOrdersResp.Data.([]*model.PurchaseOrder)

			poCodesAllow := make([]string, 0, len(purchasingOrders))
			for _, purchasingOrder := range purchasingOrders {
				if purchasingOrder.Status == enum.PurchaseOrderStatus.CONFIRMED || purchasingOrder.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
					// purchasingOrder.Status == enum.PurchaseOrderStatus.PROCESSING ||
					purchasingOrder.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || purchasingOrder.Status == enum.PurchaseOrderStatus.RECEIVED {
					poCodesAllow = append(poCodesAllow, purchasingOrder.POCode)
				}
			}
			for _, poCode := range poCodesAllow {
				if poItems, exists := poCodesMap[poCode]; exists {
					for _, poItem := range poItems {
						quantityIncoming += poItem.ExpectQuantity
						quantityIncoming -= poItem.ActualQuantity
					}
					poCodesResp = append(poCodesResp, poCode)
				}
			}
		}
	}

	waitingConfirmOrderQuantity = int64(0)
	skuItem := GetSkuItemFromSKU(productID, sellerCode, warehouseCode)
	if skuItem != nil {
		opts := client.APIOption{
			Keys: []string{},
			Q: map[string]interface{}{
				"warehouseCodeIn": []string{warehouseCode},
				"sku":             skuCode,
				"itemCode":        skuItem.ItemCode,
				"sellerCode":      sellerCode,
			},
		}

		skuInventoryInfoResp := monitoring.GetSKUInventoryInfo(opts)
		if skuInventoryInfoResp.Status == common.APIStatus.Ok {
			skuInventoryInfo := skuInventoryInfoResp.Data.([]*model.SKUInventoryInfoResponse)[0]
			waitingConfirmOrderQuantity = skuInventoryInfo.WaitingConfirmOrderQuantity
		}
	}

	return avgDemand, totalAvailableQuantity, availableQuantityWHInbound, quantityIncoming, waitingConfirmOrderQuantity, poCodesResp, common.APIStatus.Ok, waitingHoldQuantity
}

func InboundRequestItemManualUpdate(input model.InboundRequestItemManualUpdateReq) *common.APIResponse {
	// get inbound request item by inbound request code
	inboundRequestItemResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		InboundRequestCode: input.InboundRequestCode,
	}, 0, 1000, nil)
	if inboundRequestItemResp.Status != common.APIStatus.Ok {
		return inboundRequestItemResp
	}

	inboundRequestItems := inboundRequestItemResp.Data.([]*model.InboundRequestItem)
	// update inbound request item if inbound_request_item_code, inbound_request_item_is are empty

	for _, inboundRequestItem := range inboundRequestItems {
		if inboundRequestItem.InboundRequestItemCode == "" && inboundRequestItem.InboundRequestItemID == 0 {
			inboundRequestItemID, inboundRequestItemCode := model.GetInboundRequestItemID()
			resp := model.InboundRequestItemDB.UpdateOne(
				model.InboundRequestItem{
					ID:                 inboundRequestItem.ID,
					InboundRequestCode: input.InboundRequestCode,
				},
				model.InboundRequestItem{
					InboundRequestItemCode: inboundRequestItemCode,
					InboundRequestItemID:   inboundRequestItemID,
					InboundRequestType:     enum.InboundRequestType.OTHER,
				},
			)
			if resp.Status != common.APIStatus.Ok {
				return resp
			}
		}
	}

	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}
