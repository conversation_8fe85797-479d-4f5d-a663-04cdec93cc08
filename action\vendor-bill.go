package action

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/client/billing"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// define error code
const (
	BUYER_EMPTY            = "BUYER_EMPTY"
	PO_ITEM_EMPTY          = "PO_ITEM_EMPTY"
	PO_CODE_EMPTY          = "PO_CODE_EMPTY"
	BUYER_CODE_EMPTY       = "BUYER_CODE_EMPTY"
	TYPE_CONVERSION_FAILED = "TYPE_CONVERSION_FAILED"
	INTERNAL_ERROR         = "INTERNAL_ERROR"
)

// GetVendorBillItemList ...
func GetVendorBillItemList(query model.VendorBillItem, offset, limit int64, getTotal bool, sort string) *common.APIResponse {

	sortField := &primitive.M{"_id": 1}
	switch sort {
	case "-_id":
		sortField = &primitive.M{"_id": -1}
	}

	result := model.VendorBillItemDB.Query(query, offset, limit, sortField)
	if getTotal {
		result.Total = model.VendorBillItemDB.Count(query).Total
	}
	return result
}

// GetVendorBillList ...
func GetVendorBillList(query model.VendorBill, offset, limit int64, getTotal, getItems bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}
	switch sort {
	case "issuedTime":
		sortField = &primitive.M{"issued_time": 1}
	case "-issuedTime":
		sortField = &primitive.M{"issued_time": -1}
	case "dueTime":
		sortField = &primitive.M{"due_time": 1}
	case "-dueTime":
		sortField = &primitive.M{"due_time": -1}
	case "id":
		sortField = &primitive.M{"_id": 1}
	}

	// search by hash_tag: Replica DB
	for _, qM := range query.ComplexQuery {
		if _, ok := (*qM)["hash_tag"]; ok {

			// Rep DB
			opt := options.Find().SetSkip(offset).SetLimit(limit).SetSort(sortField)
			result := model.VendorBillReplicaDB.QueryWithOptions(query, opt)
			if getTotal {
				result.Total = model.VendorBillReplicaDB.Count(query).Total
			}
			return result
		}
	}

	// Normal DB
	result := model.VendorBillDB.Query(query, offset, limit, sortField)
	if getTotal {
		result.Total = model.VendorBillDB.Count(query).Total
	}

	if getItems && result.Status == common.APIStatus.Ok {
		vendorBills := result.Data.([]*model.VendorBill)
		for i := range vendorBills {
			vb := vendorBills[i]
			vbItemsResp := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillCode: vb.VendorBillCode}, 0, 1000, nil)
			if vbItemsResp.Status == common.APIStatus.Ok {
				vbItems := vbItemsResp.Data.([]*model.VendorBillItem)
				for j := range vbItems {
					vb.Items = append(vb.Items, *vbItems[j])
				}
			}
		}
	}

	return result
}

// CreateVendorBil ...
func CreateVendorBill(input model.VendorBill) *common.APIResponse {
	// validate sku code
	for _, item := range input.Items {
		if item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductID %v do not have SKU", item.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.SKU),
			}
		}
	}

	// check seller - purchaser
	if response, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return response
	}

	// t := time.Now()
	// input.IssuedTime = &t
	input.Status = enum.VendorBillStatus.DRAFT
	input.VendorBillID, input.VendorBillCode = model.GetVendorBillID()

	input.HashTag = GenHashTagVBFromInfo(&input, nil)
	vendorResp := GetVendorInfo(input.VendorCode)
	if vendorResp.Status == common.APIStatus.Ok {
		vendor := vendorResp.Data.([]*model.Seller)[0]
		input.HashTag = GenHashTagVBFromInfo(&input, vendor)
	}

	input.ProductCodes = input.GenProductCodesFromItems()

	// crate vendor bill
	createResp := model.VendorBillDB.Create(input)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}

	// create items
	for _, item := range input.Items {
		item.VendorBillID = input.VendorBillID
		item.VendorBillCode = input.VendorBillCode
		_, item.VendorBillItemCode = model.GetVendorBillItemID()
		item.IsInboundCompleted = utils.Pointer.WithBool(false)

		createItemResp := model.VendorBillItemDB.Create(item)
		if createItemResp.Status != common.APIStatus.Ok {
			return createItemResp
		}
	}

	return createResp
}

// UpdateVendorBill ...
func UpdateVendorBill(input model.VendorBill) *common.APIResponse {
	// validate seller, purchaser
	if res, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return res
	}

	// validate sku code
	for _, item := range input.Items {
		if item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductID %v do not have SKU", item.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.SKU),
			}
		}
	}
	input.ProductCodes = input.GenProductCodesFromItems()

	//
	vendorBillResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: input.VendorBillCode})
	if vendorBillResp.Status != common.APIStatus.Ok {
		return vendorBillResp
	}
	oldVB := vendorBillResp.Data.([]*model.VendorBill)[0]
	if oldVB.SellerCode != "" && input.SellerCode != "" && oldVB.SellerCode != input.SellerCode {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not allow change sellerCode",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		}
	}

	// #1 update vendor bill
	// chặn chuyển trạng thái sang CANCEL và PAID khi update
	input.Status = ""
	input.IsCompletedPO = nil
	updateResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{
			VendorBillCode: input.VendorBillCode,
			ComplexQuery: []*bson.M{
				{"status": bson.M{
					"$nin": []enum.VendorBillStatusValue{
						enum.VendorBillStatus.CANCEL,
						enum.VendorBillStatus.PAID,
					},
				}},
			},
		},
		input,
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}
	vendorBill := updateResp.Data.([]*model.VendorBill)[0]

	// update hashtag
	if input.VendorCode != "" && input.VendorCode != oldVB.VendorCode || oldVB.InvoiceNumber != input.InvoiceNumber {
		input.HashTag = GenHashTagVBFromInfo(vendorBill, nil)
		vendorResp := GetVendorInfo(input.VendorCode)
		if vendorResp.Status == common.APIStatus.Ok {
			vendor := vendorResp.Data.([]*model.Seller)[0]
			input.HashTag = GenHashTagVBFromInfo(vendorBill, vendor)
		}
	}
	model.VendorBillDB.UpdateOne(
		model.VendorBill{
			VendorBillCode: input.VendorBillCode,
		},
		input,
	)

	// #2 delete items
	oldItems := []*model.VendorBillItem{}
	oldItemsResp := model.VendorBillItemDB.Query(
		model.VendorBillItem{VendorBillCode: input.VendorBillCode},
		0, 1000, nil,
	)
	if oldItemsResp.Status == common.APIStatus.NotFound {
		// do nothing
	} else if oldItemsResp.Status != common.APIStatus.Ok {
		return oldItemsResp
	} else if oldItemsResp.Status == common.APIStatus.Ok {
		oldItems = oldItemsResp.Data.([]*model.VendorBillItem)
	}
	for _, old := range oldItems {
		existed := false
		for _, item := range input.Items {
			if old.VendorBillItemCode == item.VendorBillItemCode {
				existed = true
				break
			}
		}

		if existed == false {
			if vendorBill.Status != enum.VendorBillStatus.DRAFT {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Do not delete old lines",
					ErrorCode: string(enum.ErrorCodeInvalid.NotAllowDeleteOldLine),
				}
			}

			deleteResp := model.VendorBillItemDB.Delete(model.VendorBillItem{ID: old.ID})
			if deleteResp.Status != common.APIStatus.Ok {
				return deleteResp
			}
		}
	}

	// #3 update items
	for _, item := range input.Items {
		if item.VendorBillItemCode == "" {
			item.VendorBillID = vendorBill.VendorBillID
			item.VendorBillCode = vendorBill.VendorBillCode
			_, item.VendorBillItemCode = model.GetVendorBillItemID()
		}

		// không update
		item.IsInboundCompleted = nil
		item.ActualQuantity = 0
		item.POTrackings = nil

		upsertItemResp := model.VendorBillItemDB.UpdateOne(
			model.VendorBillItem{
				VendorBillItemCode: item.VendorBillItemCode,
			},
			item,
		)
		if upsertItemResp.Status == common.APIStatus.NotFound {
			item.IsInboundCompleted = utils.Pointer.WithBool(false)
			upsertItemResp = model.VendorBillItemDB.Create(
				item,
			)
		}
		if upsertItemResp.Status != common.APIStatus.Ok {
			return upsertItemResp
		}
	}

	// #FINAL bắn lot-date qua kho
	if updateResp.Status == common.APIStatus.Ok && input.POCode != "" {
		// đánh reason tag mới
		if vendorBill.Status == enum.VendorBillStatus.DRAFT {
			seller.ManualCheckApproveVB(client.APIOption{
				Body: model.VendorBill{
					VendorBillCode: vendorBill.VendorBillCode,
				},
			})
		}

		purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			return purchaseOrderResp
		}
		po := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]

		if po.Status != enum.PurchaseOrderStatus.COMPLETED {
			// bắn lodate qua kho
			var ZERO int64 = 0
			for i := range input.Items {
				input.Items[i].Quantity = &ZERO
			}
			resp := executeLotDateInbound(input, *po)
			if resp.Status != common.APIStatus.Ok {
				return resp
			}
		}
	}

	return updateResp
}

// UpdateVendorBill ...
func UpdateVendorBillInfo(input model.VendorBill) *common.APIResponse {
	updateResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{
			VendorBillCode: input.VendorBillCode,
		},
		model.VendorBill{Note: input.Note},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update success",
	}
}

// CancelVendorBill ... CANCEL
func CancelVendorBill(input model.VendorBill) *common.APIResponse {

	// #1 check valid status
	checkResp := model.VendorBillDB.QueryOne(
		model.VendorBill{
			VendorBillCode: input.VendorBillCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	vbCheck := checkResp.Data.([]*model.VendorBill)[0]

	// #1.1 validate VB can switch to CANCEL in setting
	isValid := CheckValidVendorBillStatus(vbCheck.Status, enum.VendorBillStatus.CANCEL)
	if !isValid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	// cancel WAIT_TO_PAID
	if vbCheck.Status == enum.VendorBillStatus.WAIT_TO_PAID || vbCheck.Status == enum.VendorBillStatus.PAID {
		// paymentCheckResp := model.PaymentItemDB.QueryOne(model.PaymentItem{VendorBillCode: vbCheck.VendorBillCode})
		paymentItemQuery := model.PaymentItemPlatform{
			VendorBillCodeIn: []string{vbCheck.VendorBillCode},
		}
		paymentCheckResp := GetSingleNewPaymentItem(paymentItemQuery, model.QueryOption{})
		if paymentCheckResp.Status == common.APIStatus.Ok {
			paymentCheckResp.Status = common.APIStatus.Invalid
			paymentCheckResp.Message = "This bill has a payment slip."
			paymentCheckResp.ErrorCode = string(enum.ErrorCodeInvalid.NotCancelVBHadPayment)
			return paymentCheckResp
		}
	}

	// #2 update VB
	updateVBResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{
			ID: vbCheck.ID,
		},
		model.VendorBill{
			Status: enum.VendorBillStatus.CANCEL,
		},
	)

	if updateVBResp.Status == common.APIStatus.Ok {
		// rollback, giảm số lượng bill_qty trên po_item
		if vbCheck.Status == enum.VendorBillStatus.APPROVED || vbCheck.Status == enum.VendorBillStatus.WAIT_TO_PAID || vbCheck.Status == enum.VendorBillStatus.PAID {
			scrollbackVendorBill(vbCheck)
		}

		if vbCheck.POCode != "" {
			// switch bill-status PO
			model.PurchaseOrderDB.UpdateOne(
				model.PurchaseOrder{
					POCode: vbCheck.POCode,
					// Status: enum.PurchaseOrderStatus.COMPLETED,
					ComplexQuery: []*bson.M{
						{
							"status": bson.M{
								"$in": []enum.PurchaseOrderStatusValue{
									enum.PurchaseOrderStatus.COMPLETED,
									enum.PurchaseOrderStatus.BILLED,
								},
							},
						},
					},
				},
				// BillStatus: enum.POBillStatus.WAITING_BILL
				model.PurchaseOrder{Status: enum.PurchaseOrderStatus.AWAITING_BILLED},
			)

			if vbCheck.Status != enum.VendorBillStatus.DRAFT {
				model.VendorBillDB.UpdateMany(model.VendorBill{
					POCode: vbCheck.POCode,
					Status: enum.VendorBillStatus.APPROVED,
				}, model.VendorBill{
					IsCompletedPO:   utils.Pointer.WithBool(false),
					IsWrongQuantity: utils.Pointer.WithBool(false),
					IsWrongLotDate:  utils.Pointer.WithBool(false),
				})
			}

		}

	}

	return updateVBResp
}

func scrollbackVendorBill(vbCheck *model.VendorBill) {
	var ZERO int64 = 0
	if vbCheck.POCode == "" || vbCheck.VendorBillCode == "" {
		return
	}

	// #1 get vendor bill item
	vbItemResp := model.VendorBillItemDB.Query(
		model.VendorBillItem{VendorBillCode: vbCheck.VendorBillCode},
		0, 1000, nil,
	)
	if vbItemResp.Status != common.APIStatus.Ok {
		return
	}
	vbItems := vbItemResp.Data.([]*model.VendorBillItem)

	// #2 đẩy lại vào storage
	for _, item := range vbItems {
		if len(item.POTrackings) == 0 {
			continue
		}
		if item.ProductID == 0 || item.ActualQuantity == 0 {
			continue
		}
		if item.Lot == "" || item.ExpDate == "" {
			item.Lot = "-"
			item.ExpDate = "-"
		}

		model.InboundCallbackStoreDB.Create(model.InboundCallbackStore{
			SellerCode: vbCheck.SellerCode,

			POCode:    vbCheck.POCode,
			ProductID: item.ProductID,
			Lot:       item.Lot,
			ExpDate:   item.ExpDate,
			Quantity:  &item.ActualQuantity,
		})
	}

	// // #3 gở VB codes cho PO
	// purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: vbCheck.POCode})
	// if purchaseOrderResp.Status == common.APIStatus.Ok {
	// 	po := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]
	// 	poVBCodes := []string{}
	// 	if po.VBCodes != nil {
	// 		for _, code := range *po.VBCodes {
	// 			if code != vbCheck.VendorBillCode {
	// 				poVBCodes = append(poVBCodes, code)
	// 			}
	// 		}
	// 	}
	// 	model.PurchaseOrderDB.UpdateOne(
	// 		model.PurchaseOrder{ID: po.ID},
	// 		model.PurchaseOrder{VBCodes: &poVBCodes},
	// 	)
	// }

	// #4 gở bill quantity cho poItems
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: vbCheck.POCode}, 0, 1000, &primitive.M{"_id": -1})
	if poItemResp.Status == common.APIStatus.Ok {
		poItems := poItemResp.Data.([]*model.PurchaseOrderItem)

		for i := range vbItems {
			vbItem := vbItems[i]
			if vbItem.Quantity == nil {
				continue
			}

			for j := range poItems {
				poItem := poItems[j]
				if poItem.ProductID != vbItem.ProductID ||
					poItem.BillQuantity == nil || *poItem.BillQuantity == 0 {
					continue
				}

				if *poItem.BillQuantity < *vbItem.Quantity {
					sl := *vbItem.Quantity - *poItem.BillQuantity
					vbItem.Quantity = &sl
					poItem.BillQuantity = &ZERO
				} else if *poItem.BillQuantity > *vbItem.Quantity {
					sl := *poItem.BillQuantity - *vbItem.Quantity
					poItem.BillQuantity = &sl
					vbItem.Quantity = &ZERO
				} else if *poItem.BillQuantity == *vbItem.Quantity {
					poItem.BillQuantity = &ZERO
					vbItem.Quantity = &ZERO
				}

				// update po item
				model.PurchaseOrderItemDB.UpdateOne(
					model.PurchaseOrderItem{ID: poItem.ID},
					model.PurchaseOrderItem{
						BillQuantity: poItem.BillQuantity,
					},
				)
				poItems[j] = poItem
			}
		}
	}

	// for update parent
	if vbCheck.POCode != "" {
		model.PurchaseOrderDB.UpdateOne(
			model.PurchaseOrder{POCode: vbCheck.POCode},
			model.PurchaseOrder{},
		)
	}
}

// SwitchStatusVendorBill ... DRAFT -> APPROVED -> WAIT_TO_PAID -> PAID
func SwitchStatusVendorBill(input model.VendorBill, acc *model.Account) *common.APIResponse {

	TRUE := true

	// #1 check valid status
	checkResp := model.VendorBillDB.QueryOne(
		model.VendorBill{
			VendorBillCode: input.VendorBillCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	vbCheck := checkResp.Data.([]*model.VendorBill)[0]
	// #1.1 validate
	isValid := CheckValidVendorBillStatus(vbCheck.Status, input.Status)
	if !isValid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}
	// get items
	vendorBillItemResp := model.VendorBillItemDB.Query(
		model.VendorBillItem{VendorBillCode: input.VendorBillCode},
		0, 1000, nil,
	)
	var items []model.VendorBillItem
	if vendorBillItemResp.Status == common.APIStatus.Ok {
		vendorBillItems := vendorBillItemResp.Data.([]*model.VendorBillItem)
		for i := range vendorBillItems {
			items = append(items, *vendorBillItems[i])
		}
	}
	vbCheck.Items = items

	// check PO
	if vbCheck.POCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "No poCode entered.",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		}
	}

	// #1.2 check items
	if len(vbCheck.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The bill is empty items.",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}
	for _, item := range vbCheck.Items {
		if item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductID %v do not have SKU", item.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.SKU),
			}
		}
	}

	// ================================ switch to APPROVED ================================
	if input.Status == enum.VendorBillStatus.APPROVED {
		// #3 update old receipt - nếu như có chênh lệch sl giửa PO và VB
		checkWrongQuantityResp := checkChangeExpectQuantity(*vbCheck)
		if checkWrongQuantityResp.Status != common.APIStatus.Ok {
			return checkWrongQuantityResp
		}

		// // step 1: đánh dấu đã mapping
		// if vbCheck.IsCompletedMapping == nil || *vbCheck.IsCompletedMapping == false {
		// 	updateVBResp := model.VendorBillDB.UpdateOne(
		// 		model.VendorBill{
		// 			ID: vbCheck.ID,
		// 		},
		// 		model.VendorBill{
		// 			IsCompletedMapping: utils.Pointer.WithBool(true),
		// 		},
		// 	)
		// 	return updateVBResp
		// }

		// step 2: duyệt VB
		return handleApproveVB(input, vbCheck, acc)
	}

	if input.Status == enum.VendorBillStatus.WAIT_TO_PAID {
		// Thông báo cho nhà cung cấp trong trường hợp sai lot/date
		// 05/01/2022 Chỉ những vendor bill của NCC trading mới bật cờ IsWrongLotDate = TRUE
		if vbCheck.IsWrongLotDate != nil && *vbCheck.IsWrongLotDate == TRUE {
			for _, item := range vbCheck.Items {
				if item.Lot != "" && item.Lot != "-" && item.ExpDate != "" && item.ExpDate != "-" {
					NotifyVendorPO(vbCheck.POCode)
					break
				}
			}
		}
		if vbCheck.TotalPrice == nil || *vbCheck.TotalPrice == 0 {
			input.Status = enum.VendorBillStatus.PAID
		}
	}

	// ================================ switch to other status ================================
	// update VB
	// now := time.Now()
	updater := model.VendorBill{
		Status: input.Status,
	}

	// if input.Status == enum.VendorBillStatus.PAID && vbCheck.DueTime == nil { // PAID - Update time paid
	// 	updater.DueTime = &now
	// }

	// #2 update VB
	updateVBResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{
			ID: vbCheck.ID,
		},
		updater,
	)
	if updater.Status == enum.VendorBillStatus.PAID {
		go billing.BuildVendorBillAnalyze(vbCheck.VendorBillCode, vbCheck.POCode)
	}

	if updateVBResp.Status == common.APIStatus.Ok && input.Status == enum.VendorBillStatus.WAIT_TO_PAID {
		vbData := updateVBResp.Data.([]*model.VendorBill)[0]
		AutoApplyPaymentVoucherToVB(vbData.VendorBillCode)
	}

	return updateVBResp
}

func handleApproveVB(input model.VendorBill, vbCheck *model.VendorBill, acc *model.Account) *common.APIResponse {

	purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
		POCode: vbCheck.POCode,
	})
	if purchaseOrderResp.Status != common.APIStatus.Ok {
		return purchaseOrderResp
	}
	po := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]
	if po.Status == enum.PurchaseOrderStatus.CANCELED {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Canceled PO cannot perform this operation",
			ErrorCode: string(enum.ErrorCodeInvalid.NotApproveAffterCancelPO),
		}
	}

	// update VB
	now := time.Now()
	updater := model.VendorBill{
		Status: input.Status,
	}

	// #1.2 check items
	if len(vbCheck.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The bill is empty.",
			ErrorCode: string(enum.ErrorCodeInvalid.NotEmptyItems),
		}
	}

	// #2 APPROVED - bắn bắn lotdate qua kho qua kho trước, thành công mới tiếp tục xử lý
	// #1.3 check item hợp lệ
	for _, item := range vbCheck.Items {
		if item.POCode == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Product has not mapped PO.",
				ErrorCode: string(enum.ErrorCodeInvalid.NotMappingItem),
			}
		}
	}

	// // #3 update old receipt - nếu như có chênh lệch sl giửa PO và VB
	// checkWrongQuantityResp := checkChangeExpectQuantity(*vbCheck)
	// if checkWrongQuantityResp.Status != common.APIStatus.Ok {
	// 	return checkWrongQuantityResp
	// }

	// #4.1 bắn lot-date qua kho
	// Trường hợp PO đã completed thì không cần phải bắn qua kho
	if po.Status != enum.PurchaseOrderStatus.COMPLETED {
		resp := executeLotDateInbound(*vbCheck, *po)
		if resp.Status != common.APIStatus.Ok {
			return resp
		}
	}

	updater.AccountingTime = &now
	updater.ApprovedTime = &now
	updater.ApprovedByName = acc.Fullname
	updater.ApprovedByID = acc.AccountID

	// #2 update VB
	updater.RemainingMoney = vbCheck.TotalPrice
	updateVBResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{
			ID: vbCheck.ID,
		},
		updater,
	)

	// #4.2 cập nhật giá mua qua SKU
	// TODO temp hardcode - need to update
	if po.WarehouseCode != "DN" && po.WarehouseCode != "TTN" {
		executeUpdateSkuPrice(*vbCheck, po)
	}
	// #4.3 cập nhật từ điển
	ExecuteUpdateNameMapping(*vbCheck)
	// #4.4 cập nhật billQuantity cho PO
	excuteUpdateBillQuantityPO(*vbCheck)
	// #4.5 update thông tin SKU-config
	// TODO temp hardcode - need to update
	if po.WarehouseCode != "TTN" {
		excuteUpdateSKUConfigVBApprove(*vbCheck)
	}
	// // #3 update VBCodes cho PO
	// if updateVBResp.Status == common.APIStatus.Ok {
	// 	poVBCodes := []string{}
	// 	if po.VBCodes != nil {
	// 		poVBCodes = *po.VBCodes
	// 	}
	// 	poVBCodes = append(poVBCodes, input.VendorBillCode)
	// 	model.PurchaseOrderDB.UpdateOne(
	// 		model.PurchaseOrder{ID: po.ID},
	// 		model.PurchaseOrder{VBCodes: &poVBCodes},
	// 	)
	// }

	// Update VB liên quan
	if vbCheck.POCode != "" {
		model.VendorBillDB.UpdateMany(model.VendorBill{
			POCode: vbCheck.POCode,
			Status: enum.VendorBillStatus.APPROVED,
			// IsCompletedPO: utils.Pointer.WithBool(true),
		}, model.VendorBill{
			IsCompletedPO:   utils.Pointer.WithBool(false),
			IsWrongQuantity: utils.Pointer.WithBool(false),
			IsWrongLotDate:  utils.Pointer.WithBool(false),
		})
	}

	return updateVBResp
}

// nếu như có chênh lệch sl giửa PO và VB
func checkChangeExpectQuantity(input model.VendorBill) *common.APIResponse {
	vendorBills := []*model.VendorBill{}

	poCode := input.POCode
	if poCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "PO Invalid.",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		}
	}

	// get all vb
	vendorBillResp := model.VendorBillDB.Query(
		model.VendorBill{
			POCode: poCode,
			ComplexQuery: []*bson.M{
				{
					"status": bson.M{"$in": []enum.VendorBillStatusValue{
						enum.VendorBillStatus.APPROVED,
						enum.VendorBillStatus.PAID,
						enum.VendorBillStatus.WAIT_TO_PAID,
					}},
				},
			},
		},
		0, 1000, nil,
	)
	if vendorBillResp.Status == common.APIStatus.NotFound {
	} else if vendorBillResp.Status == common.APIStatus.Ok {
		vendorBills = vendorBillResp.Data.([]*model.VendorBill)
	} else {
		return vendorBillResp
	}

	// mapVBProduct
	mapVBProduct := make(map[int64]int64)
	for _, vb := range vendorBills {
		vendorBillItemResp := model.VendorBillItemDB.Query(
			model.VendorBillItem{
				VendorBillCode: vb.VendorBillCode,
			},
			0, 1000, nil,
		)
		if vendorBillItemResp.Status != common.APIStatus.Ok {
			continue
		}
		vendorBillItems := vendorBillItemResp.Data.([]*model.VendorBillItem)

		for _, item := range vendorBillItems {
			if item.Quantity == nil {
				continue
			}
			mapVBProduct[item.ProductID] += *item.Quantity
		}
	}
	for _, item := range input.Items {
		if item.Quantity == nil {
			continue
		}
		mapVBProduct[item.ProductID] += *item.Quantity
	}

	// mapPOProduct
	mapPOProduct := make(map[int64]int64)
	purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(
		model.PurchaseOrderItem{
			POCode: poCode,
		},
		0, 1000, nil,
	)
	if purchaseOrderItemResp.Status != common.APIStatus.Ok {
		return purchaseOrderItemResp
	}
	poItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)
	for _, item := range poItems {
		mapPOProduct[item.ProductID] += item.ExpectQuantity
	}

	// check wrong quantity
	poItemWrongs := []model.PurchaseOrderItem{}
	for productID, vbItemQty := range mapVBProduct {
		poItemQty := mapPOProduct[productID]

		if vbItemQty > poItemQty {
			poItemWrongs = append(poItemWrongs, model.PurchaseOrderItem{
				ProductID:      productID,
				ExpectQuantity: poItemQty,
				BillQuantity:   &vbItemQty,
			})
		}
	}

	if len(poItemWrongs) > 0 {
		ids := []string{}
		for _, item := range poItemWrongs {
			ids = append(ids, fmt.Sprintf("%v (%v > %v)", item.ProductID, mapVBProduct[item.ProductID], mapPOProduct[item.ProductID]))
		}

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The product quantity of the VB is more than the PO, please update the quantity on the PO before approving, products list: " + strings.Join(ids, ", "),
			Data:      poItemWrongs,
			ErrorCode: string(enum.ErrorCodeInvalid.QuantityVBGtPO),
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK.",
	}
}

func ExecuteUpdateNameMapping(input model.VendorBill) {
	vendorBillItemResp := model.VendorBillItemDB.Query(
		model.VendorBillItem{VendorBillCode: input.VendorBillCode},
		0, 1000, nil)
	if vendorBillItemResp.Status == common.APIStatus.Ok {

		vendorBillItems := vendorBillItemResp.Data.([]*model.VendorBillItem)
		for _, vendorBillItem := range vendorBillItems {

			if vendorBillItem.ProductID == 0 {
				continue
			}

			// // get new product name
			// productResp := client.ProductClient.GetSingleProduct(&model.Product{ProductID: vendorBillItem.ProductID})
			// if productResp.Status == common.APIStatus.Ok {
			// 	product := productResp.Data[0]
			// 	vendorBillItem.ProductName = product.Name
			// }

			productVendorName := vendorBillItem.ItemName
			if productVendorName == "" {
				continue
			}
			normProductVendorName := utils.NormalizeString(productVendorName)

			// // Check data library was exists in DB
			// nameMappingResp := model.ProductNameMappingDB.QueryOne(
			// 	model.ProductNameMapping{
			// 		ProductCode: vendorBillItem.ProductCode,
			// 		// ProductSlug: normProductName,
			// 	},
			// )
			// if nameMappingResp.Status == common.APIStatus.NotFound {

			// 	// Create new data for library
			// 	newNameMapping := model.ProductNameMapping{
			// 		ProductID:   vendorBillItem.ProductID,
			// 		ProductCode: vendorBillItem.ProductCode,

			// 		ProductVendorNames: []string{productVendorName},
			// 		ProductVendorSlugs: []string{normProductVendorName},
			// 	}
			// 	model.ProductNameMappingDB.Create(newNameMapping)
			// } else if nameMappingResp.Status == common.APIStatus.Ok {

			// 	nameMapping := nameMappingResp.Data.([]*model.ProductNameMapping)[0]

			// 	// Cập nhật lại library trong trường hợp productVendorName không chứa product vendor name hiện tại
			// 	if !utils.IsContains(nameMapping.ProductVendorSlugs, normProductVendorName) {
			// 		nameMapping.ProductVendorNames = append(nameMapping.ProductVendorNames, productVendorName)
			// 		nameMapping.ProductVendorSlugs = append(nameMapping.ProductVendorSlugs, normProductVendorName)

			// 		model.ProductNameMappingDB.UpdateOne(model.ProductNameMapping{ID: nameMapping.ID}, nameMapping)
			// 	}
			// }

			productMappingResp := model.ProductMappingDB.QueryOne(
				model.ProductMapping{
					ProductID: vendorBillItem.ProductID,
				},
			)
			if productMappingResp.Status == common.APIStatus.NotFound {

				// Create new data for library
				model.ProductMappingDB.Create(model.ProductMapping{
					ProductID: vendorBillItem.ProductID,

					ProductVendors: &[]model.ProductVendor{{
						VendorCode: input.VendorCode,

						ProductVendorNames: []string{productVendorName},
						ProductVendorSlugs: []string{normProductVendorName},
					}},
				})
			} else {
				productMapping := productMappingResp.Data.([]*model.ProductMapping)[0]
				if productMapping.ProductVendors == nil {

					productMapping.ProductVendors = &[]model.ProductVendor{{
						VendorCode: input.VendorCode,

						ProductVendorNames: []string{productVendorName},
						ProductVendorSlugs: []string{normProductVendorName},
					}}
					model.ProductMappingDB.UpdateOne(model.ProductMapping{ID: productMapping.ID}, productMapping)
				} else {

					// Check data is exists before
					isExists := false
					for index := range *productMapping.ProductVendors {
						vendor := (*productMapping.ProductVendors)[index]
						if vendor.VendorCode == input.VendorCode {
							isExists = true
							if !utils.IsContains(vendor.ProductVendorSlugs, normProductVendorName) {
								vendor.ProductVendorNames = append(vendor.ProductVendorNames, productVendorName)
								vendor.ProductVendorSlugs = append(vendor.ProductVendorSlugs, normProductVendorName)
								(*productMapping.ProductVendors)[index] = vendor
								model.ProductMappingDB.UpdateOne(model.ProductMapping{ID: productMapping.ID}, productMapping)
								continue
							}
						}
					}
					if !isExists {
						*productMapping.ProductVendors = append(*productMapping.ProductVendors, model.ProductVendor{
							VendorCode:         input.VendorCode,
							ProductVendorNames: []string{productVendorName},
							ProductVendorSlugs: []string{normProductVendorName},
						})
						model.ProductMappingDB.UpdateOne(model.ProductMapping{ID: productMapping.ID}, productMapping)
					}
				}

			}
		}
	}
}

func executeUpdateSkuPrice(input model.VendorBill, po *model.PurchaseOrder) {
	if po == nil || po.WarehouseCode == "" {
		return
	}

	var vendorBillItems []model.ErpVendorBillItem

	for i := range input.Items {
		item := input.Items[i]
		if item.ProductID == 0 ||
			item.Price == nil || *item.Price == 0 {
			continue
		}
		unitPrice := *item.Price
		priceAfterDiscount := *item.PriceAfterDiscount
		// vat
		var vat float64 = -1
		if item.Vat != nil && *item.Vat > 0 {
			vat = *item.Vat
		}
		//
		vendorBillItems = append(
			vendorBillItems,
			model.ErpVendorBillItem{
				ProductID: item.ProductID,
				UnitPrice: &unitPrice,
				VAT:       vat,

				PriceAfterDiscount: &priceAfterDiscount,
				DiscountPercent:    item.DiscountPercent,
			},
		)
	}
	if len(vendorBillItems) > 0 {
		go marketplace.UpdateVendorBill(
			client.APIOption{
				Body: model.ErpVendorBill{
					WarehouseCode:   po.WarehouseCode,
					SellerCode:      input.SellerCode,
					VendorBillItems: vendorBillItems,
				},
				Keys: []string{po.POCode},
			},
		)
	}
}

func excuteUpdateBillQuantityPO(input model.VendorBill) {
	poCode := input.POCode
	if poCode == "" {
		return
	}

	vbItems := make([]model.VendorBillItem, len(input.Items))
	copy(vbItems, input.Items)

	// cập nhật bill quantity cho PO item
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, &primitive.M{"_id": -1})
	if poItemResp.Status == common.APIStatus.Ok {
		poItems := poItemResp.Data.([]*model.PurchaseOrderItem)
		// put map
		mapPOItem := make(map[int64][]model.PurchaseOrderItem)
		for i := range poItems {
			poItem := poItems[i]
			mapPOItem[poItem.ProductID] = append(mapPOItem[poItem.ProductID], *poItem)
		}

		// loop productID
		for productID := range mapPOItem {
			poMapItems := mapPOItem[productID]

			for i := range poMapItems {
				poItem := poMapItems[i]
				if poItem.BillQuantity == nil {
					var ZERO int64 = 0
					poItem.BillQuantity = &ZERO
				}
				oldBillQuantity := *poItem.BillQuantity

				// nếu đủ rồi thì không cần fill
				if poItem.ExpectQuantity <= *poItem.BillQuantity &&
					i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
					continue
				}

				// loop VendorBillItem
				for j, callbackItem := range vbItems {
					// không quan tâm line
					if poItem.ProductID != callbackItem.ProductID {
						continue
					}
					if callbackItem.Quantity == nil || *callbackItem.Quantity <= 0 {
						continue
					}

					// tính bill quantity mới
					newBillQuantity := *poItem.BillQuantity + *callbackItem.Quantity
					if newBillQuantity > poItem.ExpectQuantity &&
						i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
						newBillQuantity = poItem.ExpectQuantity
					}

					// cập nhật lại InboundCallbackItems
					sl := *callbackItem.Quantity - (newBillQuantity - *poItem.BillQuantity)
					callbackItem.Quantity = &sl
					if *callbackItem.Quantity < 0 {
						continue
					}
					vbItems[j] = callbackItem
					// cập nhật lại mapPOItem
					poItem.BillQuantity = &newBillQuantity
					mapPOItem[productID][i] = poItem
				}

				// do action: update BillQuantity PurchaseOrderItem
				if oldBillQuantity != *poItem.BillQuantity {
					model.PurchaseOrderItemDB.UpdateOne(
						model.PurchaseOrderItem{ID: poItem.ID},
						model.PurchaseOrderItem{
							BillQuantity: poItem.BillQuantity,
						},
					)
				}
			}
		}
	}

	// for update parent
	if input.POCode != "" {
		model.PurchaseOrderDB.UpdateOne(
			model.PurchaseOrder{
				POCode: input.POCode,
				Status: enum.PurchaseOrderStatus.COMPLETED,
			},
			model.PurchaseOrder{
				Status: enum.PurchaseOrderStatus.AWAITING_BILLED,
			},
		)
	}
}

func excuteUpdateSKUConfigVBApprove(input model.VendorBill) {
	for _, item := range input.Items {

		if item.SKU == "" || input.PurchaserCode == "" {
			continue
		}

		// Skip case gift
		if item.Price == nil || *item.Price == 0 ||
			item.PriceAfterDiscount == nil || *item.PriceAfterDiscount == 0 {
			continue
		}

		// Query SKU config
		skuConfigQuery := model.SkuConfig{
			SKU:           item.SKU,
			PurchaserCode: input.PurchaserCode,
		}
		skuConfigResp := model.SkuConfigDB.QueryOne(skuConfigQuery)

		if skuConfigResp.Status == common.APIStatus.NotFound {

			// fill price
			vendorConfig := newVendorConfigVB(input, item)
			vendorConfig.Priority = &utils.DEFAULT_PRIORITY

			skuConfig := model.SkuConfig{
				SellerCode:    input.SellerCode,
				PurchaserCode: input.PurchaserCode,

				SKU:         item.SKU,
				ProductCode: item.ProductCode,
				ProductID:   item.ProductID,
				// Unit:        item.Unit,

				Vendors: &[]model.VendorConfig{vendorConfig},
			}

			// create
			model.SkuConfigDB.Create(skuConfig)

		} else if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]

			// case có SKU nhưng vendors rỗng
			if skuConfig.Vendors == nil {

				// fill price
				vendorConfig := newVendorConfigVB(input, item)
				vendorConfig.Priority = &utils.DEFAULT_PRIORITY

				skuConfig.Vendors = &[]model.VendorConfig{vendorConfig}

				model.SkuConfigDB.UpdateOne(model.SkuConfig{ID: skuConfig.ID}, skuConfig)

				// case có SKU có vendors
			} else {
				var maxPriority int64 = -1
				existed := false

				for index := range *skuConfig.Vendors {
					vendor := (*skuConfig.Vendors)[index]

					// Lấy giá trị Priority lớn nhất trong sku-config
					if vendor.Priority != nil && *vendor.Priority > maxPriority {
						maxPriority = *vendor.Priority
					}

					// exist thì update lại cho vendor đó
					if vendor.VendorCode == input.VendorCode {

						if item.Price != nil {
							vendor.UnitPrice = *item.Price
						}
						if item.DiscountPercent != nil {
							vendor.DiscountPercent = *item.DiscountPercent
						}
						if item.Vat != nil {
							vendor.VAT = int64(*item.Vat)
						}
						(*skuConfig.Vendors)[index] = vendor

						existed = true
						break
					}
				}

				// nếu VendorCode chưa tồn tại trong vendors
				if existed == false {

					// Trường hợp vendor có config ncc nhưng không set ưu tiên => Mặc định là 1
					// Ngược lại thì += 1
					if maxPriority < utils.MAX_AUTO_PRIORITY {
						if maxPriority == -1 {
							maxPriority = utils.DEFAULT_PRIORITY
						} else {
							maxPriority += 1
						}
					}
					// fill price
					vendorConfig := newVendorConfigVB(input, item)
					vendorConfig.Priority = &maxPriority

					*skuConfig.Vendors = append(*skuConfig.Vendors, vendorConfig)
				}

				// Update DB
				model.SkuConfigDB.UpdateOne(
					model.SkuConfig{ID: skuConfig.ID},
					skuConfig,
				)
			}

		}
	}
}

func newVendorConfigVB(vb model.VendorBill, vbItem model.VendorBillItem) model.VendorConfig {
	vendorConfig := model.VendorConfig{
		VendorCode: vb.VendorCode,
	}
	if vbItem.Price != nil {
		vendorConfig.UnitPrice = *vbItem.Price
	}
	if vbItem.DiscountPercent != nil {
		vendorConfig.DiscountPercent = *vbItem.DiscountPercent
	}
	if vbItem.Vat != nil {
		vendorConfig.VAT = int64(*vbItem.Vat)
	}

	return vendorConfig
}

// Create VendorBill & VendorBillItem By (purchase order code)
func CreateVendorBillByPOCode(purchaseOrderCode string) *common.APIResponse {

	// case puchaseOrderCode marshal to bson matched "omitempty" field
	if purchaseOrderCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: PO_CODE_EMPTY,
			Message:   "Wrong po_code or internal error.",
		}
	}

	// function convert number percentage of 100% to floating point number
	percentage := func(n float64) float64 {
		return n / 100
	}

	// function calculate VAT_price by price_after_discount and VAT_percentage.
	// [vat_price =  price_after_discount * (vat / 100) ]
	calculateVATPrice := func(priceAfterDiscount float64, vat float64) float64 {
		if vat < 0 {
			vat = 0
		}
		return (priceAfterDiscount * percentage(vat))
	}

	//function get PO
	getPurchaseOrderByPOCode := func(POCode string) *common.APIResponse {
		return model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
			POCode: POCode,
		})
	}

	//function get POItem
	getPurchaseOrderItems := func(poCode string) *common.APIResponse {
		return model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
			POCode: poCode,
		}, 0, 1000, &primitive.M{})
	}

	//function get seller-info
	getSellerInfo := func(sellerCode string) *common.APIResponse {
		if sellerCode == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				ErrorCode: BUYER_CODE_EMPTY,
				Message:   "Wrong sellerCode or internal error.",
			}
		}
		option := client.APIOption{
			Params: map[string]string{
				"sellerClass": model.CLASS_INTERNAL,
				"sellerCodes": sellerCode,
			},
		}
		sellerResp := seller.GetSellers(option)

		if sellerResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Error,
				ErrorCode: sellerResp.ErrorCode,
				Message:   "seller not found",
			}
		}
		return &common.APIResponse{
			Status: common.APIStatus.Ok,
			Data:   sellerResp.Data,
		}
	}

	purchaseOrderGetResp := getPurchaseOrderByPOCode(purchaseOrderCode)

	//case wrong PO code or query error
	if purchaseOrderGetResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    purchaseOrderGetResp.Status,
			ErrorCode: purchaseOrderGetResp.ErrorCode,
			Message:   "Get purchase_order wrong po_code or internal error.",
		}
	}
	purchaseOrder := purchaseOrderGetResp.Data.([]*model.PurchaseOrder)[0]
	if response, ok := utils.ValidSellerPurchaser(purchaseOrder.SellerCode, purchaseOrder.PurchaserCode); !ok {
		return response
	}

	//case PO not found
	purchaseOrderItemGetResp := getPurchaseOrderItems(purchaseOrderCode)
	if purchaseOrderItemGetResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    purchaseOrderItemGetResp.Status,
			ErrorCode: purchaseOrderItemGetResp.ErrorCode,
			Message:   "Purchase Order Item query error",
		}
	}

	purchaseOrderItems := purchaseOrderItemGetResp.Data.([]*model.PurchaseOrderItem)

	// case no PO item found with current PO
	if len(purchaseOrderItems) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: PO_ITEM_EMPTY,
			Message:   "Purchase Order Item is empty",
		}
	}

	// vendorBillItem accumulators
	var vendorBillItems = make([]*model.VendorBillItem, 0, len(purchaseOrderItems))

	// vendorBill
	var (
		vendorBill                model.VendorBill
		vendorBill_VendorBillID   int64
		vendorBill_VendorBillCode string
		vendorBill_BuyerLegalName string
		vendorBill_DueTime        *time.Time
	)

	currentTime := utils.GetVietnamTimeNow()

	// gen vendor_bill ID & code
	vendorBill_VendorBillID, vendorBill_VendorBillCode = model.GetVendorBillID()

	for _, purchaseOrderItem := range purchaseOrderItems {
		var (
			vendorBillItem                    model.VendorBillItem
			vendorBillItem_VendorBillItemCode string
			// vendorBillItem_ActualQuantity     int64
			vendorBillItem_Price              = new(float64)
			vendorBillItem_Quantity           = new(int64)
			vendorBillItem_Vat                = new(float64)
			vendorBillItem_VatPrice           = new(float64)
			vendorBillItem_DiscountPrice      = new(float64)
			vendorBillItem_TotalDiscountPrice = new(float64)
			vendorBillItem_PriceAfterDiscount = new(float64)
			vendorBillItem_DiscountPercent    = new(float64)
			vendorBillItem_TotalPrice         = new(float64)
			poItem_DisCountPercent            = new(float64)

			vendorBillItem_IsInboundCompleted = false
		)

		// quantity
		purchaseOrderItem_ActualQuantity_int64 := purchaseOrderItem.ExpectQuantity
		if purchaseOrder.Status == enum.PurchaseOrderStatus.COMPLETED {
			purchaseOrderItem_ActualQuantity_int64 = purchaseOrderItem.ActualQuantity
			if !utils.IsNil(purchaseOrderItem.BillQuantity) {
				purchaseOrderItem_ActualQuantity_int64 -= *purchaseOrderItem.BillQuantity
			}
		}
		if purchaseOrderItem_ActualQuantity_int64 <= 0 {
			continue
		}

		// gen vendor_bill_item code
		_, vendorBillItem_VendorBillItemCode = model.GetVendorBillItemID()
		// type conversion helper, order matter
		if !utils.IsNil(purchaseOrderItem.DiscountPercent) {
			poItem_DisCountPercent = purchaseOrderItem.DiscountPercent
		}
		if utils.IsNil(purchaseOrderItem.DiscountPrice) {
			discountPrice := (percentage(*poItem_DisCountPercent)) * (purchaseOrderItem.UnitPrice)
			purchaseOrderItem.DiscountPrice = &discountPrice
		}
		if utils.IsNil(purchaseOrderItem.VAT) {
			purchaseOrderItem.VAT = new(float64)
		}

		vendorBillItem_TotalDiscountPrice_float64 := float64(purchaseOrderItem_ActualQuantity_int64) * (*purchaseOrderItem.DiscountPrice)
		// [(unit_price - discount_price)]
		vendorBillItem_PriceAfterDiscount_float64 := (purchaseOrderItem.UnitPrice - *purchaseOrderItem.DiscountPrice)
		vendorBillItem_VatPrice_float64 := (calculateVATPrice(vendorBillItem_PriceAfterDiscount_float64*float64(purchaseOrderItem_ActualQuantity_int64), *purchaseOrderItem.VAT))
		vendorBillItem_TotalPrice_float64 := (vendorBillItem_PriceAfterDiscount_float64 * float64(purchaseOrderItem_ActualQuantity_int64)) + vendorBillItem_VatPrice_float64
		// vender_bill_item  comtputation
		vendorBillItem_Price = &purchaseOrderItem.UnitPrice
		vendorBillItem_DiscountPercent = poItem_DisCountPercent
		// vendorBillItem_ActualQuantity = purchaseOrderItem.ActualQuantity
		vendorBillItem_Quantity = &purchaseOrderItem_ActualQuantity_int64
		vendorBillItem_Vat = purchaseOrderItem.VAT
		vendorBillItem_DiscountPrice = purchaseOrderItem.DiscountPrice
		//[discount_price * quantity]
		vendorBillItem_TotalDiscountPrice = &vendorBillItem_TotalDiscountPrice_float64
		//[price - discount_price]
		vendorBillItem_PriceAfterDiscount = &vendorBillItem_PriceAfterDiscount_float64
		//[(price_after_discount * quantity) * (vat / 100) ]
		vendorBillItem_VatPrice = &vendorBillItem_VatPrice_float64
		//[(price_after_discount * quantity) * vat_price]
		vendorBillItem_TotalPrice = &vendorBillItem_TotalPrice_float64

		//---------------- init vendor bill item ----------------------
		vendorBillItem = model.VendorBillItem{
			Price:           vendorBillItem_Price,
			DiscountPercent: vendorBillItem_DiscountPercent,
			// ActualQuantity:     vendorBillItem_ActualQuantity,
			Quantity:           vendorBillItem_Quantity,
			Vat:                vendorBillItem_Vat,
			DiscountPrice:      vendorBillItem_DiscountPrice,
			TotalDiscountPrice: vendorBillItem_TotalDiscountPrice,
			PriceAfterDiscount: vendorBillItem_PriceAfterDiscount,
			VatPrice:           vendorBillItem_VatPrice,
			TotalPrice:         vendorBillItem_TotalPrice,
			VendorBillID:       vendorBill_VendorBillID,   //vendor_bill
			VendorBillCode:     vendorBill_VendorBillCode, // vendor_bill
			VendorBillItemCode: vendorBillItem_VendorBillItemCode,
			ProductID:          purchaseOrderItem.ProductID,
			ProductCode:        purchaseOrderItem.ProductCode,
			SKU:                purchaseOrderItem.SKU,
			CreatedTime:        &currentTime,
			ProductName:        purchaseOrderItem.ProductName,
			Unit:               purchaseOrderItem.Unit,
			LastUpdatedTime:    &currentTime, // last update cua vendor_bill_item hay cua purchase_order_item
			POCode:             purchaseOrderItem.POCode,
			IsInboundCompleted: &vendorBillItem_IsInboundCompleted,
		}
		vendorBillItems = append(vendorBillItems, &vendorBillItem)
	}

	// case no vendor_bill_item found with current PO
	if len(vendorBillItems) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Bill has enough quantity",
			ErrorCode: string(enum.ErrorCodeInvalid.EnoughQuantity),
		}
	}

	//get vendor-info , if successed (len == 1)
	vendorInfoGetResp := GetVendorInfo(purchaseOrder.VendorCode)
	if vendorInfoGetResp.Status != common.APIStatus.Ok {
		return vendorInfoGetResp
	}
	vendorInfo := vendorInfoGetResp.Data.([]*model.Seller)[0]

	// get seller-info
	sellerInfoGetResp := getSellerInfo(purchaseOrder.SellerCode)
	if sellerInfoGetResp.Status != common.APIStatus.Ok {
		return sellerInfoGetResp
	}
	seller := sellerInfoGetResp.Data.([]*model.Seller)[0]

	vendorBill_BuyerLegalName = seller.Name
	currentTime = time.Now()

	//vendor_bill calc dueTime
	var (
		purchaseOrder_PaymentTerm_float64 = new(float64)
		purchaseOrder_IssuedTime_Time     = &currentTime
	)
	if purchaseOrder.PaymentTerm != nil {
		purchaseOrder_PaymentTerm_float64 = purchaseOrder.PaymentTerm
	}
	if purchaseOrder.IssuedTime != nil {
		purchaseOrder_IssuedTime_Time = purchaseOrder.IssuedTime
	}

	// calculate due_time from issued_time with payment_term
	vendorBill_DueTime = CalcDueTimeFromIssueTimeWithPaymentTerms(int(*purchaseOrder_PaymentTerm_float64), *purchaseOrder_IssuedTime_Time)

	//---------------- init vendor_bill ---------------------
	vendorBill = model.VendorBill{
		SellerCode:      purchaseOrder.SellerCode,
		PurchaserCode:   purchaseOrder.PurchaserCode,
		CreatedTime:     &currentTime,
		VendorLegalName: purchaseOrder.VendorName,
		HashTag:         purchaseOrder.HashTag,
		POCode:          purchaseOrder.POCode,
		VendorCode:      purchaseOrder.VendorCode,
		IssuedTime:      purchaseOrder.IssuedTime,
		Status:          enum.VendorBillStatus.DRAFT,
		LastUpdatedTime: &currentTime,
		VendorBillCode:  vendorBill_VendorBillCode,
		VendorBillID:    vendorBill_VendorBillID,
		BuyerLegalName:  vendorBill_BuyerLegalName,
		PaymentTerm:     purchaseOrder.PaymentTerm,
		FiscalPosition:  purchaseOrder.FiscalPosition,
		Incoterm:        purchaseOrder.Incoterm,
		BuyerTaxCode:    purchaseOrder.SellerCode,
		VendorTaxCode:   vendorInfo.Tax,
		DueTime:         vendorBill_DueTime,
	}

	// calculate vendor_bill prices
	calcVendobillFromVendorBillItems(&vendorBill, vendorBillItems)

	vendorBillItemsResp := model.VendorBillItemDB.CreateMany(vendorBillItems)
	if vendorBillItemsResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    vendorBillItemsResp.Status,
			Message:   "Insert to vendor_bill_item error",
			ErrorCode: vendorBillItemsResp.ErrorCode,
		}
	}

	vendorBill.ProductCodes = model.GenProductCodesFromItems(vendorBillItems)
	vendorBillResp := model.VendorBillDB.Create(vendorBill)

	// case insert vendor_bill error
	if vendorBillResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    vendorBillResp.Status,
			Message:   "Insert to vendor_bill error",
			ErrorCode: vendorBillResp.ErrorCode,
		}
	}

	return vendorBillResp

}

// function calculate all prices, quantity for Vendorbill from VendorbillItems
// mutate vendorBill properties
func calcVendobillFromVendorBillItems(vendorBill *model.VendorBill, vendorBillItems []*model.VendorBillItem) {

	// vendorBill default value, prevent nil dereference
	if vendorBill.TotalVatPrice == nil {
		vendorBill.TotalVatPrice = new(float64)
	}
	if vendorBill.RemainingMoney == nil {
		vendorBill.RemainingMoney = new(float64)
	}
	if vendorBill.TotalDiscountPrice == nil {
		vendorBill.TotalDiscountPrice = new(float64)
	}
	if vendorBill.TotalPriceWithoutVat == nil {
		vendorBill.TotalPriceWithoutVat = new(float64)
	}
	if vendorBill.TotalPrice == nil {
		vendorBill.TotalPrice = new(float64)
	}

	// vendorBillItems default value, prevent nil dereference
	for _, vendorBillItem := range vendorBillItems {

		// vendorBillItem default value, prevent nil dereference
		var (
			vendorBillItem_VatPrice_float64           float64
			vendorBillItem_TotalDiscountPrice_float64 float64
			vendorBillItem_PriceAfterDiscount_float64 float64
			vendorBillItem_TotalPrice_float64         float64
			vendorBillItem_Quantity_float64           float64
		)

		if vendorBillItem.Quantity != nil {
			vendorBillItem_Quantity_float64 = float64(*vendorBillItem.Quantity)
		}
		if vendorBillItem.VatPrice != nil {
			vendorBillItem_VatPrice_float64 = *vendorBillItem.VatPrice
		}
		if vendorBillItem.TotalDiscountPrice != nil {
			vendorBillItem_TotalDiscountPrice_float64 = *vendorBillItem.TotalDiscountPrice
		}
		if vendorBillItem.PriceAfterDiscount != nil {
			vendorBillItem_PriceAfterDiscount_float64 = *vendorBillItem.PriceAfterDiscount
		}
		if vendorBillItem.TotalPrice != nil {
			vendorBillItem_TotalPrice_float64 = *vendorBillItem.TotalPrice
		}
		// vendor_bill computation
		(*vendorBill.TotalVatPrice) += vendorBillItem_VatPrice_float64
		(*vendorBill.TotalDiscountPrice) += vendorBillItem_TotalDiscountPrice_float64
		(*vendorBill.TotalPriceWithoutVat) += vendorBillItem_PriceAfterDiscount_float64 * vendorBillItem_Quantity_float64
		(*vendorBill.TotalPrice) += (vendorBillItem_TotalPrice_float64)
	}

}

// Calculate due_time from issued_time with payment_term
// tính hạn thanh toán từ ngày phát hành với hạn thanh toán
func CalcDueTimeFromIssueTimeWithPaymentTerms(paymentTermNumber int, issueTime time.Time) *time.Time {
	var dueTime *time.Time

	if paymentTermNumber < 365 {
		newDueTime := issueTime.AddDate(0, 0, paymentTermNumber)
		dueTime = &newDueTime
	} else if paymentTermNumber >= 1000 {
		dayInput := paymentTermNumber / 1000
		now := utils.GetVietnamTimeNow()
		currentYear, currentMonth, currentDay := now.Date()
		if dayInput <= currentDay {
			// example: today 10/10/2022; dayInput = 9; -> newDueTime = 9/11/2022

			// get first date in next month
			nextMonthDate := time.Date(currentYear, currentMonth+1, currentDay, 0, 0, 0, 0, utils.TimeZoneVN)
			// next month same from dayInput
			newDueTime := nextMonthDate.AddDate(0, 0, dayInput)
			dueTime = &newDueTime
		} else {
			// example: today 10/10/2022; dayInput = 12; -> newDueTime = 12/10/2022

			// next day from dayInput of current month
			nextDayIn := dayInput - currentDay
			newDueTime := time.Date(currentYear, currentMonth, currentDay+nextDayIn, 0, 0, 0, 0, utils.TimeZoneVN)
			dueTime = &newDueTime
		}

	}

	return dueTime
}

// GetVendorInfo get vendor info by vendorCode
// CloneVendorBill clones an existing vendor bill identified by the given vendorBillCode.
// It retrieves the vendor bill and its associated items from the database, resets specific fields,
// generates new IDs and codes, and creates a new vendor bill and its items in the database.
//
// Parameters:
//   - vendorBillCode: The code of the vendor bill to be cloned.
//
// Returns:
//   - *common.APIResponse: The API response containing the status and data of the operation.
func CloneVendorBill(vendorBillCode string) *common.APIResponse {
	vendorBillResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: vendorBillCode})
	if vendorBillResp.Status != common.APIStatus.Ok {
		return vendorBillResp
	}
	vendorBill := vendorBillResp.Data.([]*model.VendorBill)[0]

	// Clone vendor bill
	vendorBill.ID = nil
	vendorBill.CreatedTime = nil
	vendorBill.LastUpdatedTime = nil
	vendorBill.VendorBillID = 0
	vendorBill.VendorBillCode = ""
	vendorBill.VendorBillID, vendorBill.VendorBillCode = model.GetVendorBillID()

	vendorBill.BizziInvoiceID = ""
	vendorBill.GrowthInvoiceID = ""
	vendorBill.BuymedAIinvoiceID = 0

	vendorBill.IsWrongLotDate = nil
	vendorBill.IsWrongQuantity = nil
	vendorBill.ApprovedByName = ""
	vendorBill.ApprovedTime = nil
	vendorBill.RemainingMoney = nil
	vendorBill.IsCompletedPO = nil
	vendorBill.Status = enum.VendorBillStatus.DRAFT

	vendorBill.ReasonTag = ""
	vendorBill.TicketInstanceCodes = nil

	// Clone vendor bill items
	vendorBillItemREsp := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillCode: vendorBillCode}, 0, 1000, nil)
	if vendorBillItemREsp.Status != common.APIStatus.Ok {
		return vendorBillItemREsp
	}
	listVBItems := vendorBillItemREsp.Data.([]*model.VendorBillItem)

	vendorBill.ProductCodes = model.GenProductCodesFromItems(listVBItems)

	createResp := model.VendorBillDB.Create(vendorBill)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}

	// Clone vendor bill items
	var newListVBItem []*model.VendorBillItem
	for _, vendorBillItem := range listVBItems {
		vendorBillItem.ID = nil
		vendorBillItem.CreatedTime = nil
		vendorBillItem.LastUpdatedTime = nil
		vendorBillItem.VendorBillItemCode = ""

		// Clone vendor bill item
		_, vendorBillItem.VendorBillItemCode = model.GetVendorBillItemID()
		vendorBillItem.VendorBillCode = vendorBill.VendorBillCode
		vendorBillItem.VendorBillID = vendorBill.VendorBillID

		vendorBillItem.ActualQuantity = 0
		vendorBillItem.POTrackings = nil

		vendorBillItem.IsInboundCompleted = nil //

		vendorBillItem.BizziInvoiceID = ""
		vendorBillItem.BizziInvoiceItemID = ""

		newListVBItem = append(newListVBItem, vendorBillItem)
	}

	// Create vendor bill items
	createItemsResp := model.VendorBillItemDB.CreateMany(newListVBItem)
	if createItemsResp.Status != common.APIStatus.Ok {
		return createItemsResp
	}
	return createResp
}

// GenHashTagVBFromInfo generate hash tag for vendor bill
func GenHashTagVBFromInfo(vendorBill *model.VendorBill, vendor *model.Seller) string {
	sellerIDStr := ""
	if vendor != nil {
		sellerIDStr = strconv.FormatInt(vendor.SellerID, 10)
	}
	return strings.Replace(utils.NormalizeString(sellerIDStr+" "+vendorBill.VendorLegalName+" "+vendorBill.InvoiceNumber), " ", "-", -1)
}

// AutoApplyPaymentVoucherToVB auto apply payment voucher to vendor bill
func AutoApplyPaymentVoucherToVB(vendorBillCode string) *common.APIResponse {
	if vendorBillCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid VendorBillCode",
			ErrorCode: "INVALID_VENDOR_BILL_CODE",
		}
	}

	// get full VB data
	vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: vendorBillCode})
	if vbResp.Status != common.APIStatus.Ok {
		return vbResp
	}
	vbData := vbResp.Data.([]*model.VendorBill)[0]

	//Get payment info
	paymentResp := GetNewPayment(
		model.PaymentPlatform{
			PartnerCode:     vbData.VendorCode,
			Type:            enum.PaymentPlatformType.PAYMENT,
			PrepaidPOCodeIn: []string{vbData.POCode},
			// ReasonCode:  enum.PaymentPurpose.ADVANCE_MONEY,
		},
		0, 1000, model.QueryOption{}, "-_id",
	)
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}
	payments := paymentResp.Data.([]*model.PaymentPlatform)

	// Parse reason code
	reasonCodes := make([]string, 0)
	for _, payment := range payments {
		reasonCodes = append(reasonCodes, payment.ReasonCode)
	}
	if len(reasonCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "No payment found",
		}
	}

	// Parse reason code
	reasonParses, err := ParsePaymentReasonData(reasonCodes)
	if err != nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			ErrorCode: "PARSE_REASON_ERROR",
			Message:   err.Error(),
		}
	}

	mapReasonParse := make(map[string]*model.ReasonPaymentParse)
	for i := range reasonParses {
		// reason := mapReasonParse[reasonParses[i].Data]
		mapReasonParse[reasonParses[i].Code] = reasonParses[i]
	}

	// Calculate total price of vendor bill when apply payment
	totalPriceNew := *vbData.TotalPrice
	for _, payment := range payments {
		if payment.Balance == 0 {
			continue
		}
		if totalPriceNew <= 0 {
			break
		}
		reason, ok := mapReasonParse[payment.ReasonCode]
		if !ok {
			continue
		}

		if reason.Data != enum.PaymentPlatformReasonCore.ADVANCE_MONEY {
			continue
		}
		// Check payment is included vendor bill PO
		foundPO := false
		for _, poCode := range payment.PrepaidPOCodes {
			if poCode == vbData.POCode {
				foundPO = true
				break
			}
		}
		if !foundPO {
			continue
		}

		// Calculate pay amount
		payAmount := float64(0)
		payAmount = payment.Balance
		if totalPriceNew < payment.Balance {
			payAmount = totalPriceNew
		}

		// call add payment item
		paymentItemFilter := &model.PaymentItemPlatform{
			PaymentCode:     payment.PaymentCode,
			VendorBillCode:  vbData.VendorBillCode,
			PartnerCode:     vbData.VendorCode,
			Amount:          &payAmount,
			TransactionType: "VENDOR_BILL",
		}

		// Add payment item
		AddPaymentItemVB(paymentItemFilter)

		totalPriceNew -= payAmount
	}
	vbData.TotalPrice = &totalPriceNew

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Apply payment voucher to vendor bill success",
	}
}

// WarmupBillQtyOfPO updates the bill quantities of purchase order items based on the provided purchase order codes.
// It performs the following steps:
// 1. Queries purchase orders based on the provided purchase order codes.
// 2. For each purchase order, it queries the associated vendor bills and their items.
// 3. Updates the bill quantities for each purchase order item based on the vendor bill items.
// 4. Updates the purchase order items in the database with the new bill quantities.
//
// Parameters:
// - poCodes: A slice of purchase order codes to process.
func WarmupBillQtyOfPO(poCodes []string) {

	poResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
		ComplexQuery: []*bson.M{
			{"po_code": bson.M{"$in": poCodes}},
		},
	}, 0, 1000, &primitive.M{"_id": -1})
	pos := poResp.Data.([]*model.PurchaseOrder)

	// loop PO
	for _, po := range pos {

		if po.POCode == "" {
			continue
		}

		// bill
		vbItems := []*model.VendorBillItem{}
		billResp := model.VendorBillDB.Query(model.VendorBill{POCode: po.POCode}, 0, 1000, &primitive.M{"_id": -1})
		for _, vb := range billResp.Data.([]*model.VendorBill) {
			if vb.Status == enum.VendorBillStatus.DRAFT || vb.Status == enum.VendorBillStatus.CANCEL || vb.VendorBillCode == "" {
				continue
			}

			// item
			vbItemsResp := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillCode: vb.VendorBillCode}, 0, 1000, &primitive.M{"_id": -1})
			if vbItemsResp.Status != common.APIStatus.Ok {
				continue
			}
			vbItems = append(vbItems, vbItemsResp.Data.([]*model.VendorBillItem)...)
		}

		// cập nhật bill quantity cho PO item
		poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, &primitive.M{"_id": -1})
		if poItemResp.Status != common.APIStatus.Ok {
			continue
		}
		poItems := poItemResp.Data.([]*model.PurchaseOrderItem)

		// put map
		mapPOItem := make(map[int64][]model.PurchaseOrderItem)
		for i := range poItems {
			poItem := poItems[i]
			poItem.BillQuantity = nil
			mapPOItem[poItem.ProductID] = append(mapPOItem[poItem.ProductID], *poItem)
		}

		// loop productID
		for productID := range mapPOItem {
			poMapItems := mapPOItem[productID]

			for i := range poMapItems {
				poItem := poMapItems[i]

				if poItem.POCode == "" {
					continue
				}

				var ZERO int64 = 0
				poItem.BillQuantity = &ZERO

				// nếu đủ rồi thì không cần fill
				if poItem.ExpectQuantity <= *poItem.BillQuantity &&
					i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
					continue
				}

				// loop VendorBillItem
				for j, callbackItem := range vbItems {
					// không quan tâm line
					if poItem.ProductID != callbackItem.ProductID {
						continue
					}
					if callbackItem.Quantity == nil || *callbackItem.Quantity <= 0 {
						continue
					}

					// tính bill quantity mới
					newBillQuantity := *poItem.BillQuantity + *callbackItem.Quantity
					if newBillQuantity > poItem.ExpectQuantity &&
						i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
						newBillQuantity = poItem.ExpectQuantity
					}

					// cập nhật lại InboundCallbackItems
					sl := *callbackItem.Quantity - (newBillQuantity - *poItem.BillQuantity)
					callbackItem.Quantity = &sl
					if *callbackItem.Quantity < 0 {
						continue
					}
					vbItems[j] = callbackItem
					// cập nhật lại mapPOItem
					poItem.BillQuantity = &newBillQuantity
					mapPOItem[productID][i] = poItem
				}

				// do action: update BillQuantity PurchaseOrderItem
				model.PurchaseOrderItemDB.UpdateOne(
					model.PurchaseOrderItem{ID: poItem.ID},
					model.PurchaseOrderItem{
						BillQuantity: poItem.BillQuantity,
					},
				)
			}
		}

	}

}
