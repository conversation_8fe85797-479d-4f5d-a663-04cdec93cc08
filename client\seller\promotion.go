package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var promotionlient *client.Client

const (
	putRecalcPriceAfterRebate        = "PUT::/price-after-rebate/recalc-onchange"
	getPriceAfterReabate             = "GET::/price-after-rebate"
	getVendorPromotion               = "GET::/vendor-promotion"
	gettriggerReCalcPriceAfterRebate = "GET::/trigger-re-calc-price-after-rabate"
	postCalcPriceAfterRebate         = "POST::/calc-price-after-rebate"
	getPriceAfterRebateForDeal       = "GET::/price-after-rebate-for-deal"
	getPriceAfterRebateLastest       = "GET::/price-after-rebate/lastest"
	reCallPOPromotion                = "PUT::/vendor-promotion/calculate"
)

func InitSellerPromotion(database *mongo.Database) {
	const sellerPromotionV1 = "/seller/promotion/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	promotionlient = client.NewClient(sellerPromotionV1, headers, 0)
	promotionlient.WithDatabase(database)
	promotionlient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     putRecalcPriceAfterRebate,
				Name:     "seller__PUT__promotion_recalc_price_after_rebate",
				Database: database,
			},
			{
				Path:     getPriceAfterReabate,
				Name:     "seller__GET__promotion_price_after_rebate",
				Database: database,
			},
			{
				Path:     getVendorPromotion,
				Name:     "seller__GET__promotion_get_vendor_promotion",
				Database: database,
			},
			{
				Path:     gettriggerReCalcPriceAfterRebate,
				Name:     "seller__GET__promotion_trigger_re_calc_price_after_rebate",
				Database: database,
			},
			{
				Path:     postCalcPriceAfterRebate,
				Name:     "seller__POST__promotion_post_calc_price_after_rebate",
				Database: database,
			},
			{
				Path:     getPriceAfterRebateForDeal,
				Name:     "seller__POST__promotion_get_price_after_rebate_for_deal",
				Database: database,
			},
			{
				Path:     getPriceAfterRebateLastest,
				Name:     "seller__POST__promotion_get_price_after_rebate_lastest",
				Database: database,
			},
			{
				Path:     reCallPOPromotion,
				Name:     "seller__PUT__recall_vendor_promotion",
				Database: database,
			},
		}...,
	)
}

// get seller type : [ 1: seller, 2: vendor, 3: purchaser ]
func RecalcPriceAfterRebate(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := promotionlient.WithRequest(putRecalcPriceAfterRebate, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func GetPriceAfterRebate(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp PriceAfterRebateResponse
	_, err := promotionlient.WithRequest(getPriceAfterReabate, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetVendorPromotion(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp VendorPromotionResponse
	_, err := promotionlient.WithRequest(getVendorPromotion, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func TriggerReCalcPriceAfterRebate(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp VendorPromotionResponse
	_, err := promotionlient.WithRequest(gettriggerReCalcPriceAfterRebate, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func PostCalcPriceAfterRebate(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp CalcPriceAfterRebateReponse
	_, err := promotionlient.WithRequest(postCalcPriceAfterRebate, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetPriceAfterRebateForDeal(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp PriceAfterRebateForDealResponse
	_, err := promotionlient.WithRequest(getPriceAfterRebateForDeal, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetPriceAfterRebateLastest(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp PriceAfterRebateResponse
	_, err := promotionlient.WithRequest(getPriceAfterRebateLastest, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func PutRecallVendorPromotion(opts ...client.APIOption) *common.APIResponse {
	requestOption := promotionlient.WithAPIOption(opts...)
	var resp BasicResponse
	_, err := promotionlient.WithRequest(reCallPOPromotion, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
