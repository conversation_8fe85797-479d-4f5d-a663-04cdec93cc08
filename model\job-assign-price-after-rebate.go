package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"go.mongodb.org/mongo-driver/mongo"
)

type WorkerAssignPriceAfterRebateVariable struct {
	VendorCode string `json:"vendor_code,omitempty" bson:"vendor_code,omitempty"`
	Version    string `json:"version,omitempty" bson:"version,omitempty"`
}

var AssignPriceAfterRebateJob = &job.Executor{
	ColName: "assign_price_after_rebate",
}
var ASSIGN_PRICE_AFTER_REBATE_TOPIC = "ASSIGN_PRICE_AFTER_REBATE_TOPIC"

func InitAssignPriceAfterRebate(database *mongo.Database, consumerFn job.ExecutionFn) {
	AssignPriceAfterRebateJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:   true,
		ChannelCount:       10,
		OldVersionTimeoutS: 60 * 10,
		CurVersionTimeoutS: 60 * 10,
		FailThreshold:      3,
		UniqueItem:         true,
	})

	AssignPriceAfterRebateJob.SetTopicConsumer(ASSIGN_PRICE_AFTER_REBATE_TOPIC, consumerFn)
}
