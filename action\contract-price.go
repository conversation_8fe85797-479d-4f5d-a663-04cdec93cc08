package action

import (
	"fmt"
	"math"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetContractPriceMain retrieves contract prices based on the provided query parameters.
//
// Parameters:
// - q: A pointer to a ContractPrice model containing the query parameters.
// - offset: The number of records to skip before starting to return results.
// - limit: The maximum number of records to return.
// - getTotal: A boolean indicating whether to include the total count of records matching the query.
// - sortField: A pointer to a bson.M map specifying the field by which to sort the results.
//
// Returns:
// - A pointer to an APIResponse containing the query results and status information.
func GetContractPriceMain(q *model.ContractPrice, offset, limit int64, getTotal bool, sortField *bson.M) *common.APIResponse {
	if q == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "invalid query",
		}
	}

	if sortField == nil {
		sortField = &bson.M{"_id": -1}
	}

	getResp := model.ContractPriceDB.Query(q, offset, limit, sortField)
	if getTotal {
		getResp.Total = model.ContractPriceDB.Count(q).Total
	}

	return getResp
}

// GetContractPriceItem retrieves contract price items based on the provided query parameters.
//
// Parameters:
// - q: A pointer to a ContractPriceItem model containing the query conditions.
// - offset: The number of items to skip before starting to collect the result set.
// - limit: The maximum number of items to return.
// - getTotal: A boolean indicating whether to include the total count of items matching the query.
// - sortField: A pointer to a bson.M map specifying the field by which to sort the results.
//
// Returns:
// - A pointer to an APIResponse containing the query results and status information.
func GetContractPriceItem(q *model.ContractPriceItem, offset, limit int64, getTotal bool, sortField *bson.M) *common.APIResponse {
	if q == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "invalid query",
		}
	}

	if sortField == nil {
		sortField = &bson.M{"_id": -1}
	}

	getResp := model.ContractPriceItemDB.Query(q, offset, limit, sortField)
	if getTotal {
		getResp.Total = model.ContractPriceItemDB.Count(q).Total
	}
	return getResp
}

// GetContractPriceMainCodeFromItem retrieves the main contract price codes from a given ContractPriceItem.
// It fetches a list of ContractPriceItems and extracts unique contract price codes.
//
// Parameters:
//   - q: A pointer to a ContractPriceItem.
//
// Returns:
//   - A slice of unique contract price codes as strings. If the input is nil or the API call fails, it returns nil.
func GetContractPriceMainCodeFromItem(q *model.ContractPriceItem) []string {
	if q == nil {
		return nil
	}
	getItems := GetContractPriceItem(q, 0, 200, false, nil)
	if getItems.Status != common.APIStatus.Ok {
		return nil
	}

	// Extract unique contract price codes
	items := getItems.Data.([]*model.ContractPriceItem)
	res := make([]string, 0, len(items))
	checkExistMain := make(map[string]struct{})

	for _, item := range items {
		if _, ok := checkExistMain[item.ContractPriceCode]; !ok {
			res = append(res, item.ContractPriceCode)
			checkExistMain[item.ContractPriceCode] = struct{}{}
		}
	}

	return res
}

// CreateContractPrice creates a new contract price entry in the database.
// It validates the input, generates necessary IDs and codes, and saves the main contract price
// and its associated items to the database.
//
// Parameters:
//   - input: A pointer to a model.ContractPrice struct containing the contract price details.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the status, message, and data of the operation.
//
// The function performs the following steps:
//  1. Validates the input contract price using validateContractPrice function.
//  2. Generates a new contract price ID and code, sets the status to DRAFT, and generates a hashtag.
//  3. Saves the main contract price entry to the database.
//  4. Iterates over the items in the contract price, sets their IDs and codes, and saves them to the database.
//  5. Returns an API response indicating the success or failure of the operation.
func CreateContractPrice(input *model.ContractPrice) *common.APIResponse {
	if err := validateContractPrice(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// ---------- main processing ---------
	items := input.Items
	mainID, _ := model.GetContractPriceID()
	input.ContractPriceID = mainID
	input.ContractPriceCode = fmt.Sprintf("CP%d", mainID)
	input.Status = enum.ContractPriceStatus.DRAFT
	input.HashTag = genHashtagContractPrice(input)

	// Save the main contract price
	creatMainResp := model.ContractPriceDB.Create(input)
	if creatMainResp.Status != common.APIStatus.Ok {
		return creatMainResp
	}

	// Set the contract price ID and code for each item
	for _, item := range items {
		item.ContractPriceID = input.ContractPriceID
		item.ContractPriceCode = input.ContractPriceCode
		item.WarehouseCode = input.WarehouseCode
		item.ContractPriceItemID, _ = model.GetContractPriceItemID()
	}

	// Save the contract price items
	createItemResp := model.ContractPriceItemDB.CreateMany(items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	input.Items = items

	// ---------- item processing ---------
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create contract price success",
		Data:    []*model.ContractPrice{input},
	}
}

// UpdateContractPrice updates the contract price based on the provided input.
// It validates the input, checks the contract status, processes the main contract
// and its items, and updates the contract price in the database. If the contract
// price is confirmed, it triggers a recalculation of the price after rebate.
//
// Parameters:
//   - input: A pointer to a model.ContractPrice struct containing the contract price details.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the status, message, and updated contract price data.
func UpdateContractPrice(input *model.ContractPrice) *common.APIResponse {
	if err := validateContractPrice(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// Check if the contract price code is provided
	if len(input.ContractPriceCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "contract price code is required",
		}
	}
	var (
		updated *model.ContractPrice
		items   = input.Items
	)

	// Check if the contract price exists
	getContractCheck := model.ContractPriceDB.QueryOne(&model.ContractPrice{ContractPriceCode: input.ContractPriceCode})
	if getContractCheck.Status != common.APIStatus.Ok {
		return getContractCheck
	}

	// Check if the contract price status is draft or confirmed
	contractCheck := getContractCheck.Data.([]*model.ContractPrice)[0]
	if contractCheck.Status != enum.ContractPriceStatus.DRAFT && contractCheck.Status != enum.ContractPriceStatus.CONFIRMED {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "only allow to update draft, confirmed",
		}
	}

	// ---------- main processing ---------
	if input.Name != contractCheck.Name {
		input.HashTag = genHashtagContractPrice(input)
	}

	// ---------- item processing ---------
	// Set the contract price ID and code for each item
	for _, item := range items {
		// unChange
		item.ContractPriceID = contractCheck.ContractPriceID
		item.ContractPriceCode = contractCheck.ContractPriceCode
		// change
		item.WarehouseCode = input.WarehouseCode
		item.ContractPriceItemID, _ = model.GetContractPriceItemID()
	}

	// validate duplicate contract price when update contract price with status confirmed
	if err, cp := validateDuplicateContractPrice(input, enum.ContractPriceAction.UPDATE_CONFIRMED); contractCheck.Status == enum.ContractPriceStatus.CONFIRMED && err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Existed,
			Message: err.Error(),
			Data:    []*model.ContractPrice{cp},
		}
	}

	// do not change these fields
	input.ContractPriceID = 0
	input.ContractPriceCode = ""
	input.Status = ""

	// do update
	// Update the main contract price
	updateMainResp := model.ContractPriceDB.UpdateOne(model.ContractPrice{ContractPriceCode: contractCheck.ContractPriceCode}, input)
	if updateMainResp.Status != common.APIStatus.Ok {
		return updateMainResp
	}
	updated = updateMainResp.Data.([]*model.ContractPrice)[0]

	// Delete old items and create new items
	deleteItemResp := model.ContractPriceItemDB.Delete(model.ContractPriceItem{ContractPriceCode: contractCheck.ContractPriceCode})
	if deleteItemResp.Status != common.APIStatus.Ok {
		return deleteItemResp
	}

	createItemResp := model.ContractPriceItemDB.CreateMany(items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}
	updated.Items = items

	if updated.Status == enum.ContractPriceStatus.CONFIRMED {

		if utils.HasNil(updated.StartTime, updated.EndTime, contractCheck.StartTime, contractCheck.EndTime) == false {
			// var (
			// 	oldVersionFrom = utils.ConvertTimeToStringYYYYMMDD(contractCheck.StartTime, utils.YYYYMMDD)
			// 	oldVersionTo   = utils.ConvertTimeToStringYYYYMMDD(contractCheck.EndTime, utils.YYYYMMDD)
			// 	newVersionFrom = utils.ConvertTimeToStringYYYYMMDD(updated.StartTime, utils.YYYYMMDD)
			// 	newVersionTo   = utils.ConvertTimeToStringYYYYMMDD(updated.EndTime, utils.YYYYMMDD)
			// )
			// inputRecalcPriceAfterRebate := &model.RecalcPriceAfterRebate{
			// 	ContractPriceCode: updated.ContractPriceCode,
			// 	OldVersionFrom:    oldVersionFrom,
			// 	OldVersionTo:      oldVersionTo,
			// 	NewVersionFrom:    newVersionFrom,
			// 	NewVersionTo:      newVersionTo,
			// 	Source:            fmt.Sprintf("CONTRACT PRICE:%q - UPDATE", updated.ContractPriceCode),
			// 	OldVendorCodes:    &contractCheck.VendorCodes,
			// 	NewVendorCodes:    &updated.VendorCodes,
			// 	OldWarehouseCodes: []string{contractCheck.WarehouseCode},
			// 	NewWarehouseCodes: []string{updated.WarehouseCode},
			// }

			// // call trigger tính lại giá sau rebate
			// opts := client.APIOption{
			// 	Keys: []string{updated.ContractPriceCode, oldVersionFrom, oldVersionTo, newVersionFrom, newVersionTo},
			// 	Body: inputRecalcPriceAfterRebate,
			// }
			// seller.RecalcPriceAfterRebate(opts)

			// call qua promotion tính lại giá rebate
			seller.TriggerReCalcPriceAfterRebate(client.APIOption{
				Params: map[string]string{
					"contractPriceCode": updated.ContractPriceCode,
				},
			})
		}

	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "update contract price success",
		Data:    []*model.ContractPrice{updated},
	}
}

// SwitchStatusContractPrice switches the status of a contract price based on the provided contract price code and next status.
// It performs several validations and updates the status if all conditions are met. If the status is successfully updated,
// it may trigger a recalculation of prices after rebate based on the new status.
//
// Parameters:
//   - contractPriceCode: The code of the contract price to be updated.
//   - nextStatus: The new status to be set for the contract price.
//
// Returns:
//   - *common.APIResponse: The response containing the status and message of the operation.
func SwitchStatusContractPrice(contractPriceCode string, nextStatus enum.ContractPriceStatusValue) *common.APIResponse {
	if len(contractPriceCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "contract price code is required",
		}
	}

	if len(nextStatus) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "next status is required",
		}
	}
	// Check if the contract price exists
	cpCheckResp := model.ContractPriceDB.QueryOne(&model.ContractPrice{ContractPriceCode: contractPriceCode})
	if cpCheckResp.Status != common.APIStatus.Ok {
		return cpCheckResp
	}

	cpCheck := cpCheckResp.Data.([]*model.ContractPrice)[0]
	if cpCheck.Status == nextStatus {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "next status is the same as current status",
		}
	}
	cpCheckItemsResp := model.ContractPriceItemDB.Query(&model.ContractPriceItem{ContractPriceCode: contractPriceCode}, 0, 1000, nil)
	if cpCheckItemsResp.Status != common.APIStatus.Ok {
		return cpCheckItemsResp
	}
	cpCheck.Items = cpCheckItemsResp.Data.([]*model.ContractPriceItem)
	// validate duplicate contract price when confirm
	if err, cp := validateDuplicateContractPrice(cpCheck, enum.ContractPriceAction.CHANGE_STATUS); nextStatus == enum.ContractPriceStatus.CONFIRMED && err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Existed,
			Message: err.Error(),
			Data:    []*model.ContractPrice{cp},
		}
	}

	// Check if the contract price status is draft or confirmed
	if cpCheck.Status == enum.ContractPriceStatus.CANCELED {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "contract price is canceled",
		}
	}

	// Check if the contract price status is draft or confirmed
	updateResp := model.ContractPriceDB.UpdateOne(model.ContractPrice{ContractPriceCode: contractPriceCode}, &model.ContractPrice{Status: nextStatus})
	if updateResp.Status == common.APIStatus.Ok {
		updatedCP := updateResp.Data.([]*model.ContractPrice)[0]
		if utils.HasNil(updatedCP.StartTime, updatedCP.EndTime, cpCheck.StartTime, cpCheck.EndTime) == false {
			switch updatedCP.Status {
			case enum.ContractPriceStatus.CANCELED, enum.ContractPriceStatus.CONFIRMED:
				// var (
				// 	oldVersionFrom = utils.ConvertTimeToStringYYYYMMDD(cpCheck.StartTime, utils.YYYYMMDD)
				// 	oldVersionTo   = utils.ConvertTimeToStringYYYYMMDD(cpCheck.EndTime, utils.YYYYMMDD)
				// 	newVersionFrom = utils.ConvertTimeToStringYYYYMMDD(updatedCP.StartTime, utils.YYYYMMDD)
				// 	newVersionTo   = utils.ConvertTimeToStringYYYYMMDD(updatedCP.EndTime, utils.YYYYMMDD)
				// )
				// inputRecalcPriceAfterRebate := &model.RecalcPriceAfterRebate{
				// 	ContractPriceCode: updatedCP.ContractPriceCode,
				// 	OldVersionFrom:    oldVersionFrom,
				// 	OldVersionTo:      oldVersionTo,
				// 	NewVersionFrom:    newVersionFrom,
				// 	NewVersionTo:      newVersionTo,
				// 	Source:            fmt.Sprintf("CONTRACT PRICE:%q - SWITCH_STATUS:%q", updatedCP.ContractPriceCode, updatedCP.Status),
				// 	OldVendorCodes:    &cpCheck.VendorCodes,
				// 	NewVendorCodes:    &updatedCP.VendorCodes,
				// 	OldWarehouseCodes: []string{cpCheck.WarehouseCode},
				// 	NewWarehouseCodes: []string{updatedCP.WarehouseCode},
				// }
				// opts := client.APIOption{
				// 	Keys: []string{updatedCP.ContractPriceCode, oldVersionFrom, oldVersionTo, newVersionFrom, newVersionTo},
				// 	Body: inputRecalcPriceAfterRebate,
				// }
				// seller.RecalcPriceAfterRebate(opts)

				// Nếu:
				// 1. Status mới là CONFIRMED
				// 2. Status mới là CANCELED && Status cũ là CONFIRMED
				if nextStatus == enum.ContractPriceStatus.CONFIRMED || (nextStatus == enum.ContractPriceStatus.CANCELED && cpCheck.Status == enum.ContractPriceStatus.CONFIRMED) {
					seller.TriggerReCalcPriceAfterRebate(client.APIOption{
						Params: map[string]string{
							"contractPriceCode": updatedCP.ContractPriceCode,
						},
					})
				}
			}

		}

	}

	return updateResp
}

// gen hashtag
// genHashtagContractPrice generates a hashtag string for a given ContractPrice.
// It concatenates the normalized name and contract price code, replacing spaces with hyphens.
//
// Parameters:
// - input: A pointer to a ContractPrice model.
//
// Returns:
// - A string representing the hashtag for the ContractPrice.
func genHashtagContractPrice(input *model.ContractPrice) string {
	name := ""
	if input.Name != nil {
		name = *input.Name
	}
	return strings.Replace(utils.NormalizeString(name+" "+fmt.Sprint(input.ContractPriceCode)), " ", "-", -1)
}

// validateContractPrice validates the given ContractPrice input.
// It checks for the following conditions:
// - input is not nil
// - input.Items is not empty
// - input.VendorCodes is not empty
// - each vendor code is valid and not a child vendor
// - input.StartTime and input.EndTime are not nil
// - input.WarehouseCode is not empty
// - input.StartTime is before input.EndTime
// - each item in input.Items has valid product information, unit price, and VAT
//
// Returns an error if any of the validation checks fail.
func validateContractPrice(input *model.ContractPrice) error {

	if input == nil {
		return fmt.Errorf("invalid input")
	}
	if len(input.Items) == 0 {
		return fmt.Errorf("items are required")
	}
	if len(input.VendorCodes) == 0 {
		return fmt.Errorf("vendor code is required")
	}
	// if vendor is child, reuturn error
	if len(input.VendorCodes) > 0 {
		for _, vendorCode := range input.VendorCodes {
			opts := client.APIOption{
				Keys: []string{vendorCode, "CHILD_VENDOR"},
				Q: model.Seller{
					SellerClass: model.CLASS_VENDOR,
				},
				Params: map[string]string{
					"sellerCodes": vendorCode,
				},
			}
			getVendorResp := seller.GetSellers(opts)
			if getVendorResp.Status != common.APIStatus.Ok {
				return fmt.Errorf("vendor code is invalid")
			}
			vendorChild := getVendorResp.Data.([]*model.Seller)[0]

			if len(vendorChild.LineManager) > 0 {
				return fmt.Errorf("vendor code is child vendor")
			}
		}

	}
	// check time
	if input.StartTime == nil || input.EndTime == nil {
		return fmt.Errorf("start time and end time are required")
	}
	// check warehouse code
	if len(input.WarehouseCode) == 0 {
		return fmt.Errorf("warehouse code is required")
	}

	*input.StartTime = input.StartTime.In(utils.TimeZoneVN)
	*input.EndTime = input.EndTime.In(utils.TimeZoneVN)

	input.StartTime = utils.Pointer.WithTime(utils.GetFirstTimeOfDate(*input.StartTime))
	input.EndTime = utils.Pointer.WithTime(utils.GetLastTimeOfDate(*input.EndTime))

	// check time
	if input.StartTime.After(*input.EndTime) {
		return fmt.Errorf("start time must be before end time")
	}

	// check item
	for i, item := range input.Items {
		if item == nil {
			return fmt.Errorf("invalid item")
		}

		if utils.HasZero(item.ProductCode, item.ProductID, item.ProductName) {
			return fmt.Errorf("item at %d product info is required", i+1)
		}

		if item.UnitPrice < 0 {
			return fmt.Errorf("item at %d unit price must be greater than or equal to 0", i+1)
		}
		if item.VAT < (-1) {
			return fmt.Errorf("item at %d vat must be greater than or equal to -1", i+1)
		}
		if item.VAT > 100 {
			return fmt.Errorf("item at %d vat must be less than or equal to 100", i+1)
		}
	}
	return nil
}

// CloneContractPrice clones an existing contract price and its associated items
// based on the provided contract price code. It performs the following steps:
//  1. Queries the main contract price using the provided contract price code.
//  2. If the query is successful, it resets certain fields of the contract price
//     to prepare for cloning (e.g., ID, CreatedTime, LastUpdatedTime, HashTag,
//     ContractPriceCode, ContractPriceID).
//  3. Queries the contract price items associated with the provided contract price code.
//  4. If the query is successful, it resets certain fields of each contract price item
//     to prepare for cloning (e.g., ID, CreatedTime, LastUpdatedTime).
//  5. Associates the cloned contract price items with the cloned contract price.
//  6. Creates a new contract price with the cloned data and returns the response.
//
// Parameters:
// - contractPriceCode: The code of the contract price to be cloned.
//
// Returns:
// - A pointer to an APIResponse containing the result of the clone operation.
func CloneContractPrice(contractPriceCode string) *common.APIResponse {
	//Query contract price main
	contractPriceResp := model.ContractPriceDB.QueryOne(model.ContractPrice{ContractPriceCode: contractPriceCode})
	if contractPriceResp.Status != common.APIStatus.Ok {
		return contractPriceResp
	}
	contractPrice := contractPriceResp.Data.([]*model.ContractPrice)[0]

	// set nil for new data
	contractPrice.ID = nil
	contractPrice.CreatedTime = nil
	contractPrice.LastUpdatedTime = nil
	contractPrice.HashTag = ""
	contractPrice.ContractPriceCode = ""
	contractPrice.ContractPriceID = 0

	//Query contract price items
	contractPriceItemResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{ContractPriceCode: contractPriceCode}, 0, 1000, nil)
	if contractPriceItemResp.Status != common.APIStatus.Ok {
		return contractPriceItemResp
	}

	listContractPriceItems := contractPriceItemResp.Data.([]*model.ContractPriceItem)
	// set nil for new data
	for _, contractPriceItem := range listContractPriceItems {
		contractPriceItem.ID = nil
		contractPriceItem.CreatedTime = nil
		contractPriceItem.LastUpdatedTime = nil

	}

	contractPrice.Items = listContractPriceItems
	createResp := CreateContractPrice(contractPrice)

	return createResp
}

// GetContractPriceItemV2 retrieves the contract price item for a given product ID, warehouse, and vendor code.
// It first validates the contract price item and then attempts to fetch approved contract prices.
// If no approved contract prices are found, it tries to get the vendor information and fetches the contract prices again.
// It then queries the contract price items based on the product ID and contract price IDs.
// If no contract price items are found, it returns a not found response.
// It filters the contract prices and calculates the difference in days from the start time.
// Finally, it retrieves the new contract price and returns the corresponding contract price item.
//
// Parameters:
// - productID: The ID of the product.
// - warehouse: The warehouse identifier.
// - vendorCode: The vendor code.
//
// Returns:
// - *common.APIResponse: The API response containing the contract price item or an error message.
func GetContractPriceItemV2(productID int64, warehouse string, vendorCode string) *common.APIResponse {

	// Validate the contract price item
	if response := validContractPriceItem(productID, warehouse, vendorCode); response != nil {
		return response
	}

	// get vendor
	opts := client.APIOption{
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
		},
		Limit: utils.Pointer.WithInt(1),
		Params: map[string]string{
			"sellerCode": vendorCode,
		},
	}

	getVendorResp := seller.GetSellers(opts)
	if getVendorResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No vendor found with vendor code",
		}
	}

	vendorInfo := getVendorResp.Data.([]*model.Seller)[0]
	if vendorInfo != nil && vendorInfo.LineManager != "" {
		vendorCode = vendorInfo.LineManager
	}
	// Get approved contract prices
	contractPricesApproved := getContractPriceApproved(warehouse, vendorCode)

	// No contract price found
	if len(contractPricesApproved) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price found",
		}
	}

	// Get contract price items
	contractPriceIDs := make([]int64, 0, len(contractPricesApproved))
	contractPriceIDMap := make(map[int64]*model.ContractPrice)
	for _, item := range contractPricesApproved {
		contractPriceIDs = append(contractPriceIDs, item.ContractPriceID)
		contractPriceIDMap[item.ContractPriceID] = item
	}

	// Query contract price items
	contractPriceItemsResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{
		ProductID: productID,
		ComplexQuery: []bson.M{
			{
				"contract_price_id": bson.M{
					"$in": contractPriceIDs,
				},
			},
		},
	}, 0, 1000, nil)
	if contractPriceItemsResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price item found",
		}
	}
	// No contract price item found
	contractPriceItems := contractPriceItemsResp.Data.([]*model.ContractPriceItem)
	if len(contractPriceItems) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price item found",
		}
	}

	contractPriceFiltered := make([]*model.ContractPrice, 0, len(contractPriceItems))
	contractPriceItemsMap := make(map[int64]*model.ContractPriceItem)
	// set map data
	for _, item := range contractPriceItems {
		if contractPrice, ok := contractPriceIDMap[item.ContractPriceID]; ok {
			contractPriceFiltered = append(contractPriceFiltered, contractPrice)
			contractPriceItemsMap[item.ContractPriceID] = item
		}
	}

	contractPriceDiffDaysMap := make(map[int64]int)
	// Calculate the difference in days from the start times
	for _, item := range contractPriceFiltered {
		if item.StartTime == nil {
			continue
		}

		diffDays := math.Ceil(utils.GetVietnamTimeNow().Sub(*item.StartTime).Hours() / 24)
		contractPriceDiffDaysMap[item.ContractPriceID] = int(diffDays)
	}

	// Get the contract price with the maximum difference in days
	maxContractPriceID := getMinValuePairs(contractPriceDiffDaysMap)
	newContractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		ComplexQuery: []bson.M{
			{
				"contract_price_id": bson.M{
					"$in": maxContractPriceID,
				},
			},
		},
	}, 0, 1000, &primitive.M{"_id": -1})
	// No new contract price found
	if newContractPriceResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No new contract price found",
		}
	}

	newContractPrice := newContractPriceResp.Data.([]*model.ContractPrice)[0]
	getContractPriceItem := contractPriceItemsMap[newContractPrice.ContractPriceID]

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create contract price success",
		Data:    []*model.ContractPriceItem{getContractPriceItem},
	}
}

// getContractPriceApproved retrieves a list of approved contract prices for a given warehouse and vendor code.
// It queries the database for contract prices that match the specified vendor code and filters the results based on the following criteria:
// - The warehouse code must match the specified warehouse or be "00" (indicating all warehouses).
// - The contract price status must be CONFIRMED.
// - The current time must be within the contract price's start and end time.
//
// Parameters:
// - warehouse: The code of the warehouse to filter contract prices.
// - vendorCode: The code of the vendor to filter contract prices.
//
// Returns:
// - A slice of pointers to ContractPrice objects that meet the criteria, or nil if no matching contract prices are found or if there is an error in the query.
func getContractPriceApproved(warehouse string, vendorCode string) []*model.ContractPrice {
	contractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		ComplexQuery: []bson.M{
			{
				"vendor_codes": bson.M{
					"$in": []string{vendorCode},
				},
			},
		},
	}, 0, 1000, nil)
	if contractPriceResp.Status != common.APIStatus.Ok {
		return nil
	}

	contractPrices := contractPriceResp.Data.([]*model.ContractPrice)
	if len(contractPrices) == 0 {
		return nil
	}

	contractPricesApproved := make([]*model.ContractPrice, 0, len(contractPrices))
	for _, item := range contractPrices {
		if item.WarehouseCode != "00" && item.WarehouseCode != warehouse {
			continue
		}
		if item.Status != enum.ContractPriceStatus.CONFIRMED {
			continue
		}
		if item.StartTime.After(utils.GetVietnamTimeNow()) || item.EndTime.Before(utils.GetVietnamTimeNow()) {
			continue
		}

		contractPricesApproved = append(contractPricesApproved, item)
	}

	return contractPricesApproved
}

// validContractPriceItem validates the contract price item based on the provided productID, warehouse, and vendorCode.
// It returns an APIResponse with an appropriate status and message if any of the validations fail.
//
// Parameters:
//   - productID: an int64 representing the product ID. It must be greater than 0.
//   - warehouse: a string representing the warehouse. It must not be empty.
//   - vendorCode: a string representing the vendor code. It must not be empty.
//
// Returns:
//   - *common.APIResponse: a pointer to an APIResponse containing the validation status and message.
//     If all validations pass, it returns nil.
func validContractPriceItem(productID int64, warehouse string, vendorCode string) *common.APIResponse {
	if productID == 0 || productID < 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "productID is not equal or greater than 0",
		}
	}

	if warehouse == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "warehouse is required",
		}
	}

	if vendorCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "vendorCode is required",
		}
	}

	return nil
}

func getMaxValuePairs(myMap map[int64]int) []int64 {
	// Variable to store the maximum value found
	var maxVal int

	// Find the maximum value in the map
	for _, v := range myMap {
		if v > maxVal {
			maxVal = v
		}
	}

	// Map to store pairs with the maximum value
	maxValPairs := []int64{}

	// Collect pairs with the maximum value
	for k, v := range myMap {
		if v == maxVal {
			maxValPairs = append(maxValPairs, k)
		}
	}

	return maxValPairs
}

func getMinValuePairs(myMap map[int64]int) []int64 {
	// Variable to store the minimum value found
	minVal := math.MaxInt64

	// Find the minimum value in the map
	for _, v := range myMap {
		if v < minVal {
			minVal = v
		}
	}

	// Map to store pairs with the minimum value
	minValPairs := []int64{}

	// Collect pairs with the minimum value
	for k, v := range myMap {
		if v == minVal {
			minValPairs = append(minValPairs, k)
		}
	}

	return minValPairs
}

// GetValidContractPriceItem retrieves valid contract price items based on the provided product IDs, warehouse code, and vendor codes.
// It performs the following steps:
// 1. If the warehouse code is "00", it considers all warehouse codes.
// 2. Queries the contract prices that meet the following criteria:
//   - Status is CONFIRMED
//   - Vendor code is in the provided vendor codes
//   - Warehouse code is in the considered warehouse codes
//   - Start time is less than or equal to the current time
//   - End time is greater than or equal to the current time
//
// 3. Converts the list of contract prices to a map for easier access.
// 4. Queries the contract price items that meet the following criteria:
//   - Product ID is in the provided product IDs
//   - Contract price code is in the contract price codes obtained from the previous query
//
// 5. Converts the list of contract price items to the final result map, where the key is a combination of product code and warehouse code.
// 6. If a contract price item already exists in the result map, it compares the start time and contract price ID to determine if the new item should replace the existing one.
//
// Parameters:
// - productIDs: A slice of product IDs to filter the contract price items.
// - warehouseCode: The warehouse code to filter the contract price items. If "00", all warehouse codes are considered.
// - vendorCodes: A slice of vendor codes to filter the contract prices.
//
// Returns:
// A map where the key is a combination of product code and warehouse code, and the value is a pointer to the corresponding ContractPriceItem.
func GetValidContractPriceItem(productIDs []int64, warehouseCode string, vendorCodes []string) map[string]*model.ContractPriceItem {

	// nếu warehouse code = 00 thì lấy tất cả warehouse code
	var warehouseCodes = []string{"00"}
	if warehouseCode != "00" {
		warehouseCodes = append(warehouseCodes, warehouseCode)
	}
	var mapContractPriceItem = make(map[string]*model.ContractPriceItem)

	// Query tất contract price hợp lệ
	// 1. status = CONFIRMED
	// 2. vendor_code in vendorCodes
	// 3. warehouse_code in warehouseCodes
	// 4. start_time <= now
	// 5. end_time >= now
	contractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		ComplexQuery: []bson.M{
			{"status": enum.ContractPriceStatus.CONFIRMED},
			{"vendor_codes": bson.M{"$in": vendorCodes}},
			{"warehouse_code": bson.M{"$in": warehouseCodes}},
			{"start_time": bson.M{"$lte": utils.GetVietnamTimeNow()}},
			{"end_time": bson.M{"$gte": utils.GetVietnamTimeNow()}},
		},
	}, 0, 1000, &bson.M{"start_time": 1})

	if contractPriceResp.Status != common.APIStatus.Ok {
		return mapContractPriceItem
	}

	// Chuyển từ danh sách contract price sang map
	contractPrices := contractPriceResp.Data.([]*model.ContractPrice)
	contractPriceCodes := make([]string, 0, len(contractPrices))
	mapContractPrice := make(map[string]*model.ContractPrice)
	for _, item := range contractPrices {
		contractPriceCodes = append(contractPriceCodes, item.ContractPriceCode)
		mapContractPrice[item.ContractPriceCode] = item
	}

	// query tất cả contract price item hợp lệ
	// 1. product_id in productIDs
	// 2. warehouse_code = warehouseCode
	// 3. contract_price_code in contractPriceCodes

	contractPriceItemsResp := model.ContractPriceItemDB.Query(&model.ContractPriceItem{
		ComplexQuery: []bson.M{
			{
				"product_id": bson.M{"$in": productIDs},
				"contract_price_code": bson.M{
					"$in": contractPriceCodes,
				},
			},
		},
	}, 0, 1000, nil)

	if contractPriceItemsResp.Status != common.APIStatus.Ok {
		return mapContractPriceItem
	}

	contractPriceItems := contractPriceItemsResp.Data.([]*model.ContractPriceItem)
	// Chuyển từ danh sách contract price item sang kết quả cuối cùng
	for _, item := range contractPriceItems {
		// key = product_code + warehouse_code
		key := item.ProductCode + "_" + warehouseCode

		// nếu đã tồn tại tiếp tục kiểm tra
		if contractItem, ok := mapContractPriceItem[key]; contractItem != nil && ok {

			if _, ok := mapContractPrice[contractItem.ContractPriceCode]; !ok {
				continue
			}
			newCP := mapContractPrice[item.ContractPriceCode]                   // giá trị hiện tại B
			cp := mapContractPrice[mapContractPriceItem[key].ContractPriceCode] // kết quả hiện tại A
			if cp == nil {
				continue
			}
			// item.starttime > resultItem.starttime
			// Giá trị mới, có ngày bắt đầu nằm sau ngày bắt đầu cũ
			if newCP.StartTime.After(*cp.StartTime) {
				mapContractPriceItem[key] = item

			} else if newCP.StartTime == cp.StartTime {
				// nếu thời gian bắt đầu giống nhau thì so sánh giá
				// nếu giá lớn thì đi so sánh ID
				if float64(newCP.ContractPriceID) > float64(cp.ContractPriceID) {
					mapContractPriceItem[key] = item
					continue
				}
			}
		} else {
			if contractPrice, ok := mapContractPrice[item.ContractPriceCode]; contractPrice != nil && ok {
				mapContractPriceItem[key] = item
			}
		}
	}
	return mapContractPriceItem
}

// VerifyContractPrice validates the given contract price input and processes it to update the winner unit price and contract price code for each item.
// It returns an APIResponse indicating the success or failure of the operation.
//
// Parameters:
//   - input: A pointer to a model.ContractPrice object containing the contract price details to be verified and processed.
//
// Returns:
//   - A pointer to a common.APIResponse object containing the status, message, and data of the operation.
func VerifyContractPrice(input *model.ContractPrice) *common.APIResponse {

	if err := validateContractPrice(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// ---------- main processing ---------
	items := input.Items

	var productIDs []int64
	for _, item := range items {
		productIDs = append(productIDs, item.ProductID)
	}

	// get valid contract price item
	mapValidProduct := GetValidContractPriceItem(productIDs, input.WarehouseCode, input.VendorCodes)

	var responseItems []*model.ContractPriceItem
	for _, item := range items {

		// for _, warehouseCode := range warehouseCodes {
		key := item.ProductCode + "_" + input.WarehouseCode
		if validContractPrice, ok := mapValidProduct[key]; ok {
			item.WinnerUnitPrice = validContractPrice.UnitPrice
			item.WinnerContractPriceCode = validContractPrice.ContractPriceCode
		}
		// }
		responseItems = append(responseItems, item)
	}
	input.Items = responseItems

	// ---------- item processing ---------
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create contract price success",
		Data:    []*model.ContractPrice{input},
	}
}

func MigrateContractPriceItem(updater *model.ContractPriceItem) *common.APIResponse {
	// find contract price item

	if updater.ProductID == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "product id is required",
		}
	}
	if updater.UnitPrice == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "contract price id is required",
		}
	}
	contractPriceItemResp := model.ContractPriceItemDB.QueryOne(&model.ContractPriceItem{
		ContractPriceID: updater.ContractPriceID,
		ProductID:       updater.ProductID,
	})

	if contractPriceItemResp.Status != common.APIStatus.Ok {
		return contractPriceItemResp
	}

	contractPriceItem := contractPriceItemResp.Data.([]*model.ContractPriceItem)[0]

	if contractPriceItem.UnitPriceBK != contractPriceItem.UnitPrice {
		contractPriceItem.UnitPriceBK = contractPriceItem.UnitPrice
	}

	// var now = utils.GetVietnamTimeNow()
	// var yesterday = now.AddDate(0, 0, -1)
	// query := model.PriceAfterRebate{
	// 	ContractPriceID: updater.ContractPriceID,
	// 	ProductID:       updater.ProductID,
	// 	Version:         utils.ConvertTimeToStringYYYYMMDD(&yesterday, ""),
	// }

	// otps := client.APIOption{
	// 	Q:     query,
	// 	Limit: utils.Pointer.WithInt(1),
	// }

	// getPriceAfterRebateResp := seller.GetPriceAfterRebate(otps)
	// if getPriceAfterRebateResp.Status != common.APIStatus.Ok {
	// 	return getPriceAfterRebateResp
	// }

	// priceAfterRebates := getPriceAfterRebateResp.Data.([]*model.PriceAfterRebate)[0]
	// fmt.Println("priceAfterRebates", priceAfterRebates)
	// vat := priceAfterRebates.Vat
	// discount := priceAfterRebates.Discount
	// fixedPromotion := priceAfterRebates.FixedPromotion
	// adhocPromotion := priceAfterRebates.AdhocPromotion
	// var B float64
	// if vat != nil && *vat != 0 {
	// 	B *= (1 + *vat/100)
	// }
	// if discount != nil && *discount != 0 {
	// 	B *= (1 - *discount/100)
	// }
	// if fixedPromotion != nil && *fixedPromotion != 0 {
	// 	B *= (1 - *fixedPromotion/100)
	// }
	// if adhocPromotion != nil && *adhocPromotion != 0 {
	// 	B *= (1 - *adhocPromotion/100)
	// }
	// A := updater.UnitPrice
	// newPrice := math.Round(A/B) / 1000
	// newUnitPrice := math.Round(1000*newPrice) / 1000
	// fmt.Println("A", B)
	// fmt.Println("newUnitPrice", newUnitPrice)
	// if newUnitPrice == contractPriceItem.UnitPrice {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Ok,
	// 		Message: "unit price is not change",
	// 	}
	// }
	contractPriceItem.UnitPrice = updater.UnitPrice

	return model.ContractPriceItemDB.UpdateOne(&model.ContractPriceItem{ID: contractPriceItem.ID}, contractPriceItem)
}

func validateDuplicateContractPrice(checkCP *model.ContractPrice, actionType enum.ContractPriceActionValue) (error, *model.ContractPrice) {

	var (
		startTime      = checkCP.StartTime.UTC()
		endTime        = checkCP.EndTime.UTC()
		warehouseCodes = []string{checkCP.WarehouseCode}
		vendorCodes    = checkCP.VendorCodes
		productCodes   = []string{}
		mapCheckItem   = make(map[string]*model.ContractPriceItem)
	)

	for _, checkItem := range checkCP.Items {
		productCodes = append(productCodes, checkItem.ProductCode)
		mapCheckItem[checkItem.ProductCode] = checkItem
	}
	q := &model.ContractPrice{
		Status: enum.ContractPriceStatus.CONFIRMED,
	}
	complexQuery := []bson.M{}
	if utils.IsContains(warehouseCodes, "00") {
		warehouseCodes = []string{"00", "HN", "BD", "DN"}
	} else {
		warehouseCodes = append(warehouseCodes, "00")
	}
	complexQuery = append(complexQuery, bson.M{
		"warehouse_code": bson.M{"$in": warehouseCodes},
		"vendor_codes":   bson.M{"$in": vendorCodes},
		// "$and": []bson.M{
		// 	{"start_time": bson.M{"$lte": utils.GetLastTimeOfDate(startTime), "$gte": utils.GetFirstTimeOfDate(startTime)}},
		// 	{"end_time": bson.M{"$lte": utils.GetLastTimeOfDate(endTime), "$gte": utils.GetFirstTimeOfDate(endTime)}},
		// },
	})

	q.ComplexQuery = complexQuery

	availableCPResp := model.ContractPriceDB.Query(q, 0, 1000, nil)
	if availableCPResp.Status == common.APIStatus.NotFound {
		return nil, nil
	} else if availableCPResp.Status != common.APIStatus.Ok {
		return fmt.Errorf("error when check duplicate contract price"), nil
	}
	availableCPs := availableCPResp.Data.([]*model.ContractPrice)
	for _, cp := range availableCPs {
		switch actionType {
		case enum.ContractPriceAction.CHANGE_STATUS:
			// do nothing
		case enum.ContractPriceAction.UPDATE_CONFIRMED:
			if cp.ContractPriceCode == checkCP.ContractPriceCode {
				continue
			}
		default:
			return fmt.Errorf("invalid action type"), nil
		}

		if utils.HasNil(cp.StartTime, cp.EndTime, startTime, endTime) {
			continue
		}

		// nếu thời bắt đầu khác nhau hoặc thời gian kết thúc khác nhau thì bỏ qua

		if startTime != *cp.StartTime || endTime != *cp.EndTime {
			continue
		}

		cpItemsResp := model.ContractPriceItemDB.Query(&model.ContractPriceItem{ContractPriceCode: cp.ContractPriceCode}, 0, 1000, nil)
		if cpItemsResp.Status != common.APIStatus.Ok {
			continue
		}

		for _, cpItem := range cpItemsResp.Data.([]*model.ContractPriceItem) {
			// Same vendor,
			// same start time
			// same end time
			// same product code
			// => not allowed to approve
			if utils.IsContains(productCodes, cpItem.ProductCode) {
				productInfo := fmt.Sprintf("%d - %s", cpItem.ProductID, cpItem.ProductName)

				return fmt.Errorf(fmt.Sprintf("duplicate contract price with contract price code %s - %s", cp.ContractPriceCode, productInfo)), cp
			}
		}
	}
	return nil, nil
}

func GetContractPriceItemValid(productID int64, warehouse string, vendorCode string, checkingTime time.Time) *common.APIResponse {
	if response := validContractPriceItem(productID, warehouse, vendorCode); response != nil {
		return response
	}

	// get vendor
	opts := client.APIOption{
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
		},
		Limit: utils.Pointer.WithInt(1),
		Params: map[string]string{
			"sellerCode": vendorCode,
		},
	}
	getVendorInfoResp := seller.GetSellers(opts)
	if getVendorInfoResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No vendor found with vendor code",
		}
	}

	vendorInfo := getVendorInfoResp.Data.([]*model.Seller)[0]
	if vendorInfo != nil && vendorInfo.LineManager != "" {
		vendorCode = vendorInfo.LineManager
	}

	contractPricesApproved := getContractPriceApprovedByDate(warehouse, vendorCode, checkingTime)
	if len(contractPricesApproved) == 0 {
		// get vendor
		opts := client.APIOption{
			Q: model.Seller{
				SellerClass: model.CLASS_VENDOR,
			},
			Limit: utils.Pointer.WithInt(1),
			Params: map[string]string{
				"sellerCode": vendorCode,
			},
		}
		getVendorResp := seller.GetSellers(opts)
		if getVendorResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "No vendor found with vendor code",
			}
		}

		vendor := getVendorResp.Data.([]*model.Seller)[0]
		contractPricesApproved = getContractPriceApprovedByDate(warehouse, vendor.LineManager, checkingTime)
	}

	if len(contractPricesApproved) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price found",
		}
	}

	contractPriceIDs := make([]int64, 0, len(contractPricesApproved))
	contractPriceIDMap := make(map[int64]*model.ContractPrice)
	for _, item := range contractPricesApproved {
		contractPriceIDs = append(contractPriceIDs, item.ContractPriceID)
		contractPriceIDMap[item.ContractPriceID] = item
	}

	contractPriceItemsResp := model.ContractPriceItemDB.Query(model.ContractPriceItem{
		ProductID: productID,
		ComplexQuery: []bson.M{
			{
				"contract_price_id": bson.M{
					"$in": contractPriceIDs,
				},
			},
		},
	}, 0, 1000, nil)
	if contractPriceItemsResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price item found",
		}
	}

	contractPriceItems := contractPriceItemsResp.Data.([]*model.ContractPriceItem)
	if len(contractPriceItems) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No contract price item found",
		}
	}

	contractPriceFiltered := make([]*model.ContractPrice, 0, len(contractPriceItems))
	contractPriceItemsMap := make(map[int64]*model.ContractPriceItem)
	for _, item := range contractPriceItems {
		if contractPrice, ok := contractPriceIDMap[item.ContractPriceID]; ok {
			contractPriceFiltered = append(contractPriceFiltered, contractPrice)
			contractPriceItemsMap[item.ContractPriceID] = item
		}
	}

	contractPriceDiffDaysMap := make(map[int64]int)
	for _, item := range contractPriceFiltered {
		if item.StartTime == nil {
			continue
		}

		diffDays := math.Ceil(utils.GetVietnamTimeNow().Sub(*item.StartTime).Hours() / 24)
		contractPriceDiffDaysMap[item.ContractPriceID] = int(diffDays)
	}

	maxContractPriceID := getMinValuePairs(contractPriceDiffDaysMap)
	newContractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		ComplexQuery: []bson.M{
			{
				"contract_price_id": bson.M{
					"$in": maxContractPriceID,
				},
			},
		},
	}, 0, 1000, &primitive.M{"_id": -1})
	if newContractPriceResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "No new contract price found",
		}
	}

	newContractPrice := newContractPriceResp.Data.([]*model.ContractPrice)[0]
	getContractPriceItem := contractPriceItemsMap[newContractPrice.ContractPriceID]

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create contract price success",
		Data:    []*model.ContractPriceItem{getContractPriceItem},
	}
}

func getContractPriceApprovedByDate(warehouse string, vendorCode string, checkingTime time.Time) []*model.ContractPrice {
	contractPriceResp := model.ContractPriceDB.Query(model.ContractPrice{
		ComplexQuery: []bson.M{
			{
				"vendor_codes": bson.M{
					"$in": []string{vendorCode},
				},
			},
		},
	}, 0, 1000, nil)
	if contractPriceResp.Status != common.APIStatus.Ok {
		return nil
	}

	contractPrices := contractPriceResp.Data.([]*model.ContractPrice)
	if len(contractPrices) == 0 {
		return nil
	}

	contractPricesApproved := make([]*model.ContractPrice, 0, len(contractPrices))
	for _, item := range contractPrices {
		if item.WarehouseCode != "00" && item.WarehouseCode != warehouse {
			continue
		}
		if item.Status != enum.ContractPriceStatus.CONFIRMED {
			continue
		}
		if item.StartTime.After(checkingTime) || item.EndTime.Before(checkingTime) {
			continue
		}

		contractPricesApproved = append(contractPricesApproved, item)
	}

	return contractPricesApproved
}

type GetPurchaseSkuInfoResponse struct {
	FavVendor         *model.VendorConfig      `json:"favVendor" bson:"fav_vendor"`
	ContractPriceItem *model.ContractPriceItem `json:"contractPriceItem" bson:"contract_price_item"`
}

func GetPurchaseSkuInfo(productID int64, warehouse string, sellerCode string) *common.APIResponse {

	var (
		favVendorCode string
		favVendor     *model.VendorConfig
	)

	if productID == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "product id is required",
		}
	}
	// convert purchase
	purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(warehouse)

	if !ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "invalid warehouse code",
			ErrorCode: "INVALID_WAREHOUSE_CODE",
		}
	}
	// get SkuConfig
	skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
		ProductID:     productID,
		SellerCode:    sellerCode,
		PurchaserCode: purchaserCode,
	})

	if skuConfigResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "No sku config found",
			ErrorCode: "SKU_CÒNFIG_NOT_FOUND",
		}
	}

	result := &GetPurchaseSkuInfoResponse{}
	// get favorite vendor
	skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]
	if skuConfig.Vendors != nil {
		for _, vendor := range *skuConfig.Vendors {
			if vendor.Priority != nil && *vendor.Priority == 0 {
				favVendorCode = vendor.VendorCode
				favVendor = &vendor
				break
			}
		}
	}

	if favVendorCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "No favorite vendor found",
			ErrorCode: "FAVORITE_VENDOR_NOT_FOUND",
		}
	}

	result.FavVendor = favVendor
	// Base on: favVendorCode, warehouse, productID
	// Get contract price item valid
	validContractPriceResp := GetContractPriceItemValid(productID, warehouse, favVendorCode, utils.GetVietnamTimeNow())
	if validContractPriceResp.Status == common.APIStatus.Ok {
		contractPriceItem := validContractPriceResp.Data.([]*model.ContractPriceItem)[0]
		result.ContractPriceItem = contractPriceItem
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create contract price success",
	}
}

func ToolMigrateContractPrice(contractPriceCode string) *common.APIResponse {

	_id := primitive.NilObjectID

	for {
		// query contract price có end_time từ 31/12/2024 đến 1/1/2025
		q := model.ContractPrice{
			Status: enum.ContractPriceStatus.CONFIRMED,
			ComplexQuery: []bson.M{
				{
					"end_time": bson.M{
						"$gte": utils.GetFirstTimeOfDate(time.Date(2024, 12, 31, 0, 0, 0, 0, utils.TimeZoneVN)),
						"$lte": utils.GetLastTimeOfDate(time.Date(2025, 1, 1, 0, 0, 0, 0, utils.TimeZoneVN)),
					},
					"_id": bson.M{"$gt": _id},
				},
			},
		}
		if contractPriceCode != "" {
			q.ContractPriceCode = contractPriceCode
		}
		contractPriceResp := model.ContractPriceDB.Query(
			q, 0, 100, &primitive.M{"_id": 1},
		)

		if contractPriceResp.Status != common.APIStatus.Ok {
			break
		}

		contractPrices := contractPriceResp.Data.([]*model.ContractPrice)

		for _, contractPrice := range contractPrices {
			_id = *contractPrice.ID

			// end time mới là sẽ cộng thêm 1 năm
			newEndTime := contractPrice.EndTime.AddDate(1, 0, 0)

			// update contract price
			contractPrice.EndTime = &newEndTime
			UpdateResp := model.ContractPriceDB.UpdateOne(&model.ContractPrice{ID: contractPrice.ID}, contractPrice)
			if UpdateResp.Status != common.APIStatus.Ok {
				fmt.Printf("Update contract price %s failed\n", contractPrice.ContractPriceCode)
			}
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update po item success",
	}
}
