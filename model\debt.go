package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Debt struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Version string `json:"version,omitempty" bson:"version,omitempty"`

	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorName string `json:"vendorName,omitempty" bson:"vendor_name,omitempty"`

	// debt
	FiscalPosition *float64 `json:"fiscalPosition" bson:"fiscal_position,omitempty"`
	PaymentTerm    *float64 `json:"paymentTerm" bson:"payment_term,omitempty"`

	// vượt công nợ
	IsOverFiscalPosition    bool     `json:"isOverFiscalPosition" bson:"is_over_fiscal_position,omitempty"`
	RemainingFiscalPosition *float64 `json:"remainingFiscalPosition" bson:"remaining_fiscal_fosition,omitempty"`

	AdjustmentBillTotalPrice float64 `json:"adjustmentBillTotalPrice" bson:"adjustment_bill_total_price,omitempty"`
	BillTotalPrice           float64 `json:"billTotalPrice,omitempty" bson:"bill_total_price,omitempty"`
	POWaitTotalPrice         float64 `json:"poWaitTotalPrice,omitempty" bson:"po_wait_total_price,omitempty"`
	PONewTotalPrice          float64 `json:"poNewTotalPrice,omitempty" bson:"po_new_total_price,omitempty"`
	DebtTotalPrice           float64 `json:"debtTotalPrice,omitempty" bson:"debt_total_price,omitempty"`

	BalanceTotal float64 `json:"balanceTotal,omitempty" bson:"balance_total,omitempty"`

	AdjustmentBillWTPs *[]string `json:"adjustmentBillWTPs,omitempty" bson:"adjustment_bill_wtps,omitempty"` // only INCREASE
	VendorBillCodeWTPs []string  `json:"vendorBillCodeWTPs,omitempty" bson:"vendor_bill_code_wtps,omitempty"`

	// query at backend
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// DebtDB ...
var DebtDB = &db.Instance{
	ColName:        "debt",
	TemplateObject: &Debt{},
}

var DebtPlatformDB = &db.Instance{
	ColName:        "debt_platform",
	TemplateObject: &Debt{},
}

// InitSkuModel ...
func InitDebtModel(s *mongo.Database) {
	DebtDB.ApplyDatabase(s)
	DebtPlatformDB.ApplyDatabase(s)

	// t := true

	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "is_over_fiscal_position", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "bill_total_price", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_wait_total_price", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_new_total_price", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "debt_total_price", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "balance_total", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "fiscal_position", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = DebtDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "remaining_fiscal_fosition", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
