package model

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type TransferRequest struct {
	ID                  *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime         *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime     *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedByAccountID  int64               `json:"createdByAccountID,omitempty" bson:"created_by_account_id,omitempty"`
	CreatedByEmployeeID int64               `json:"createdByEmployeeID,omitempty" bson:"-"`

	OrgID        int64  `json:"orgID,omitempty" bson:"org_id,omitempty"`           // 1 || 2 || 3
	BranchCode   string `json:"branchCode,omitempty" bson:"branch_code,omitempty"` // VN || KH || TH
	CurrencyCode string `json:"currencyCode,omitempty" bson:"currency_code,omitempty"`

	CompanyCode          string `json:"companyCode,omitempty" bson:"company_code,omitempty"`
	CompanyAccountNumber string `json:"companyAccountNumber,omitempty" bson:"company_account_number,omitempty"`

	ReceiverType string `json:"receiverType,omitempty" bson:"receiver_type,omitempty"` // CUSTOMER || SUPPLIER || SELLER

	TransferRequestID   int64  `json:"transferRequestID,omitempty" bson:"transfer_request_id,omitempty"`
	TransferRequestCode string `json:"transferRequestCode,omitempty" bson:"transfer_request_code,omitempty"`
	WorkflowRequestCode string `json:"workflowRequestCode,omitempty" bson:"workflow_request_code,omitempty"`
	WorkflowCode        string `json:"workflowCode,omitempty" bson:"workflow_code,omitempty"`

	Type   enum.TransferRuleTypeEnum `json:"type,omitempty" bson:"type,omitempty"`
	Status string                    `json:"status,omitempty" bson:"status,omitempty"`

	TotalAmount        float64 `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	TransactionBanking string  `json:"transactionBanking,omitempty" bson:"transaction_banking,omitempty"` // Banking transaction type

	Note *string `json:"note,omitempty" bson:"note,omitempty"`

	Items []*TransferRequestItem `json:"items,omitempty" bson:"items,omitempty"`
}

type TransferRequestItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	TransferRequestCode     string `json:"transferRequestCode,omitempty" bson:"transfer_request_code,omitempty"` // link with TransferRequest
	TransferRequestItemID   int64  `json:"transferRequestItemID,omitempty" bson:"transfer_request_item_id,omitempty"`
	TransferRequestItemCode string `json:"transferRequestItemCode,omitempty" bson:"transfer_request_item_code,omitempty"`

	RelateType enum.TransferRuleTypeEnum `json:"relateType,omitempty" bson:"relate_type,omitempty"`
	RelateCode string                    `json:"relateCode,omitempty" bson:"relate_code,omitempty"` // this field save code of object relate with transfer request item. Ex: clck

	// receiver
	ReceiverType string `json:"receiverType,omitempty" bson:"receiver_type,omitempty"` // CUSTOMER || SUPPLIER || SELLER
	ReceiverID   int64  `json:"receiverID,omitempty" bson:"receiver_id,omitempty"`
	ReceiverCode string `json:"receiverCode,omitempty" bson:"receiver_code,omitempty"`
	// object
	ItemObjectType enum.TransferRuleTypeEnum `json:"itemObjectType,omitempty" bson:"item_object_type,omitempty"` // ORDER || SELLER_RECONCILIATION
	ItemObjectID   int64                     `json:"itemObjectID,omitempty" bson:"item_object_id,omitempty"`
	ItemObjectCode string                    `json:"itemObjectCode,omitempty" bson:"item_object_code,omitempty"`
	// ref object
	ItemObjectRefType enum.TransferRuleTypeEnum `json:"itemObjectRefType,omitempty" bson:"item_object_ref_type,omitempty"` // SO
	ItemObjectRefID   int64                     `json:"itemObjectRefID,omitempty" bson:"item_object_ref_id,omitempty"`
	ItemObjectRefCode string                    `json:"itemObjectRefCode,omitempty" bson:"item_object_ref_code,omitempty"`

	ItemObjectExtras *[]ItemObjectExtra `json:"itemObjectExtras,omitempty" bson:"item_object_extras,omitempty"`

	// receiver bank
	ReceiverBankAccountName   string  `json:"receiverBankAccountName,omitempty" bson:"receiver_bank_account_name,omitempty"`
	ReceiverBankAccountNumber string  `json:"receiverBankAccountNumber,omitempty" bson:"receiver_bank_account_number,omitempty"`
	ReceiverBankName          string  `json:"receiverBankName,omitempty" bson:"receiver_bank_name,omitempty"`
	ReceiverBankCode          string  `json:"receiverBankCode,omitempty" bson:"receiver_bank_code,omitempty"`
	ReceiverBankID            int64   `json:"receiverBankID,omitempty" bson:"receiver_bank_id,omitempty"`
	ReceiverBankGlobalCode    string  `json:"receiverBankGlobalCode,omitempty" bson:"receiver_bank_global_code,omitempty"`
	ReceiverBankBranchName    *string `json:"receiverBankBranchName,omitempty" bson:"receiver_bank_branch_name,omitempty"`
	ReceiverBankBranchID      *int64  `json:"receiverBankBranchID,omitempty" bson:"receiver_bank_branch_id,omitempty"`
	ReceiverBankBranchCode    string  `json:"receiverBankBranchCode,omitempty" bson:"receiver_bank_branch_code,omitempty"`

	Content string  `json:"content,omitempty" bson:"content,omitempty"`
	Amount  float64 `json:"amount,omitempty" bson:"amount,omitempty"`

	LogicalObject *LogicalObject `json:"logicalObject,omitempty" bson:"logical_object,omitempty"` // Logical object for the transfer

	// transfer result
	Status          string `json:"status,omitempty" bson:"status,omitempty"` // PROCESSING || SUCCESS || FAILED
	TransactionCode string `json:"transactionCode,omitempty" bson:"transaction_code,omitempty"`
	TransferRemark  string `json:"transferRemark,omitempty" bson:"transfer_remark,omitempty"`
}

type LogicalObject struct {
	MaxAmount       float64 `json:"maxAmount,omitempty" bson:"max_amount,omitempty"`              // Maximum amount for the transfer
	MaxLengthRemark int64   `json:"maxLengthRemark,omitempty" bson:"max_length_remark,omitempty"` // Maximum length of the remark
}

type ItemObjectExtra struct {
	Type    string `json:"type,omitempty" bson:"type,omitempty"`
	ID      int64  `json:"id,omitempty" bson:"id,omitempty"`
	Code    string `json:"code,omitempty" bson:"code,omitempty"`
	Display bool   `json:"display,omitempty" bson:"display,omitempty"`
}
