package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type InboundCallbackStore struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`

	POCode    string `json:"poCode" bson:"po_code,omitempty"`
	ProductID int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	Lot       string `json:"lot" bson:"lot,omitempty"`
	ExpDate   string `json:"expDate" bson:"exp_date,omitempty"`

	Quantity *int64 `json:"quantity,omitempty" bson:"quantity,omitempty"`

	MigrateMetadata *MigrateMetadata `json:"-" bson:"migrate_metadata,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	POCodeIn     []string  `json:"poCodeIn,omitempty" bson:"-"`
}

// CallbackStorageDB ...
var InboundCallbackStoreDB = &db.Instance{
	ColName:        "inbound_callback_store",
	TemplateObject: &InboundCallbackStore{},
}

// InitSkuModel ...
func InitInboundCallbackStoreModel(s *mongo.Database) {
	InboundCallbackStoreDB.ApplyDatabase(s)

	// // - INDEX
	// // - BE
	// // POCode, ProductID, Lot, ExpDate
	// // - FE
	// // ProductID, SellerCode

	// t := true
	// _ = InboundCallbackStoreDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "lot", Value: 1},
	// 	primitive.E{Key: "exp_date", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // s.Collection("inbound_callback_store").Indexes().DropOne(context.TODO(), "created_time_1")
	// var second int32 = 60 * 60 * 24 * 90 // 90 ngày
	// _ = InboundCallbackStoreDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &second,
	// })
	// _ = InboundCallbackStoreDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
