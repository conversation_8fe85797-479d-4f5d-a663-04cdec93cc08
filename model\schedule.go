package model

import (
	// Import the schedule package for scheduling functionalities
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	// Import the mongo package for MongoDB database interactions
	"go.mongodb.org/mongo-driver/mongo"
)

// Declare a global variable to hold the schedule configuration
var ConfigSchedule *schedule.ConfigDB

// CreateConfigSchedule initializes the schedule configuration with the given database and process map
func CreateConfigSchedule(s *mongo.Database, mapProcess map[string]schedule.Process) {
	// Create a new schedule configuration with the specified collection name and process map
	ConfigSchedule = schedule.NewConfigDB("_schedule", mapProcess)
	// Initialize the schedule configuration with the provided MongoDB database
	ConfigSchedule.Init(s)
}

// The StartSchedule function is commented out and would start the schedule if uncommented
// func StartSchedule() {
// 	ConfigSchedule.Start()
// }
