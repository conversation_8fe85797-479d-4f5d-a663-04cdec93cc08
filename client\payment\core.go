package payment

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"go.mongodb.org/mongo-driver/mongo"
)

var paymentCoreClient *client.Client

const (
	getPaymentTransfer    = "GET::/payment/transfer"
	createPaymentTransfer = "POST::/payment/transfer"
	getTransactionList    = "POST::/transaction/list"
)

func InitPaymentCore(database *mongo.Database) {
	const paymentCore = "/payment/vpbank-adapter/v1"

	// headers := map[string]string{"Authorization": conf.Config.APIKey}
	paymentCoreClient = client.NewClient(paymentCore, nil, 0)
	paymentCoreClient.WithDatabase(database)
	paymentCoreClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     getPaymentTransfer,
				Name:     "payment__GET__payment_transfer",
				Database: database,
			},
			{
				Path:     createPaymentTransfer,
				Name:     "payment__POST__payment_transfer",
				Database: database,
			},
			{
				Path:     getTransactionList,
				Name:     "payment__GET__transaction_list",
				Database: database,
			},
		}...,
	)
}

func GetPaymentTransfer(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentResponse
	_, err := paymentCoreClient.WithRequest(getPaymentTransfer, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func CreatePaymentTransfer(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentResponse
	_, err := paymentCoreClient.WithRequest(createPaymentTransfer, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetTransactionList(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp TransactionResponse
	_, err := paymentCoreClient.WithRequest(getTransactionList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
