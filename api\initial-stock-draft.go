package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetPOList
func GetInitialStockDraftList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		sort     = req.GetParam("sort")
	)

	// validate input
	var query model.InitialStockDraft
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// validate input
	if len(query.SKUIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"sku": bson.M{
			"$in": query.SKUIn,
		}})
	}

	// validate input
	if query.CreatedTimeFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFrom,
			},
		})
	}

	// validate input
	if query.CreatedTimeTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTimeTo,
			},
		})
	}

	// validate input
	if query.IDFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$gt": query.IDFrom,
			},
		})
	}

	// validate input
	if query.IDTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$lt": query.IDTo,
			},
		})
	}

	// do action
	return resp.Respond(action.GetInitialStockDraftList(query, offset, limit, getTotal, sort))
}

// CreateInitialStockDraft ...
func CreateInitialStockDraft(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InitialStockDraft

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.CreateInitialStockDraft(input))
}

// UpdateInitialStockDraft ...
func UpdateInitialStockDraft(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InitialStockDraft

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.UpdateInitialStockDraft(input))
}
