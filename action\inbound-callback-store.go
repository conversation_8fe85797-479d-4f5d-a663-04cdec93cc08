package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetInboundCallbackStore
// GetInboundCallbackStore retrieves inbound callback store records based on the provided query, offset, and limit.
// If getTotal is true, it also fetches the total count of records matching the query.
//
// Parameters:
//   - query: A pointer to an InboundCallbackStore model containing the query criteria.
//   - offset: The number of records to skip before starting to collect the result set.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to fetch the total count of matching records.
//
// Returns:
//   - A pointer to an APIResponse containing the retrieved records and optionally the total count.
func GetInboundCallbackStore(query *model.InboundCallbackStore, offset, limit int64, getTotal bool) *common.APIResponse {

	inboundCallbackResp := model.InboundCallbackStoreDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.InboundCallbackStoreDB.Count(query)
		inboundCallbackResp.Total = countResp.Total
	}

	return inboundCallbackResp
}

// DeleteInboundCallbackStore
// DeleteInboundCallbackStore deletes an inbound callback store record based on the provided query.
// It takes a pointer to an InboundCallbackStore model as an argument and returns an APIResponse.
//
// Parameters:
//   - query: A pointer to an InboundCallbackStore model containing the criteria for deletion.
//
// Returns:
//   - *common.APIResponse: The response from the delete operation, indicating success or failure.
func DeleteInboundCallbackStore(query *model.InboundCallbackStore) *common.APIResponse {

	inboundCallbackResp := model.InboundCallbackStoreDB.Delete(query)
	return inboundCallbackResp
}

// WarehouseCallbackLotDate ...
// WarehouseCallbackLotDate processes the inbound callback for a warehouse lot date.
// It performs the following actions:
// 1. Checks if the inbound callback status is "CHECKIN". If so, it updates the lead time in the purchase order and returns.
// 2. Updates the actual quantities for purchase order items.
// 3. Retrieves the purchase order based on the provided PO code and updates its bill status to "WAITING_BILL" if the purchase order status is "COMPLETED".
// 4. Iterates through the inbound callback items and creates a new record in the InboundCallbackStore for each item with valid product ID and done quantity.
//   - If the lot or expiration date is empty, it sets them to "-".
//   - If the creation of the InboundCallbackStore record fails, it returns immediately.
//
// Parameters:
// - input: The inbound callback data containing status, PO code, and inbound callback items.
func WarehouseCallbackLotDate(input model.InboundCallback) {
	// kiem tra inboundCB gui status CHECKIN khong ?
	if input.Status == "CHECKIN" {
		// neu inboundcallback tra ve status la CHECKIN thi k can cap nhat lai truong leadTime va FirstInboundTime
		updateLeadtimeInPO(input)
		return
	}

	// #2 cập nhật actual cho PO item
	updateActualPOItems(input)

	// #3 ghi vào storage
	sellerCode := ""
	purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
		POCode: input.POCode,
	})
	if purchaseOrderResp.Status == common.APIStatus.Ok {
		purchaseOrder := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]
		sellerCode = purchaseOrder.SellerCode

		// update về WAITING_BILL
		if purchaseOrder.Status == enum.PurchaseOrderStatus.COMPLETED {
			model.PurchaseOrderDB.UpdateOne(
				model.PurchaseOrder{ID: purchaseOrder.ID},
				model.PurchaseOrder{Status: enum.PurchaseOrderStatus.AWAITING_BILLED},
			)
		}
	}

	// #4 ghi vào storage
	for _, itemCallback := range input.InboundCallbackItems {
		if itemCallback.ProductID == 0 || itemCallback.DoneQuantity == 0 {
			continue
		}
		if itemCallback.Lot == "" || itemCallback.ExpDate == "" {
			itemCallback.Lot = "-"
			itemCallback.ExpDate = "-"
		}

		// tạo record InboundCallbackStore
		createResp := model.InboundCallbackStoreDB.Create(model.InboundCallbackStore{
			SellerCode: sellerCode,

			POCode:    input.POCode,
			ProductID: itemCallback.ProductID,
			Lot:       itemCallback.Lot,
			ExpDate:   itemCallback.ExpDate,
			Quantity:  &itemCallback.DoneQuantity,
		})
		if createResp.Status != common.APIStatus.Ok {
			return
		}
	}
}
