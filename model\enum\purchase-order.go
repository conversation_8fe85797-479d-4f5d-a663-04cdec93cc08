package enum

// PurchaseOrderStatusValue ..
type PurchaseOrderStatusValue string

// nolint
type purchaseOrderStatus struct {
	DRAFT     PurchaseOrderStatusValue
	CONFIRMED PurchaseOrderStatusValue
	// SENT_PO    PurchaseOrderStatusValue
	// PROCESSING PurchaseOrderStatusValue
	COMPLETED PurchaseOrderStatusValue
	CANCELED  PurchaseOrderStatusValue

	// new status
	HANDOVER_COMPLETED PurchaseOrderStatusValue
	PARTIALLY_RECEIVED PurchaseOrderStatusValue
	RECEIVED           PurchaseOrderStatusValue
	AWAITING_BILLED    PurchaseOrderStatusValue
	BILLED             PurchaseOrderStatusValue
}

// SkuStatus ...
var PurchaseOrderStatus = &purchaseOrderStatus{
	"DRAFT",
	"CONFIRMED",
	// "SENT_PO",
	// "PROCESSING",
	"COMPLETED",
	"CANCELED",

	// new status
	"HANDOVER_COMPLETED",
	"PARTIALLY_RECEIVED",
	"RECEIVED",
	"AWAITING_BILLED",
	"BILLED",
}

type POType string

type poType struct {
	NORMAL              POType
	IT                  POType
	REPLENISHMENT       POType
	RECEIPTGIFTPRODUCTS POType
	HEDGING             POType
}

var POTypeVal = &poType{
	NORMAL:              "NORMAL",              // bình thường
	IT:                  "IT",                  // hàng luân chuyển nội bộ
	REPLENISHMENT:       "REPLENISHMENT",       // hàng bổ sung
	RECEIPTGIFTPRODUCTS: "RECEIPTGIFTPRODUCTS", // nhập hàng tặng
	HEDGING:             "HEDGING",             // phòng ngừa rủi ro
}

var POTypeNotHedging = []POType{POTypeVal.NORMAL, POTypeVal.IT, POTypeVal.REPLENISHMENT, POTypeVal.RECEIPTGIFTPRODUCTS}

// POBillStatusValue ..
type POBillStatusValue string

// nolint
type poBillStatusValue struct {
	WAITING_BILL  POBillStatusValue
	BILL_RECEIVED POBillStatusValue
}

// SkuStatus ...
var POBillStatus = &poBillStatusValue{
	"WAITING_BILL",
	"BILL_RECEIVED",
}

// RequireConfirmValue ..
type RequireConfirmValue string

// nolint
type requireConfirm struct {
	BLANK                     RequireConfirmValue
	CHANGE_ESTIMATED_DELIVERY RequireConfirmValue
}

// SkuStatus ...
var RequireConfirm = &requireConfirm{
	"",
	"CHANGE_ESTIMATED_DELIVERY",
}

// create by system
type (
	POCreateBytSystemValue string
	poCreateBySystem       struct {
		INTERNAL_CONFIRM_BIDDING POCreateBytSystemValue
		INTERNAL_MEDX            POCreateBytSystemValue
		SUPPLIER                 POCreateBytSystemValue
	}
)

var POCreateBySystem = &poCreateBySystem{
	INTERNAL_CONFIRM_BIDDING: "INTERNAL_CONFIRM_BIDDING",
	INTERNAL_MEDX:            "INTERNAL_MEDX",
	SUPPLIER:                 "SUPPLIER",
}

type FirstMileByValue string

type firstMileByValue struct {
	BUYMED FirstMileByValue
	VENDOR FirstMileByValue
}

var FirstMileBy = &firstMileByValue{
	"BUYMED",
	"VENDOR",
}
