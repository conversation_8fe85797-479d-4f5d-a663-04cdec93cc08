package enum

type NotificationDeliveryValue string
type notificationDeliveryEnt struct {
	NEW       NotificationDeliveryValue
	DELIVERED NotificationDeliveryValue
}

var NotificationDelivery = &notificationDeliveryEnt{
	"NEW",
	"DELIVERED",
}

type AccountTypeValue string
type accountTypeEnt struct {
	CUSTOMER AccountTypeValue
	EMPLOYEE AccountTypeValue
	VENDOR   AccountTypeValue
	SELLER   AccountTypeValue
}

var AccountType = &accountTypeEnt{
	"CUSTOMER",
	"EMPLOYEE",
	"VENDOR",
	"SELLER",
}

type SessionStatusValue string
type sessionStatusEnt struct {
	ACTIVE  SessionStatusValue
	EXPIRED SessionStatusValue
}

var SessionStatus = &sessionStatusEnt{
	"ACTIVE",
	"EXPIRED",
}

type EmailStatusValue string
type emailStatusEnt struct {
	NEW      EmailStatusValue
	VERIFIED EmailStatusValue
}

// EmailStatus ...
var EmailStatus = emailStatusEnt{
	NEW:      "NEW",
	VERIFIED: "VERIFIED",
}
