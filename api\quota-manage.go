package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetQuotaList ...
func GetQuotaList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		sort     = req.GetParam("sort")
	)

	// parse input
	var query model.QuotaManage
	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// add more complex query
	if len(query.VendorCodeIn) > 0 {
		query.VendorCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"vendor_code": bson.M{"$in": query.VendorCodeIn},
		})
	}

	// add more complex query
	if len(query.ProductIDIn) > 0 {
		query.ProductID = 0
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{"$in": query.ProductIDIn},
		})
	}

	// add more complex query
	if len(query.WarehouseCodeIn) > 0 {
		query.WarehouseCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"warehouse_code": bson.M{"$in": query.WarehouseCodeIn},
		})
	}

	// add more complex query
	if len(query.CICAccountVendorIn) > 0 {
		query.CICAccountVendor = nil
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"cic_account_vendor": bson.M{"$in": query.CICAccountVendorIn},
		})
	}

	// add more complex query
	if len(query.CICAccountProductIn) > 0 {
		query.CICAccountProduct = nil
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"cic_account_product": bson.M{"$in": query.CICAccountProductIn},
		})
	}

	// add more complex query
	if query.IDFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": query.IDFrom,
		})
	}

	// add more complex query
	if query.IDTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": query.IDTo,
		})
	}

	//do action
	return resp.Respond(action.GetQuotaList(&query, offset, limit, getTotal, sort))
}

// CreateQuota ...
func CreateQuota(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.QuotaManage
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ProductID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Product ID is required or must have positive",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if *input.Quota < 0 || input.Quota == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quota is required or must have positive",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Period == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Period type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}
	// check exits
	checkExits := model.QuotaManageDB.QueryOne(model.QuotaManage{
		ProductID:     input.ProductID,
		VendorCode:    input.VendorCode,
		Period:        input.Period,
		WarehouseCode: input.WarehouseCode,
	})
	if checkExits.Status == common.APIStatus.Ok {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "Quota existed",
			ErrorCode: "EXISTED",
		})
	}

	// do action
	return resp.Respond(action.CreateQuota(input))
}

// UpdateQuota
func UpdateQuota(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.QuotaManage
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ProductID <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Product ID is required or must have positive",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Quota == nil || *input.Quota < 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Quota is required or must have positive",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Period == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Period type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// check exits
	checkExits := model.QuotaManageDB.QueryOne(model.QuotaManage{
		ProductID:     input.ProductID,
		VendorCode:    input.VendorCode,
		Period:        input.Period,
		WarehouseCode: input.WarehouseCode,
	})

	// check exits
	if checkExits.Status == common.APIStatus.Ok {
		dataQuota := checkExits.Data.([]*model.QuotaManage)[0]
		if dataQuota.ProductID != input.ProductID || dataQuota.VendorCode != input.VendorCode || dataQuota.Period != input.Period || dataQuota.WarehouseCode != input.WarehouseCode {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Existed,
				Message:   "Quota existed",
				ErrorCode: "EXISTED",
			})
		}
	}

	// do action
	return resp.Respond(action.UpdateQuota(input))
}

// CalculateActualReceiptQty ...
func CalculateActualReceiptQty(_ sdk.APIRequest, resp sdk.APIResponder) error {
	go action.CalculateActualReceiptQty()
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing...",
	})
}

// DeleteQuota
func DeleteQuota(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		code = req.GetParam("code")
	)

	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid quota Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.DeleteQuota(code))
}
