package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// CreatePIC ...
func CreatePIC(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PIC

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.CreatePIC(input))
}

// UpdatePIC ...
func UpdatePIC(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PIC

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.UpdatePIC(input))
}

// DeletePIC ...
func DeletePIC(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		objectCode    = req.GetParam("objectCode")
		objectType    = req.GetParam("objectType")
		warehouseCode = req.GetParam("warehouseCode")
	)

	// validate input
	if objectCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid ObjectCode",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// validate input
	if warehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Warehouse code",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}	
	
	// do action
	return resp.Respond(action.DeletePIC(objectCode, enum.ObjectTypeValue(objectType), warehouseCode))
}

// GetPICList ...
func GetPICList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.PIC
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetPICList(&query, offset, limit, getTotal))
}
