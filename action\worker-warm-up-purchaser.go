package action

import (
	"fmt"
	"log"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// function init purchaser when DB connected
func WarmupWarehouseAndPurchaser() {
	// warm up warehouses first
	warmupWarehouses()
	// warm up purchaser 2nd
	warmupPurchasers()

	warmupWarehousesModel()
}

// warmupWarehouses retrieves a list of active warehouses with the group code "BUYMED" 
// from the warehouse management system (WMS) and updates the global warehouse list.
//
// It first sets up the API options with a limit of 100 and filters for active warehouses 
// with the group code "BUYMED". It then calls the GetWarehouse function to get the 
// warehouse data. If the API call is not successful, it logs a fatal error and returns.
//
// If the API call is successful, it processes the returned warehouse data, converting 
// it into a list of utils.Warehouse objects. Finally, it updates the global warehouse 
// list with the new data.
func warmupWarehouses() {
	option := client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]string{
			"status":    "ACTIVE",
			"groupCode": "BUYMED",
		},
	}
	getWMSResp := warehouse.GetWarehouse(option)
	if getWMSResp.Status != common.APIStatus.Ok {
		log.Fatal("getWMSResp.Status != common.APIStatus.Ok")
		return
	}

	// process data
	var (
		listWarehouses = make([]utils.Warehouse, 0)
	)

	// convert data
	warehouses := getWMSResp.Data.([]*model.Warehouse)
	for _, warehouse := range warehouses {
		listWarehouses = append(listWarehouses, utils.Warehouse{
			ID:   int64(warehouse.WarehouseID),
			Code: warehouse.Code,
		})
	}

	// reset global warehouses
	utils.SetWarehouses(listWarehouses)
}

// warmupWarehousesModel retrieves a list of active warehouses from the warehouse management system (WMS)
func warmupWarehousesModel() {
	option := client.APIOption{
		Limit: utils.Pointer.WithInt(100),
		Q: map[string]string{
			"status": "ACTIVE",
		},
	}
	getWMSResp := warehouse.GetWarehouse(option)
	if getWMSResp.Status != common.APIStatus.Ok {
		log.Fatal("getWMSResp.Status != common.APIStatus.Ok")
		return
	}
	warehouses := getWMSResp.Data.([]*model.Warehouse)

	// reset global warehouses
	utils.SetWarehousesModel(warehouses)
}

// function refetch and modified cache purchaser
func warmupPurchasers() {
	option := client.APIOption{
		Params: map[string]string{
			"sellerClass": model.CLASS_PURCHASER,
			"sellerCode":  "",
		},
		Keys: []string{model.CLASS_PURCHASER},
	}

	purchaserResp := seller.GetSellers(option)

	if purchaserResp.Status != common.APIStatus.Ok {
		log.Fatal("purchaserResp.Status != common.APIStatus.Ok")
		return
	}

	// process data
	var (
		purchasersData               = purchaserResp.Data.([]*model.Seller)
		tempPurchaserCodes           = make([]string, 0)
		tempMapWarehouseToPurchaser  = make(map[utils.Warehouse]string)
		tempMapPurchaserToWarehouses = make(map[string][]utils.Warehouse)
	)

	for _, purchaser := range purchasersData {
		// purchaser code bắt buộc
		if purchaser.Code == "" {
			return
		}
		// list purchaserCodes
		tempPurchaserCodes = append(tempPurchaserCodes, purchaser.Code)

		// map from purchasers
		for _, wmsID := range purchaser.WarehouseIDs {
			wms, ok := utils.SelectWMSByID(wmsID)
			if !ok {
				// wmsID có thể chưa hỗ trợ hoặc không tồn tại
				continue
			}
			tempMapWarehouseToPurchaser[wms] = purchaser.Code
			tempMapPurchaserToWarehouses[purchaser.Code] = append(tempMapPurchaserToWarehouses[purchaser.Code], wms)
		}

		if len(tempMapWarehouseToPurchaser) == 0 || len(tempMapPurchaserToWarehouses) == 0 {
			log.Fatal("len(tempMapWarehouseToPurchaser) == 0 || len(tempMapPurchaserToWarehouses)")
			return
		}
	}

	//reset global purchaser_codes
	utils.SetPurchaserCodes(tempPurchaserCodes)
	// reset global mapWarehouseCodePurchaser
	utils.SetMapWarehouseToPurchaser(tempMapWarehouseToPurchaser)
	// reset global mapPurchaserToWarehouseCode
	utils.SetMapPurchaserToWarehouses(tempMapPurchaserToWarehouses)

	// log
	fmt.Println(tempMapWarehouseToPurchaser)
	fmt.Println(tempMapPurchaserToWarehouses)
	fmt.Println(utils.WAREHOUSES)
}
