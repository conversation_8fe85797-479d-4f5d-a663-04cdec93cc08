package api

import (
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetSellerQuotationPOHistory
func GetSellerQuotationPOHistory(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal      = req.GetParam("getTotal") == strconv.FormatBool(true)
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
		qStr          = req.GetParam("q")
	)

	// validate input
	var query model.QuotationPOHistory
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	query.PurchaserCode = purchaserCode
	query.SellerCode = sellerCode
	// do action
	return resp.Respond(action.GetSellerQuotationPOHistory(query, offset, limit, getTotal))
}
