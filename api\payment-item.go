package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetPaymentItem ...
func GetPaymentItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// parse input
	var query model.PaymentItemPlatform
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	qOption := model.QueryOption{
		Total: getTotal,
	}
	// do action
	return resp.Respond(action.GetPaymentItem(query, offset, limit, qOption))
}

// AddPaymentItem
func AddPaymentItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input *model.PaymentItemPlatform
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		input.CreatedByID = acc.AccountID
		input.CreatedByName = acc.Fullname
		return resp.Respond(action.AddPaymentItem(input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// CancelPaymentItem ...
func CancelPaymentItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.PaymentItemPlatform
	)

	// parse input
	if err := json.Unmarshal([]byte(qStr), &query); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CancelPaymentItem(query.PaymentItemCode))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// DeletePaymentItem ...
func DeletePaymentItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.PaymentItemPlatform
	)

	// parse input
	if err := json.Unmarshal([]byte(qStr), &query); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data." + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.DeletePaymentItem(query.PaymentItemCode))
}
