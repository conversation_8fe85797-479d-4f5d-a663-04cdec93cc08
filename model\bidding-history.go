package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type BiddingHistory struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Version     string `json:"version,omitempty" bson:"version,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	PurchaserCode         string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	WarehouseCode         string `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	DeliveryWarehouseCode string `json:"deliveryWarehouseCode" bson:"delivery_warehouse_code,omitempty"`

	SellerCode string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	UnitPrice         float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"` // without vat
	VAT               *int64  `json:"vat,omitempty" bson:"vat,omitempty"`
	UnitPriceAfterVAT float64 `json:"unitPriceAfterVat,omitempty" bson:"unit_price_after_vat,omitempty"`

	// FE view
	QuantityBidding *int64 `json:"quantityBidding,omitempty" bson:"quantity_bidding,omitempty"`
	QuantityExpect  int64  `json:"quantityExpect,omitempty" bson:"quantity_expect,omitempty"`

	ComplexQuery []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedFrom  *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo    *time.Time `json:"createdTo,omitempty" bson:"-"`

	ProductIDIn []*int64 `json:"productIDIn,omitempty" bson:"-"`
}

// BiddingHistoryDB ...
var BiddingHistoryDB = &db.Instance{
	ColName:        "bidding_history",
	TemplateObject: &BiddingHistory{},
}

func InitBiddingHistoryModel(s *mongo.Database) {
	BiddingHistoryDB.ApplyDatabase(s)

	// t := true
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// t := true
	// // FE supplier bidding history
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// var second int32 = 60 * 60 * 24 * 14 // 14 ngày
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &second,
	// })

	// // FE supplier bidding history
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = BiddingHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
