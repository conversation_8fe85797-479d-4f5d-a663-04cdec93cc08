package model

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Product ...
type Product struct {
	ProductID int64 `json:"productID,omitempty"`

	Code                string   `json:"code,omitempty" bson:"code,omitempty"`
	CreateBy            *string  `json:"createBy,omitempty" bson:"create_by,omitempty"`
	CategoryCodes       []string `json:"categoryCodes,omitempty" bson:"category_codes,omitempty"`
	Name                string   `json:"name,omitempty" bson:"name,omitempty"`
	Unit                string   `json:"unit,omitempty" bson:"unit,omitempty"`
	Origin              string   `json:"origin,omitempty" bson:"origin,omitempty"`
	Vat                 *int64   `json:"vat,omitempty" bson:"vat,omitempty"`
	IsIngredientSpecial *bool    `json:"isIngredientSpecial,omitempty" bson:"is_ingredient_special,omitempty"`

	SellerCategoryCode    string `json:"sellerCategoryCode,omitempty" bson:"seller_category_code,omitempty"`
	SellerSubCategoryCode string `json:"sellerSubCategoryCode,omitempty" bson:"seller_sub_category_code,omitempty"`
}

// ========================================================================

// Sku ...
type Sku struct {
	// basic info
	SKU           string               `json:"sku,omitempty" bson:"sku,omitempty"` // sku code
	ProductCode   string               `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID     int64                `json:"productID,omitempty" bson:"product_id,omitempty"`
	SellerCode    string               `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	PurchaserCode string               `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	Status        *enum.SkuStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	LotDates      []LotDates           `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	SellerClass   string               `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
}

type LotDates struct {
	IsNearExpired bool   `json:"isNearExpired" bson:"is_near_expired,omitempty"`
	ExpiredDate   string `json:"expiredDate" bson:"expired_date,omitempty"`
}

// ========================================================================

// ErpVendorBill ...
type ErpVendorBill struct {
	WarehouseCode   string              `json:"warehouseCode"`
	SellerCode      string              `json:"sellerCode"`
	POID            int64               `json:"poID"`
	VendorBillItems []ErpVendorBillItem `json:"vendorBillItems"`

	VendorBillID     int64  `json:"vendorBillId"`
	VendorBillStatus string `json:"vendorBillStatus"`
}

type ErpVendorBillItem struct {
	ProductID int64    `json:"productID"`
	UnitPrice *float64 `json:"unitPrice"`
	VAT       float64  `json:"vat"`

	PriceAfterDiscount *float64 `json:"priceAfterDiscount"`
	DiscountPercent    *float64 `json:"discountPercent"`
	// DiscountAmount     *int64  `discountAmount:"vat" bson:"-"`
	// Quantity           int64   `json:"quantity" bson:"-"`
	// AmountTax          int64   `json:"amountTax" bson:"-"`
}

const (
	CLASS_PURCHASER = "PURCHASER"
	CLASS_VENDOR    = "VENDOR"
	CLASS_INTERNAL  = "INTERNAL"
)

type Seller struct {
	Code                string    `json:"code,omitempty" bson:"code,omitempty"`
	SellerID            int64     `json:"sellerID,omitempty" bson:"seller_id,omitempty"`
	AccountID           int64     `json:"accountID,omitempty" bson:"account_id,omitempty"`
	Avatar              *string   `json:"avatar,omitempty" bson:"avatar,omitempty"`
	Name                string    `json:"name,omitempty" bson:"name,omitempty"`
	DisplayName         string    `json:"displayName,omitempty" bson:"display_Name,omitempty"`
	Email               string    `json:"email,omitempty" bson:"email,omitempty"`
	AccountantEmail     string    `json:"accountantEmail,omitempty" bson:"accountant_email,omitempty"`
	LoginEmail          string    `json:"-" bson:"login_email,omitempty"`
	Username            string    `json:"username,omitempty" bson:"username,omitempty"`
	Address             string    `json:"address,omitempty" bson:"address,omitempty"`
	Slug                string    `json:"slug,omitempty" bson:"slug,omitempty"`
	Phone               string    `json:"phone,omitempty" bson:"phone,omitempty"`
	LoginPhone          string    `json:"-" bson:"login_phone,omitempty"`
	Status              string    `json:"status,omitempty" bson:"status,omitempty"`
	ProvinceCode        string    `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	DistrictCode        string    `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	WardCode            string    `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	Tax                 string    `json:"tax,omitempty" bson:"tax,omitempty"`
	LegalRepresentative string    `json:"legalRepresentative,omitempty" bson:"legal_representative,omitempty"`
	BusinessLicense     string    `json:"businessLicense,omitempty" bson:"business_license,omitempty"`
	IdentityImages      *[]string `json:"identityImages,omitempty" bson:"identity_images,omitempty"`
	LineManager         string    `json:"lineManager,omitempty" bson:"line_manager,omitempty"`

	// UseManagerForPriceAfterRebate bool `json:"useManagerForPriceAfterRebate,omitempty" bson:"use_manager_for_price_after_rebate,omitempty"`

	SellerType enum.SellerType `json:"sellerType,omitempty" bson:"seller_type,omitempty"`

	LocationCodes  []string `json:"locationCodes,omitempty" bson:"location_codes,omitempty"`
	MigratedFromV1 bool     `json:"-" bson:"migrated_from_v1,omitempty"`
	WarehouseIDs   []int64  `json:"warehouseIDs,omitempty" bson:"warehouse_ids,omitempty"`
	AutoApprove    *bool    `json:"autoApprove,omitempty" bson:"auto_approve,omitempty"`
	SellerClass    string   `json:"sellerClass,omitempty" bson:"seller_class,omitempty"`
	Note           string   `json:"note,omitempty" bson:"note,omitempty"`

	FiscalPosition         *float64              `json:"fiscalPosition,omitempty" bson:"fiscal_position,omitempty"`
	PaymentTerm            *float64              `json:"paymentTerm,omitempty" bson:"payment_term,omitempty"`
	SellerWarehouseAddress string                `json:"sellerWarehouseAddress,omitempty" bson:"seller_warehouse_address,omitempty"`
	MinOrderAmount         *float64              `json:"minOrderAmount,omitempty" bson:"min_order_amount,omitempty"`
	InboundAddresses       []InboundAddress      `json:"inboundAddresses,omitempty" bson:"inbound_addresses,omitempty"`
	FirstMileBy            enum.FirstMileByValue `json:"firstMileBy,omitempty" bson:"first_mile_by,omitempty"`
	FirstMileConfig        *[]*FirstMileConfig   `json:"firstMileConfig,omitempty" bson:"first_mile_config,omitempty"`

	// message
	IsActiveZNS           *bool     `json:"isActiveZNS,omitempty" bson:"is_active_zns,omitempty"`
	ReceivedMessagePhones *[]string `json:"receivedMessagePhones,omitempty" bson:"received_message_phones,omitempty"`
	SystemIntegrateSFTP   []string  `json:"systemIntegrateSFTP,omitempty" bson:"system_integrate_sftp,omitempty"`
	EntityID              int64     `json:"entityID,omitempty" bson:"entity_id,omitempty"`

	// vendor with erp
	VendorCodeRefs []string `json:"vendorCodeRefs,omitempty" bson:"vendor_code_refs,omitempty"`

	PaymentInfo *PaymentInfo `json:"paymentInfo,omitempty" bson:"-"`
}

type PaymentInfo struct {
	SellerID                  int64  `json:"sellerID,omitempty" bson:"seller_id,omitempty"`
	SellerCode                string `json:"sellerCode" bson:"seller_code,omitempty"`
	BankCode                  string `json:"bankCode" bson:"bank_code,omitempty"`
	BankID                    int64  `json:"bankId,omitempty" bson:"bank_id,omitempty"`
	BankBranchName            string `json:"bankBranchName" bson:"bank_branch_name,omitempty"`
	BankAccountName           string `json:"bankAccountName" bson:"bank_account_name,omitempty"`
	BankAccountNumber         string `json:"bankAccountNumber" bson:"bank_account_number,omitempty"`
	ProvinceCode              string `json:"provinceCode" bson:"province_code,omitempty"`
	VendorNameUnmark          string `json:"vendorNameUnmark" bson:"vendor_name_unmark,omitempty"`
	ParentVendorCode          string `json:"parentVendorCode" bson:"parent_vendor_code,omitempty"`
	HasPaymentForParentVendor bool   `json:"hasPaymentForParentVendor" bson:"has_payment_for_parent_vendor,omitempty"`

	//
	BankName     string `json:"bankName" bson:"bank_name,omitempty"`
	GlobalBankID int64  `json:"globalBankID,omitempty" bson:"global_bank_id,omitempty"`
}

type FirstMileTicket struct {
	POCode              string `json:"poCode" bson:"-"`
	FirstMileTicketCode string `json:"firstMileTicketCode" bson:"-"`
	ParentReferenceCode string `json:"parentReferenceCode,omitempty" bson:"-"`
	Status              string `json:"status,omitempty" bson:"-"`
	ActionBy            string `json:"actionBy,omitempty" bson:"-"`
	DefaultHubCode      string `json:"defaultHubCode,omitempty" bson:"-"`
}
type FirstMileConfig struct {
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	IsAutoCreate  *bool  `json:"isAutoCreate,omitempty" bson:"is_auto_create,omitempty"`
}

type InboundAddress struct {
	WarehouseCode string  `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	WarehouseID   int     `json:"warehouseID,omitempty" bson:"warehouse_id,omitempty"`
	Address       string  `json:"address,omitempty" bson:"address,omitempty"`
	Leadtime      float64 `json:"leadtime" bson:"leadtime,omitempty"`
}

type SaleOrder struct {
	AdminID       int64  `json:"adminId,omitempty" bson:"admin_id,omitempty"`
	SaleOrderCode string `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Status        string `json:"status,omitempty" bson:"status,omitempty"`

	TotalAmount           float64 `json:"totalAmount,omitempty" bson:"total_amount,omitempty"`
	CODAmount             float64 `json:"codAmount,omitempty" bson:"cod_amount,omitempty"`
	TotalAmountWithoutVat float64 `json:"totalAmountWithoutVat,omitempty" bson:"total_amount_without_vat,omitempty"`
	VatAmount             float64 `json:"vatAmount,omitempty" bson:"vat_amount,omitempty"`

	// Statuses []string `json:"statuses,omitempty"`

	// CreatedBy     string `json:"createdBy,omitempty" bson:"created_by,omitempty"`
	// UpdatedBy     string `json:"updatedBy,omitempty" bson:"updated_by,omitempty"`
	// ID            primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	// CollectOnDelivery *bool                    `json:"collectOnDelivery,omitempty" bson:"collect_on_delivery,omitempty"`
	// CreatedTime       *time.Time               `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	// LastUpdatedTime   *time.Time               `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	// CustomerInfos     *CustomerSaleOrder       `json:"customer,omitempty" bson:"customer,omitempty"`
	// VersionNo         string                   `json:"versionNo,omitempty" bson:"version_no,omitempty"`
	// Priority          int64                    `json:"priority,omitempty" bson:"priority,omitempty"`
	// SaleOrderTime     int64                    `json:"saleOrderTime,omitempty" bson:"sale_order_time,omitempty"`
	// PaymentMethod     *enum.PaymentMethodValue `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`
	// PaymentMethodName string                   `json:"paymentMethodName,omitempty" bson:"payment_method_name,omitempty"`
	// OrderLines        []*model.SaleOrderItem   `json:"orderLines,omitempty" bson:"-,omitempty"`
	// Logs              []*model.SaleOrderLog    `json:"logs" bson:"logs,omitempty"`
	// TrackingCode      string                   `json:"trackingCode,omitempty" bson:"tracking_code,omitempty"`
	// TplCode           string                   `json:"tplCode,omitempty" bson:"tpl_code,omitempty"`
	// Sort              float64                  `json:"sort,omitempty" bson:"sort,omitempty"`
}

// Order ...
type Order struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	OrderID   int64  `json:"orderId,omitempty" bson:"order_id,omitempty"` //
	OrderCode string `json:"orderCode" bson:"order_code,omitempty"`       //

	// customer info
	AccountID               int64   `json:"accountId,omitempty" bson:"account_id,omitempty"`                              // mã tài khoản
	CustomerID              int64   `json:"customerId,omitempty" bson:"customer_id,omitempty"`                            // mã khách hàng
	CustomerCode            string  `json:"customerCode,omitempty" bson:"customer_code,omitempty"`                        // mã khách hàng
	CustomerName            string  `json:"customerName,omitempty" bson:"customer_name,omitempty"`                        // tên người nhận
	CustomerPhone           string  `json:"customerPhone,omitempty" bson:"customer_phone,omitempty"`                      // điện thoại người nhận
	CustomerEmail           *string `json:"customerEmail,omitempty" bson:"customer_email,omitempty"`                      // email người nhận
	CustomerShippingAddress string  `json:"customerShippingAddress,omitempty" bson:"customer_shipping_address,omitempty"` // địa chỉ người nhận
	CustomerDistrictCode    string  `json:"customerDistrictCode,omitempty" bson:"customer_district_code,omitempty"`       // khu vực nhận
	CustomerWardCode        string  `json:"customerWardCode,omitempty" bson:"customer_ward_code,omitempty"`               //
	CustomerProvinceCode    string  `json:"customerProvinceCode,omitempty" bson:"customer_province_code,omitempty"`       //
	CustomerOrderIndex      int64   `json:"customerOrderIndex,omitempty" bson:"customer_order_index,omitempty"`           //

	ProvinceCode string `json:"provinceCode,omitempty" bson:"province_code,omitempty"` //
	RegionCode   string `json:"regionCode,omitempty" bson:"region_code,omitempty"`     //

	// payment & delivery
	PaymentMethod          string     `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"`                    // phương thức thanh toán cod/chuyển khoản
	PaymentMethodFee       *float64   `json:"paymentMethodFee,omitempty" bson:"payment_method_fee,omitempty"`             // phí phương thức thanh toán cod/chuyển khoản
	DeliveryMethod         string     `json:"deliveryMethod,omitempty" bson:"delivery_method,omitempty"`                  // hình thức giao hàng
	DeliveryMethodFee      *float64   `json:"deliveryMethodFee,omitempty" bson:"delivery_method_fee,omitempty"`           // phí hình thức giao hàng
	DeliveryStatus         string     `json:"deliveryStatus,omitempty" bson:"delivery_status,omitempty"`                  // trạng thái nhà vận chuyển: đang lấy,...
	DeliveryTrackingNumber string     `json:"deliveryTrackingNumber,omitempty" bson:"delivery_tracking_number,omitempty"` // mã tracking
	DeliveryDate           *time.Time `json:"deliveryDate,omitempty" bson:"delivery_date,omitempty"`                      // ngày giao mong muốn
	ExtraFee               *float64   `json:"extraFee,omitempty" bson:"extra_fee,omitempty"`                              // phụ phí

	// price
	TotalActualPrice *float64 `json:"totalActualPrice,omitempty" bson:"total_actual_price,omitempty"`
	ActualPrice      *float64 `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	TotalPrice       *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`       // tổng tiền đơn hàng sau cùng
	Price            *float64 `json:"price,omitempty" bson:"price,omitempty"`                  // tổng tiển chưa trừ các khoản khác
	TotalDiscount    *float64 `json:"totalDiscount,omitempty" bson:"total_discount,omitempty"` // tổng số tiền được giảm
	TotalFee         *float64 `json:"totalFee,omitempty" bson:"total_fee,omitempty"`           // tổng phí

	// order info
	ConfirmationDate *time.Time `json:"confirmationDate,omitempty" bson:"confirmation_date,omitempty"` // ngày xác nhận -- field cũ
	CompletedTime    *time.Time `json:"completedTime,omitempty" bson:"completed_time,omitempty"`       // thời gian hoàn tất đơn hàng -- status = completed
	Source           *string    `json:"source,omitempty" bson:"source,omitempty"`                      // nguồn đơn hàng (web/mobile)
	Status           string     `json:"status,omitempty" bson:"status,omitempty"`                      // trạng thái đơn hàng
	Note             *string    `json:"note,omitempty" bson:"note,omitempty"`                          // ghi chú đơn hàng
	PrivateNote      string     `json:"privateNote,omitempty" bson:"private_note,omitempty"`           // ghi chú nội bộ đơn hàng
	SaleOrderCode    string     `json:"saleOrderCode,omitempty" bson:"sale_order_code,omitempty"`      // so
	ConfirmType      string     `json:"confirmType,omitempty" bson:"confirm_type,omitempty"`           // xác nhận đơn hàng
	HasDeal          bool       `json:"hasDeal,omitempty" bson:"has_deal,omitempty"`

	// promotion
	RedeemCode *[]*string `json:"redeemCode,omitempty" bson:"redeem_code,omitempty"` // mã giảm giá

	// //statistic
	// TotalItem     *int64 `json:"totalItem,omitempty" bson:"total_item,omitempty"`
	// TotalQuantity *int64 `json:"totalQuantity,omitempty" bson:"total_quantity,omitempty"`
	// Point         *float64 `json:"point,omitempty" bson:"point,omitempty"` // point = actual price / 100.000

	//Invoice *InvoiceRequest `json:"invoice,omitempty" bson:"invoice,omitempty"`
	StatusIn []enum.OrderStateValue `json:"statusIn,omitempty" bson:"-"`
}

// OrderItem ...
type OrderItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	// reference data
	OrderID           int64   `json:"orderId,omitempty" bson:"order_id,omitempty"`
	OrderCode         string  `json:"orderCode,omitempty" bson:"order_code,omitempty"`
	Sku               string  `json:"sku,omitempty" bson:"sku,omitempty"`
	Quantity          int64   `json:"quantity,omitempty" bson:"quantity,omitempty"`
	ReservedQuantity  *int64  `json:"reservedQuantity,omitempty" bson:"reserved_quantity,omitempty"`
	OutboundQuantity  *int64  `json:"outboundQuantity,omitempty" bson:"outbound_quantity,omitempty"`
	CompletedQuantity *int64  `json:"completedQuantity,omitempty" bson:"completed_quantity,omitempty"`
	MaxQuantity       int64   `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`
	IsImportant       *bool   `json:"isImportant,omitempty" bson:"is_important,omitempty"`
	Type              string  `json:"type,omitempty" bson:"type,omitempty"`
	DealCode          *string `json:"dealCode,omitempty" bson:"deal_code,omitempty"`
	SellerCode        string  `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	ProductCode       string  `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID         int64   `json:"productID,omitempty" bson:"product_id,omitempty"`
	Price             float64 `json:"price,omitempty" bson:"price,omitempty,omitempty"`
	SellerPrice       float64 `json:"sellerPrice,omitempty" bson:"seller_price,omitempty"`
	TotalPrice        float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	ActualPrice       float64 `json:"actualPrice,omitempty" bson:"actual_price,omitempty"`
	TotalActualPrice  float64 `json:"totalActualPrice,omitempty" bson:"total_actual_price,omitempty"`
	//Fee               *FeesApply           `json:"fee,omitempty" bson:"fee,omitempty"`
	VAT *float64 `json:"vat,omitempty" bson:"vat,omitempty"`
	//Skus              *[]*SubSku           `json:"-" bson:"skus,omitempty"`                                   // save for combo
	SkuExpiredDate *time.Time `json:"skuExpiredDate,omitempty" bson:"skuexpired_date,omitempty"` // expired date
	SkuStatus      string     `json:"skuStatus,omitempty" bson:"sku_status,omitempty"`           // view web

	SubItems []*OrderItem `json:"subItems,omitempty" bson:"sub_items,omitempty"`
}

type SKULocationRequest struct {
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	LocationCode  string `json:"locationCode,omitempty" bson:"location_code,omitempty"`
	SKU           string `json:"sku,omitempty" bson:"sku,omitempty"`
}

type GetQuotationProductVendorPayload struct {
	Query    QuotationProductVendor `json:"q,omitempty" bson:"-"`
	Offset   int64                  `json:"offset,omitempty" bson:"-"`
	Limit    int64                  `json:"limit,omitempty" bson:"-"`
	GetTotal bool                   `json:"getTotal,omitempty" bson:"-"`
}

type GetQuotationProductPayload struct {
	Query    QuotationProduct `json:"q,omitempty" bson:"-"`
	Offset   int64            `json:"offset,omitempty" bson:"-"`
	Limit    int64            `json:"limit,omitempty" bson:"-"`
	GetTotal bool             `json:"getTotal,omitempty" bson:"-"`
	Sort     string           `json:"sort,omitempty" bson:"-"`
}

type GetVendorQuotationPayload struct {
	Query         Quotation `json:"q,omitempty" bson:"-"`
	Offset        int64     `json:"offset,omitempty" bson:"-"`
	Limit         int64     `json:"limit,omitempty" bson:"-"`
	GetTotal      bool      `json:"getTotal,omitempty" bson:"-"`
	IsOnlyVendor  bool      `json:"isOnlyVendor,omitempty" bson:"-"`
	IsLastUpdated bool      `json:"isLastUpdated,omitempty" bson:"-"`
	IsMarket      bool      `json:"isMarket,omitempty" bson:"-"`
}

type GetSellerQuotationPayload struct {
	Offset     int64     `json:"offset,omitempty" bson:"-"`
	Limit      int64     `json:"limit,omitempty" bson:"-"`
	GetTotal   bool      `json:"getTotal,omitempty" bson:"-"`
	Query      Quotation `json:"q,omitempty" bson:"-"`
	Sort       string    `json:"sort,omitempty" bson:"-"`
	IsCountAll bool      `json:"isCountAll,omitempty" bson:"-"`
	Filter     string    `json:"filter,omitempty" bson:"-"`
	Search     string    `json:"search,omitempty" bson:"-"`
}

type CreatePOFromBiddingInput struct {
	PO PurchaseOrder `json:"po"`
}

type BiddingQuotationInput struct {
	Items  []Bidding `json:"items"`
	Vendor *Seller   `json:"vendor"`
}

type ZNSMessage struct {
	Topic    string `json:"topic,omitempty" bson:"topic,omitempty"`
	Source   string `json:"source,omitempty" bson:"source,omitempty"`
	Receiver string `json:"receiver,omitempty" bson:"receiver,omitempty"`

	// template
	UseTemplate bool                   `json:"useTemplate,omitempty" bson:"use_template,omitempty"`
	Dictionary  map[string]interface{} `json:"dictionary,omitempty" bson:"dictionary,omitempty"`

	// job
	ReadyTime time.Time `json:"readyTime,omitempty" bson:"ready_time,omitempty"`
	Keys      []string  `json:"keys,omitempty" bson:"-"`
}

type SearchInput struct {
	Model  string            `json:"model"`
	Offset int64             `json:"offset"`
	Limit  int64             `json:"limit"`
	Text   string            `json:"text"`
	Filter map[string]string `json:"filter,omitempty"`
}

type UpdateProductContentRequest struct {
	Model  string            `json:"model"`
	Key    string            `json:"key"` // quotation.ProductCode
	Text   string            `json:"text"`
	Filter map[string]string `json:"filter"`
}

type UpdateMultiProductRequest struct {
	Model     string                         `json:"model"`
	RootModel string                         `json:"rootModel"`
	Documents []*UpdateProductContentRequest `json:"documents"`
}

type Notification struct {
	ID              *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty" `
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code         string                 `json:"code,omitempty" bson:"code,omitempty" `
	UserId       int64                  `json:"userId,omitempty" bson:"user_id,omitempty" `
	Username     string                 `json:"username,omitempty" bson:"username,omitempty"`
	ReceiverType *enum.AccountTypeValue `json:"receiverType,omitempty" bson:"receiver_type,omitempty"`
	IsRead       *bool                  `json:"isRead,omitempty" bson:"is_read,omitempty" `

	Topic       string `json:"topic,omitempty" bson:"topic,omitempty"`
	Title       string `json:"title,omitempty" bson:"title,omitempty"`
	Description string `json:"description,omitempty" bson:"description,omitempty" `
	Link        string `json:"link,omitempty" bson:"link,omitempty" `
	EntityID    int64  `json:"entityID,omitempty" bson:"entity_id,omitempty" `

	Owner string `json:"owner,omitempty" bson:"owner,omitempty"`
}

type SKUItem struct {
	// basic info
	SKU              string               `json:"sku,omitempty"` // sku code
	ProductCode      string               `json:"productCode,omitempty"`
	ProductID        int64                `json:"productID,omitempty"`
	SellerCode       string               `json:"sellerCode,omitempty"`
	PurchaserCode    string               `json:"purchaserCode,omitempty"`
	Status           *enum.SkuStatusValue `json:"status,omitempty"`
	LotDates         []LotDates           `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	SellerClass      string               `json:"seller_class,omitempty"`
	RetailPriceType  string               `json:"retailPriceType,omitempty"`
	RetailPriceValue int64                `json:"retailPriceValue,omitempty"`
	ItemCode         string               `json:"itemCode,omitempty"`

	// combo
	IsCombo *bool      `json:"isCombo,omitempty" bson:"is_combo,omitempty"`
	SKUs    *[]*SubSku `json:"skus,omitempty" bson:"skus,omitempty"`

	// location
	LocationCodes  *[]string `json:"locationCodes,omitempty" bson:"-"`
	LocationCodeIn []string  `json:"locationCodeIn,omitempty" bson:"-"`

	LocationWarehouse string `json:"locationWarehouse,omitempty" bson:"-"`
}

// SubSku sub sku and quantity of its
type SubSku struct {
	ItemCode    string `json:"itemCode" bson:"item_code,omitempty"`
	SKU         string `json:"sku" bson:"sku,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	Quantity    int    `json:"quantity" bson:"quantity,omitempty"`
}
type Warehouse struct {
	Address         string    `json:"address"`
	Area            int       `json:"area"`
	Areas           []string  `json:"areas"`
	Code            string    `json:"code"`
	CreatedBy       int       `json:"createdBy"`
	CreatedTime     time.Time `json:"createdTime"`
	DistrictCode    string    `json:"districtCode"`
	DistrictName    string    `json:"districtName"`
	LastUpdatedTime time.Time `json:"lastUpdatedTime"`
	Length          int       `json:"length"`
	ManagerID       int       `json:"managerId"`
	Name            string    `json:"name"`
	ProvinceName    string    `json:"provinceName"`
	Status          string    `json:"status"`
	Type            string    `json:"type"`
	UpdatedBy       int       `json:"updatedBy"`
	VersionNo       string    `json:"versionNo"`
	WardCode        string    `json:"wardCode"`
	WardName        string    `json:"wardName"`
	WarehouseID     int       `json:"warehouseId"`
	Width           int       `json:"width"`
}
type InboundTicket struct {
	WarehouseCode           string                  `json:"warehouseCode"`
	SellerCode              string                  `json:"sellerCode"`
	VendorCode              string                  `json:"vendorCode"`
	POID                    int64                   `json:"poId"`
	POCode                  string                  `json:"poCode"`
	InboundRefs             []string                `json:"inboundRefs"`
	Type                    string                  `json:"type"`
	ExternalType            string                  `json:"externalType"`
	Status                  enum.ReceiptStatusValue `json:"status"`
	EstimatedDeliveryTime   *time.Time              `json:"estimatedDeliveryTime,omitempty"`
	InboundTicketItems      []InboundTicketItem     `json:"inboundTicketItems"`
	PickupAtVendorWarehouse *bool                   `json:"pickupAtVendorWarehouse"`
	Tags                    *[]string               `json:"tags,omitempty" bson:"tags,omitempty"`
	SellerInfo              SellerInfo              `json:"sellerInfo,omitempty" bson:"-"`
}

type InboundTicketItem struct {
	LineID            int64    `json:"lineId"`
	ProductID         int64    `json:"productId"`
	ProductCode       string   `json:"productCode"`
	SKU               string   `json:"sku"`
	Name              string   `json:"name"`
	ImageURL          string   `json:"imageUrl"`
	Packaging         string   `json:"packaging"`
	VAT               *float64 `json:"vat"`
	DemandQuantity    int64    `json:"demandQuantity"`
	InvoiceExportable bool     `json:"invoiceExportable"`
	IsNearExp         *bool    `json:"isNearExp"`

	// sync
	InboundQuantity   int64  `json:"inboundQuantity"`
	AdminProductID    int64  `json:"adminProductId"`
	VendorProductName string `json:"vendorProductName"`

	// for unknown
	OldSKU  string `json:"oldSku"`
	ErrorID int64  `json:"errorId"`
}

type InboundTicketLotDate struct {
	RequestID     int64                      `json:"requestId"`
	CheckSystem   string                     `json:"checkSystem"`
	WarehouseCode string                     `json:"warehouseCode"`
	POCode        string                     `json:"poCode"`
	Items         []InboundTicketLotDateItem `json:"items"`
}

type InboundTicketLotDateItem struct {
	// ProductID   int64  `json:"productId"` // deprecated
	SKU         string `json:"sku"`
	Lot         string `json:"lot"`
	ExpiredDate string `json:"expiredDate"`
	Quantity    int64  `json:"quantity"`

	VendorProductName string `json:"vendorProductName"`
}

type InventorySkuLocation struct {
	SKU               string       `json:"sku,omitempty" bson:"sku,omitempty"`
	LocationCode      string       `json:"locationCode,omitempty" bson:"location_code,omitempty"`
	StockQuantity     int64        `json:"stockQuantity,omitempty" bson:"stock_quantity"`
	SKUInfo           InventorySku `json:"skuInfo,omitempty" bson:"sku_info"`
	WarehouseCode     string       `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Quantity          int64        `json:"quantity,omitempty" bson:"quantity,omitempty"`
	AvailableQuantity int64        `json:"availableQuantity,omitempty" bson:"available_quantity,omitempty"`
}

type InventorySku struct {
	Name        string `json:"name,omitempty"`
	Productcode string `json:"productCode,omitempty"`
	ProductID   int64  `json:"productId,omitempty"`
	Sku         string `json:"sku,omitempty"`

	// Imageurl  string `json:"imageUrl,omitempty"`
	// Keyword   string `json:"keyword,omitempty"`
	Packaging string `json:"packaging,omitempty"`

	AvailableQuantity      int64 `json:"availableQuantity,omitempty"`
	WaitingHoldQuantity    int64 `json:"waitingHoldQuantity,omitempty"`
	WaitingInboundQuantity int64 `json:"waitingInboundQuantity,omitempty"`
	// DemandQuantity    int64 `json:"demandQuantity,omitempty"`
	// MinQuantity       int64 `json:"minQuantity,omitempty"`
	// MaxQuantity       int64 `json:"maxQuantity,omitempty"`
	// MissingQuantity   int64 `json:"missingQuantity,omitempty"`
	// OnholdQuantity    int64 `json:"onHoldQuantity,omitempty"`
	// StockQuantity     int64 `json:"stockQuantity,omitempty"`

	// VersionNo string `json:"versionNo,omitempty"`

	WarehouseCode string `json:"warehouseCode,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty"`
}

const (
	INVENTORY_LOCATION_INBOUND_CODE = "WH-INBOUND"
)

type UploadFile struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// reference data
	UploadID   int64  `json:"-" bson:"upload_id,omitempty"`
	UploadCode string `json:"uploadCode" bson:"upload_code,omitempty"`

	// input
	Data     string `json:"data,omitempty" bson:"-"`
	FileName string `json:"fileName,omitempty" bson:"file_name,omitempty"`

	Link string `json:"link,omitempty" bson:"link,omitempty"` // unique

	Mime       string `json:"mime,omitempty" bson:"mime,omitempty"`              // application/pdf || application/vnd.openxmlformats-officedocument.wordprocessingml.document || application/msword
	FileType   string `json:"fileType,omitempty" bson:"file_type,omitempty"`     // DOCUMENT, IMAGE ...
	FileExtend string `json:"fileExtend,omitempty" bson:"file_extend,omitempty"` // jpeg, png | pdf, docx, doc ...

	RefType string `json:"refType,omitempty" bson:"ref_type,omitempty"` // Loại đối tượng tham chiếu, VD: PRODUCT, SKU, SELLER ...
	RefID   int    `json:"refID,omitempty" bson:"ref_id,omitempty"`     // ID của đối tượng tham chiếu, VD: product_id
	RefCode string `json:"refCode,omitempty" bson:"ref_code,omitempty"` // Code của đối tượng tham chiếu, VD: product_code

	CreateByID   int64  `json:"createByID,omitempty" bson:"create_by_id,omitempty"`
	CreateByName string `json:"createByName,omitempty" bson:"create_by_name,omitempty"`

	Token string `json:"token,omitempty" bson:"-"`
}

type RecalcPriceAfterRebate struct {
	PromotionCode     string `json:"promotionCode,omitempty" bson:"promotion_code,omitempty"`
	ContractPriceCode string `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	RebateCode        string `json:"rebateCode,omitempty" bson:"rebate_code,omitempty"`

	OldVersionFrom string `json:"oldVersionFrom,omitempty" bson:"old_version_from,omitempty"`
	OldVersionTo   string `json:"oldVersionTo,omitempty" bson:"old_version_to,omitempty"`

	NewVersionFrom string `json:"newVersionFrom,omitempty" bson:"new_version_from,omitempty"`
	NewVersionTo   string `json:"newVersionTo,omitempty" bson:"new_version_to,omitempty"`

	OldVendorCodes *[]string `json:"oldVendorCodes,omitempty" bson:"old_vendor_codes,omitempty"`
	NewVendorCodes *[]string `json:"newVendorCodes,omitempty" bson:"new_vendor_codes,omitempty"`

	OldWarehouseCodes []string `json:"oldWarehouseCodes,omitempty" bson:"old_warehouse_codes,omitempty"`
	NewWarehouseCodes []string `json:"newWarehouseCodes,omitempty" bson:"new_warehouse_codes,omitempty"`

	Source string `json:"source,omitempty" bson:"source,omitempty"`
}

type PriceAfterRebate struct {
	// meta
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	Version       string `json:"version,omitempty" bson:"version,omitempty"`

	// ref
	ContractPriceCode string `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	ContractPriceID   int64  `json:"contractPriceID,omitempty" bson:"contract_price_id,omitempty"`

	ProductID   int64  `json:"productId,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`

	// price after rebate
	PriceAfterRebate                   *float64 `json:"priceAfterRebate,omitempty" bson:"price_after_rebate,omitempty"`                                        // không khuyến mãi ad hoc
	PriceAfterRebateWithAdhocPromotion *float64 `json:"priceAfterRebateWithAdhocPromotion,omitempty" bson:"price_after_rebate_with_adhoc_promotion,omitempty"` // tính adhoc

	PriceContract *float64 `json:"priceContract,omitempty" bson:"price_contract,omitempty"`  // giá trên hợp đồng
	Vat           *float64 `json:"vat,omitempty" bson:"vat,omitempty"`                       // thuế VAT %
	Discount      *float64 `json:"discount,omitempty" bson:"discount,omitempty"`             // Discount %
	PriceAfterVAT *float64 `json:"priceAfterVAT,omitempty" bson:"price_after_vat,omitempty"` // giá sau thuế theo bảng giá

	// khuyến mãi %
	Rebate         *float64 `json:"rebate,omitempty" bson:"rebate,omitempty"`
	FixedPromotion *float64 `json:"fixedPromotion,omitempty" bson:"fixed_promotion,omitempty"`
	AdhocPromotion *float64 `json:"adhocPromotion,omitempty" bson:"adhoc_promotion,omitempty"`

	// Using for compare days
	DiffDay int `json:"diffDay,omitempty" bson:"-"`
}

type RefundBill struct {
	RefundBillID   int64  `json:"refundBillID,omitempty" bson:"refund_bill_id,omitempty"`
	RefundBillCode string `json:"refundBillCode,omitempty" bson:"refund_bill_code,omitempty"`

	WarehouseCode string                     `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	Status        enum.RefundBillStatusValue `json:"status,omitempty" bson:"status,omitempty"`
}

type ShippingOrder struct {
	ParentReferenceCode string   `json:"parentReferenceCode,omitempty" bson:"-"`
	OrderValue          *float64 `json:"orderValue,omitempty" bson:"-"`
}
type DealRequest struct {
	SellerCode          string   `json:"sellerCode,omitempty" bson:"-"`
	WarehouseCodes      []string `json:"warehouseCodes,omitempty" bson:"-"`
	VendorCode          string   `json:"vendorCode,omitempty" bson:"-"`
	ProductIDs          []int64  `json:"productIDs,omitempty" bson:"-"`
	VendorPromotionCode string   `json:"vendorPromotionCode,omitempty" bson:"-"`
}

type GetSellerBiddingRateRequest struct {
	Offset       int64       `json:"offset,omitempty" bson:"-"`
	Limit        int64       `json:"limit,omitempty" bson:"-"`
	GetTotal     bool        `json:"getTotal,omitempty" bson:"-"`
	GetToday     bool        `json:"getToday,omitempty" bson:"-"`
	GetSkuConfig bool        `json:"GetSkuConfig,omitempty" bson:"-"`
	Query        BiddingRate `json:"q,omitempty" bson:"-"`
	Sort         string      `json:"sort,omitempty" bson:"-"`
	Filter       string      `json:"filter,omitempty" bson:"-"`
	Search       string      `json:"search,omitempty" bson:"-"`
}

// UpdateSKUStatusRequest ...
type UpdateSKUStatusRequest struct {
	WarehouseCode      string `json:"warehouseCode"`
	SellerCode         string `json:"sellerCode"`
	ProductID          int64  `json:"productID"`
	ProductCode        string `json:"productCode"`
	ItemCode           string `json:"itemCode"`
	SKU                string `json:"sku"`
	CreatedByAccountID int64  `json:"createdByAccountID"`
	CreatedName        string `json:"createdName"`
}

// PUTBiddingRateNoteRequest ...
type PUTBiddingRateNoteRequest struct {
	SKU           string `json:"sku"`
	ItemCode      string `json:"itemCode"`
	WarehouseCode string `json:"warehouseCode"`
	OldStatus     string `json:"oldStatus"`
	NewStatus     string `json:"newStatus"`
}

type InboundRequestDealRequest struct {
	SellerCode         string   `json:"sellerCode,omitempty"`
	WarehouseCode      string   `json:"warehouseCode,omitempty"`
	VendorCode         string   `json:"vendorCode,omitempty"`
	InboundRequestCode string   `json:"inboundRequestCode,omitempty"`
	PromotionCode      string   `json:"promotionCode,omitempty"`
	ContractPriceCode  string   `json:"contractPriceCode,omitempty"`
	SKUs               []string `json:"skus,omitempty"`
}
type InboundRequestItemDealRequest struct {
	InboundRequestCode string `json:"inboundRequestCode,omitempty"`
	SKU                string `json:"sku,omitempty"`
	DealTicketCode     string `json:"dealTicketCode,omitempty"`
}

type InboundRequestPurchaseOrderRequest struct {
	InboundRequestCode string `json:"inboundRequestCode,omitempty"`
}

type ReasonSetting struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedByID     int64      `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`

	ReasonID   int64  `json:"reasonId,omitempty" bson:"reason_id,omitempty"`
	ReasonCode string `json:"reasonCode,omitempty" bson:"reason_code,omitempty"`

	ReasonType      string `json:"reasonType,omitempty" bson:"reason_type,omitempty"`
	ReasonName      string `json:"reasonName,omitempty" bson:"reason_name,omitempty"`
	ReasonShortName string `json:"reasonShortName,omitempty" bson:"reason_short_name,omitempty"`

	ExtraData    []ReasonRefMetaData `json:"extraData,omitempty" bson:"extra_data,omitempty"` // cho partner custom data, trường hợp cần lưu thêm thông tin
	ReasonCodeIn []string            `json:"reasonCodeIn,omitempty" bson:"-"`

	Status      string `json:"status,omitempty" bson:"status,omitempty"`
	CompanyCode string `json:"companyCode,omitempty" bson:"company_code,omitempty"`
}

type ReasonRefMetaData struct {
	Key      string `json:"key,omitempty" bson:"key,omitempty"`
	Value    any    `json:"value,omitempty" bson:"value,omitempty"`
	KeyValue string `json:"keyValue,omitempty" bson:"key_value,omitempty"`
}

type ReasonSettingQuery struct {
	Offset int64         `json:"offset,omitempty"`
	Limit  int64         `json:"limit,omitempty"`
	Option QueryOption   `json:"option,omitempty"`
	Query  ReasonSetting `json:"q,omitempty"`
	Sort   string        `json:"sort,omitempty"`
}

type QueryOption struct {
	Total bool `json:"total,omitempty"`
	Items bool `json:"items,omitempty"`
}

type PaymentPlatformQuery struct {
	Offset int64           `json:"offset,omitempty"`
	Limit  int64           `json:"limit,omitempty"`
	Option QueryOption     `json:"option,omitempty"`
	Query  PaymentPlatform `json:"q,omitempty"`
	Sort   string          `json:"sort,omitempty"`
}

type PaymentItemPlatformQuery struct {
	Offset int64               `json:"offset,omitempty"`
	Limit  int64               `json:"limit,omitempty"`
	Option QueryOption         `json:"option,omitempty"`
	Query  PaymentItemPlatform `json:"q,omitempty"`
	Sort   string              `json:"sort,omitempty"`
}

type CalcPriceAfterRebateRequest struct {
	ProductID     int64   `json:"productID"`
	WarehouseCode string  `json:"warehouseCode"`
	VendorCode    string  `json:"vendorCode"`
	Discount      float64 `json:"discount"`
	UnitPrice     float64 `json:"unitPrice"`
	VAT           float64 `json:"vat"`
	ApplyTime     string  `json:"applyTime"`

	ContractPriceCode *string `json:"contractPriceCode,omitempty"`
	PromotionCode     *string `json:"promotionCode,omitempty"`
}

type InboundRequestItemValidateImportReq struct {
	ApplyTime      string `json:"applyTime"`
	ProductID      int64  `json:"productID"`
	WarehouseCode  string `json:"warehouseCode"`
	VendorID       *int64 `json:"vendorID"`
	PromotionID    *int64 `json:"promotionID"`
	Kind           string `json:"kind"`
	ExpectQuantity int64  `json:"expectQuantity"`
	NumDayInStock  int64  `json:"numDayInStock"`
	SellerCode     string `json:"sellerCode"`
}

type InboundRequestItemImportReq struct {
	SellerCode             string                                `json:"sellerCode"`
	ApplyTime              string                                `json:"applyTime"`
	ProductCode            string                                `json:"productCode"`
	ProductID              int64                                 `json:"productID"`
	SKU                    string                                `json:"sku"`
	WarehouseCode          string                                `json:"warehouseCode"`
	VendorID               *int64                                `json:"vendorID,omitempty"`
	Discount               float64                               `json:"discount"`
	ExpectQuantity         int64                                 `json:"expectQuantity"`
	MetaData               InboundRequestItemMetaDataReq         `json:"metaData"`
	UnitPrice              float64                               `json:"unitPrice,omitempty" `
	VAT                    float64                               `json:"vat,omitempty"`
	ContractPriceCode      string                                `json:"contractPriceCode,omitempty" `
	ContractPriceStartTime *time.Time                            `json:"contractPriceStartTime,omitempty"`
	ContractPriceEndTime   *time.Time                            `json:"contractPriceEndTime,omitempty"`
	Reason                 enum.InboundRequestReasonToOtherValue `json:"reason,omitempty"`
	InboundRequestType     enum.InboundRequestTypeValue          `json:"inboundRequestType,omitempty"`
	CreatedByName          string                                `json:"createdByName,omitempty"`
	CreatedByID            int64                                 `json:"createdById,omitempty"`
	Name                   string                                `json:"name,omitempty"`
	PromotionID            *int64                                `json:"promotionID,omitempty"`
	TotalRebate            float64                               `json:"totalRebate,omitempty"`
}

type InboundRequestItemMetaDataReq struct {
	AvailableQuantityWHInbound float64 `json:"availableQuantityWHInbound"`
	AVGDemand                  float64 `json:"avgDemand"`
	NumDayInStock              float64 `json:"numDayInStock"`
	QuantityIncoming           float64 `json:"quantityIncoming"`
	TotalAvailableQuantity     float64 `json:"totalAvailableQuantity"`
	DemandClassificationCode   string  `json:"demandClassificationCode,omitempty"`
	Total30DaysDemand          float64 `json:"total30DaysDemand"`
	HedgingAVGDemand           float64 `json:"hedgingAVGDemand,omitempty"`
}

type InboundRequestItemManualUpdateReq struct {
	InboundRequestCode string `json:"inboundRequestCode"`
}

type MarketplaceDealTicket struct {
	TicketStatus string    `json:"ticketStatus,omitempty"`
	TicketCode   string    `json:"ticketCode,omitempty"`
	Note         string    `json:"note,omitempty"`
	Deal         *DealResp `json:"deal,omitempty"`
}

type MarketplaceDeal struct {
	Code            string           `json:"code,omitempty"`
	SellerCode      string           `json:"sellerCode,omitempty"`
	Status          string           `json:"status,omitempty"`
	TicketCode      string           `json:"ticketCode,omitempty"`
	AreaCodes       []string         `json:"areaCodes,omitempty"`
	Price           *float64         `json:"price,omitempty"`
	SKUs            []*DealSku       `json:"skus,omitempty" bson:"skus,omitempty"`
	VendorPromoInfo *VendorPromoInfo `json:"vendorPromoInfo,omitempty" bson:"vendor_promo_info,omitempty"`

	StartTime time.Time `json:"startTime" bson:"start_time,omitempty"` // thoi gian bat dau
	EndTime   time.Time `json:"endTime" bson:"end_time,omitempty"`     // thoi gian ket thuc
	ReadyTime time.Time `json:"readyTime" bson:"ready_time,omitempty"` // thoi gian cho phep hien thi
	Source    string    `json:"source,omitempty" bson:"source,omitempty"`

	VendorPromotionCode string `json:"vendorPromotionCode,omitempty" bson:"vendor_promotion_code,omitempty"`
}

type DealResp struct {
	SKUs      []*DealSku `json:"skus,omitempty" bson:"skus,omitempty"`
	AreaCodes []string   `json:"areaCodes,omitempty"`
	Status    string     `json:"status,omitempty"`
}

type DealSku struct {
	SKU         string   `json:"sku" bson:"sku,omitempty"`
	ProductCode string   `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int      `json:"productID,omitempty" bson:"product_id,omitempty"`
	Quantity    int      `json:"quantity" bson:"quantity,omitempty"`
	ItemCodes   []string `json:"itemCodes,omitempty" bson:"item_codes,omitempty"` // for ref
}

type VendorPromoInfo struct {
	PromoCode string   `json:"promoCode,omitempty" bson:"promo_code,omitempty"`
	PromoName string   `json:"promoName,omitempty" bson:"promo_name,omitempty"`
	Price     *float64 `json:"price,omitempty" bson:"price,omitempty"`
	Source    string   `json:"source,omitempty" bson:"source,omitempty"`
}

type Bank struct {
	ID     primitive.ObjectID `json:"id" bson:"_id,omitempty" `
	BankID int64              `json:"bankID,omitempty" bson:"bank_id,omitempty"`
	Code   string             `json:"code,omitempty" bson:"code,omitempty"`
	Name   string             `json:"name" bson:"name,omitempty"`

	GlobalBankID int64 `json:"globalBankID,omitempty" bson:"global_bank_id,omitempty"`
}

// =============================================================

// Action represents an action with associated metadata
type Action struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	OrgID int64 `json:"orgID,omitempty" bson:"org_id,omitempty"`
	// EntityID int64 `json:"entityID,omitempty" bson:"entity_id,omitempty"`

	Name string `json:"name,omitempty" bson:"name,omitempty"`
	Code string `json:"code,omitempty" bson:"code,omitempty"`

	Module string `json:"module,omitempty" bson:"module,omitempty"` // for frontend
	Type   string `json:"type,omitempty" bson:"type,omitempty"`     // HTTP, EMAIL, ...

	// EMAIL
	EmailTemplate *EmailTemplate `json:"emailTemplate,omitempty" bson:"email_template,omitempty"`
}

// EmailTemplate defines the structure for email templates
type EmailTemplate struct {
	ToTemplate  []string `json:"toTemplate,omitempty" bson:"to_template,omitempty"`   // {{pickAccountID}}, <EMAIL>, <EMAIL>
	CCTemplate  []string `json:"ccTemplate,omitempty" bson:"cc_template,omitempty"`   // {{pickAccountID}}, ...
	BccTemplate []string `json:"bccTemplate,omitempty" bson:"bcc_template,omitempty"` // {{pickAccountID}}, <EMAIL>, <EMAIL>

	SenderName      string `json:"senderName,omitempty" bson:"sender_name,omitempty"`           // request {{fromName}} for something
	SubjectTemplate string `json:"subjectTemplate,omitempty" bson:"subject_template,omitempty"` // request {{fromName}} for something
	ContentTemplate string `json:"contentTemplate,omitempty" bson:"content_template,omitempty"` // request {{fromName}} for something
}

// =============================================================

type InputSendEmail struct {
	From Email `json:"from,omitempty" bson:"from,omitempty"`
	// ReplyTo Email     `json:"replyTo,omitempty"`
	To      []Email   `json:"to,omitempty" bson:"to,omitempty"`
	Bcc     []Email   `json:"bcc,omitempty" bson:"bcc,omitempty"`
	Cc      []Email   `json:"cc,omitempty" bson:"cc,omitempty"`
	Subject string    `json:"subject,omitempty" bson:"subject,omitempty"`
	Content []Content `json:"content,omitempty" bson:"content,omitempty"`
	// Attachments []Attachment `json:"attachments"`
}

type Content struct {
	Type  string `json:"type,omitempty"`
	Value string `json:"value,omitempty"`
}

type Email struct {
	User  string `json:"user,omitempty" bson:"user,omitempty"`
	Host  string `json:"host,omitempty" bson:"host,omitempty"`
	Email string `json:"email,omitempty" bson:"email,omitempty"`
	Name  string `json:"name,omitempty" bson:"name,omitempty"`
}
