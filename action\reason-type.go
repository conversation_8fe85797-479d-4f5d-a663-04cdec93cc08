package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	json "go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateReasonType(input model.ReasonType) *common.APIResponse {
	// create
	input.ReasonTypeID, _ = model.GetReasonTypeID()
	// query
	resp := model.ReasonTypeDB.QueryOne(&model.ReasonType{
		Code: input.Code,
	})

	if resp.Status != common.APIStatus.Ok {
		createResp := model.ReasonTypeDB.Create(input)

		return createResp
	}

	return &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		ErrorCode: string(enum.ErrorCodeInvalid.ReasonExist),
		Message:   "This reason type was exist",
	}
}

func UpdateReasonType(input model.ReasonType) *common.APIResponse {
	// query
	resp := model.ReasonTypeDB.QueryOne(&model.ReasonType{
		Code: input.Code,
	})

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	respData := resp.Data.([]*model.ReasonType)[0]
	input.Code = respData.Code
	updateResp := model.ReasonTypeDB.UpdateOne(
		model.ReasonType{
			Code: input.Code,
		},
		input)

	return updateResp
}

// Delete Reason ...
func DeleteReasonType(code string) *common.APIResponse {
	reasonResp := GetReasonTypeByCode(code)
	if reasonResp.Status != common.APIStatus.Ok {
		return reasonResp
	}
	return model.ReasonTypeDB.Delete(&model.ReasonType{ID: reasonResp.Data.([]*model.ReasonType)[0].ID})
}

// Get Reason by code
func GetReasonTypeByCode(code string) *common.APIResponse {
	return model.ReasonTypeDB.QueryOne(&model.ReasonType{Code: code})
}

// Get Reason by ID
func GetReasonTypeList(query *model.ReasonType, offset, limit int64, getTotal bool) *common.APIResponse {
	if len(query.CodeIn) > 0 {
		query.Code = ""
		query.ComplexQuery = append(query.ComplexQuery, &json.M{"code": json.M{
			"$in": query.CodeIn,
		}})
	}
	result := model.ReasonTypeDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if getTotal {
		result.Total = model.ReasonTypeDB.Count(query).Total
	}

	return result
}
