package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetQuotationExport ...
func GetQuotationExport(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		qStr       = req.GetParam("q")
		groupCode  = req.GetParam("groupCode")
	)

	// validate input
	if groupCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid groupCode",
			ErrorCode: string(enum.ErrorCodeInvalid.GroupCode),
		})
	}

	// parse input
	var query model.QuotationExport
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}
	query.SellerCode = strings.ToUpper(sellerCode)

	// if downloadType != "ALL" {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"quantity_expect_with_so": bson.M{"$gt": 0},
	// 	})
	// }

	// do action
	return resp.Respond(action.GetQuotationExport(&query, groupCode, offset, limit, getTotal))
}

// GetQuotationExportDraft ...
func GetQuotationExportDraft(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		qStr       = req.GetParam("q")
		groupCode  = req.GetParam("groupCode")
		// downloadType = req.GetVar("downloadType")
		typ = req.GetParam("type")
	)

	// validate input
	if groupCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid groupCode",
			ErrorCode: string(enum.ErrorCodeInvalid.GroupCode),
		})
	}

	// parse input
	var query model.QuotationExport
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data. " + err.Error(),
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}
	query.SellerCode = strings.ToUpper(sellerCode)

	// do action
	return resp.Respond(action.GetQuotationExportDraft(&query, groupCode, offset, limit, getTotal, enum.QuotationType(typ)))
}
