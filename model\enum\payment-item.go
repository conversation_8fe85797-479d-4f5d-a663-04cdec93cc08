package enum

type (
	PaymentItemType string
	paymentItemType struct {
		ADJUSTMENT_BILL PaymentItemType
		VENDOR_BILL     PaymentItemType
	}
)

var PaymentItemTypeVal = &paymentItemType{
	ADJUSTMENT_BILL: "ADJUSTMENT_BILL",
	VENDOR_BILL:     "VENDOR_BILL",
}

/**
 * Transaction type
 */
type (
	TransactionType string

	transactionType struct {
		VENDOR_BILL     TransactionType // hoá đơn
		ADJUSTMENT_BILL TransactionType // hoá đơn điều chỉnh
		RECEIPT_VOUCHER TransactionType // phiếu thu
		PAYMENT_VOUCHER TransactionType // phiếu chi
	}
)

var Transaction = &transactionType{
	VENDOR_BILL:     "VENDOR_BILL",
	ADJUSTMENT_BILL: "ADJUSTMENT_BILL",
	RECEIPT_VOUCHER: "RECEIPT_VOUCHER",
	PAYMENT_VOUCHER: "PAYMENT_VOUCHER",
}
