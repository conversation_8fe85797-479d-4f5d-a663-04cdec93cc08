package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// GetSkuConfig ...
func GetSkuConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal      = req.GetParam("getTotal") == "true"
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
		qStr          = req.GetParam("q")
		sort          = req.GetParam("sort")
	)

	// validate input
	var query model.SkuConfig
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}
	query.SellerCode = sellerCode
	query.PurchaserCode = purchaserCode

	// do action
	return resp.Respond(action.GetSkuConfig(&query, offset, limit, getTotal, sort))
}

// GetSkuConfigList
func GetSkuConfigList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset        = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit         = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal      = req.GetParam("getTotal") == "true"
		qStr          = req.GetParam("q")
		sort          = req.GetParam("sort")
		warehouseCode = req.GetParam("warehouseCode") // conver from warehouse to purchaser
	)

	// validate input
	var query model.SkuConfig
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// map warehouse to purchaser
	if warehouseCode != "" {
		// map warehouse to purchaser
		purchaserCode, _ := utils.WarehouseCodeToPurchaserCode(warehouseCode)
		if purchaserCode != "" {
			query.PurchaserCode = purchaserCode
		}
	}

	// do action
	return resp.Respond(action.GetSkuConfig(&query, offset, limit, getTotal, sort))
}

// CreateSkuConfig
func CreateSkuConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
		input         model.SkuConfig
	)

	// validate input
	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}
	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.ProductID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid ProductID",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}
	if input.SKU == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SKU",
			ErrorCode: string(enum.ErrorCodeInvalid.SKU),
		})
	}

	// if input.Vendors == nil || len(*input.Vendors) == 0 {
	// 	return resp.Respond(&common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Invalid VendorCodes",
	// 		ErrorCode: string(enum.ErrorCodeInvalid.VendorCodes),
	// 	})
	// }
	input.SellerCode = sellerCode
	input.PurchaserCode = purchaserCode

	// do action
	return resp.Respond(action.CreateSkuConfig(&input))
}

// UpdateSkuConfig
func UpdateSkuConfig(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
		input         model.SkuConfig
	)

	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.SKU == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SKU",
			ErrorCode: string(enum.ErrorCodeInvalid.SKU),
		})
	}

	// if input.Vendors == nil || len(*input.Vendors) == 0 {
	// 	return resp.Respond(&common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Invalid VendorCodes",
	// 		ErrorCode: string(enum.ErrorCodeInvalid.VendorCodes),
	// 	})
	// }
	input.SellerCode = strings.ToUpper(sellerCode)
	input.PurchaserCode = purchaserCode

	// do action
	return resp.Respond(action.UpdateSkuConfig(&input))
}

// CreateSkuConfigWithCompletedPO ...
func CreateSkuConfigWithCompletedPO(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.CreateSkuConfigWithCompletedPO(input.POCode))
}
