package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotationTracking struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	Version string `json:"version,omitempty" bson:"version,omitempty"`

	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	TotalQuantityConfirmed *int64 `json:"totalQuantityConfirmed,omitempty" bson:"total_quantity_confirmed,omitempty"`
	TotalQuantityExpect    int64  `json:"totalQuantityExpect,omitempty" bson:"total_quantity_expect,omitempty"` // không có số lượng của vendor

	// For query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// QuotationTrackingDB ...
var QuotationTrackingDB = &db.Instance{
	ColName:        "quotation_tracking",
	TemplateObject: &QuotationTracking{},
}

func InitQuotationTrackingModel(s *mongo.Database) {
	QuotationTrackingDB.ApplyDatabase(s)
	// t := true
	// _ = QuotationTrackingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "sku", Value: 1},
	// 	primitive.E{Key: "warehouseCode", Value: 1},
	// 	primitive.E{Key: "version", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
}
