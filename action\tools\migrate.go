package tools

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/integration"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func VendorBillFillInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		// offset           = sdk.ParseInt64(req.GetParam("offset"), 0)
		// limit            = sdk.ParseInt64(req.GetParam("limit"), 20)
		q                = req.GetParam("q")
		isOnlySellerCode = req.GetParam("isOnlySellerCode") == "true"
		isOnlyVendorCode = req.GetParam("isOnlyVendorCode") == "true"
	)

	// if limit < 0 {
	// 	limit = 20
	// }

	// if limit > 1000 {
	// 	limit = 1000
	// }

	// if offset < 0 {
	// 	offset = 0
	// }

	// fill query
	query := model.VendorBill{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	// query.Status = enum.VendorBillStatus.DRAFT

	// Nếu có truyền ID || VendorBillID || VendorBillCode thì force query theo ID || VendorBillID || VendorBillCode, để update lại
	if query.ID != nil || query.VendorBillID != 0 || query.VendorBillCode != "" {
	} else {

		orCondition := []bson.M{}
		if isOnlySellerCode {
			orCondition = append(orCondition, bson.M{
				"seller_code": bson.M{
					"$exists": false,
				},
			})
		} else if isOnlyVendorCode {
			orCondition = append(orCondition, bson.M{
				"vendor_code": bson.M{
					"$exists": false,
				},
			})
		} else {
			orCondition = append(orCondition, bson.M{
				"seller_code": bson.M{
					"$exists": false,
				},
				"vendor_code": bson.M{
					"$exists": false,
				},
			})
		}

		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"$or": orCondition,
		})
	}

	var limit int64 = 100
	var vendorBills []*model.VendorBill

	var (
		_id_offset = primitive.NilObjectID
	)

	for {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"_id": bson.M{"$gt": _id_offset}})
		vendorBillResp := model.VendorBillDB.Query(query, 0, 100, nil)
		if vendorBillResp.Status != common.APIStatus.Ok {
			break
		}
		vbs := vendorBillResp.Data.([]*model.VendorBill)
		vendorBills = append(vendorBills, vbs...)

		if len(vbs) < int(limit) {
			break
		}

		_id_offset = *vbs[len(vbs)-1].ID
	}

	for _, vendorBill := range vendorBills {

		// Fill seller code base on PO
		if vendorBill.POCode != "" && vendorBill.SellerCode == "" {
			purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
				POCode: vendorBill.POCode,
			})
			if purchaseOrderResp.Status == common.APIStatus.Ok {
				purchaseOrder := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]
				vendorBill.SellerCode = purchaseOrder.SellerCode
			}
		}

		option := client.APIOption{
			Q: model.Seller{
				Tax: vendorBill.VendorTaxCode,
			}}

		vendorResp := seller.GetSellers(option)

		if vendorResp.Status == common.APIStatus.Ok {
			vendor := vendorResp.Data.([]*model.Seller)[0]
			vendorBill.VendorCode = vendor.Code
			vendorBill.VendorLegalName = vendor.Name
		}

		model.VendorBillDB.UpdateOne(model.VendorBill{ID: vendorBill.ID}, vendorBill)
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK.",
	})
}

// MigrateDeletePO ...
func MigrateDeletePO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	if len(input.POCodeIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCodes",
		})
	}

	for _, poCode := range input.POCodeIn {
		if poCode == "" {
			continue
		}

		if input.Status == enum.PurchaseOrderStatus.CANCELED {
			model.PurchaseOrderDB.UpdateOne(
				model.PurchaseOrder{
					POCode: poCode,
				},
				model.PurchaseOrder{
					Status: enum.PurchaseOrderStatus.CANCELED,
				},
			)

		} else {
			model.PurchaseOrderDB.Delete(model.PurchaseOrder{
				POCode: poCode,
			})
			model.PurchaseOrderItemDB.Delete(model.PurchaseOrderItem{
				POCode: poCode,
			})
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK.",
	})
}

// FillPOBillStatus ...
func FillPOBillStatus(req sdk.APIRequest, resp sdk.APIResponder) error {
	total := 0
	fmt.Println("FillPOBillStatus begin")

	_id := primitive.NilObjectID
	for {

		// get all po
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
				Status: enum.PurchaseOrderStatus.COMPLETED,
			},
			0, 100, &primitive.M{"_id": 1},
		)
		if purchaseOrderResp.Status == common.APIStatus.NotFound {
			break
		}

		purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for _, po := range purchaseOrders {
			_id = *po.ID

			model.PurchaseOrderDB.UpdateOne(
				model.PurchaseOrder{ID: po.ID},
				model.PurchaseOrder{Status: enum.PurchaseOrderStatus.AWAITING_BILLED},
			)
		}

		total += len(purchaseOrders)
	}

	fmt.Println("FillPOBillStatus done", total)

	return resp.Respond(
		&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Thành công",
		},
	)
}

func MigrateUpdatePOItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var id = req.GetParam("id")
	var isUpdateProduct = req.GetParam("isUpdateProduct") == "true"

	if id == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: ID",
		})
	}

	_id, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input ID.",
		})
	}

	var input model.PurchaseOrderItem
	err = req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if isUpdateProduct {
		itemResp := model.PurchaseOrderItemDB.QueryOne(model.PurchaseOrderItem{ID: &_id})
		if itemResp.Status == common.APIStatus.Ok {
			item := itemResp.Data.([]*model.PurchaseOrderItem)[0]
			if item.ProductID != 0 && item.SellerCode != "" && item.PurchaserCode == "" {
				item.PurchaserCode = input.PurchaserCode
				option := client.APIOption{
					Q:     model.SKUItem{ProductID: item.ProductID, SellerCode: item.SellerCode, PurchaserCode: item.PurchaserCode},
					Limit: utils.Pointer.WithInt(1),
				}
				skuResp := marketplace.GetListSKUItem(option)
				if listSKU, ok := skuResp.Data.([]*model.SKUItem); (skuResp.Status == common.APIStatus.Ok) && ok {
					sku := listSKU[0]
					input.SKU = sku.SKU
					input.ProductCode = sku.ProductCode
				}
			}
		}
	}

	return resp.Respond(
		model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{
			ID: &_id,
		}, input))
}

func MigrateDeletPOItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var id = req.GetParam("id")
	if id == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: ID",
		})
	}

	_id, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input ID.",
		})
	}

	return resp.Respond(
		model.PurchaseOrderItemDB.Delete(model.PurchaseOrderItem{
			ID: &_id,
		}))
}

// POForceUpdate ...
func POForceUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCode",
		})
	}

	return resp.Respond(model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: input.POCode,
		},
		input,
	))
}

// VBForceUpdate ...
func VBForceUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	if input.VendorBillID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "VendorBillID",
		})
	}

	return resp.Respond(model.VendorBillDB.UpdateOne(
		model.VendorBill{
			VendorBillID: input.VendorBillID,
		},
		input,
	))
}

func MigrateNameMapping(req sdk.APIRequest, resp sdk.APIResponder) error {

	// VB
	_id := primitive.NilObjectID
	for {
		vendorBillResp := model.VendorBillDB.Query(
			model.VendorBill{
				Status: enum.VendorBillStatus.PAID,
				ComplexQuery: []*bson.M{
					{"vendor_bill_id": bson.M{"$lt": 10000000000}},
					{"_id": bson.M{"$gt": _id}},
				},
			},

			0, 100, &primitive.M{"_id": 1},
		)
		if vendorBillResp.Status == common.APIStatus.NotFound {
			break
		}
		if vendorBillResp.Status != common.APIStatus.Ok {
			continue
		}

		vendorBills := vendorBillResp.Data.([]*model.VendorBill)
		for _, vb := range vendorBills {
			_id = *vb.ID
			if vb.VendorBillCode != "" && vb.VendorCode != "" {
				action.ExecuteUpdateNameMapping(*vb)
			}
		}
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Sync success",
	})
}

// DeleteESProductCode ...
func DeleteESProductCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.SkuConfig
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	if input.ProductCodeIn == nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "ProductCodeIn",
		})
	}

	for _, productCode := range *input.ProductCodeIn {
		params := map[string]string{
			"model":      conf.Config.SearchProductModel,
			"documentId": productCode,
		}
		integration.RemoveProduct(client.APIOption{Params: params})
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK.",
	})
}

// DeleteInboundRequest ...
func DeleteInboundRequest(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		inboundRequestIDStr = req.GetParam("inboundRequestID")
	)
	inboundRequestID, _ := strconv.Atoi(inboundRequestIDStr)

	if inboundRequestIDStr == "" || inboundRequestID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Required inboundRequestID",
		})
	}

	deleteResp := model.InboundRequestItemDB.Delete(model.InboundRequestItem{
		InboundRequestID: int64(inboundRequestID),
	})
	if deleteResp.Status != common.APIStatus.Ok {
		return resp.Respond(deleteResp)
	}

	// do action
	return resp.Respond(model.InboundRequestDB.Delete(model.InboundRequest{
		InboundRequestID: int64(inboundRequestID),
	}))
}

// ForceInboundRequestItem ...
func ForceInboundRequestItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.InboundRequestItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if input.InboundRequestCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Required paymentItemCode",
			ErrorCode: string(enum.ErrorCodeInvalid.PaymentItemCode),
		})
	}

	updateResp := model.InboundRequestItemDB.UpdateMany(
		model.InboundRequestItem{
			InboundRequestCode: input.InboundRequestCode,
		},
		input,
	)

	// do action
	return resp.Respond(updateResp)
}

// ForceInboundRequestItem ...
func DeleteQuoation(req sdk.APIRequest, resp sdk.APIResponder) error {

	arr := []string{
		"MEDX.ACEMOL-325-NADYPHAR-C40V",
		"MEDX.DHG-HAG-001",
		"MEDX.DMTS-DOM-AMO-001",
		"MEDX.DMTS-DOM-CEP-001",
		"MEDX.DMTS-DOM-DOR-002",
		"MEDX.DMTS-JAN-NIZ-004",
		"MEDX.ENTERIC-NADYPHAR-C100V",
		"MEDX.NAD-ACE-004",
		"MEDX.NAD-PEC-001",
		"MEDX.NUL-PEP-001",
		"MEDX.PAT-JAN-001",
		"MEDX.WE672AXH",
	}

	dresp := model.QuotationDB.Delete(model.Quotation{
		ComplexQuery: []*bson.M{
			{"sku": bson.M{"$in": arr}},
		},
	})

	return resp.Respond(dresp)
}

type VendorBillItemFix struct {
	ActualQuantity     *int64 `json:"actualQuantity,omitempty" bson:"actual_quantity,omitempty"` // SL thực từ kho: callback
	IsInboundCompleted *bool  `json:"isInboundCompleted,omitempty" bson:"is_inbound_completed,omitempty"`
}

// ForceUpdateVBItem ...
func ForceUpdateVBItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	vendorBillCodeStr := req.GetParam("vendorBillCodes")
	vendorBillCodes := strings.Split(vendorBillCodeStr, ",")
	if vendorBillCodeStr == "" || len(vendorBillCodes) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Required vendorBillCodes",
		})
	}

	var ZERO int64 = 0
	updateResp := model.VendorBillItemDB.UpdateMany(
		model.VendorBillItem{
			ComplexQuery: []*bson.M{
				{"vendor_bill_code": bson.M{"$in": vendorBillCodes}},
			},
		},
		VendorBillItemFix{
			IsInboundCompleted: utils.Pointer.WithBool(false),
			ActualQuantity:     &ZERO,
		},
	)

	// do action
	return resp.Respond(updateResp)
}

// ForceUpdatePayment
func ForceUpdatePayment(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PaymentLegacy
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.PaymentCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PaymentCode",
			ErrorCode: string(enum.ErrorCodeInvalid.PaymentCode),
		})
	}

	return resp.Respond(model.PaymentLegacyDB.UpdateOne(
		model.PaymentLegacy{PaymentCode: input.PaymentCode},
		input,
	))
}

// FillBillQuantity...
func FillBillQuantity(req sdk.APIRequest, resp sdk.APIResponder) error {
	total := 0

	var poCodes = req.GetParam("poCodes")
	if poCodes == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCodes invalid",
		})
	}

	if poCodes != "ALL" {
		poCodeArr := strings.Split(poCodes, ",")
		for _, poCode := range poCodeArr {
			if strings.Trim(poCode, " ") == "" {
				continue
			}
			poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
				POCode: poCode,
				ComplexQuery: []*bson.M{
					{
						"status": bson.M{
							"$in": []enum.PurchaseOrderStatusValue{
								enum.PurchaseOrderStatus.CONFIRMED, enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
								// enum.PurchaseOrderStatus.PROCESSING,
								enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED,
								enum.PurchaseOrderStatus.COMPLETED,
								enum.PurchaseOrderStatus.AWAITING_BILLED, enum.PurchaseOrderStatus.BILLED,
							},
						},
					},
				},
			})
			if poResp.Status != common.APIStatus.Ok {
				continue
			}
			action.HandleRefillBillQuantity(poCode)
		}

		return resp.Respond(
			&common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Successful.",
			},
		)
	}

	// For loops all po
	_id := primitive.NilObjectID
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
					{
						"status": bson.M{
							"$in": []enum.PurchaseOrderStatusValue{
								enum.PurchaseOrderStatus.CONFIRMED, enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
								// enum.PurchaseOrderStatus.PROCESSING,
								enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED,
								enum.PurchaseOrderStatus.COMPLETED,
								enum.PurchaseOrderStatus.AWAITING_BILLED, enum.PurchaseOrderStatus.BILLED,
							},
						},
					},
				},
			},
			0, 100, &primitive.M{"_id": 1},
		)
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			break
		}

		purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for _, po := range purchaseOrders {
			_id = *po.ID
			action.HandleRefillBillQuantity(po.POCode)
		}

		total += len(purchaseOrders)
	}

	fmt.Println("FillBillQuantity done", total)

	return resp.Respond(
		&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Successful.",
		},
	)
}

// DeleteSetting
func DeleteSetting(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		areYouSure = req.GetParam("areYouSure") == "true"
	)

	if areYouSure != true {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid areYouSure.",
		})
	}

	// do action
	return resp.Respond(model.SettingDB.Delete(nil))
}

func SyncReceptToWarehouse(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var query model.PurchaseOrder
	err := req.GetContent(&query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data: " + err.Error(),
		})
	}

	if len(query.POCodeIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "POCodeIn invalid",
		})
	}

	query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_code": bson.M{
		"$in": query.POCodeIn,
	}})

	purchaseOrderResp := model.PurchaseOrderDB.Query(query, 0, 1000, nil)
	if purchaseOrderResp.Status != common.APIStatus.Ok {
		return resp.Respond(purchaseOrderResp)
	}

	purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
	for i := range purchaseOrders {
		purchaseOrder := purchaseOrders[i]

		// sync
		action.CreateReceptToWarehouse(purchaseOrder)
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "OK",
	})
}

// func SyncWarehouseToPO(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	var query model.PurchaseOrder
// 	err := req.GetContent(&query)
// 	if err != nil {
// 		return resp.Respond(&common.APIResponse{
// 			Status:  common.APIStatus.Invalid,
// 			Message: "Can not parse input data: " + err.Error(),
// 		})
// 	}

// 	if query.POCode == "" {
// 		return resp.Respond(&common.APIResponse{
// 			Status:  common.APIStatus.Invalid,
// 			Message: "Can not parse input data: " + err.Error(),
// 		})
// 	}

// 	// sync
// 	return resp.Respond(action.SyncWarehouseToPO(query.POCode))
// }

// ConfirmedPushingGroup ...
func ForceUpdatePushingGroup(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.AdminPushingGroup
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.GroupCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "GroupCode invalid.",
			ErrorCode: string(enum.ErrorCodeInvalid.GroupCode),
		})
	}

	return resp.Respond(model.AdminPushingGroupDB.UpdateOne(model.AdminPushingGroup{GroupCode: input.GroupCode}, input))
}

func MigratePromotionJobQueue(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []string
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data. " + err.Error(),
		})
	}

	for _, poCode := range input {
		if len(poCode) == 0 {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "po empty",
			})
		}

		// push in queue for calculate vendor promotion
		err := model.VendorPromotionJob.Push(model.PurchaseOrder{POCode: poCode}, &job.JobItemMetadata{
			Topic:     model.VENDOR_PROMOTION_TOPIC_JOB,
			Keys:      []string{poCode, string(enum.PurchaseOrderStatus.CONFIRMED)},
			UniqueKey: poCode,
			ReadyTime: utils.Pointer.WithTime(utils.GetVietnamTimeNow()),
		})

		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: fmt.Sprintf("%s: %s", poCode, err.Error()),
			})
		}

	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "ok"})
}

func MigrateFulfillmentPercentPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Printf("Start calc migrate update fulfillment for PO")
	timeFrom := (time.Now().In(utils.TimeZoneVN).AddDate(0, 0, -15))
	_id := primitive.NewObjectID()

Loop:
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$lt": _id}},
					{
						"status": bson.M{
							"$in": []enum.PurchaseOrderStatusValue{
								enum.PurchaseOrderStatus.CONFIRMED, enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
								// enum.PurchaseOrderStatus.PROCESSING,
								enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED,
								enum.PurchaseOrderStatus.COMPLETED,
								enum.PurchaseOrderStatus.AWAITING_BILLED, enum.PurchaseOrderStatus.BILLED,
							},
						},
					},
				},
			},
			0, 100, &primitive.M{"_id": -1},
		)
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			break
		}

		purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for _, po := range purchaseOrders {
			if po.CreatedTime.Before(timeFrom) {
				break Loop
			}
			_id = *po.ID
			updateFulfillmentPO(po.POCode)
		}
	}

	fmt.Println("End calc migrate update fulfillment for PO")
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func updateFulfillmentPO(poCode string) {
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, &primitive.M{"_id": -1})
	poItems := poItemResp.Data.([]*model.PurchaseOrderItem)
	var totalActualQuantity, totalExpectQuantity int64 = 0, 0
	var fulfillmentPercent float64 = 0.0
	for _, item := range poItems {
		totalActualQuantity += item.ActualQuantity
		totalExpectQuantity += item.ExpectQuantity
	}
	if totalExpectQuantity != 0 {
		fulfillmentPercent = (float64(totalActualQuantity) / float64(totalExpectQuantity)) * 100
		if fulfillmentPercent > 100 {
			fulfillmentPercent = 100
		}
	}

	model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: poCode,
		},
		model.PurchaseOrder{
			FulfillmentPercent: &fulfillmentPercent,
		},
	)
}

func VBUpdateDuetime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input []*model.VendorBill
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	fmt.Println("Start update duetime VB")
	//update dueTime vbs
	for _, vendorBill := range input {
		if vendorBill.VendorBillID != 0 {
			model.VendorBillDB.UpdateOne(model.VendorBill{VendorBillID: vendorBill.VendorBillID}, model.VendorBill{
				DueTime: vendorBill.DueTime,
			})
		}
	}

	fmt.Println("End update duetime VB")
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigrateHashtagPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("MigrateHashtagPO BEGIN...")
	defer fmt.Println("MigrateHashtagPO END...")

	//get all vendor
	vendorList := []*model.Seller{}
	for offset, limit := 0, 100; ; offset += limit {
		option := client.APIOption{
			Params: map[string]string{
				"sellerClass": model.CLASS_VENDOR,
			},
			Offset: utils.Pointer.WithInt(offset),
			Limit:  utils.Pointer.WithInt(limit),
		}
		getVendorResp := seller.GetSellers(option)

		if getVendorResp.Status != common.APIStatus.Ok {
			break
		}
		vendors := getVendorResp.Data.([]*model.Seller)
		vendorList = append(vendorList, vendors...)
	}

	_id := primitive.NilObjectID
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			},
			0, 100, &primitive.M{"_id": 1},
		)
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			break
		}

		purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for _, po := range purchaseOrders {
			_id = *po.ID
			if po.VendorCode == "" || po.VendorName == "" {
				continue
			}
			for _, vendor := range vendorList {
				if vendor.Code == po.VendorCode {
					newHashTag := action.GenHashTagPOFromInfo(po, vendor)
					updateResp := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{ID: po.ID}, model.PurchaseOrder{HashTag: newHashTag})
					if updateResp.Status != common.APIStatus.Ok {
						break
					}
					break
				}
			}
		}
	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigrateHashtagVB(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("MigrateHashtagVB BEGIN...")
	defer fmt.Println("MigrateHashtagVB END...")

	//get all vendor
	vendorList := []*model.Seller{}
	for offset, limit := 0, 100; ; offset += limit {
		option := client.APIOption{
			Params: map[string]string{
				"sellerClass": model.CLASS_VENDOR,
			},
			Offset: utils.Pointer.WithInt(offset),
			Limit:  utils.Pointer.WithInt(limit),
		}
		getVendorResp := seller.GetSellers(option)

		if getVendorResp.Status != common.APIStatus.Ok {
			break
		}
		vendors := getVendorResp.Data.([]*model.Seller)
		vendorList = append(vendorList, vendors...)
	}

	_id := primitive.NilObjectID
	for {
		vendorBillResp := model.VendorBillDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			},
			0, 100, &primitive.M{"_id": 1},
		)
		if vendorBillResp.Status != common.APIStatus.Ok {
			break
		}

		vendorBills := vendorBillResp.Data.([]*model.VendorBill)
		for _, vb := range vendorBills {
			_id = *vb.ID
			if vb.VendorCode == "" {
				continue
			}
			for _, vendor := range vendorList {
				if vendor.Code == vb.VendorCode {
					newHashTag := action.GenHashTagVBFromInfo(vb, vendor)
					updateResp := model.VendorBillDB.UpdateOne(model.PurchaseOrder{ID: vb.ID}, model.PurchaseOrder{HashTag: newHashTag})
					if updateResp.Status != common.APIStatus.Ok {
						break
					}
					break
				}
			}
		}
	}

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigrateDuetimeFollowPaymentTermVendor(req sdk.APIRequest, resp sdk.APIResponder) error {
	type VendorMigrateDuetime struct {
		VendorIDIn  []int64    `json:"vendorIDIn"`
		CreatedTime *time.Time `json:"createdTime"`
	}

	var input VendorMigrateDuetime
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if input.CreatedTime == nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Created time is required.",
		})
	}

	if len(input.VendorIDIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Vendor ID is required.",
		})
	}
	var vbUpdate int64 = 0

	for _, vendorID := range input.VendorIDIn {
		option := client.APIOption{
			Q: model.Seller{
				SellerID:    vendorID,
				SellerClass: model.CLASS_VENDOR,
			}}

		vendorResp := seller.GetSellers(option)
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not get vendor.",
			})
		}

		vendor := vendorResp.Data.([]*model.Seller)[0]
		if vendor.PaymentTerm == nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Vendor payment not setup paymentTerm",
			})
		}

		//update vendor bill

		var offset int64 = 0
		for {
			vendorBillResp := model.VendorBillDB.Query(
				model.VendorBill{
					ComplexQuery: []*bson.M{
						{"vendor_code": vendor.Code},
						{"created_time": bson.M{"$gte": input.CreatedTime}},
					},
				},
				offset, 100, &primitive.M{"_id": 1},
			)
			if vendorBillResp.Status != common.APIStatus.Ok {
				break
			}

			vendorBills := vendorBillResp.Data.([]*model.VendorBill)
			for _, vb := range vendorBills {
				if vb.VendorCode == "" {
					continue
				}
				newDueTime := action.CalcDueTimeFromIssueTimeWithPaymentTerms(int(*vendor.PaymentTerm), *vb.IssuedTime)
				updateResp := model.VendorBillDB.UpdateOne(model.VendorBill{ID: vb.ID}, model.VendorBill{DueTime: newDueTime})

				if updateResp.Status != common.APIStatus.Ok {
					return resp.Respond(&common.APIResponse{
						Status:  common.APIStatus.Invalid,
						Message: "Can not update duetime vendor bill.",
					})
				}
				vbUpdate++
			}
			offset += 100
		}
	}

	fmt.Println("VendorIDs: ", len(input.VendorIDIn))
	fmt.Println("vbUpdate: ", vbUpdate)
	fmt.Println("MigrateDuetimeFollowPaymentTermVendor END...")

	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigrateUpdatePOItemAdd(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		areYouSure                    = req.GetParam("areYouSure")
		isUpdatePriceAll              = req.GetParam("isUpdatePriceAll") == "true"
		ignoreCreatePurchaseOrderItem = req.GetParam("ignoreCreatePurchaseOrderItem") == "true"
	)
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.PurchaseOrderItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if input.POCode == "" || input.SKU == "" || input.SellerCode == "" || input.PurchaserCode == "" || input.DeliveryWarehouseCode == "" || input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "POCode/SKU/SellerCode/PurchaserCode/DeliveryWarehouseCode/WarehouseCode is required.",
		})
	}

	getPOResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
	if getPOResp.Status != common.APIStatus.Ok {
		return resp.Respond(getPOResp)
	}
	purchaseOrder := getPOResp.Data.([]*model.PurchaseOrder)[0]

	if !ignoreCreatePurchaseOrderItem {
		//check PO
		input.POID = purchaseOrder.POID
		input.POCode = purchaseOrder.POCode
		input.DeliveryWarehouseCode = purchaseOrder.DeliveryWarehouseCode
		input.WarehouseCode = purchaseOrder.WarehouseCode
		input.SellerCode = purchaseOrder.SellerCode
		input.PurchaserCode = purchaseOrder.PurchaserCode
		input.VendorCode = purchaseOrder.VendorCode
		if input.POItemID == 0 {
			input.POItemID, _ = model.GetPOItemID()
		}

		createItemResp := model.PurchaseOrderItemDB.Create(input)
		fmt.Println("createItemResp: ", createItemResp)
		if createItemResp.Status == common.APIStatus.Ok {
			//update search skus in PO
			if !utils.IsContains(purchaseOrder.SKUs, input.SKU) {
				purchaseOrder.SKUs = append(purchaseOrder.SKUs, input.SKU)
			}
			model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POID: purchaseOrder.POID}, model.PurchaseOrder{SKUs: purchaseOrder.SKUs})
		}
	}
	if isUpdatePriceAll {
		// update price all in PO. Make sure that know what you are doing

		purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: input.POCode}, 0, 1000, &primitive.M{"_id": -1})
		if purchaseOrderItemResp.Status != common.APIStatus.Ok {
			return resp.Respond(purchaseOrderItemResp)
		}
		purchaseOrderItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)

		result := CalculatePricePO(purchaseOrderItems, purchaseOrder)
		purchaseOrderResp := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POID: purchaseOrder.POID}, model.PurchaseOrder{
			TotalPrice:           result.TotalPrice,
			TotalVatPrice:        result.TotalVatPrice,
			TotalWithoutVatPrice: result.TotalWithoutVatPrice,
		})
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			return resp.Respond(purchaseOrderResp)
		}

		purchaseOrderItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: input.POCode}, 0, 1000, &primitive.M{"_id": -1})
		if purchaseOrderItemsResp.Status != common.APIStatus.Ok {
			return resp.Respond(purchaseOrderItemsResp)
		}

		purchaseOrderItems = purchaseOrderItemsResp.Data.([]*model.PurchaseOrderItem)
		for _, item := range purchaseOrderItems {
			if item.BillQuantity == nil {
				item.BillQuantity = &item.ExpectQuantity
			}

			model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{POItemID: item.POItemID}, model.PurchaseOrderItem{
				ActualQuantity: *item.BillQuantity,
			})
		}

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done.",
	})
}

func ForceQuotationAssignVendorTime(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.Quotation
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}
	fmt.Println("ForceQuotationAssignVendorTime START...")
	var qUpdate int64 = 0
	t := time.Now()

	quotationQuery := model.Quotation{
		WarehouseCode: input.WarehouseCode,
		ComplexQuery: []*bson.M{
			{
				"assign_vendor_time": bson.M{"$lte": &t},
			},
		}}

	listQuotation := []*model.Quotation{}
	for offset, limit := int64(0), int64(100); ; offset += limit {
		quotationResp := model.QuotationDB.Query(quotationQuery, offset, limit, &bson.M{"_id": 1})
		if quotationResp.Status != common.APIStatus.Ok {
			break
		}

		listQuotation = append(listQuotation, quotationResp.Data.([]*model.Quotation)...)
	}

	for _, quotation := range listQuotation {
		updateTime := quotation.AssignVendorTime.Add(30 * time.Duration(time.Minute))
		model.QuotationDB.UpdateOne(model.Quotation{ID: quotation.ID}, model.Quotation{AssignVendorTime: &updateTime})
		qUpdate++
	}

	fmt.Println("ForceQuotationAssignVendorTime END...")
	fmt.Println("qUpdate: ", qUpdate)
	return resp.Respond(&common.APIResponse{Status: common.APIStatus.Ok, Message: "Processing..."})
}

func MigrateSkuConfigNoneTrading(req sdk.APIRequest, resp sdk.APIResponder) error {

	var (
		inputSellerCode        = req.GetParam("sellerCode")
		inputSellerCodeMigrate = req.GetParam("sellerCodeMigrate")
	)

	if len(inputSellerCode) <= 0 || len(inputSellerCodeMigrate) <= 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Error,
			Message: "Do not have sellerCode or sellerCodeMigrate",
		})
	}

	go migrateSkuConfigNoneTrading(inputSellerCode, inputSellerCodeMigrate)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate skuNonTrading by skuConfig run in background",
	})
}

func migrateSkuConfigNoneTrading(sellerCode string, sellerCodeMigrate string) {
	fmt.Println("Migrate sku non trading BEGIN")
	defer fmt.Println("Migrate sku non trading END")

	var index int64 = 0
	var limit int64 = 100

	for {
		skuConfigResp := model.SkuConfigDB.Query(
			model.Sku{
				SellerCode: sellerCode,
			}, limit*index, limit, &primitive.M{"_id": -1})

		if skuConfigResp.Status != common.APIStatus.Ok {
			break
		}

		skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)

		for _, skuConfig := range skuConfigs {

			if skuConfig.Vendors != nil {
				vendors := *skuConfig.Vendors
				for i := range vendors {
					if vendors[i].Priority != nil && *vendors[i].Priority == 0 {
						vendors[i].Priority = nil
					}
				}
			}

			//Get skuItem non trading
			option := client.APIOption{
				Limit: utils.Pointer.WithInt(100),
				Q:     model.SKUItem{SellerCode: sellerCodeMigrate, ProductCode: skuConfig.ProductCode},
			}

			skuItemNoneTradingResp := marketplace.GetListSKUItem(option)
			if skuItemNoneTradingResp.Status != common.APIStatus.Ok {
				continue
			}

			skuItemNoneTradingData := skuItemNoneTradingResp.Data.([]*model.SKUItem)
			for _, skuItemNonTrading := range skuItemNoneTradingData {
				skuConfig.SKU = skuItemNonTrading.SKU
				skuConfig.SellerCode = sellerCodeMigrate
				skuConfig.ID = &primitive.NilObjectID
				action.CreateSkuConfig(skuConfig)
			}
		}
		index++
	}
}

func MigratePOType(req sdk.APIRequest, resp sdk.APIResponder) error {
	var areYouSure = req.GetParam("areYouSure")
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	go migratePOType()

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate POType is processing...",
	})
}

func migratePOType() {
	fmt.Println("Migrate POType START")
	defer fmt.Println("Migrate POType END")

	index := 0
	_id := primitive.NilObjectID
	for {
		purchaseOrderResp := model.PurchaseOrderDB.Query(
			model.PurchaseOrder{
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			}, 0, 100, &primitive.M{"_id": 1},
		)
		if purchaseOrderResp.Status != common.APIStatus.Ok {
			break
		}

		purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)
		for _, po := range purchaseOrders {
			_id = *po.ID
			if po.Type != "" {
				continue
			}
			model.PurchaseOrderDB.UpdateOne(
				model.PurchaseOrder{ID: po.ID},
				model.PurchaseOrder{Type: enum.POTypeVal.NORMAL},
			)
			index++
		}
	}
	fmt.Println("Total PO: ", index)
}

var isRunCheckVBWrongQuantity bool

func ToolCheckVendorBillWrongQuantity(req sdk.APIRequest, resp sdk.APIResponder) error {
	if isRunCheckVBWrongQuantity {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Processing...",
		})
	} else {
		isRunCheckVBWrongQuantity = true
	}

	defer func() {
		isRunCheckVBWrongQuantity = false
	}()

	var (
		_id_offset = primitive.NilObjectID
	)

	vendorBillData := []*model.VendorBill{}
	totalBack := 0
	for {
		getVBResp := model.VendorBillDB.Query(model.VendorBill{
			Status:          enum.VendorBillStatus.APPROVED,
			IsWrongQuantity: utils.Pointer.WithBool(true),
			ComplexQuery: []*primitive.M{
				{"_id": bson.M{"$gt": _id_offset}},
			},
		}, 0, 100, &primitive.M{
			"_id": 1,
		})
		if getVBResp.Status != common.APIStatus.Ok {
			break
		}

		vendorBills := getVBResp.Data.([]*model.VendorBill)

		vendorBillData = append(vendorBillData, getVBResp.Data.([]*model.VendorBill)...)
		_id_offset = *vendorBills[len(vendorBills)-1].ID
	}

	for _, vb := range vendorBillData {
		getPOResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: vb.POCode})
		if getPOResp.Status != common.APIStatus.Ok {
			continue
		}
		po := getPOResp.Data.([]*model.PurchaseOrder)[0]
		// check PO status
		if po.Status != enum.PurchaseOrderStatus.COMPLETED {
			model.VendorBillDB.UpdateOne(model.VendorBill{
				VendorBillCode: vb.VendorBillCode,
			}, model.VendorBill{
				IsWrongQuantity: utils.Pointer.WithBool(false),
			})
			totalBack++
		}
	}

	fmt.Println("totalVB WT back: ", totalBack)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "totalVB WT back: " + strconv.Itoa(totalBack),
	})
}

// Before when using this function, please check logic func action.calculatePricePO again cuz it's main logic
func CalculatePricePO(items []*model.PurchaseOrderItem, input *model.PurchaseOrder) *model.PurchaseOrder {

	var tempTotalWithoutVatPrice float64 = 0
	var tempTotalVatPrice float64 = 0
	var tempTotalPrice float64 = 0
	var newItems []*model.PurchaseOrderItem
	// poItem includes: UnitPrice, DiscountPercent, VAT, ExpectQuantity
	for _, poItem := range items {
		if poItem == nil || poItem.UnitPrice == 0 {
			continue
		}

		// Discount price
		var discountPriceTemp float64 = 0
		if poItem.DiscountPercent != nil && *poItem.DiscountPercent > 0 {
			discountPriceTemp = poItem.UnitPrice * *poItem.DiscountPercent / 100
		}
		poItem.DiscountPrice = &discountPriceTemp

		// price after discount
		var tempPriceAfterDiscount float64 = 0
		tempPriceAfterDiscount = poItem.UnitPrice - *poItem.DiscountPrice
		poItem.PriceAfterDiscount = &tempPriceAfterDiscount

		// vat price
		var tempVATPrice float64 = 0
		if poItem.VAT != nil && *poItem.VAT > 0 {
			tempVATPrice = *poItem.PriceAfterDiscount * *poItem.VAT / 100
		}
		poItem.VATPrice = &tempVATPrice

		// Total price item
		tempTotalPriceItem := *poItem.PriceAfterDiscount*float64(poItem.ExpectQuantity) + *poItem.VATPrice*float64(poItem.ExpectQuantity)
		poItem.TotalPrice = &tempTotalPriceItem

		// PO Total
		tempTotalWithoutVatPrice += *poItem.PriceAfterDiscount * float64(poItem.ExpectQuantity)
		tempTotalVatPrice += *poItem.VATPrice * float64(poItem.ExpectQuantity)
		newItems = append(newItems, poItem)
	}
	tempTotalPrice = tempTotalWithoutVatPrice + tempTotalVatPrice

	input.TotalWithoutVatPrice = &tempTotalWithoutVatPrice
	input.TotalVatPrice = &tempTotalVatPrice
	input.TotalPrice = &tempTotalPrice
	input.Items = newItems
	return input
}

func MigrateBillQtyPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var poCodes = req.GetParam("poCodes")

	action.WarmupBillQtyOfPO(strings.Split(poCodes, ","))

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate POType is processing...",
	})
}
func MigrateInboundRequestHedging(req sdk.APIRequest, resp sdk.APIResponder) error {

	var areYouSure = req.GetParam("areYouSure") == "true"
	var inboundRequestID = sdk.ParseInt64(req.GetParam("inboundRequestID"), 0)
	if areYouSure {
		// Tìm tất cả inbound request hiện tại: đã được duyệt và còn thời gian tồn tại
		// inbound request type : OTHER
		// inbound request status: CONFIRMED
		// purchaseOrderCode != ""

		_id := primitive.NilObjectID
		for {
			inboundRequestQuery := model.InboundRequest{
				Status:             enum.InboundRequestStatus.CONFIRMED,
				InboundRequestType: enum.InboundRequestType.OTHER,
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			}

			if inboundRequestID != 0 {
				inboundRequestQuery.InboundRequestID = inboundRequestID
			}
			inboundRequestResp := model.InboundRequestDB.Query(inboundRequestQuery, 0, 100, &primitive.M{"_id": 1})
			if inboundRequestResp.Status != common.APIStatus.Ok {
				break
			}

			// duyệt qua từng kết quả và xử lý theo logic sau:
			// 1. DB inbound request: cập nhật từ field inboundRequestCode => PoCodes
			//
			// 2. DB inbound request item: cập nhật thêm poCode
			//
			// 3. DB purchase order: do nothing
			//
			// 4. DB purchase order item: thêm field inboundRequestCode, inboundRequestID
			//

			inboundRequests := inboundRequestResp.Data.([]*model.InboundRequest)
			for _, inboundRequest := range inboundRequests {
				_id = *inboundRequest.ID

				if inboundRequest.PurchaseOrderCode == nil || *inboundRequest.PurchaseOrderCode == "" {
					continue
				}
				// Nếu PO đã migrate rồi thì bỏ qua
				if inboundRequest.POCodes != nil && len(*inboundRequest.POCodes) > 0 {
					continue
				}

				model.InboundRequestDB.UpdateOne(
					model.InboundRequest{ID: inboundRequest.ID},
					model.InboundRequest{
						POCodes: &[]string{*inboundRequest.PurchaseOrderCode},
						Tags:    &[]string{"has_po"},
					},
				)
				model.InboundRequestItemDB.UpdateMany(
					model.InboundRequestItem{InboundRequestCode: inboundRequest.InboundRequestCode},
					model.InboundRequestItem{
						POCode: *inboundRequest.PurchaseOrderCode,
					},
				)
				model.PurchaseOrderItemDB.UpdateMany(
					model.PurchaseOrderItem{POCode: *inboundRequest.PurchaseOrderCode},
					model.PurchaseOrderItem{
						InboundRequestCode: inboundRequest.InboundRequestCode,
						InboundRequestID:   inboundRequest.InboundRequestID,
					},
				)
			}
		}

	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing...",
	})

}

func MigrateVendorBillPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	mapPO := map[int64]string{
		766882: "PO656852",
		766554: "PO657960",
		766346: "PODN657953",
		766309: "PO657329",
		766168: "PO657382",
		766003: "PODN657948",
		765610: "PO654522",
		765514: "POHN657938",
		765120: "POHN656827",
		764815: "PODN656790",
		764814: "PODN656788",
		764595: "PO656176",
		764451: "PO656224",
		764273: "PO657426",
		764144: "PO656854",
		763592: "PODN656824",
		763589: "PO655597",
		763112: "PO656145",
		762559: "PODN656783",
		762551: "PODN656785",
		762339: "PO656211",
		762089: "PO656211",
		761542: "PO657987",
		761439: "POHN655622",
		761103: "PODN656801",
		759938: "POHN655626",
		759901: "PO652683",
		759689: "PO631554",
		759367: "PO653305",
		758856: "POHN656152",
		758569: "POHN656152",
		758529: "POHN652151",
		758514: "POHN652150",
		758509: "POHN652145",
		758505: "POHN653249",
		758303: "POHN656151",
		758213: "PO652713",
		758008: "POHN656151",
		757866: "PODN656802",
		757853: "PODN652720",
		757665: "POHN652144",
		757652: "POHN653241",
		757637: "PO652684",
		757634: "PO652684",
		757527: "POHN652699",
		756737: "POHN651541",
		756598: "POHN656147",
		755402: "POHN649793",
		754767: "PODN651501",
		754727: "PO649892",
		753497: "POHN648209",
		753458: "PO648136",
		753264: "PO649278",
		753260: "PO649281",
		753250: "PO649279",
		753246: "PO649272",
		753037: "PO649908",
		752757: "POHN645765",
		751965: "PO644071",
		751528: "PO648221",
		751500: "PODN648696",
		751495: "PO648686",
		751458: "PO648685",
		751091: "PO642472",
		750430: "PODN651500",
		750426: "PODN651499",
		749875: "POHN645803",
		749860: "PO645865",
		749238: "POHN657989",
		747370: "PO644076",
		747146: "PO632637",
		746671: "PO644617",
		746475: "PO644650",
		746401: "PODN645319",
		746396: "PODN646997",
		746079: "PO645231",
		744031: "POHN641289",
		739552: "PO640710",
		737746: "PO644635",
		737518: "PO638333",
		737030: "PODN645807",
		734827: "POHN638381",
		734767: "PODN639569",
		727134: "POHN633125",
		726755: "PODN633184",
		726754: "PODN633183",
		725153: "POHN638946",
		725147: "PODN632616",
		724727: "POHN632105",
		724683: "POHN640663",
		724291: "PO632059",
		723093: "PO627446",
		718165: "PODN626853",
		716334: "PODN628549",
		715455: "PO627370",
		710367: "POHN622174",
		704860: "POHN617460",
		703447: "POHN610336",
		690334: "PODN619232",
		686478: "POHN645852",
		680089: "PO608189",
		663525: "PO595995",
		641717: "POHN584336",
	}

	for vbID, poCode := range mapPO {
		if vbID == 0 || poCode == "" {
			continue
		}
		vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillID: vbID})
		if vbResp.Status != common.APIStatus.Ok {
			continue
		}
		vb := vbResp.Data.([]*model.VendorBill)[0]

		model.VendorBillDB.UpdateOne(model.VendorBill{ID: vb.ID}, model.VendorBill{POCode: poCode})
	}

	fmt.Print(mapPO)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing...",
	})
}

func MigratePOStatus(req sdk.APIRequest, resp sdk.APIResponder) error {

	// PO status COMPLETED + bill status WAITING_BILL => AWAITING_BILLED
	model.PurchaseOrderDB.UpdateMany(
		bson.M{
			"status":             enum.PurchaseOrderStatus.CONFIRMED,
			"first_inbound_time": bson.M{"$ne": nil},
		},
		model.PurchaseOrder{
			Status: enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
			MigrateMetadata: &map[string]string{
				"old_status": string(enum.PurchaseOrderStatus.CONFIRMED),
			},
		},
	)

	// // PO status COMPLETED + bill status BILL_RECEIVED => AWAITING_BILLED
	// model.PurchaseOrderDB.UpdateMany(
	// 	bson.M{
	// 		"status":      enum.PurchaseOrderStatus.COMPLETED,
	// 		"bill_status": enum.POBillStatus.BILL_RECEIVED,
	// 	},
	// 	model.PurchaseOrder{
	// 		Status: enum.PurchaseOrderStatus.AWAITING_BILLED, // worker will update to BILLED or COMPLETED
	// 	},
	// )

	// // PROCESSING
	// _id := primitive.NilObjectID
	// for {
	// 	// get all po
	// 	purchaseOrderResp := model.PurchaseOrderDB.Query(
	// 		model.PurchaseOrder{
	// 			Status: enum.PurchaseOrderStatus.PARTIALLY_RECEIVED,
	// 			ComplexQuery: []*bson.M{
	// 				{"_id": bson.M{"$gt": _id}},
	// 			},
	// 		},
	// 		0, 100, &primitive.M{"_id": 1},
	// 	)
	// 	if purchaseOrderResp.Status == common.APIStatus.NotFound {
	// 		break
	// 	}
	// 	purchaseOrders := purchaseOrderResp.Data.([]*model.PurchaseOrder)

	// 	for _, po := range purchaseOrders {
	// 		_id = *po.ID

	// 		// total ActualQuantity, total ExpectQuantity
	// 		var totalActualQuantity, totalExpectQuantity int64 = 0, 0

	// 		// #2 cập nhật actual cho PO item
	// 		poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, &primitive.M{"_id": -1})
	// 		if poItemResp.Status == common.APIStatus.Ok {
	// 			poItems := poItemResp.Data.([]*model.PurchaseOrderItem)
	// 			for i := range poItems {
	// 				poItem := poItems[i]
	// 				totalExpectQuantity += poItem.ExpectQuantity
	// 				totalActualQuantity += poItem.ActualQuantity
	// 			}
	// 		}

	// 		status := enum.PurchaseOrderStatus.PARTIALLY_RECEIVED
	// 		if po.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED && totalActualQuantity >= totalExpectQuantity {
	// 			status = enum.PurchaseOrderStatus.RECEIVED
	// 		}

	// 		model.PurchaseOrderDB.UpdateOne(
	// 			model.PurchaseOrder{ID: po.ID},
	// 			model.PurchaseOrder{
	// 				Status: status,
	// 				MigrateMetadata: &map[string]string{
	// 					"old_status": string(po.Status),
	// 				},
	// 			},
	// 		)
	// 	}
	// }

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Migrate POType is processing...",
	})
}

func MigratePOItemAdd(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		areYouSure = req.GetParam("areYouSure")
	)
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input []model.PurchaseOrderItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	for _, item := range input {
		if item.POCode == "" || item.SKU == "" || item.SellerCode == "" || item.PurchaserCode == "" || item.DeliveryWarehouseCode == "" || item.WarehouseCode == "" {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "POCode/SKU/SellerCode/PurchaserCode/DeliveryWarehouseCode/WarehouseCode is required.",
			})
		}
	}

	getPOResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input[0].POCode})
	if getPOResp.Status != common.APIStatus.Ok {
		return resp.Respond(getPOResp)
	}
	purchaseOrder := getPOResp.Data.([]*model.PurchaseOrder)[0]

	items := []model.PurchaseOrderItem{}

	for _, item := range input {
		item.POID = purchaseOrder.POID
		item.POCode = purchaseOrder.POCode
		item.DeliveryWarehouseCode = purchaseOrder.DeliveryWarehouseCode
		item.WarehouseCode = purchaseOrder.WarehouseCode
		item.SellerCode = purchaseOrder.SellerCode
		item.PurchaserCode = purchaseOrder.PurchaserCode
		item.VendorCode = purchaseOrder.VendorCode
		if item.POItemID == 0 {
			item.POItemID, _ = model.GetPOItemID()
		}

		items = append(items, item)
	}

	createItemResp := model.PurchaseOrderItemDB.Create(items)
	fmt.Println("createItemResp: ", createItemResp)
	if createItemResp.Status == common.APIStatus.Ok {
		//update search skus in PO
		skus := []string{}
		for _, item := range input {
			skus = append(skus, item.SKU)
		}

		purchaseOrder.SKUs = skus
		res := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POID: purchaseOrder.POID}, model.PurchaseOrder{SKUs: purchaseOrder.SKUs})
		return resp.Respond(res)
	}

	return resp.Respond(createItemResp)
}

func MigrateUpdatePOItemUpdate(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		areYouSure = req.GetParam("areYouSure")
	)
	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	var input model.PurchaseOrderItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	if input.POCode == "" || input.SKU == "" || input.SellerCode == "" || input.PurchaserCode == "" || input.DeliveryWarehouseCode == "" || input.WarehouseCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "POCode/SKU/SellerCode/PurchaserCode/DeliveryWarehouseCode/WarehouseCode is required.",
		})
	}

	getPOItemResp := model.PurchaseOrderItemDB.QueryOne(model.PurchaseOrderItem{POItemID: input.POItemID, POCode: input.POCode})
	if getPOItemResp.Status != common.APIStatus.Ok {
		return resp.Respond(getPOItemResp)
	}
	updater := getPOItemResp.Data.([]*model.PurchaseOrderItem)[0]

	if input.ActualQuantity >= 0 {
		updater.ActualQuantity = input.ActualQuantity
	}
	model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{POItemID: input.POItemID, POCode: input.POCode}, updater)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Done.",
	})
}
