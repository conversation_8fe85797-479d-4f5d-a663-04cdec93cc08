package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type VendorBill struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ApprovedTime    *time.Time          `json:"approvedTime,omitempty" bson:"approved_time,omitempty"`
	DueTime         *time.Time          `json:"dueTime,omitempty" bson:"due_time,omitempty"`       // null, -- <PERSON><PERSON><PERSON> đến hạn thanh toán (tính toán dựa trên <PERSON>ạn <PERSON>h toán/ payment term c<PERSON><PERSON> cung cấp được user l<PERSON><PERSON> tr<PERSON><PERSON>)
	IssuedTime      *time.Time          `json:"issuedTime,omitempty" bson:"issued_time,omitempty"` // "2020-11-01T04:00:14.084+00:00", -- Ngày phát hành hóa đơn
	AccountingTime  *time.Time          `json:"accountingTime,omitempty" bson:"accounting_time,omitempty"`
	// ReceivedTime   *time.Time `json:"receivedTime,omitempty" bson:"received_time,omitempty"` // "2020-11-19T04:00:16.409715+00:00", -- Ngày giờ nhận hóa đơn (trên hệ thống Bizzi)

	ApprovedByName string `json:"approvedByName,omitempty" bson:"approved_by_name,omitempty"`
	ApprovedByID   int64  `json:"approvedById,omitempty" bson:"approved_by_id,omitempty"`

	VendorBillID   int64  `json:"vendorBillID,omitempty" bson:"vendor_bill_id,omitempty"`
	VendorBillCode string `json:"vendorBillCode,omitempty" bson:"vendor_bill_code,omitempty"`

	BizziInvoiceID  string `json:"bizziInvoiceID,omitempty" bson:"bizzi_invoice_id,omitempty"`
	GrowthInvoiceID string `json:"growthInvoiceID,omitempty" bson:"growth_invoice_id,omitempty"`

	POCode string `json:"poCode,omitempty" bson:"po_code,omitempty"`

	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	IsCompletedPO   *bool `json:"isCompletedPO,omitempty" bson:"is_completed_po,omitempty"`
	IsWrongQuantity *bool `json:"isWrongQuantity,omitempty" bson:"is_wrong_quantity,omitempty"`
	IsWrongLotDate  *bool `json:"isWrongLotDate,omitempty" bson:"is_wrong_lot_date,omitempty"`

	InvoiceNumber string `json:"invoiceNumber,omitempty" bson:"invoice_number,omitempty"` // "9877659", -- Số hóa đơn

	VendorTaxCode         string `json:"vendorTaxCode,omitempty" bson:"vendor_tax_code,omitempty"`
	VendorLegalName       string `json:"vendorLegalName,omitempty" bson:"vendor_legal_name,omitempty"`              // "CÔNG TY TNHH MULTI-COLOR VIỆT NAM", -- Tên công ty bán hàng (supplier/vendor)
	VendorLegalNameOrigin string `json:"vendorLegalNameOrigin,omitempty" bson:"vendor_legal_name_origin,omitempty"` // "CÔNG TY TNHH MULTI-COLOR VIỆT NAM", -- Tên công ty bán hàng nguyên bản bizzi sẽ không bị cập nhật

	BuyerTaxCode   string `json:"buyerTaxCode,omitempty" bson:"buyer_tax_code,omitempty"`
	BuyerLegalName string `json:"buyerLegalName,omitempty" bson:"buyer_legal_name,omitempty"` // "CÔNG TY TNHH BIZZI VIETNAM", -- Tên công ty mua hàng
	// BuyerDisplayName string `json:"buyerDisplayName,omitempty" bson:"buyer_display_name,omitempty"` // "CONG TY TNHH DUOC PHAM  MEDX _121135593", -- Họ tên người mua hàng
	BuyerAddressLine string `json:"buyerAddressLine,omitempty" bson:"buyer_address_line,omitempty"`

	Vat                   *float64 `json:"vat,omitempty" bson:"vat,omitempty"`                                        // 5, -- Thuế suất (0 tương ứng 0%; 5 tương ứng 5%; 10 tương ứng 10%; -1 tương ứng Không chịu thuế; null tương ứng Không kê khai, -1000 tương ứng Thuế hỗn hợp)
	TotalVatPrice         *float64 `json:"totalVatPrice,omitempty" bson:"total_vat_price,omitempty"`                  // 3693000, -- Tổng số tiền thuế VAT
	TotalDiscountPrice    *float64 `json:"totalDiscountPrice,omitempty" bson:"total_discount_price,omitempty"`        // 0, -- Tổng số tiền chiết khấu trên toàn bộ hóa đơn
	TotalDiscountAfterVat *float64 `json:"totalDiscountAfterVat,omitempty" bson:"total_discount_after_vat,omitempty"` // tổng tiền chiết khấu sau VAT

	TotalPriceWithoutVat *float64 `json:"totalPriceWithoutVat,omitempty" bson:"total_price_without_vat,omitempty"` // 36930000, -- Tổng số tiền chưa VAT
	TotalPrice           *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`                       // ********, -- Tổng số tiền sau VAT
	RemainingMoney       *float64 `json:"remainingMoney,omitempty" bson:"remaining_money,omitempty"`               // ********, -- Tổng số tiền sau VAT

	// SourceDocument        string `json:"sourceDocument,omitempty" bson:"source_document,omitempty"`
	VendorReference   string `json:"vendorReference,omitempty" bson:"vendor_reference,omitempty"`
	BankAccountNumber string `json:"bankAccountNumber,omitempty" bson:"bank_account_number,omitempty"`

	// Difference
	HaveInvoiceDifference *bool    `json:"haveInvoiceDifference,omitempty" bson:"have_invoice_difference,omitempty"`
	DifferencePrice       *float64 `json:"differencePrice,omitempty" bson:"difference_price,omitempty"`

	PurchaseUserID       string   `json:"purchaseUserID,omitempty" bson:"purchase_user_id,omitempty"`
	PurchaseUserName     string   `json:"purchaseUserName,omitempty" bson:"purchase_user_name,omitempty"`
	Incoterm             string   `json:"incoterm,omitempty" bson:"incoterm,omitempty"`
	Iournal              string   `json:"journal,omitempty" bson:"journal,omitempty"`
	FiscalPosition       *float64 `json:"fiscalPosition,omitempty" bson:"fiscal_position,omitempty"`
	AccountID            string   `json:"accountID,omitempty" bson:"account_id,omitempty"`
	AccountName          string   `json:"accountName,omitempty" bson:"account_name,omitempty"`
	PaymentTerm          *float64 `json:"paymentTerm,omitempty" bson:"payment_term,omitempty"`
	ReferenceDescription string   `json:"referenceDescription,omitempty" bson:"reference_description,omitempty"`
	TemplateCode         string   `json:"templateCode,omitempty" bson:"template_code,omitempty"`   // 01GTKT3/003", -- Mẫu số hóa đơn
	InvoiceSeries        string   `json:"invoiceSeries,omitempty" bson:"invoice_series,omitempty"` // "PA/20P", -- Ký hiệu hóa đơn
	Note                 *string  `json:"note,omitempty" bson:"note,omitempty"`

	HashTag string `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`

	MigrateMetadata *MigrateMetadata `json:"-" bson:"migrate_metadata,omitempty"`

	// IsCompletedMapping *bool                      `json:"isCompletedMapping,omitempty" bson:"is_completed_mapping,omitempty"`
	Status enum.VendorBillStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	CreatedTransferRequestCodes []string `json:"createdTransferRequestCodes,omitempty" bson:"created_transfer_request_codes,omitempty"`

	ReasonTag string `json:"reasonTag,omitempty" bson:"reason_tag,omitempty"`

	TicketInstanceCodes []string `json:"ticketInstanceCodes,omitempty" bson:"ticket_instance_codes,omitempty"`

	// for create

	Items []VendorBillItem `json:"items,omitempty" bson:"-"`

	//sp filter item
	ProductCodes []string `json:"productCodes,omitempty" bson:"product_codes,omitempty"`

	// query at backend
	ComplexQuery        []*bson.M                    `json:"-" bson:"$and,omitempty"`
	VendorCodeIn        []string                     `json:"vendorCodeIn,omitempty" bson:"-"`
	VendorBillIDIn      []int                        `json:"vendorBillIDIn,omitempty" bson:"-"`
	VendorBillCodeIn    []string                     `json:"vendorBillCodeIn,omitempty" bson:"-"`
	InvoiceNumberIn     []string                     `json:"invoiceNumberIn,omitempty" bson:"-"`
	POCodeIn            []string                     `json:"poCodeIn,omitempty" bson:"-"`
	StatusIn            []enum.VendorBillStatusValue `json:"statusIn,omitempty" bson:"-"`
	DueTimeQuery        *time.Time                   `json:"dueTimeQuery,omitempty" bson:"-"`
	IssuedTimeQuery     *time.Time                   `json:"issuedTimeQuery,omitempty" bson:"-"`
	IssuedTimeFromQuery *time.Time                   `json:"issuedTimeFromQuery,omitempty" bson:"-"`
	IssuedTimeToQuery   *time.Time                   `json:"issuedTimeToQuery,omitempty" bson:"-"`
	DueTimeFromQuery    *time.Time                   `json:"dueTimeFromQuery,omitempty" bson:"-"`
	DueTimeToQuery      *time.Time                   `json:"dueTimeToQuery,omitempty" bson:"-"`
	ProductCodeIn       []string                     `json:"productCodeIn,omitempty" bson:"-"`
	CreatedFrom         *time.Time                   `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo           *time.Time                   `json:"createdTo,omitempty" bson:"-"`
	LastestID           *primitive.ObjectID          `json:"lastestID,omitempty" bson:"-"`

	ReasonTagIn []string `json:"reasonTagIn,omitempty" bson:"-"`

	// IsCompletedMappingQuery string                       `json:"isCompletedMappingQuery,omitempty" bson:"-"`
	BuymedAIinvoiceID int64 `json:"buymedAIinvoiceID,omitempty" bson:"buymed_ai_invoice_id,omitempty"`

}

func (vb VendorBill) GenProductCodesFromItems() []string {
	productCodes := []string{}
	for _, item := range vb.Items {
		if item.ProductCode == "" {
			continue
		}
		for i := range productCodes {
			if item.ProductCode == productCodes[i] {
				continue
			}
		}
		productCodes = append(productCodes, item.ProductCode)
	}
	return productCodes
}

func GenProductCodesFromItems(items []*VendorBillItem) []string {
	productCodes := []string{}
	for _, item := range items {
		if item.ProductCode == "" {
			continue
		}
		for i := range productCodes {
			if item.ProductCode == productCodes[i] {
				continue
			}
		}
		productCodes = append(productCodes, item.ProductCode)
	}
	return productCodes
}

// VendorBillDB ...
var VendorBillDB = &db.Instance{
	ColName:        "vendor_bill",
	TemplateObject: &VendorBill{},
}

var VendorBillReplicaDB = &db.Instance{
	ColName:        "vendor_bill",
	TemplateObject: &VendorBill{},
}

// InitSkuModel ...
func InitVendorBillModel(s *mongo.Database) {
	VendorBillDB.ApplyDatabase(s)

	// // - INDEX
	// // - BE
	// // VendorBillCode
	// // BizziInvoiceID
	// // Status
	// // VendorBillCode, Status
	// // POCode
	// // - WORKER
	// // VendorCode, Status
	// // - FE
	// // VendorBillCode
	// // InvoiceNumber
	// // HashTag
	// // DueTimeQuery
	// // IssuedTimeQuery
	// // SellerCode, status

	// t := true
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_bill_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_bill_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "bizzi_invoice_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// 	Sparse:     &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "vendor_bill_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "due_time", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "issued_time", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "invoice_number", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "hash_tag", Value: 1},
	// 	primitive.E{Key: "po_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // debt worker
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // FE
	// _ = VendorBillDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

func InitVendorBillReplicaModel(s *mongo.Database) {
	VendorBillReplicaDB.ApplyDatabase(s)
}

type VendorBillItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	VendorBillID   int64  `json:"vendorBillID,omitempty" bson:"vendor_bill_id,omitempty"`
	VendorBillCode string `json:"vendorBillCode,omitempty" bson:"vendor_bill_code,omitempty"`

	// VendorBillItemID   int64  `json:"vendorBillItemID,omitempty" bson:"vendor_bill_item_id,omitempty"`
	VendorBillItemCode string `json:"vendorBillItemCode,omitempty" bson:"vendor_bill_item_code,omitempty"`

	BizziInvoiceID     string `json:"bizziInvoiceID,omitempty" bson:"bizzi_invoice_id,omitempty"`
	BizziInvoiceItemID string `json:"bizziInvoiceItemID,omitempty" bson:"bizzi_invoice_item_id,omitempty"`

	ItemCode  string `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	ItemName  string `json:"itemName,omitempty" bson:"item_name,omitempty"`
	AccountID string `json:"accountID,omitempty" bson:"account_id,omitempty"`

	Unit           string `json:"unit,omitempty" bson:"unit,omitempty"`
	Quantity       *int64 `json:"quantity,omitempty" bson:"quantity,omitempty"`
	ActualQuantity int64  `json:"actualQuantity,omitempty" bson:"actual_quantity,omitempty"` // SL thực từ kho: callback

	Vat                *float64 `json:"vat,omitempty" bson:"vat,omitempty"`
	VatPrice           *float64 `json:"vatPrice,omitempty" bson:"vat_price,omitempty"`
	DiscountPercent    *float64 `json:"discountPercent,omitempty" bson:"discount_percent,omitempty"`
	DiscountPrice      *float64 `json:"discountPrice,omitempty" bson:"discount_price,omitempty"`
	PriceAfterDiscount *float64 `json:"priceAfterDiscount,omitempty" bson:"price_after_discount,omitempty"`
	TotalDiscountPrice *float64 `json:"totalDiscountPrice,omitempty" bson:"total_discount_price,omitempty"`

	Price      *float64 `json:"price,omitempty" bson:"price,omitempty"`
	TotalPrice *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`

	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	Lot     string `json:"lot" bson:"lot,omitempty"`
	ExpDate string `json:"expDate" bson:"exp_date,omitempty"`
	// LineID             int64    `json:"lineID,omitempty" bson:"line_id,omitempty"`

	POCode             string       `json:"poCode" bson:"po_code,omitempty"`
	POTrackings        []POTracking `json:"poTrackings" bson:"po_trackings,omitempty"`
	IsInboundCompleted *bool        `json:"isInboundCompleted,omitempty" bson:"is_inbound_completed,omitempty"`

	// For export
	VendorCode      string `json:"vendorCode,omitempty" bson:"-"`
	VendorTaxCode   string `json:"vendorTaxCode,omitempty" bson:"-"`
	VendorLegalName string `json:"vendorLegalName,omitempty" bson:"-"` // "CÔNG TY TNHH MULTI-COLOR VIỆT NAM", -- Tên công ty bán hàng (supplier/vendor)

	MigrateMetadata *MigrateMetadata `json:"-" bson:"migrate_metadata,omitempty"`

	ComplexQuery     []*bson.M `json:"-" bson:"$and,omitempty"`
	POCodeIn         []string  `json:"poCodeIn,omitempty" bson:"-"`
	VendorBillIDIn   []int64   `json:"vendorBillIDIn,omitempty" bson:"-"`
	VendorBillCodeIn []string  `json:"vendorBillCodeIn,omitempty" bson:"-"`

	// for output
	IssuedTime *time.Time `json:"issuedTime,omitempty" bson:"-"` // "2020-11-01T04:00:14.084+00:00", -- Ngày phát hành hóa đơn
}

type POTracking struct {
	CreatedTime *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`

	POCode    string `json:"poCode" bson:"po_code,omitempty"`
	ProductID int64  `json:"productID,omitempty" bson:"product_id,omitempty"`

	Lot     string `json:"lot" bson:"lot,omitempty"`
	ExpDate string `json:"expDate" bson:"exp_date,omitempty"`

	ReceivedQuantity int64 `json:"receivedQuantity,omitempty" bson:"received_quantity,omitempty"` // callback
}

// VendorBillItemDB ...
var VendorBillItemDB = &db.Instance{
	ColName:        "vendor_bill_item",
	TemplateObject: &VendorBillItem{},
}

// InitSkuModel ...
func InitVendorBillItemModel(s *mongo.Database) {
	VendorBillItemDB.ApplyDatabase(s)

	// // - INDEX
	// // - BE
	// // VendorBillCode
	// // VendorBillCode, IsInboundCompleted
	// // VendorBillItemCode
	// // ProductID
	// // - FE
	// // VendorBillCode
	// // vendor_bill_id, is_inbound_completed

	// t := true
	// _ = VendorBillItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_bill_item_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = VendorBillItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_bill_id", Value: 1},
	// 	primitive.E{Key: "is_inbound_completed", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = VendorBillItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_bill_code", Value: 1},
	// 	primitive.E{Key: "is_inbound_completed", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = VendorBillItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

}
