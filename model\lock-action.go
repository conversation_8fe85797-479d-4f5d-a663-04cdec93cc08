package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type LockAction struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Key string `json:"key,omitempty" bson:"key,omitempty"`
}

// LockActionDB ...
var LockActionDB = &db.Instance{
	ColName:        "lock_ation",
	TemplateObject: &LockAction{},
}

func InitLockActionModel(s *mongo.Database) {
	LockActionDB.ApplyDatabase(s)

	// t := true
	// _ = LockActionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "key", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// var expire int32 = 5 // expire
	// _ = LockActionDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })
}
