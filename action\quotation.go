package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// select option (private or publish) for quotation
type privateQuotation struct {
	stopPublish         *bool // stop publish           (lv_0)
	forcePublish        *bool // force publish          (lv_1)
	existVendorFavorite *bool // exist vendor favorite  (lv_2)
}

var PurchaserWarehouseCode = map[string][]string{
	"PUR_HCM": []string{"HCM", "BD", "TTN"},
	"PUR_DN":  []string{"DN"},
	"PUR_HN":  []string{"HN"},
}

func (p *privateQuotation) isPrivate() bool {
	if p.stopPublish != nil && *p.stopPublish {
		return true
	}
	if p.forcePublish != nil && *p.forcePublish {
		// fmt.Println("forcePublish ....")
		return false
	}
	if p.existVendorFavorite != nil && *p.existVendorFavorite {
		// fmt.Println("existVendorFavorite ....")
		return true
	}
	return false
}

// SearchQuotation ...
// func SearchQuotation(query model.Quotation, offset, limit int64, isDistinct bool) *common.APIResponse {
func SearchQuotation(query SearchQuery, offset, limit int64, isDistinct bool) *common.APIResponse {

	// if query.ProductName != "" {
	// 	search := utils.ParserQ(query.ProductName)
	// 	query.ProductName = ""
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{
	// 		"hash_tag": primitive.Regex{Pattern: fmt.Sprintf(".*%s.*", search), Options: ""},
	// 	})
	// }
	// quotationResp := model.QuotationDB.Query(query, offset, limit, nil)

	// query ES search
	quotationResp := SearchProduct(nil, &query)
	if quotationResp.Status != common.APIStatus.Ok {
		return quotationResp
	}
	quotations := quotationResp.Data.([]*model.Quotation)

	// distinct
	if isDistinct == true {
		dataDistinct := []*model.Quotation{}
		mapDistinct := make(map[int64]bool)
		for i := range quotations {
			quotation := quotations[i]
			if mapDistinct[quotation.ProductID] {
				continue
			}
			mapDistinct[quotation.ProductID] = true

			dataDistinct = append(dataDistinct, quotation)
		}

		// update list
		quotations = dataDistinct
	}

	// return
	quotationResp.Data = quotations
	if len(quotations) == 0 {
		quotationResp.Status = common.APIStatus.NotFound
		return quotationResp
	}
	return quotationResp
}

// GetVendorQuotation ...
func GetVendorQuotation(query model.Quotation, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	var sortField *bson.M
	switch sort {
	case "vendorCode":
		sortField = &primitive.M{"vendor_code": 1}
	case "-vendorCode":
		sortField = &primitive.M{"vendor_code": -1}
	case "quantity":
		sortField = &primitive.M{"quantity_expect": 1}
	case "-quantity":
		sortField = &primitive.M{"quantity_expect": -1}
	case "product_id":
		sortField = &primitive.M{"product_id": 1}
	case "-product_id":
		sortField = &primitive.M{"product_id": -1}
	case "-lastUpdatedTime":
		sortField = &primitive.M{"last_updated_time": -1}
	case "seller_code":
		sortField = &primitive.M{"seller_code": 1}
	case "-seller_code":
		sortField = &primitive.M{"seller_code": -1}
	default:
		sortField = &primitive.M{"product_id": -1}
	}

	quotationResp := model.QuotationDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.QuotationDB.Count(query)
		quotationResp.Total = countResp.Total
	}

	return quotationResp
}

type CreateQuotationsInput struct {
	Mode  string            `json:"mode,omitempty"` // OVERRITE, ADD
	Items []model.Quotation `json:"items,omitempty"`
}

// SellerCreateQuotations creates quotations for a seller based on the provided input.
// It performs various validations and checks before creating or updating the quotations.
//
// Parameters:
// - input: CreateQuotationsInput - The input data for creating quotations.
// - sellerCode: string - The code of the seller creating the quotations.
// - forcePublishOpts: ...bool - Optional boolean flags for force publishing options.
//
// Returns:
// - *common.APIResponse - The API response containing the status, message, and any relevant data.
//
// The function performs the following steps:
// 1. Validates the sellerCode and input items.
// 2. Checks for specific conditions related to warehouse codes and seller codes.
// 3. Inserts purchaser codes based on warehouse codes.
// 4. Creates or updates quotations based on the input mode (OVERRITE or ADD).
// 5. Handles vendor configurations and bidding sessions.
// 6. Tracks and updates quotation data in the database.
// 7. Returns the API response with the status and any error messages or created quotations.
func SellerCreateQuotations(input CreateQuotationsInput, sellerCode string, forcePublishOpts ...bool) *common.APIResponse {

	errorList := []model.Quotation{}

	if sellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "sellerCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		}
	}

	// validate
	for i := range input.Items {
		quotation := input.Items[i]

		// TODO tạm chặn tạo PO từ kho HCM (sau này dynamic)
		if quotation.WarehouseCode == "HCM" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Không được tạo PO cho kho " + quotation.WarehouseCode,
			}
		}

		if quotation.SellerCode == "MEDX-HN" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: quotation.SellerCode + " không được tạo.",
			}
		}

		if quotation.WarehouseCode == "" {
			input.Items[i].Logs = []string{string(enum.ErrorCodeInvalid.Warehouse)}
			errorList = append(errorList, quotation)
			continue
		}

		// insert purchaser_code from warehouse_code
		if purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(quotation.WarehouseCode); ok {
			input.Items[i].PurchaserCode = purchaserCode
		} else {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Do not create for " + quotation.WarehouseCode,
			}
		}
	}

	if len(errorList) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "An error occurred.",
			Data:    errorList,
		}
	}

	var (
		successCreatedQuotations         []model.Quotation
		successCreatedOrUpdateQuotations []model.Quotation
		now                              = utils.GetVietnamTimeNow()
	)

	// create quotation
	for i := range input.Items {
		quotation := input.Items[i]

		// check config
		blockFavQuotation := isBlockFavQuotation(quotation.SKU, quotation.WarehouseCode)
		blockBiddingSession := isBlockBidding(quotation.SKU, quotation.WarehouseCode)

		quotation.IsBidding = utils.Pointer.WithBool(false)
		quotation.VendorCodes = &[]string{}
		quotation.SellerCode = strings.ToUpper(sellerCode)
		quotation.BlockReleaseToFavSession = utils.Pointer.WithBool(blockFavQuotation)
		quotation.BlockReleaseToBiddingSession = utils.Pointer.WithBool(blockBiddingSession)

		originExpect := utils.Pointer.WithInt64(*quotation.QuantityExpect)

		//handle vendor expect quantity
		if quotation.VendorExpectQuantity == nil {
			quotation.VendorExpectQuantity = utils.Pointer.WithInt64(*quotation.QuantityExpect)
		}

		/* chặn báo giá công khai */
		pv := new(privateQuotation)
		if len(forcePublishOpts) > 0 {
			pv.forcePublish = utils.Pointer.WithBool(forcePublishOpts[0])
		}

		// check exist quotation
		quotationCheckResp := model.QuotationDB.QueryOne(model.Quotation{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			SellerCode:    sellerCode,
			WarehouseCode: quotation.WarehouseCode,
			// PurchaserCode: quotation.PurchaserCode, // không phải là key
		})
		if quotationCheckResp.Status == common.APIStatus.Ok {
			// quotationCheck := quotationCheckResp.Data.([]*model.Quotation)[0]

			// phase 1: nhưng đã có quotation
			checkQuotationProductVendorResp := model.QuotationProductVendorDB.QueryOne(model.QuotationProductVendor{
				ProductID:     quotation.ProductID,
				SKU:           quotation.SKU,
				WarehouseCode: quotation.WarehouseCode,
			})
			if checkQuotationProductVendorResp.Status == common.APIStatus.Ok {
				// phase 1 thì lấy số VendorQuantityExpect gán vào QuantityExpect
				quotation.QuantityExpect = quotation.VendorExpectQuantity
			}

			// check mode
			if input.Mode == "OVERRITE" {
				// update quotation
				updateQuotationResp := model.QuotationDB.UpdateOne(
					model.Quotation{
						ProductID:     quotation.ProductID,
						SKU:           quotation.SKU,
						SellerCode:    sellerCode,
						WarehouseCode: quotation.WarehouseCode,
						// PurchaserCode: quotation.PurchaserCode, // không phải là key
					},
					model.Quotation{
						Unit:                         quotation.Unit,
						QuantityExpect:               quotation.QuantityExpect,
						VendorExpectQuantity:         quotation.VendorExpectQuantity,
						BlockReleaseToBiddingSession: quotation.BlockReleaseToBiddingSession,
						BlockReleaseToFavSession:     quotation.BlockReleaseToFavSession,
						// UnitPrice:      quotation.UnitPrice,
						// VAT:            quotation.VAT,

						// BI
						// ActionTrackingLastest: enum.QuotationActionTrackingValue(input.Mode),
					},
				)
				if updateQuotationResp.Status != common.APIStatus.Ok {
					quotation.Logs = []string{updateQuotationResp.Message}
					errorList = append(errorList, quotation)
					continue
				}
				successCreatedOrUpdateQuotations = append(successCreatedOrUpdateQuotations, quotation)
				// update expectQuantity
				getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
					WarehouseCode: quotation.WarehouseCode,
					Version:       utils.GetCurrentVersionDDMMYYYY(),
					SKU:           quotation.SKU,
				})
				if getTrackingQuotationResp.Status == common.APIStatus.Ok {
					oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
					if oldTrackingQuotation.TotalQuantityConfirmed == nil {
						oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
					}
					newOriginExpect := *oldTrackingQuotation.TotalQuantityConfirmed + *quotation.QuantityExpect
					model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
						Version:       utils.GetCurrentVersionDDMMYYYY(),
						WarehouseCode: quotation.WarehouseCode,
						SKU:           quotation.SKU,
					}, model.QuotationTracking{
						TotalQuantityExpect: newOriginExpect,
					})
				}
				continue

			}

		} else if quotationCheckResp.Status != common.APIStatus.NotFound {
			// chỉ NotFound mới thực hiện logic bên dưới
			continue
		}

		// ============================== TẠO QUOTATION ==============================

		// get product
		productResp := marketplace.GetSingleProduct(
			client.APIOption{
				Q: model.Product{ProductID: quotation.ProductID},
			},
		)

		if productResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   productResp.Message,
				ErrorCode: productResp.ErrorCode,
			}
		}
		product := productResp.Data.([]*model.Product)[0]
		// quotation.ManufacturerCode = product.ManufacturerCode
		quotation.Origin = product.Origin
		if quotation.Origin != "VN" {
			quotation.Origin = "FOREIGN"
		}
		quotation.ProductName = product.Name
		quotation.Unit = product.Unit

		// Get sku config, check favorite
		var vendorConfigs []model.VendorConfig
		var skuConfig *model.SkuConfig

		// lấy giá theo kho cần trên Quotation và map với purchaser
		skuConfigResp := model.SkuConfigReadDB.QueryOne(model.SkuConfig{
			SellerCode:    sellerCode,
			ProductID:     quotation.ProductID,
			PurchaserCode: quotation.PurchaserCode,
		})

		if skuConfigResp.Status == common.APIStatus.Error {
			quotation.Logs = []string{skuConfigResp.Message}
			errorList = append(errorList, quotation)
			continue
			// return skuConfigResp
		} else if skuConfigResp.Status == common.APIStatus.NotFound {
			quotation.Logs = []string{skuConfigResp.Message}
			errorList = append(errorList, quotation)
			continue
			// quotation.IsNewProduct = utils.Pointer.WithBool(true)

			// TODO tạm thời xóa bididng khi quotation có expect quantity = 0
			// // Check has bidding
			// checkBiddingResp := model.BiddingDB.QueryOne(model.Bidding{
			// 	ProductID: quotation.ProductID,
			// })
			// if checkBiddingResp.Status == common.APIStatus.Ok {
			// 	quotation.IsBidding = utils.Pointer.WithBool(true)
			// }

		} else if skuConfigResp.Status == common.APIStatus.Ok {
			skuConfig = skuConfigResp.Data.([]*model.SkuConfig)[0]

			pv.stopPublish = skuConfig.StopPublishBidding

			// Khóa mua hàng cho SKU
			if skuConfig.IsActive != nil && !*skuConfig.IsActive {
				quotation.Logs = []string{string(enum.ErrorCodeInvalid.SkuInactive)}
				errorList = append(errorList, quotation)
				continue
			}

			// check PublishPrice
			if skuConfig.PublishPriceAfterVAT == nil || *skuConfig.PublishPriceAfterVAT == 0 {
				quotation.Logs = []string{string(enum.ErrorCodeInvalid.SkuPublishPrice)}
				errorList = append(errorList, quotation)
				continue
			}
			// check sku-config nếu có vendor-favorite thì ưu tiên hiển thị cho vendor-favorite
			if skuConfig.Vendors != nil {
				for _, vendor := range *skuConfig.Vendors {
					// if vendor.Priority != nil {
					// 	if utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
					// 		quotation.VendorCodes = &[]string{vendor.VendorCode}
					// 		quotation.AssignVendorTime = utils.GetAdminstrativeTime()
					// 		quotation.IsLowPriority = utils.Pointer.WithBool(false)
					// 	} else if utils.IsInt64Contains(utils.LOW_PRIORITY, *vendor.Priority) {
					// 		vendorConfigs = append(vendorConfigs, vendor)
					// 	}
					// }
					// if vendor.Priority != nil && utils.IsInt64Contains(utils.ALL_PRIORITY, *vendor.Priority) {
					if vendor.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
						isExisted := false
						for _, v := range vendorConfigs {
							if v.VendorCode == vendor.VendorCode {
								isExisted = true
								break
							}
						}
						if !isExisted {
							vendorConfigs = append(vendorConfigs, vendor)
						}
					}
				}
			}

			// sort.Slice(vendorConfigs, func(i, j int) bool {
			// 	return *vendorConfigs[i].Priority < *vendorConfigs[j].Priority
			// })

			// if len(vendorConfigs) > 0 {
			// 	// Trường hợp không có ncc ưu tiên 0, gán cho 2 ncc ưu tiên 1,2
			// 	if quotation.VendorCodes == nil || len(*quotation.VendorCodes) == 0 {
			// 		var vendorCodes []string
			// 		for _, vendor := range vendorConfigs {
			// 			vendorCodes = append(vendorCodes, vendor.VendorCode)
			// 		}
			// 		quotation.VendorCodes = &vendorCodes
			// 		quotation.IsLowPriority = utils.Pointer.WithBool(true)
			// 		quotation.AssignVendorTime = utils.GetAdminstrativeTime()
			// 	} else {
			// 		quotation.Vendors = &vendorConfigs
			// 	}
			// }

			// gán VendorCodes cho quotation
			vendorCodes := []string{}
			for _, vendor := range vendorConfigs {
				vendorCodes = append(vendorCodes, vendor.VendorCode)
			}
			if len(vendorCodes) > 0 {
				quotation.AssignVendorTime = utils.GetAdminstrativeTime()
			}
			quotation.VendorCodes = &vendorCodes
		}

		// gán lại IsBidding
		biddingResp := model.BiddingDB.QueryOne(model.Bidding{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
		})
		// vào if này khi 2 quotation khác sku, cùng productID+WH
		if biddingResp.Status == common.APIStatus.Ok {
			quotation.IsBidding = utils.Pointer.WithBool(true)
		}

		// [ Trường hơp có nhà cung cấp fav(0, ưu tiên 1, 2...) ]
		// Case 1: Trường hợp có fav 0
		//     + Phase 1: chỉ private cho nhà cung cấp fav 0
		//     + Phase 2: publish cho toàn bộ nhà cung cấp
		// Case 2: Trường hợp không có fav 0
		//     + Phase 1: Cho hiển thị cả ưu tiên (1, 2...) và pubish
		//     + Phase 2: publish cho toàn bộ nhà cung cấp
		if quotation.VendorCodes != nil && len(*quotation.VendorCodes) > 0 {
			// Case 1
			quotation.QuotationStatus = enum.QuotationStatus.STOP_PUBLISH
			quotation.QuantityExpect = utils.Pointer.WithInt64(*quotation.VendorExpectQuantity)

			// Case 2
			// kiểm tra xem có fav 0 không, nếu không có thì publish
			for _, vendor := range vendorConfigs {
				if vendor.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
					pv.existVendorFavorite = utils.Pointer.WithBool(true)
					break
				}
			}
		}

		// [ Trường hơp không có cài đặt nhà cung cấp fav(0, ưu tiên 1,2...) ]
		// => Cho hiển thị publish
		if !pv.isPrivate() {
			quotation.QuotationStatus = enum.QuotationStatus.PUBLISH
			quotation.PublishBiddingTime = &now
		} else {
			quotation.QuotationStatus = enum.QuotationStatus.STOP_PUBLISH
		}

		// create
		quotation.CreatedTime = &now
		quotation.LastUpdatedTime = &now
		quotation.PrWhHashtag = combinePrWhHashtag(quotation.ProductID, quotation.WarehouseCode)
		quotation.Hashtag = strings.Replace(utils.NormalizeString(fmt.Sprintf("%d %v", quotation.ProductID, quotation.ProductName)), " ", "-", -1)
		quotation.Type = enum.QuotationTypeVal.NORMAL
		// quotation.ActionTrackingLastest = enum.QuotationActionTrackingValue(input.Mode)
		genQuotationInfo(&quotation, skuConfig)

		// stop create quotation with quantity = 0
		if quotation.QuantityExpect != nil && *quotation.QuantityExpect <= 0 {
			quotation.Logs = []string{fmt.Sprintf("%s :QuantityExpect <= 0", quotation.SKU)}
			errorList = append(errorList, quotation)
			continue
		}
		resp := model.QuotationDB.Create(quotation)
		if resp.Status != common.APIStatus.Ok {
			quotation.Logs = []string{resp.Message}
			errorList = append(errorList, quotation)
			continue
			// return resp
		}

		// rollback ADD thì mở ra
		// if !notUpdateTrackingQuotation {
		//trường hợp quotation đó đã mất 404
		//ví dụ fav confirm >= sl quotation
		//rollback thì không vào đây
		getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
			WarehouseCode: quotation.WarehouseCode,
			Version:       utils.GetCurrentVersionDDMMYYYY(),
			SKU:           quotation.SKU,
		})
		if getTrackingQuotationResp.Status == common.APIStatus.Ok {
			oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]

			if oldTrackingQuotation.TotalQuantityConfirmed == nil {
				oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
			}
			newOriginExpect := *oldTrackingQuotation.TotalQuantityConfirmed + *quotation.QuantityExpect // cho cả OVERRITE & ADD
			model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
				Version:       utils.GetCurrentVersionDDMMYYYY(),
				WarehouseCode: quotation.WarehouseCode,
				SKU:           quotation.SKU,
			}, model.QuotationTracking{
				TotalQuantityExpect: newOriginExpect,
			})
		} else {
			// nếu chưa có tracking thì tạo mới
			model.QuotationTrackingDB.Create(model.QuotationTracking{
				SellerCode:             quotation.SellerCode,
				WarehouseCode:          quotation.WarehouseCode,
				Version:                utils.GetCurrentVersionDDMMYYYY(),
				ProductCode:            quotation.ProductCode,
				ProductID:              quotation.ProductID,
				SKU:                    quotation.SKU,
				TotalQuantityConfirmed: utils.Pointer.WithInt64(0),
				TotalQuantityExpect:    *originExpect,
			})
		}
		// }

		// add to success list to response
		successCreatedQuotations = append(successCreatedQuotations, quotation)
		successCreatedOrUpdateQuotations = append(successCreatedOrUpdateQuotations, quotation)
		// thêm ES search
		WarmupSearch(quotation.ProductCode, &quotation)

		// ---------------- QuotationProductVendor ----------------
		if len(vendorConfigs) > 0 && (len(forcePublishOpts) == 0 || !forcePublishOpts[0]) {
			for _, vendor := range vendorConfigs {
				// tracking
				distinctVendor := model.QuotationProductVendor{
					ProductID:     quotation.ProductID,
					SKU:           quotation.SKU,
					ProductCode:   quotation.ProductCode,
					WarehouseCode: quotation.WarehouseCode,
					PurchaserCode: quotation.PurchaserCode,
					VendorCode:    vendor.VendorCode,
					IsLowPriority: utils.Pointer.WithBool(true),
				}
				if vendor.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
					distinctVendor.IsLowPriority = utils.Pointer.WithBool(false)
					//sau khi tao quotation va kiem tra vendor fav thi gui zns cho vendor
					sendZNSVendorCreateQuotation(vendor.VendorCode)
				}
				model.QuotationProductVendorDB.Create(distinctVendor)
			}
		}

		// ---------------- QuotationProduct ----------------
		if quotation.QuotationStatus == enum.QuotationStatus.PUBLISH {

			var tempPriceAfterDiscount float64 = 0
			var tempTotal float64 = 0
			if skuConfig.PurchaseVAT != nil {
				tempPriceAfterDiscount = float64(*skuConfig.PurchaseVAT)
				if quotation.QuantityExpect != nil {
					tempTotal = float64(*quotation.QuantityExpect * *skuConfig.PurchaseVAT)
				}
			}
			// create
			model.QuotationProductDB.Create(model.QuotationProduct{
				ProductID:   quotation.ProductID,
				SKU:         quotation.SKU,
				ProductCode: quotation.ProductCode,
				SellerCode:  quotation.SellerCode,
				// ManufacturerCode:          quotation.ManufacturerCode,
				Origin:                       quotation.Origin,
				WarehouseCode:                quotation.WarehouseCode,
				PurchaserCode:                quotation.PurchaserCode,
				PrWhHashtag:                  combinePrWhHashtag(quotation.ProductID, quotation.WarehouseCode),
				CurrentQuantityExpect:        quotation.QuantityExpect,
				CurrentPriceAfterDiscount:    &tempPriceAfterDiscount,
				CurrentTotalPrice:            &tempTotal,
				BlockReleaseToBiddingSession: quotation.BlockReleaseToBiddingSession,
				BlockReleaseToFavSession:     quotation.BlockReleaseToFavSession,
			})
		}

	}

	if len(successCreatedOrUpdateQuotations) > 0 {
		// create bidding rate
		var isRollBack bool
		if len(forcePublishOpts) > 1 {
			isRollBack = forcePublishOpts[1]
		}
		// nếu là roll back quotation thì không cập nhật lại bidding rate
		if !isRollBack {
			biddingRates := ConvertQuotationToBiddingRateInput(successCreatedOrUpdateQuotations)
			input := CreateBiddingRateInput{
				Mode:  input.Mode,
				Items: biddingRates,
			}
			respSellerCreateBiddingRate := SellerCreateBiddingRate(input, sellerCode)
			if respSellerCreateBiddingRate.Status != common.APIStatus.Ok {
				fmt.Println("create bidding rate fail resp: ", respSellerCreateBiddingRate)
			}
		}
	}

	// return error
	if len(errorList) > 0 {

		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "An error occurred.",
			Data:    errorList,
		}
	}

	// return success
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Create data success",
		Data:    successCreatedQuotations,
	}
}

// SellerUpdateQuotation updates a seller's quotation based on the provided input.
// It performs various checks and updates the quotation status accordingly.
//
// Parameters:
//   - input: model.Quotation - The quotation details to be updated.
//
// Returns:
//   - *common.APIResponse - The response containing the status and message of the update operation.
//
// The function performs the following steps:
//  1. Validates the input fields (SellerCode, ProductID, SKU).
//  2. Copies certain fields from the input to temporary variables.
//  3. Sets specific fields in the input to zero values.
//  4. Constructs a selector for the quotation to be updated.
//  5. Updates the quotation in the database.
//  6. Warms up the search cache for the updated product.
//  7. If the quotation status is updated to PUBLISH, it performs additional checks and updates:
//     - Checks if the product is allowed to be published for bidding.
//     - Deletes any existing private quotation sessions.
//     - Updates the quotation status to PUBLISH and creates a new quotation product.
//  8. If the quotation status is updated to STOP_PUBLISH, it deletes the published quotation product and updates the status.
//
// Note: The function interacts with various database models and performs multiple database operations.
func SellerUpdateQuotation(input model.Quotation) *common.APIResponse {
	if input.SellerCode == "" || input.ProductID == 0 || input.SKU == "" { // || input.WarehouseCode == ""
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "SellerCode, ProductID, SKU required",
		}
	}

	var (
		// copy
		tempActionByName = input.ActionByName
		tempActionByID   = input.ActionByID
		tempStatus       = input.QuotationStatus
	)

	// zero omitempty
	input.ActionByID = 0
	input.ActionByName = ""
	input.QuotationStatus = ""

	// do action
	selector := model.Quotation{
		ProductID:     input.ProductID,
		SKU:           input.SKU,
		SellerCode:    input.SellerCode,
		WarehouseCode: input.WarehouseCode,
		// PurchaserCode: input.PurchaserCode, // không phải là key
	}

	// BI
	// input.ActionTrackingLastest = enum.QuotationActionTracking.USER_UPDATE

	updateResp := model.QuotationDB.UpdateOne(
		selector,
		input,
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}
	quotation := updateResp.Data.([]*model.Quotation)[0]

	// thêm ES search
	WarmupSearch(quotation.ProductCode, nil)

	if tempStatus != "" {

		// tracking
		if tempStatus == enum.QuotationStatus.PUBLISH {

			// check xem chặn ra phiên báo giá công khai
			skuConfResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
				ProductID:     quotation.ProductID,
				SKU:           quotation.SKU,
				SellerCode:    quotation.SellerCode,
				PurchaserCode: quotation.PurchaserCode,
			})
			if skuConfResp.Status != common.APIStatus.Ok {
				return skuConfResp
			}
			skuConf := skuConfResp.Data.([]*model.SkuConfig)[0]
			stopPublish := false

			if skuConf.StopPublishBidding != nil {
				stopPublish = *skuConf.StopPublishBidding
			}

			if stopPublish == true {
				return &common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "This product is not allowed to publish bidding",
				}
			}

			// && (quotation.VendorCodes == nil || len(*quotation.VendorCodes) < utils.LIMIT_PRIORITY)

			// nếu đang private xoá phiên private
			checkQuotationProductVendorResp := model.QuotationProductVendorDB.QueryOne(model.QuotationProductVendor{
				ProductID:     quotation.ProductID,
				SKU:           quotation.SKU,
				WarehouseCode: quotation.WarehouseCode,
			})

			switch checkQuotationProductVendorResp.Status {
			case common.APIStatus.Ok:
				if quotation.VendorCodes != nil {
					for _, vendorCode := range *quotation.VendorCodes {
						deleteQuotaVendorResp := model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
							ProductID:     quotation.ProductID,
							SKU:           quotation.SKU,
							VendorCode:    vendorCode,
							WarehouseCode: quotation.WarehouseCode,
						})
						if deleteQuotaVendorResp.Status != common.APIStatus.Ok {
							return deleteQuotaVendorResp
						}
					}
				}
			case common.APIStatus.NotFound:
				// do nothing
			default:
				return checkQuotationProductVendorResp
			}

			var (
				now          = utils.GetVietnamTimeNow()
				emptyVendors = []string{}
			)

			// update quotation
			updater := model.Quotation{
				QuotationStatus:    enum.QuotationStatus.PUBLISH,
				PublishBiddingTime: &now,
				VendorCodes:        &emptyVendors,
				ActionByName:       tempActionByName,
				ActionByID:         tempActionByID,
				LastActionTime:     &now,
			}

			// xoá cho worker unassign không phải cào
			updateQuotationResp := model.QuotationDB.UpdateOne(selector, updater)
			if updateQuotationResp.Status != common.APIStatus.Ok {
				return updateQuotationResp
			}

			// tạo quotation product
			return model.QuotationProductDB.Create(model.QuotationProduct{
				ProductID:                    quotation.ProductID,
				SKU:                          quotation.SKU,
				SellerCode:                   quotation.SellerCode,
				ProductCode:                  quotation.ProductCode,
				Origin:                       quotation.Origin,
				WarehouseCode:                quotation.WarehouseCode,
				PurchaserCode:                quotation.PurchaserCode,
				PrWhHashtag:                  combinePrWhHashtag(quotation.ProductID, quotation.WarehouseCode),
				BlockReleaseToBiddingSession: quotation.BlockReleaseToBiddingSession,
				BlockReleaseToFavSession:     quotation.BlockReleaseToFavSession,
				// Không cần update những con số current, worker sẽ tự update
			})

		} else if tempStatus == enum.QuotationStatus.STOP_PUBLISH {
			checkQuotationResp := model.QuotationDB.QueryOne(model.Quotation{
				ProductID:       quotation.ProductID,
				SKU:             quotation.SKU,
				WarehouseCode:   quotation.WarehouseCode,
				QuotationStatus: enum.QuotationStatus.PUBLISH,
				// PurchaserCode:   quotation.PurchaserCode, // không phải là key
			})

			// nếu không còn báo giá công khai thì xóa
			if checkQuotationResp.Status == common.APIStatus.Ok {
				model.QuotationProductDB.Delete(model.QuotationProduct{
					ProductID:     quotation.ProductID,
					SKU:           quotation.SKU,
					WarehouseCode: quotation.WarehouseCode,
				})

				updateResp = model.QuotationDB.UpdateOne(model.Quotation{
					ProductID:       quotation.ProductID,
					SKU:             quotation.SKU,
					WarehouseCode:   quotation.WarehouseCode,
					QuotationStatus: enum.QuotationStatus.PUBLISH},
					model.Quotation{
						QuotationStatus: enum.QuotationStatus.STOP_PUBLISH,
					})
			}
		}
	}
	return updateResp
}

// DeleteAllQuotations deletes all quotations for a given seller and warehouse.
// It can force delete all quotations or delete based on provided product codes or SKUs.
//
// Parameters:
// - isForceDeleteAll: A boolean indicating whether to force delete all quotations.
// - sellerCode: A string representing the seller's code. This is required.
// - codesArr: A slice of strings containing product codes to delete.
// - skusArr: A slice of strings containing SKUs to delete.
// - warehouseCode: A string representing the warehouse code. This is required if codesArr is provided.
//
// Returns:
// - *common.APIResponse: An API response indicating the status and message of the operation.
//
// The function performs the following steps:
//  1. Validates the sellerCode and warehouseCode (if codesArr is provided).
//  2. Constructs a query to find quotations based on the provided parameters.
//  3. If isForceDeleteAll is true, it iteratively deletes quotations in batches of 100 until all are deleted.
//  4. For each deleted quotation, it performs additional cleanup operations such as removing product search entries,
//     updating bidding information, and deleting related records from other collections.
func DeleteAllQuotations(isForceDeleteAll bool, sellerCode string, codesArr, skusArr []string, warehouseCode string) *common.APIResponse {
	if sellerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sellerCode required",
		}
	}

	// construct query
	quotationQuery := model.Quotation{
		SellerCode:    sellerCode,
		WarehouseCode: warehouseCode,
	}

	deleteQuotationBy := "DELETE ALL"

	// check codes
	if len(codesArr) > 0 {
		if warehouseCode == "" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "warehouseCode required",
			}
		}
		quotationQuery.ComplexQuery = []*bson.M{
			{
				"product_code": &bson.M{
					"$in": codesArr,
				},
			},
		}
		deleteQuotationBy = strings.Join(codesArr, ", ")
	}

	// check skus
	if len(skusArr) > 0 {
		quotationQuery.ComplexQuery = []*bson.M{
			{
				"sku": &bson.M{
					"$in": skusArr,
				},
			},
		}
		deleteQuotationBy = strings.Join(skusArr, ", ")
	}

	fmt.Println(sellerCode + " " + warehouseCode + " " + deleteQuotationBy)

	// delete all quotations
	if isForceDeleteAll {
		var limit int64 = 100
		for {
			quotationResp := model.QuotationDB.Query(quotationQuery, 0, limit, nil)
			if quotationResp.Status != common.APIStatus.Ok {
				break
			}

			quotations := quotationResp.Data.([]*model.Quotation)

			// delete quotations
			for _, quotation := range quotations {
				model.QuotationDB.Delete(model.Quotation{ID: quotation.ID})
				quotationResp := model.QuotationDB.QueryOne(model.Quotation{ProductCode: quotation.ProductCode})
				if quotationResp.Status != common.APIStatus.Ok {
					// xóa ES search
					removeProductSearch(quotation.ProductCode)
				}

				// gở bidding khỏi seller này: chỉ cần 1, nhưng trừ hao 1000
				biddingResp := model.BiddingDB.Query(model.Bidding{
					ProductID:     quotation.ProductID,
					SKU:           quotation.SKU,
					WarehouseCode: quotation.WarehouseCode,
					ComplexQuery: []*bson.M{
						{"selected_bys": sellerCode},
					},
				}, 0, 1000, nil)
				if biddingResp.Status == common.APIStatus.Ok {
					biddings := biddingResp.Data.([]*model.Bidding)

					for i := range biddings {
						bidding := biddings[i]

						selectedBys := []string{}
						if bidding.SelectedBys != nil {
							for _, seller := range *bidding.SelectedBys {
								if seller != sellerCode {
									selectedBys = append(selectedBys, seller)
								}
							}
							model.BiddingDB.UpdateOne(
								model.Bidding{ID: bidding.ID},
								model.Bidding{SelectedBys: &selectedBys},
							)
						}
					}
				}

				// Check quotation other warehouse
				checkQuotationResp := model.QuotationDB.QueryOne(model.Quotation{
					ProductID:     quotation.ProductID,
					SKU:           quotation.SKU,
					WarehouseCode: quotation.WarehouseCode,
				})
				if checkQuotationResp.Status == common.APIStatus.NotFound {
					model.QuotationProductDB.Delete(model.QuotationProduct{
						ProductID:     quotation.ProductID,
						SKU:           quotation.SKU,
						WarehouseCode: quotation.WarehouseCode,
					})
					model.BiddingDB.Delete(model.Bidding{
						ProductID:     quotation.ProductID,
						SKU:           quotation.SKU,
						WarehouseCode: quotation.WarehouseCode,
					})
					// Xóa quotation distinct để không view dư
					model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
						ProductID:     quotation.ProductID,
						SKU:           quotation.SKU,
						WarehouseCode: quotation.WarehouseCode,
					})
				}
				// xóa QuotationProductVendorDB
				if quotation.VendorCodes != nil && len(*quotation.VendorCodes) > 0 {
					for _, vendorCode := range *quotation.VendorCodes {
						resp := model.QuotationDB.QueryOne(model.Quotation{
							ProductID:     quotation.ProductID,
							SKU:           quotation.SKU,
							WarehouseCode: quotation.WarehouseCode,
							ComplexQuery: []*bson.M{{
								"vendor_codes": vendorCode,
							}},
						})
						if resp.Status == common.APIStatus.NotFound {
							model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
								ProductID:     quotation.ProductID,
								SKU:           quotation.SKU,
								WarehouseCode: quotation.WarehouseCode,
								VendorCode:    vendorCode,
							})
						}
					}
				}

			}

			if len(quotations) < int(limit) {
				break
			}
		}
	}

	fmt.Println(sellerCode + " DELETE DONE")
	fmt.Println(sellerCode + " DELETE DONE")
	fmt.Println(sellerCode + " DELETE DONE")

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Delete data success",
	}
}

// PO-quotation input tất cả đều bắt buộc
type CreatePOFromQuotationInput struct {
	PO model.PurchaseOrder `json:"po" bson:"-"`
	// Infos []CreatePOFromQuotationInfo `json:"infos" bson:"-"`
}

// PO-quotation info tất cả field đều bắt buôc
type CreatePOFromQuotationInfo struct {
	ProductID             int64  `json:"productID" bson:"-"`
	QuantityConfirmed     int64  `json:"quantityConfirmed" bson:"-"`
	WarehouseCode         string `json:"warehouseCode" bson:"-"` // kho cần
	SellerCode            string `json:"sellerCode" bson:"-"`
	DeliveryWarehouseCode string `json:"deliveryWarehouseCode" bson:"-"`
}

// CreatePOFromQuotation
// CreatePOFromQuotation creates a Purchase Order (PO) from a given quotation input.
// It performs various validations and checks before creating or updating the PO.
//
// Parameters:
//   - input: A pointer to CreatePOFromQuotationInput containing the details for creating the PO.
//   - acc: A pointer to model.Account representing the account performing the action.
//
// Returns:
//   - A pointer to common.APIResponse containing the status, message, and error code of the operation.
//
// The function performs the following steps:
//   1. Validates the input and account.
//   2. Checks if WarehouseCode and DeliveryWarehouseCode are provided.
//   3. Validates the PO code and purchaser code.
//   4. Ensures that PO items are provided.
//   5. Checks for duplicate actions at the same time.
//   6. Compares PO items with quotations and validates them.
//   7. Creates or updates the PO based on the input.
//   8. Updates the quotations if the PO is successfully created.
//   9. Creates a log for the confirmed quotations.
//
// Possible error responses:
//   - Invalid input data.
//   - Missing WarehouseCode or DeliveryWarehouseCode.
//   - Invalid delivery warehouse code.
//   - Missing PO items.
//   - Invalid PO item format.
//   - Data changes in quotations.
//   - Error creating or updating the PO.
//   - No data to create PO.
func CreatePOFromQuotation(input *CreatePOFromQuotationInput, acc *model.Account) *common.APIResponse {

	// validate
	if utils.HasNil(input, acc) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// check WarehouseCode and DeliveryWarehouseCode
	if input.PO.WarehouseCode == "" || input.PO.DeliveryWarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode, DeliveryWarehouseCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// check DeliveryWarehouseCode trước khi tạo
	if input.PO.POCode == "" {
		purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(input.PO.WarehouseCode)
		if !ok {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Invalid delivery warehouse code",
				ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
			}
		}
		input.PO.PurchaserCode = purchaserCode

		if response, ok := utils.ValidSellerPurchaser(input.PO.SellerCode, input.PO.PurchaserCode); !ok {
			return response
		}
	}

	if len(input.PO.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POItems are required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check dup action in same time
	chilKeys := []string{}
	for _, item := range input.PO.Items {
		// nếu có POItemID -> gửi lên item cũ -> skip
		if item.POItemID != 0 {
			continue
		}
		chilKeys = append(chilKeys, fmt.Sprintf("%v.%v.%v", item.WarehouseCode, item.SellerCode, item.SKU))
	}
	checkLockAcitonresp := CreateLockActions("CREATE_PO_QUOTATION", input.PO.VendorCode, chilKeys)
	if checkLockAcitonresp.Status != common.APIStatus.Ok {
		checkLockAcitonresp.Message = "Có lỗi xảy ra khi thao tác đồng thời, vui lòng thử lại sau ít phút"
		checkLockAcitonresp.ErrorCode = string(enum.ErrorCodeInvalid.LockAction)
		return checkLockAcitonresp
	}

	var (
		quotations = make([]*model.Quotation, 0)
		ZERO       = int64(0)
		inputPO    = input.PO
	)

	// Compare PO items with quotation
	for _, item := range inputPO.Items {
		// nếu có POItemID -> gửi lên item cũ -> skip
		if item.POItemID != 0 {
			continue
		}

		// validate
		if item.ExpectQuantity == 0 || item.ProductID == 0 || item.WarehouseCode == "" || item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "PO Item wrong format",
				ErrorCode: string(enum.ErrorCodeInvalid.Info),
			}
		}

		// Query quotation items
		var quotationResp = new(common.APIResponse)
		switch item.SourceQuotation {
		case enum.QuotationTypeVal.HEDGING:
			if len(inputPO.VendorCode) == 0 {
				quotationResp.Status = common.APIStatus.Invalid
			}

			// Query quotation items
			quotationQuery := model.Quotation{
				ProductID:   item.ProductID,
				SKU:         item.SKU,
				VendorCodes: &[]string{inputPO.VendorCode},
				ComplexQuery: []*bson.M{
					{"quantity_expect": &bson.M{"$gt": 0}},
				},
				// validate kho cần trên quotation
				SellerCode:    item.SellerCode,
				WarehouseCode: item.WarehouseCode,
			}

			quotationResp = model.QuotationHedgingDB.QueryOne(quotationQuery)
		default:
			// Query quotation items
			quotationQuery := model.Quotation{
				ProductID: item.ProductID,
				SKU:       item.SKU,
				ComplexQuery: []*bson.M{
					{"quantity_expect": &bson.M{"$gt": 0}},
					{"vendor_codes": inputPO.VendorCode},
				},
				// validate kho cần trên quotation
				SellerCode:    item.SellerCode,
				WarehouseCode: item.WarehouseCode,
			}

			quotationResp = model.QuotationDB.QueryOne(quotationQuery)
		}


		// Check quotation
		if quotationResp.Status != common.APIStatus.Ok {
			quotationResp.ErrorCode = string(enum.ErrorCodeNotFound.Quotations)
			quotationResp.Message = "Có sự thay đổi dữ liệu báo giá, vui lòng tải lại trang"
			return quotationResp
		}

		quotation := quotationResp.Data.([]*model.Quotation)[0]
		if quotation.QuantityExpect == nil {
			continue
		}

		// Check quantity
		quotation.QuantityConfirmed += item.ExpectQuantity // For FE internal display
		quotation.CurrentQuantityConfirmed = item.ExpectQuantity
		quotation.Type = item.SourceQuotation
		quotations = append(quotations, quotation)
	}

	if len(quotations) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "No data to create PO.",
			ErrorCode: string(enum.ErrorCodeNotFound.Quotations),
		}
	}

	// Create/Update PO
	createPOResp := &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Error case",
	}

	// Create PO
	if inputPO.POCode == "" {
		now := utils.GetVietnamTimeNow()
		inputPO.StartQuotationTime = quotations[0].AssignVendorTime
		if utils.IsNil(inputPO.StartQuotationTime) {
			inputPO.StartQuotationTime = quotations[0].CreatedTime
		}
		inputPO.IssuedTime = &now
		inputPO.PlanningTime = &now
		createPOResp = CreatePurchaseOrder(inputPO, acc)
	} else {
		createPOResp = UpdatePurchaseOrder(inputPO, false)
	}

	// Check create PO
	if createPOResp.Status != common.APIStatus.Ok {
		if inputPO.POCode == "" {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.CreatePO)
		} else {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.UpdatePO)
		}
		return createPOResp
	}

	poData := createPOResp.Data.([]*model.PurchaseOrder)[0]

	// Tạo PO thành công thì mới update quotation
	for _, quotation := range quotations {
		if quotation.Type == enum.QuotationTypeVal.HEDGING {
			if quotation.CurrentQuantityConfirmed <= 0 || quotation.VendorCodes == nil || len(*quotation.VendorCodes) != 1 {
				continue
			}
			// xoá line khi đã tạo po
			model.QuotationHedgingDB.Delete(model.Quotation{ID: quotation.ID})
			continue
		}

		//calc remain
		// quatity_expect luôn >= 0
		if quotation.QuantityExpect != nil {
			getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
				WarehouseCode: quotation.WarehouseCode,
				Version:       utils.GetCurrentVersionDDMMYYYY(),
				SKU:           quotation.SKU,
			})
			if getTrackingQuotationResp.Status == common.APIStatus.Ok {

				// update tracking
				oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
				if oldTrackingQuotation.TotalQuantityConfirmed == nil {
					oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
				}
				newConfirmTracking := *oldTrackingQuotation.TotalQuantityConfirmed + quotation.QuantityConfirmed
				model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
					Version:       utils.GetCurrentVersionDDMMYYYY(),
					WarehouseCode: quotation.WarehouseCode,
					SKU:           quotation.SKU,
				}, model.QuotationTracking{
					TotalQuantityConfirmed: &newConfirmTracking,
				})

				remainQuantity := oldTrackingQuotation.TotalQuantityExpect - newConfirmTracking
				if quotation.VendorExpectQuantity != nil && *quotation.VendorExpectQuantity <= quotation.QuantityConfirmed ||
					remainQuantity <= 0 {
					quotation.QuantityExpect = &ZERO

				} else {
					*quotation.QuantityExpect = remainQuantity
				}
			}
		}

		// for other vendor
		if utils.IsNil(quotation.QuantityExpect) || *quotation.QuantityExpect <= 0 {
			quotation = executeQuotationWithQuantity0(inputPO, *quotation)

			// // Trường hợp nhiều seller chung 1 kho
			// checkQuotationResp := model.QuotationDB.QueryOne(model.Quotation{
			// 	ProductID:     quotation.ProductID,
			// 	WarehouseCode: quotation.WarehouseCode,
			// 	ComplexQuery: []*bson.M{{
			// 		"vendor_codes": inputPO.VendorCode,
			// 	}},
			// })
			// if checkQuotationResp.Status == common.APIStatus.NotFound {
			// 	model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{ProductID: quotation.ProductID, VendorCode: inputPO.VendorCode, WarehouseCode: quotation.WarehouseCode})
			// }

		} else {
			quotation = executeQuotationWithQuantityGt0(inputPO, *quotation)
		}

		// get poItem cho history
		poItem := model.PurchaseOrderItem{}
		for i := range inputPO.Items {
			item := inputPO.Items[i]
			if item.SKU == quotation.SKU {
				poItem = *item
				break
			}
		}

		// Create log confirm
		model.QuotationPOHistoryDB.Create(model.QuotationPOHistory{
			CreatedBySystem:   inputPO.CreatedBySystem,
			CreatedByName:     acc.Fullname,
			PurchaserCode:     inputPO.PurchaserCode,
			SellerCode:        quotation.SellerCode,
			WarehouseCode:     quotation.WarehouseCode,
			VendorCode:        inputPO.VendorCode,
			Version:           utils.GetCurrentVersionDDMMYYYY(),
			ProductName:       quotation.ProductName,
			ProductCode:       quotation.ProductCode,
			ProductID:         quotation.ProductID,
			SKU:               quotation.SKU,
			POCode:            poData.POCode,
			POStatus:          string(poData.Status),
			QuantityConfirmed: quotation.CurrentQuantityConfirmed,
			ReferPrice:        poItem.ReferPrice,
		})
	}

	return createPOResp
}

// CreatePOFromQuotation
func CreatePOFromQuotationV2(input *CreatePOFromQuotationInput, acc *model.Account) *common.APIResponse {
	if utils.HasNil(input, acc) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	if input.PO.WarehouseCode == "" || input.PO.DeliveryWarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode, DeliveryWarehouseCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// check thông tin trước khi tạo, cập nhật
	purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(input.PO.WarehouseCode)
	if !ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid delivery warehouse code",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}
	input.PO.PurchaserCode = purchaserCode

	if response, ok := utils.ValidSellerPurchaser(input.PO.SellerCode, input.PO.PurchaserCode); !ok {
		return response
	}

	if len(input.PO.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POItems are required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check dup action in same time
	chilKeys := []string{}
	for _, item := range input.PO.Items {
		// nếu có POItemID -> gửi lên item cũ -> skip
		if item.POItemID != 0 {
			continue
		}
		chilKeys = append(chilKeys, fmt.Sprintf("%v.%v.%v", item.WarehouseCode, item.SellerCode, item.SKU))
	}
	checkLockAcitonresp := CreateLockActions("CREATE_PO_QUOTATION", input.PO.VendorCode, chilKeys)
	if checkLockAcitonresp.Status != common.APIStatus.Ok {
		checkLockAcitonresp.Message = "Có lỗi xảy ra khi thao tác đồng thời, vui lòng thử lại sau ít phút"
		checkLockAcitonresp.ErrorCode = string(enum.ErrorCodeInvalid.LockAction)
		return checkLockAcitonresp
	}

	var (
		quotations = make([]*model.Quotation, 0)
		ZERO       = int64(0)
		inputPO    = input.PO
	)

	var newItems = []*model.PurchaseOrderItem{}
	// Compare PO items with quotation
	for _, item := range inputPO.Items {
		// nếu có POItemID -> gửi lên item cũ -> skip
		if item.POItemID != 0 {
			newItems = append(newItems, item)
			continue
		}
		// validate
		if item.ExpectQuantity == 0 || item.ProductID == 0 || item.WarehouseCode == "" || item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "PO Item wrong format",
				ErrorCode: string(enum.ErrorCodeInvalid.Info),
			}
		}

		// TODO luồng tạo PO từ input

		// Thứ tự tạo item sẽ lần lượt
		// 1. item quotation (mua hàng thông thường)
		// 2. item cho quotation hedging 1
		// 3. item cho quotation hedging 2
		// 4. ...
		// cho đến khi số lượng input của sản phẩm bằng 0 thì dừng lại

		totalExpectQuantity := item.ExpectQuantity
		var vat float64
		if item.VAT != nil && *item.VAT > 0 {
			vat = *item.VAT
		}

		// Dựa trên quotation hiện tại, tạo PO item cho quotation tương ứng
		quotationResp := model.QuotationDB.QueryOne(model.Quotation{
			ProductID: item.ProductID,
			SKU:       item.SKU,
			ComplexQuery: []*bson.M{
				{"quantity_expect": &bson.M{"$gt": 0}},
				{"vendor_codes": inputPO.VendorCode},
			},
			SellerCode:    item.SellerCode,
			WarehouseCode: item.WarehouseCode,
		})

		if quotationResp.Status == common.APIStatus.Ok {
			quotation := quotationResp.Data.([]*model.Quotation)[0]
			if quotation.QuantityExpect == nil || *quotation.QuantityExpect == 0 {
				continue
			}

			newItem := *item

			var newQuantityExpect = totalExpectQuantity

			if newQuantityExpect > *quotation.QuantityExpect {
				newQuantityExpect = *quotation.QuantityExpect
			}
			newItem.ExpectQuantity = newQuantityExpect
			newItem.SourceQuotation = enum.QuotationTypeVal.NORMAL

			quotation.QuantityConfirmed += newItem.ExpectQuantity // For FE internal display
			quotation.CurrentQuantityConfirmed = newItem.ExpectQuantity
			quotation.Type = newItem.SourceQuotation
			quotations = append(quotations, quotation)
			newItems = append(newItems, &newItem)

			// giảm đi số lượng của totalExpectQuantity
			totalExpectQuantity = totalExpectQuantity - *quotation.QuantityExpect

		}

		// if totalExpectQuantity <= 0 {
		// 	continue
		// }

		// Dưa trên danh sách quotation hedging, tạo PO item cho từng quotation hedging tương ứng.
		quotationHedgingResp := model.QuotationHedgingDB.Query(model.Quotation{
			ProductID: item.ProductID,
			SKU:       item.SKU,
			ComplexQuery: []*bson.M{
				{"quantity_expect": &bson.M{"$gt": 0}},
				{"vendor_codes": inputPO.VendorCode},
			},
			SellerCode:    item.SellerCode,
			WarehouseCode: item.WarehouseCode,
		}, 0, 100, &bson.M{"created_time": 1})

		if quotationHedgingResp.Status == common.APIStatus.Ok {
			quotationHedgings := quotationHedgingResp.Data.([]*model.Quotation)
			for _, quotationHedging := range quotationHedgings {
				if quotationHedging.QuantityExpect == nil || *quotationHedging.QuantityExpect == 0 {
					continue
				}

				var newQuantityExpect = totalExpectQuantity

				var hedgingExpectationQuantity = *quotationHedging.QuantityExpect
				if quotationHedging.VendorExpectQuantity != nil && *quotationHedging.VendorExpectQuantity > 0 {
					hedgingExpectationQuantity = *quotationHedging.VendorExpectQuantity
				}
				if newQuantityExpect > hedgingExpectationQuantity {
					newQuantityExpect = hedgingExpectationQuantity
				}

				quotationHedging.QuantityConfirmed += newQuantityExpect // For FE internal display
				quotationHedging.CurrentQuantityConfirmed = newQuantityExpect
				quotationHedging.Type = enum.QuotationTypeVal.HEDGING
				// Thêm vô array quotations cần xử lý.
				quotations = append(quotations, quotationHedging)

				// Nếu tổng <= 0 thì bỏ qua
				// Trường hợp để tông totalExpectQuantity <= 0 :
				// 1. số lượng cung cấp nhỏ hơn hoặc bằng số lương yêu cầu của quotation
				if totalExpectQuantity <= 0 {
					continue
				}
				// nếu total exp
				newItem := *item

				// thông tin inbound reuqest
				newItem.ExpectQuantity = newQuantityExpect
				newItem.InboundRequestCode = quotationHedging.InboundRequestCode
				newItem.InboundRequestID = quotationHedging.InboundRequestID
				newItem.InboundRequestItemCode = quotationHedging.InboundRequestItemCode
				newItem.SourceQuotation = enum.QuotationTypeVal.HEDGING
				newItem.VAT = &vat
				newItems = append(newItems, &newItem)

				// giảm đi số lượng của totalExpectQuantit
				totalExpectQuantity = totalExpectQuantity - hedgingExpectationQuantity
			}
		}

		// // nếu totalExpectQuantity > 0 thì công tổng số còn lại vào SP PO thường
		// if totalExpectQuantity > 0 {
		// 	newItem := *item
		// 	newItem.ExpectQuantity = totalExpectQuantity
		// 	newItem.SourceQuotation = enum.QuotationTypeVal.NORMAL

		// 	newItems = append(newItems, &newItem)
		// }
	}

	if len(newItems) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.NotFound,
			Message:   "No data to create PO.",
			ErrorCode: string(enum.ErrorCodeNotFound.Quotations),
		}
	}
	inputPO.Items = newItems
	// Create/Update PO
	createPOResp := &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Error case",
	}

	if inputPO.POCode == "" {
		now := utils.GetVietnamTimeNow()
		inputPO.StartQuotationTime = quotations[0].AssignVendorTime
		if utils.IsNil(inputPO.StartQuotationTime) {
			inputPO.StartQuotationTime = quotations[0].CreatedTime
		}
		inputPO.IssuedTime = &now
		inputPO.PlanningTime = &now
		createPOResp = CreatePurchaseOrder(inputPO, acc)
	} else {
		createPOResp = UpdatePurchaseOrder(inputPO, false)
	}

	if createPOResp.Status != common.APIStatus.Ok {
		if inputPO.POCode == "" {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.CreatePO)
		} else {
			createPOResp.ErrorCode = string(enum.ErrorCodeError.UpdatePO)
		}
		return createPOResp
	}

	poData := createPOResp.Data.([]*model.PurchaseOrder)[0]

	// Tạo PO thành công thì mới update quotation
	for _, quotation := range quotations {
		if quotation.Type == enum.QuotationTypeVal.HEDGING {
			if quotation.VendorCodes == nil || len(*quotation.VendorCodes) != 1 {
				continue
			}
			// Cập nhật ngược lại PO,
			// Nếu quotatation hedging:
			// + có inboundrequest code
			// + có số lượng confirm > 0
			if quotation.InboundRequestCode != "" && quotation.CurrentQuantityConfirmed > 0 {
				ibResp := model.InboundRequestDB.QueryOne(model.InboundRequest{InboundRequestCode: quotation.InboundRequestCode})
				if ibResp.Status == common.APIStatus.Ok {
					ib := ibResp.Data.([]*model.InboundRequest)[0]

					poCodes := []string{}
					if ib.POCodes != nil && len(*ib.POCodes) > 0 {
						poCodes = *ib.POCodes
					}
					poCodes = append(poCodes, poData.POCode)

					// kiểm tra lại danh sách PO code, nếu có trạng thái cancel thì loại bỏ
					checkPoCodeResp := model.PurchaseOrderDB.Query(
						model.PurchaseOrder{
							ComplexQuery: []*bson.M{{
								"po_code": &bson.M{"$in": poCodes},
							}},
						},
						0, 100, nil)

					newPoCode := []string{}
					if checkPoCodeResp.Status == common.APIStatus.Ok {
						poDatas := checkPoCodeResp.Data.([]*model.PurchaseOrder)
						for _, po := range poDatas {
							// if po.Status == enum.PurchaseOrderStatus.CANCELED {
							// 	continue
							// }
							newPoCode = append(newPoCode, po.POCode)
						}
					}
					newTags := []string{}
					if ib.Tags != nil && len(*ib.Tags) > 0 {
						isExisted := false
						newTags = *ib.Tags
						for _, tag := range newTags {
							if tag == "has_po" {
								isExisted = true
								continue
							}
						}
						if !isExisted {
							newTags = append(newTags, "has_po")
						}
					} else {
						newTags = []string{"has_po"}
					}
					model.InboundRequestDB.UpdateOne(model.InboundRequest{ID: ib.ID}, model.InboundRequest{POCodes: &newPoCode, Tags: &newTags})
				}

				model.InboundRequestItemDB.UpdateOne(
					model.InboundRequestItem{
						InboundRequestCode:     quotation.InboundRequestCode,
						InboundRequestItemCode: quotation.InboundRequestItemCode,
					},
					model.InboundRequestItem{
						POCode: poData.POCode,
					},
				)

			}

			// xoá line khi đã tạo po
			model.QuotationHedgingDB.Delete(model.Quotation{ID: quotation.ID})

			continue
		}

		//calc remain
		// quatity_expect luôn >= 0
		if quotation.QuantityExpect != nil {
			getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
				WarehouseCode: quotation.WarehouseCode,
				Version:       utils.GetCurrentVersionDDMMYYYY(),
				SKU:           quotation.SKU,
			})
			if getTrackingQuotationResp.Status == common.APIStatus.Ok {

				// update tracking
				oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
				if oldTrackingQuotation.TotalQuantityConfirmed == nil {
					oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
				}
				newConfirmTracking := *oldTrackingQuotation.TotalQuantityConfirmed + quotation.QuantityConfirmed
				model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
					Version:       utils.GetCurrentVersionDDMMYYYY(),
					WarehouseCode: quotation.WarehouseCode,
					SKU:           quotation.SKU,
				}, model.QuotationTracking{
					TotalQuantityConfirmed: &newConfirmTracking,
				})

				remainQuantity := oldTrackingQuotation.TotalQuantityExpect - newConfirmTracking
				if quotation.VendorExpectQuantity != nil && *quotation.VendorExpectQuantity <= quotation.QuantityConfirmed ||
					remainQuantity <= 0 {
					quotation.QuantityExpect = &ZERO

				} else {
					*quotation.QuantityExpect = remainQuantity
				}
			}
		}

		// for other vendor
		if utils.IsNil(quotation.QuantityExpect) || *quotation.QuantityExpect <= 0 {
			quotation = executeQuotationWithQuantity0(inputPO, *quotation)
		} else {
			quotation = executeQuotationWithQuantityGt0(inputPO, *quotation)
		}

		// get poItem cho history
		poItem := model.PurchaseOrderItem{}
		for i := range inputPO.Items {
			item := inputPO.Items[i]
			if item.SKU == quotation.SKU {
				poItem = *item
				break
			}
		}

		if acc != nil {
			// Create log confirm
			model.QuotationPOHistoryDB.Create(model.QuotationPOHistory{
				CreatedBySystem:   inputPO.CreatedBySystem,
				CreatedByName:     acc.Fullname,
				PurchaserCode:     inputPO.PurchaserCode,
				SellerCode:        quotation.SellerCode,
				WarehouseCode:     quotation.WarehouseCode,
				VendorCode:        inputPO.VendorCode,
				Version:           utils.GetCurrentVersionDDMMYYYY(),
				ProductName:       quotation.ProductName,
				ProductCode:       quotation.ProductCode,
				ProductID:         quotation.ProductID,
				SKU:               quotation.SKU,
				POCode:            poData.POCode,
				POStatus:          string(poData.Status),
				QuantityConfirmed: quotation.CurrentQuantityConfirmed,
				ReferPrice:        poItem.ReferPrice,
			})
		}
	}

	return createPOResp
}

func executeQuotationWithQuantity0(inputPO model.PurchaseOrder, quotation model.Quotation) *model.Quotation {

	// xóa quotation
	model.QuotationDB.Delete(model.Quotation{ID: quotation.ID})
	quotationResp := model.QuotationDB.QueryOne(model.Quotation{ProductCode: quotation.ProductCode})
	if quotationResp.Status != common.APIStatus.Ok {
		// xóa ES search
		removeProductSearch(quotation.ProductCode)
	}

	// Check other quotation same product exists in DB
	// If notfound, delete product quotaion -- for FE view
	resp := model.QuotationDB.QueryOne(model.Quotation{
		ProductID:     quotation.ProductID,
		SKU:           quotation.SKU,
		WarehouseCode: quotation.WarehouseCode,
	})
	if resp.Status == common.APIStatus.NotFound {
		model.QuotationProductDB.Delete(model.QuotationProduct{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
		})
		// Xóa hết bidding khi không còn quotation nào
		model.BiddingDB.Delete(model.Bidding{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
		})
		// Xóa quotation distinct để không view dư
		model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
		})

		// Update bidding rate isValid = true
		model.BiddingRateDB.UpdateOne(
			model.BiddingRate{SKU: quotation.SKU, WarehouseCode: quotation.WarehouseCode, DaySpan: utils.GetCurrentVersionDDMMYYYY()},
			model.BiddingRate{IsValid: true})
	}

	// xóa QuotationProductVendorDB
	resp = model.QuotationDB.QueryOne(model.Quotation{
		ProductID:     quotation.ProductID,
		SKU:           quotation.SKU,
		WarehouseCode: quotation.WarehouseCode,
		ComplexQuery: []*bson.M{{
			"vendor_codes": inputPO.VendorCode,
		}},
		// PurchaserCode: quotation.PurchaserCode, // không phải là key
	})
	if resp.Status == common.APIStatus.NotFound {
		// Xóa quotation distinct để không view dư
		model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
			VendorCode:    inputPO.VendorCode,
			// PurchaserCode: quotation.PurchaserCode, // không phải là key
		})
		// // Xóa hết bidding khi không còn quotation nào
		// model.BiddingDB.Delete(model.Bidding{
		// 	ProductID:     quotation.ProductID,
		// 	WarehouseCode: quotation.WarehouseCode,
		// 	VendorCode:    inputPO.VendorCode,
		// })
	}

	return &quotation
}

func executeQuotationWithQuantityGt0(inputPO model.PurchaseOrder, quotation model.Quotation) *model.Quotation {
	if utils.HasZero(quotation.SellerCode, quotation.ProductID, quotation.PurchaserCode) {
		return &quotation
	}
	// get sku config
	getSKUConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
		SellerCode:    quotation.SellerCode,
		ProductID:     quotation.ProductID,
		SKU:           quotation.SKU,
		PurchaserCode: quotation.PurchaserCode,
	})
	if getSKUConfigResp.Status != common.APIStatus.Ok {
		return &quotation
	}

	var (
		skuConf  = getSKUConfigResp.Data.([]*model.SkuConfig)[0]
		emptyArr = []string{}
		now      = time.Now()
		// gán lại vendorCode khi đang private
		newVendors []string
	)

	pvStopPublish := skuConf.StopPublishBidding

	// gán lại vendorCode khi đang private
	// var oldVendors []string
	if quotation.VendorCodes != nil && len(*quotation.VendorCodes) > 0 {
		// oldVendors = *quotation.VendorCodes
		// từ [0] xuống [1, 2]
		for _, vendorCode := range *quotation.VendorCodes {
			if vendorCode != inputPO.VendorCode {
				newVendors = append(newVendors, vendorCode)
			}
		}

		if len(newVendors) == 0 {
			quotation.QuotationStatus = enum.QuotationStatus.PUBLISH
			quotation.VendorCodes = &emptyArr
			quotation.PublishBiddingTime = &now
		} else {
			quotation.VendorCodes = &newVendors
		}
	}

	// BI
	// quotation.ActionTrackingLastest = enum.QuotationActionTracking.PO_INCREASE

	// Update quotation
	model.QuotationDB.UpdateOne(
		model.Quotation{ID: quotation.ID},
		quotation,
	)

	// update ES search
	WarmupSearch(quotation.ProductCode, nil)

	// // hiện tại là [0], nếu quotation còn vendor [1, 2] đang chờ => cập nhật lại QuotationProductVendorDB
	// if len(newVendors) > 0 {
	// 	// tracking: add display quotation for new vendor favorite
	// 	for _, vendorCode := range newVendors {
	// 		model.QuotationProductVendorDB.Create(model.QuotationProductVendor{
	// 			ProductID:     quotation.ProductID,
	// 			ProductCode:   quotation.ProductCode,
	// 			VendorCode:    vendorCode,
	// 			WarehouseCode: quotation.WarehouseCode,
	// 			PurchaserCode: quotation.PurchaserCode,
	// 			// IsLowPriority: quotation.IsLowPriority, // TODO IsLowPriority
	// 			IsLowPriority: utils.Pointer.WithBool(true),
	// 		})
	// 	}
	// } else
	if len(newVendors) == 0 && (pvStopPublish == nil || !*pvStopPublish) {
		// } else if quotation.VendorCodes == nil || len(*quotation.VendorCodes) == 0 {
		quotationPrdResp := model.QuotationProductDB.QueryOne(model.QuotationProduct{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			WarehouseCode: quotation.WarehouseCode,
		})
		if quotationPrdResp.Status == common.APIStatus.NotFound {
			model.QuotationProductDB.Create(model.QuotationProduct{
				ProductID:   quotation.ProductID,
				SKU:         quotation.SKU,
				SellerCode:  quotation.SellerCode,
				ProductCode: quotation.ProductCode,
				// ManufacturerCode: quotation.ManufacturerCode,
				Origin:                       quotation.Origin,
				WarehouseCode:                quotation.WarehouseCode,
				PurchaserCode:                quotation.PurchaserCode,
				PrWhHashtag:                  combinePrWhHashtag(quotation.ProductID, quotation.WarehouseCode),
				BlockReleaseToBiddingSession: quotation.BlockReleaseToBiddingSession,
				BlockReleaseToFavSession:     quotation.BlockReleaseToFavSession,
			})
		}
	}
	// Tracking: delete display quotation for old vendor favorite
	checkQuotationResp := model.QuotationDB.QueryOne(model.Quotation{
		ProductID:     quotation.ProductID,
		SKU:           quotation.SKU,
		WarehouseCode: quotation.WarehouseCode,
		ComplexQuery: []*bson.M{{
			"vendor_codes": inputPO.VendorCode,
		}},
		// PurchaserCode: quotation.PurchaserCode, // không phải là key
	})
	if checkQuotationResp.Status == common.APIStatus.NotFound {
		model.QuotationProductVendorDB.Delete(model.QuotationProductVendor{
			ProductID:     quotation.ProductID,
			SKU:           quotation.SKU,
			VendorCode:    inputPO.VendorCode,
			WarehouseCode: quotation.WarehouseCode,
		})
	}

	return &quotation
}

func genQuotationInfo(quota *model.Quotation, skuConfig *model.SkuConfig) {
	if quota == nil || skuConfig == nil {
		return
	}

	if len(quota.SKU) == 0 {
		return
	}

	if skuConfig.PublishPriceAfterVAT != nil && quota.QuantityExpect != nil {
		quota.TotalPublishPrice = int64(*skuConfig.PublishPriceAfterVAT) * (*quota.QuantityExpect)
	}

	getPicSKUResp := model.PICDB.QueryOne(model.PIC{
		ObjectCode:    quota.SKU,
		ObjectType:    enum.ObjectType.SKU,
		WarehouseCode: quota.WarehouseCode,
	})

	if getPicSKUResp.Status == common.APIStatus.Ok {
		picSKU := getPicSKUResp.Data.([]*model.PIC)[0]
		if picSKU.PICAccountID != nil {
			quota.SkuPIC = *picSKU.PICAccountID
		}
	}

	if skuConfig.Vendors == nil || len(*skuConfig.Vendors) == 0 {
		return
	}

	// get pic vendor
	vendorCode := ""
	for _, vendor := range *skuConfig.Vendors {
		if vendor.Priority == nil {
			continue
		}
		if utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
			vendorCode = vendor.VendorCode
			break
		}
	}

	if len(vendorCode) == 0 {
		return
	}

	getPicVendorResp := model.PICDB.QueryOne(model.PIC{
		ObjectCode:    vendorCode,
		ObjectType:    enum.ObjectType.VENDOR,
		WarehouseCode: quota.WarehouseCode,
	})
	if getPicVendorResp.Status == common.APIStatus.Ok {
		picVendor := getPicVendorResp.Data.([]*model.PIC)[0]
		if picVendor.PICAccountID != nil {
			quota.VendorPIC = *picVendor.PICAccountID
		}
	}

	// get vendor
	opts := client.APIOption{
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
		},
		Limit: utils.Pointer.WithInt(1),
		Params: map[string]string{
			"sellerCode": vendorCode,
		},
	}
	getVendorResp := seller.GetSellers(opts)
	if getVendorResp.Status != common.APIStatus.Ok {
		return
	}

	vendor := getVendorResp.Data.([]*model.Seller)[0]
	quota.FavoriteVendorID = vendor.SellerID
	quota.FavoriteVendorName = vendor.Name
	quota.FavoriteVendorPhone = vendor.Phone
	if vendor.PaymentTerm != nil {
		quota.PaymentTerm = *vendor.PaymentTerm
	}

}
