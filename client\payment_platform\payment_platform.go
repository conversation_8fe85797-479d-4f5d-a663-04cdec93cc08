package payment_platform

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	getPaymentList      = "QUERY::/payment/list"
	createPayment       = "POST::/payment"
	updatePayment       = "PUT::/payment"
	getReasonList       = "QUERY::/reason/list"
	switchStatusPayment = "PUT::/payment/switch-status"
	getPaymentItemList  = "QUERY::/payment-item/list"
	deletePaymentItem   = "DELETE::/payment-item/delete"

	pathCreatePaymentOrder = "POST::/transfer-request"
)

var paymentCoreClient *client.Client

func InitPaymentPlatformClient(database *mongo.Database) {
	const paymentCore = "/billing/payment/v1"

	headers := map[string]string{"Authorization": conf.Config.SSOToken}
	paymentCoreClient = client.NewClientWithHost(conf.Config.SSOHost, paymentCore, headers, 0)
	paymentCoreClient.WithDatabase(database)
	paymentCoreClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     getPaymentList,
				Name:     "payment_platform__QUERY__payment_list",
				Database: database,
			},

			{
				Path:     createPayment,
				Name:     "payment_platform__POST__payment",
				Database: database,
			},
			{
				Path:     updatePayment,
				Name:     "payment_platform__PUT__payment",
				Database: database,
			},
			{
				Path:     getReasonList,
				Name:     "payment_platform__QUERY__reason_list",
				Database: database,
			},
			{
				Path:     switchStatusPayment,
				Name:     "payment_platform__PUT__payment_switch_status",
				Database: database,
			},

			{
				Path:     getPaymentItemList,
				Name:     "payment_platform__QUERY__payment_item_list",
				Database: database,
			},
			{
				Path:     deletePaymentItem,
				Name:     "payment_platform__DELETE__payment_item_delete",
				Database: database,
			},
			{
				Path:     pathCreatePaymentOrder,
				Name:     "payment_platform__POST__transfer_request",
				Database: database,
			},
		}...,
	)
}

func GetPaymentList(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentPlatformReponse
	_, err := paymentCoreClient.WithRequest(getPaymentList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func CreatePayment(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentPlatformReponse
	_, err := paymentCoreClient.WithRequest(createPayment, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func UpdatePayment(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentPlatformReponse
	_, err := paymentCoreClient.WithRequest(updatePayment, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetReasonSetting(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp ReasonSettingResponse
	_, err := paymentCoreClient.WithRequest(getReasonList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func SwitchStatusPayment(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentPlatformReponse
	_, err := paymentCoreClient.WithRequest(switchStatusPayment, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetPaymentItemList(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentItemResponse
	_, err := paymentCoreClient.WithRequest(getPaymentItemList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func DeletePaymentItem(opts ...client.APIOption) *common.APIResponse {
	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp PaymentItemResponse
	_, err := paymentCoreClient.WithRequest(deletePaymentItem, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func CreatePaymentOrder(opts ...client.APIOption) *common.APIResponse {

	requestOption := paymentCoreClient.WithAPIOption(opts...)
	var resp TransferRequestResponse
	_, err := paymentCoreClient.WithRequest(pathCreatePaymentOrder, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
