package core

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	employeeListPath = "GET::/employee/all-v2"
)

var (
	account *client.Client
)

func InitAccountClient(database *mongo.Database) {
	headers := map[string]string{"Authorization": conf.Config.APIKey}
	account = client.NewClient("/core/account/v1", headers, 0)
	account.WithDatabase(database)
}

// GetRegionList ...
func GetAccountList(opts ...client.APIOption) *common.APIResponse {
	requestOption := account.WithAPIOption(opts...)
	var resp AccountResponse
	_, err := account.WithRequest(employeeListPath, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.<PERSON>, err)
}
