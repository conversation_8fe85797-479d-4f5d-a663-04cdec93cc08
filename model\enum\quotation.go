package enum

// QuotationStatusValue ..
type QuotationStatusValue string

// nolint
type quotationStatus struct {
	PUBLISH      QuotationStatusValue
	STOP_PUBLISH QuotationStatusValue
}

// SkuStatus ...
var QuotationStatus = &quotationStatus{
	"PUBLISH",
	"STOP_PUBLISH",
}

// ======================================================

// QuotationActionTrackingValue ..
// type QuotationActionTrackingValue string

// // nolint
// type quotationActionTracking struct {
// 	BIDDING     QuotationActionTrackingValue
// 	USER_UPDATE QuotationActionTrackingValue
// 	PO_INCREASE QuotationActionTrackingValue
// 	// mod
// 	OVERRITE QuotationActionTrackingValue
// 	ADD      QuotationActionTrackingValue
// }

// // SkuStatus ...
// var QuotationActionTracking = &quotationActionTracking{
// 	"BIDDING",
// 	"USER_UPDATE",
// 	"PO_INCREASE",
// 	// mod
// 	"OVERRITE",
// 	"ADD",
// }

type (
	QuotationType string

	quotationtype struct {
		NORMAL  QuotationType
		HEDGING QuotationType
	}
)

var QuotationTypeVal = &quotationtype{
	NORMAL:  "NORMAL",
	HEDGING: "HEDGING",
}
