package core

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var coreClient *client.Client

const (
	genToken           = "GET::/access-token/gen"
	postUploadDocument = "POST::/upload/document"
)

func InitCoreV1Client(database *mongo.Database) {
	const fileManagerV1 = "/core/file-manager/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	coreClient = client.NewClient(fileManagerV1, headers, 0)
	coreClient.WithDatabase(database)
	coreClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     genToken,
				Database: database,
				Name:     "file_manager__GET__gen_token",
			},
			{
				Path:     postUploadDocument,
				Name:     "upload_document",
				Database: database,
			},
		}...,
	)
}

func GenToken(opts ...client.APIOption) *common.APIResponse {
	requestOption := coreClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := coreClient.WithRequest(genToken, requestOption, &resp)
	return client.FormatResponse(resp, nil, err)
}

func UploadDocument(opts ...client.APIOption) *common.APIResponse {
	requestOption := coreClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := coreClient.WithRequest(postUploadDocument, requestOption, &resp)
	return client.FormatResponse(resp, nil, err)
}
