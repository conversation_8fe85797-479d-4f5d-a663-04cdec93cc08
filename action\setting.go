package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetSetting
func GetSetting(query *model.Setting, offset, limit int64, getTotal bool) *common.APIResponse {

	settingResp := model.SettingDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.SettingDB.Count(query)
		settingResp.Total = countResp.Total
	}

	return settingResp
}

// CreateSetting ...
func CreateSetting(input *model.Setting) *common.APIResponse {
	resp := model.SettingDB.QueryAll()
	if resp.Status != common.APIStatus.NotFound {
		return resp
	}
	return model.SettingDB.Create(input)
}

// UpdateSetting ...
func UpdateSetting(input *model.Setting) *common.APIResponse {
	resp := model.SettingDB.QueryAll()
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	setting := resp.Data.([]*model.Setting)[0]

	return model.SettingDB.UpdateOne(
		model.Setting{ID: setting.ID},
		input)
}
