package billing

import (
	"errors"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

const (
	PaidAB     = "PaidAB"
	RollbackAB = "RollbackAB"
)

type ABill struct {
	OrgUid     int64  `bson:"orgUid,omitempty" json:"orgUid,omitempty"`
	EntityUid  int64  `bson:"entityUid,omitempty" json:"entityUid,omitempty"`
	ServiceUid int64  `bson:"serviceUid,omitempty" json:"serviceUid,omitempty"`
	Type       string `bson:"type" json:"type"`

	VendorCode string `bson:"vendorCode" json:"vendorCode"`
	VendorName string `bson:"vendorName" json:"vendorName"`

	Code          string `json:"code" bson:"code"`
	AdjustType    string `bson:"adjustType" json:"adjustType"`
	InvoiceNumber string `bson:"invoiceNumber" json:"invoiceNumber"`
	Note          string `bson:"note" json:"note"`

	TotalVat             *float64 `bson:"totalVat" json:"totalVat"`
	TotalAmountBeforeVat *float64 `bson:"totalAmountBeforeVat" json:"totalAmountBeforeVat"`
	TotalAmountAfterVat  *float64 `bson:"totalAmountAfterVat" json:"totalAmountAfterVat"`
	TotalDiscountAmount  *float64 `bson:"totalDiscountAmount" json:"totalDiscountAmount"`

	Items []ABItem `bson:"items" json:"items"`

	CollectionDate time.Time `json:"collectionDate" bson:"collectionDate"`
}

func (vb *ABill) Bind(m model.AdjustmentBill) *ABill {
	vb.VendorCode = m.VendorCode
	vb.VendorName = m.VendorName
	vb.Code = m.AdjustmentBillCode
	vb.AdjustType = string(m.AdjustType)
	vb.InvoiceNumber = m.InvoiceNumber
	vb.Note = m.Note
	vb.TotalVat = m.TotalVAT
	vb.TotalAmountBeforeVat = m.TotalAmountBeforeVAT
	vb.TotalAmountAfterVat = m.TotalAmountAfterVAT
	vb.TotalDiscountAmount = m.TotalDiscountAmount
	return vb
}

type ABItem struct {
	OriginInvoiceNumber string   `bson:"originInvoiceNumber" json:"originInvoiceNumber"`
	Quantity            int64    `bson:"quantity" json:"quantity"`
	Vat                 *float64 `bson:"vat" json:"vat"`
	ProductCode         string   `bson:"productCode" json:"productCode"`
	AdjustPrice         *float64 `bson:"adjustPrice" json:"adjustPrice"`
	VatPrice            *float64 `bson:"vatPrice" json:"vatPrice"`
	TotalAmountAfterVat *float64 `bson:"totalAmountAfterVat" json:"totalAmountAfterVat"`
	Sku                 string   `bson:"sku" json:"sku"`
}

func (i *ABItem) BindList(m []*model.AdjustmentBillItem) ([]ABItem, error) {
	var rs []ABItem
	var productCodes []string
	listSkuMap := make(map[string]model.Sku)
	for _, item := range m {
		if !utils.IsContains(productCodes, item.ProductCode) {
			productCodes = append(productCodes, item.ProductCode)
		}
		rs = append(rs, ABItem{
			OriginInvoiceNumber: item.OriginInvoiceNumber,
			Quantity:            item.Quantity,
			Vat:                 item.Vat,
			ProductCode:         item.ProductCode,
			AdjustPrice:         item.AdjustPrice,
			VatPrice:            item.VatPrice,
			TotalAmountAfterVat: item.TotalAmountAfterVAT,
		})
	}
	if len(productCodes) > 0 {
		option := client.APIOption{
			Limit:  utils.Pointer.WithInt(1000),
			Params: map[string]string{"productCodes": strings.Join(productCodes, ",")},
		}
		listSkuRs := marketplace.GetListSKU(option)
		if listSkuRs.Status != common.APIStatus.Ok {
			return nil, errors.New(listSkuRs.Message)
		}
		listSku := listSkuRs.Data.([]*model.Sku)
		for _, sku := range listSku {
			listSkuMap[sku.ProductCode] = *sku
		}
	}
	for index, item := range rs {
		if sku, ok := listSkuMap[item.ProductCode]; ok {
			rs[index].Sku = sku.SKU
		} else {
			rs[index].Sku = item.ProductCode
		}
	}
	return rs, nil
}

func BuildABAnalyze(billCode string) {
	defer recoverFunc()
	bcSvc := NewBillingCenterSvcClient()
	if bcSvc == nil {
		panic("Init Billing Center client fail")
	}
	var bill *model.AdjustmentBill
	var billItems []*model.AdjustmentBillItem
	var entity EntityTf
	wg := NewWgGroup()
	wg.Go(func() error {
		billRs := model.AdjustmentBillDB.QueryOne(model.AdjustmentBill{AdjustmentBillCode: billCode})
		if billRs.Status != common.APIStatus.Ok {
			return errors.New(billRs.Message)
		}
		bill = billRs.Data.([]*model.AdjustmentBill)[0]
		seller, err := bcSvc.GetVendor(bill.VendorCode)
		if err != nil {
			return err
		}
		entity, err = bcSvc.GetEntity(seller.EntityID)
		if err != nil {
			return err
		}
		return nil
	})
	wg.Go(func() error {
		billItemRs := model.AdjustmentBillItemDB.Query(model.AdjustmentBillItem{AdjustmentBillCode: billCode}, 0, 1000, nil)
		if billItemRs.Status != common.APIStatus.Ok {
			return errors.New(billItemRs.Message)
		}
		billItems = billItemRs.Data.([]*model.AdjustmentBillItem)
		return nil
	})
	err := wg.Wait()
	if err != nil {
		panic(err)
	}
	rs := new(ABill).Bind(*bill)
	items, err := new(ABItem).BindList(billItems)
	if err != nil {
		panic(err)
	}
	rs.Type = PaidAB
	rs.Items = items
	rs.EntityUid = entity.EntityID
	rs.OrgUid = entity.OrgID
	rs.ServiceUid = 2
	rs.CollectionDate = time.Now()
	err = bcSvc.PutABill(rs)
	if err != nil {
		panic(err)
	}
}

func BuildRollbackABAnalyze(billCode string) {
	defer recoverFunc()
	bcSvc := NewBillingCenterSvcClient()
	if bcSvc == nil {
		panic("Init Billing Center client fail")
	}
	billRs := model.AdjustmentBillDB.QueryOne(model.AdjustmentBill{AdjustmentBillCode: billCode})
	if billRs.Status != common.APIStatus.Ok {
		panic(billRs.Message)
	}
	bill := billRs.Data.([]*model.AdjustmentBill)[0]
	seller, err := bcSvc.GetVendor(bill.VendorCode)
	if err != nil {
		panic(err)
	}
	entity, err := bcSvc.GetEntity(seller.EntityID)
	if err != nil {
		panic(err)
	}
	rs := ABill{
		OrgUid:     entity.OrgID,
		EntityUid:  entity.EntityID,
		ServiceUid: 2,
		Type:       RollbackAB,
		Code:       billCode,
		VendorCode: bill.VendorCode,
		AdjustType: string(bill.AdjustType),
	}
	err = bcSvc.PutABill(rs)
	if err != nil {
		panic(err)
	}
}
