package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// Create Reason ...
func CreateReasonType(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ReasonType

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.Code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason type code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Name == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Name is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}
	// do action
	return resp.Respond(action.CreateReasonType(input))
}

// Update Reason ...
func UpdateReasonType(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.ReasonType

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.Code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Reason type code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Name == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Name is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.UpdateReasonType(input))
}

// Delete Reason ...
func DeleteReasonType(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		code = req.GetParam("code")
	)

	// validate input
	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid reason type Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.DeleteReasonType(code))
}

// Get Reason ...
func GetReasonTypeByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	code := req.GetParam("code")

	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid reason type Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.GetReasonTypeByCode(code))
}

// Get Reason List ...
func GetReasonTypeList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	var query model.ReasonType
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetReasonTypeList(&query, offset, limit, getTotal))
}
