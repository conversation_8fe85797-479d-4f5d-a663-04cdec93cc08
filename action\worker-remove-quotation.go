package action

import (
	"fmt"
	"os"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// WorkerRemoveQuotation is a scheduled task that removes quotations from the database.
// It runs only if the environment is not set to "uat" and within the first hour of the day.
// The function performs the following steps:
// 1. Checks the current time and exits if it is past the first hour of the day.
// 2. Logs the start of the worker process.
// 3. Continuously queries and deletes quotations from the database until no more are found.
// 4. Deletes related entries from various other databases.
// 5. Sleeps for 5 minutes before ending the process.
// 6. Logs the end of the worker process.
func WorkerRemoveQuotation() {
	timeNow := utils.GetTimeNow()

	if os.Getenv("env") == "uat" {
		return
	}

	// Chạy từ 0 - 0h59'
	if timeNow > 1*60 {
		time.Sleep(5 * time.Minute)
		return
	}

	fmt.Println("Begin WorkerRemoveQuotation")

	// xóa QuotationDB
	for {
		quotationResp := model.QuotationDB.Query(nil, 0, 300, nil)
		if quotationResp.Status == common.APIStatus.Ok {
			quotations := quotationResp.Data.([]*model.Quotation)
			for _, quotation := range quotations {
				// xóa quotation
				model.QuotationDB.Delete(model.Quotation{ID: quotation.ID})

				// xóa ES search
				removeProductSearch(quotation.ProductCode)
			}
		}
		if quotationResp.Status == common.APIStatus.NotFound {
			break
		}
	}

	// xóa các bảng liên quan
	model.QuotationHedgingDB.Delete(nil)
	model.QuotationProductDB.Delete(nil)
	model.QuotationProductVendorDB.Delete(nil)
	model.BiddingDB.Delete(nil)

	model.QuotationExportDraftDB.Delete(nil)
	model.QuotationExportHedgingDraftDB.Delete(nil)

	time.Sleep(5 * time.Minute)

	fmt.Println("End WorkerRemoveQuotation")
}
