package enum

type PaymentPlatfromType string

type paymentPlatformType struct {
	RECEIPT PaymentPlatfromType //Phiếu thu
	PAYMENT PaymentPlatfromType //Phiếu chi
}

var PaymentPlatformType = &paymentPlatformType{
	RECEIPT: "RECEIPT",
	PAYMENT: "PAYMENT",
}

type PaymentCreatedBySourceEnum string

type paymentPlatformCreatedBySource struct {
	THUOCSIVN_INTERNAL_SELLER PaymentCreatedBySourceEnum // THUOCSI_INTENAL_SELLER
}

var PaymentCreatedBySource = &paymentPlatformCreatedBySource{
	THUOCSIVN_INTERNAL_SELLER: "THUOCSIVN_INTERNAL_SELLER",
}

type PaymentPlatformPartnerTypeValue string

type paymentPlatformPartnerTypeValue struct {
	THUOCSIVN_VENDOR PaymentPlatformPartnerTypeValue
}

var PaymentPlatformPartnerType = &paymentPlatformPartnerTypeValue{
	THUOCSIVN_VENDOR: "THUOCSIVN_VENDOR",
}

type PaymentPlatformObjectTypeValue string

type paymentPlatformObjectType struct {
	VENDOR_BILL      PaymentPlatformObjectTypeValue
	ADJUSMENT_BILL   PaymentPlatformObjectTypeValue
	OTHER            PaymentPlatformObjectTypeValue
	CLEARING_PAYMENT PaymentPlatformObjectTypeValue
	PAYMENT          PaymentPlatformObjectTypeValue
}

var PaymentPlatformObjectType = &paymentPlatformObjectType{
	VENDOR_BILL:      "THUOCSIVN_VENDOR_BILL",
	ADJUSMENT_BILL:   "THUOCSIVN_ADJUSMENT_BILL",
	OTHER:            "OTHER",
	CLEARING_PAYMENT: "CLEARING_PAYMENT",
	PAYMENT:          "PAYMENT",
}

// PaymentStatusValue payment status
type PaymentPlatformStatusValue string
type paymentPlatformStatus struct {
	DRAFT            PaymentPlatformStatusValue
	WAIT_TO_APPROVED PaymentPlatformStatusValue // WAITING
	APPROVED         PaymentPlatformStatusValue // CONFIRMED
	COMPLETED        PaymentPlatformStatusValue // DONE
	CANCELLED        PaymentPlatformStatusValue // CANCELED
}

var PaymentPlatformStatus = &paymentPlatformStatus{
	DRAFT:            "DRAFT",
	WAIT_TO_APPROVED: "WAIT_TO_APPROVED",
	APPROVED:         "APPROVED",
	COMPLETED:        "COMPLETED",
	CANCELLED:        "CANCELLED",
}

type PaymentPlatformMethodValue string
type paymentPlatformMethod struct {
	CASH            PaymentPlatformMethodValue //Tiền mặt
	BANK            PaymentPlatformMethodValue // Ngân hàng
	CLEARING_CREDIT PaymentPlatformMethodValue // cấn công nợ
	OTHER           PaymentPlatformMethodValue // Khác
}

var PaymentPlatformMethod = &paymentPlatformMethod{
	CASH:            "CASH",
	BANK:            "BANK",
	CLEARING_CREDIT: "CLEARING_CREDIT",
	OTHER:           "OTHER",
}

type (
	PaymentPlatformReasonType string
	paymentPlatformReasonType struct {
		ADVANCE_MONEY         PaymentPlatformReasonType // chi tạm ứng (ADVANCE_PAYMENT)
		BILL                  PaymentPlatformReasonType // chi trả nợ (DEBT_PAYMENT)
		PAY_DIRECT            PaymentPlatformReasonType // thu trực tiếp (DIRECT_RECEIPT)
		DEBT                  PaymentPlatformReasonType // cấn trừ hoá đơn (VB_CLEARING_DEBT)
		PAYMENT_CLEARING_DEBT PaymentPlatformReasonType // cấn trừ thu chi
	}
)

var PaymentPlatformReasonCore = &paymentPlatformReasonType{
	PAY_DIRECT:            "PAY_DIRECT",
	DEBT:                  "DEBT",
	ADVANCE_MONEY:         "ADVANCE_MONEY",
	BILL:                  "BILL",
	PAYMENT_CLEARING_DEBT: "PAYMENT_CLEARING_DEBT",
}

type TransferRuleTypeEnum string
type transferRuleTypeEnum struct {
	THUOCSIVN_VENDOR_BILL TransferRuleTypeEnum // VB
	THUOCSIVN_PO          TransferRuleTypeEnum // PO
}

var TransferRuleType = &transferRuleTypeEnum{
	THUOCSIVN_VENDOR_BILL: "THUOCSIVN_VENDOR_BILL",
	THUOCSIVN_PO:          "THUOCSIVN_PO",
}
