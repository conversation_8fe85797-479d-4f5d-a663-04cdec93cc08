package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type PIC struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// unique
	SellerCode string               `json:"sellerCode" bson:"seller_code,omitempty"`
	ObjectCode string               `json:"objectCode,omitempty" bson:"object_code,omitempty"`
	ObjectType enum.ObjectTypeValue `json:"objectType,omitempty" bson:"object_type,omitempty"`

	WarehouseCode  string   `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	PICAccountID   *int64   `json:"picAccountID,omitempty" bson:"pic_account_id,omitempty"`
	CICAccountID   *int64   `json:"cicAccountID,omitempty" bson:"cic_account_id,omitempty"`
	MinOrderAmount *float64 `json:"minOrderAmount,omitempty" bson:"min_order_amount,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []*bson.M `json:"-" bson:"$or,omitempty"`
}

// PICDB ...
var PICDB = &db.Instance{
	ColName:        "pic",
	TemplateObject: &PIC{},
}

// InitPICMappingModel ...
func InitPICMappingModel(s *mongo.Database) {
	PICDB.ApplyDatabase(s)
}
