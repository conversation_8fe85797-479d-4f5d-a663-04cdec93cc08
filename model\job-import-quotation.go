package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"go.mongodb.org/mongo-driver/mongo"
)

// CallbackOOSJob ...
var ImportQuotationJob = &job.Executor{
	ColName: "import_quotation",
}

func InitImportQuotationJob(database *mongo.Database, consumerFn job.ExecutionFn) {
	ImportQuotationJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:   true,
		ChannelCount:       10,
		OldVersionTimeoutS: 60 * 60,
		CurVersionTimeoutS: 60 * 30,
	})

	ImportQuotationJob.SetTopicConsumer("IMPORT_QUOTATION", consumerFn)
}
