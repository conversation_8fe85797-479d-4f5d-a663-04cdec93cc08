package utils

import (
	"reflect"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// function check nil without panic
func IsNil(i interface{}) bool {
	iType := reflect.TypeOf(i)
	iValue := reflect.ValueOf(i)
	if !iValue.IsValid() {
		return true
	}
	switch iType.Kind() {
	case reflect.Ptr, reflect.Interface, reflect.Slice, reflect.Map, reflect.Chan, reflect.Func, reflect.UnsafePointer:
		return iValue.IsNil()
	default:
		return false
	}
}

// function check list object, if one element == nil -> true, otherwise -> false
func HasNil(listI ...interface{}) bool {
	for _, v := range listI {
		if IsNil(v) {
			return true
		}
	}
	return false
}

// function check any variable is zero or not with panic-free
func IsZero(i interface{}) bool {
	if IsNil(i) {
		return true
	}
	iValue := reflect.ValueOf(i)
	return iValue.IsZero()
}

// function check list variables are zero or not with panic-free
func HasZero(listI ...interface{}) bool {
	for _, v := range listI {
		if IsZero(v) {
			return true
		}
	}
	return false
}

// valid seller and purchaser and return api_response correspondingly
func ValidSellerPurchaser(sellerCode, purchaserCode string) (*common.APIResponse, bool) {
	if !validSeller(sellerCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid SellerCode",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		}, false
	}
	if !validPurchaser(purchaserCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid PurchaserCode",
			ErrorCode: string(enum.ErrorCodeInvalid.Purchaser),
		}, false
	}
	return nil, true
}

// check if purchaser is in list of purchasers
func validPurchaser(purchaserCode string) bool {
	if len(PURCHASERS_CODES) == 0 {
		return false
	}
	return IsContains(PURCHASERS_CODES, purchaserCode)
}

// check if sellerCode is in list of internal-sellers
func validSeller(sellerCode string) bool {
	if len(INTERNAL_SELLERS) == 0 {
		return false
	}
	return IsContains(INTERNAL_SELLERS, sellerCode)
}

// check warehouseCode is in list of warehouses
func ValidWarehouseCode(warehouseCode string) bool {
	_, ok := SelectWMSByCode(warehouseCode)
	return ok
}
