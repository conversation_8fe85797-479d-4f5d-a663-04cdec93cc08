package client

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/pkg/sftp"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"golang.org/x/crypto/ssh"
)

type SFTPClient interface {
	OpenFile(path string, mode int) (*sftp.File, error)
	// Getwd() (string, error)
	// Mkdir(path string) error
	// Remove(path string) error
	// Write(p []byte) (int, error)
	// Read(p []byte) (int, error)
	// RemoveDirectory(path string) error
}

const (
	// DefaultTimeout is the default timeout for dialing an SSH server.
	SFTPDefaultDurationReconnect = time.Minute
)

type sftpClient struct {
	session        *sftp.Client
	conf           conf.SFTPConfig
	isReconnecting bool
	mu             sync.Mutex
	du             time.Duration
}

func (c *sftpClient) OpenFile(path string, mode int) (*sftp.File, error) {
	f, err := c.session.OpenFile(path, mode)
	if err == sftp.ErrSSHFxConnectionLost {
		go c.reconnect()
	}
	return f, err
}

// func (c *sftpClient) Getwd() (string, error) {
// 	p, err := c.session.Getwd()
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return p, err
// }

// func (c *sftpClient) Mkdir(path string) error {
// 	err := c.session.Mkdir(path)
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return err
// }

// func (c *sftpClient) Remove(path string) error {
// 	err := c.session.Remove(path)
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return err
// }
// func (c *sftpClient) Write(p []byte) (int, error) {
// 	n, err := c.session.Write(p)
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return n, err
// }
// func (c *sftpClient) Read(p []byte) (int, error) {
// 	n, err := c.session.Read(p)
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return n, err
// }

// func (c *sftpClient) RemoveDirectory(path string) error {
// 	err := c.session.RemoveDirectory(path)
// 	if err == sftp.ErrSSHFxConnectionLost {
// 		go c.reconnect()
// 	}
// 	return err
// }

func (c *sftpClient) reconnect() {
	// if !c.conf.AutoReconnect {
	// 	return
	// }

	// check is retrying
	c.mu.Lock()
	if c.isReconnecting {
		c.mu.Unlock()
		return
	}
	c.isReconnecting = true
	c.mu.Unlock()

	dur := time.Minute

	for {
		session, err := initSFTPClientSession(c.conf)
		if err == nil {
			// replace session
			c.session = session
			break
		}

		log.Printf("SFTP: reconnecting failed to %q after %v\n", c.conf.Host, dur)
		time.Sleep(dur)
		dur = dur * 2 //  double duration
	}

	// set is retrying back
	c.mu.Lock()
	c.isReconnecting = false
	c.mu.Unlock()
}

func InitSFTPClient(conf conf.SFTPConfig) (SFTPClient, error) {
	session, err := initSFTPClientSession(conf)
	if err != nil {
		return nil, err
	}

	return &sftpClient{
		session: session,
		conf:    conf,
		du:      SFTPDefaultDurationReconnect,
	}, nil
}

func initSFTPClientSession(conf conf.SFTPConfig) (*sftp.Client, error) {
	// get user - pass - host
	var (
		user     = conf.Username
		password = conf.Password
		host     = conf.Host
	)

	log.Printf("SFTP: Connecting to %q ...\n", host)
	// init config
	config := ssh.ClientConfig{
		User:            user,
		Auth:            []ssh.AuthMethod{ssh.Password(password)},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}
	addr := fmt.Sprintf("%s:%d", host, conf.Port)

	// connect to server via ssh TCP
	conn, err := ssh.Dial("tcp", addr, &config)
	if err != nil {
		return nil, err
	}

	// create connection session from ssh client with subsystem SFTP
	clientSession, err := sftp.NewClient(conn)
	if err != nil {
		return nil, err
	}

	log.Printf("SFTP: Connected to %q \n", host)
	return clientSession, nil
}
