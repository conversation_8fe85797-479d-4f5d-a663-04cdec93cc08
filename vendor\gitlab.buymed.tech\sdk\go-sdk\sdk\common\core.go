package common

// Error custom error of sdk
type Error struct {
	Type    string
	Message string
	Data    interface{}
}

func (e *Error) Error() string {
	return e.Type + " : " + e.Message
}

// APIResponse This is  response object with JSON format
type APIResponse struct {
	Status    string            `json:"status"`
	Data      interface{}       `json:"data,omitempty"`
	Message   string            `json:"message"`
	ErrorCode string            `json:"errorCode,omitempty"`
	Total     int64             `json:"total,omitempty"`
	Headers   map[string]string `json:"headers,omitempty"`
}

// StatusEnum ...
type StatusEnum struct {
	Ok            string
	Error         string
	Invalid       string
	NotFound      string
	Forbidden     string
	Existed       string
	Unauthorized  string
	Unprocessable string
}

// APIStatus Published enum
var APIStatus = &StatusEnum{
	Ok:            "OK",
	Error:         "ERROR",
	Invalid:       "INVALID",
	NotFound:      "NOT_FOUND",
	Forbidden:     "FORBIDDEN",
	Existed:       "EXISTED",
	Unauthorized:  "UNAUTHORIZED",
	Unprocessable: "UNPROCESSABLE",
}

// MethodValue ...
type MethodValue struct {
	Value string
}

// MethodEnum ...
type MethodEnum struct {
	GET     *MethodValue
	POST    *MethodValue
	PUT     *MethodValue
	DELETE  *MethodValue
	OPTIONS *MethodValue
}

// APIMethod Published enum
var APIMethod = MethodEnum{
	GET:     &MethodValue{Value: "GET"},
	POST:    &MethodValue{Value: "POST"},
	PUT:     &MethodValue{Value: "PUT"},
	DELETE:  &MethodValue{Value: "DELETE"},
	OPTIONS: &MethodValue{Value: "OPTIONS"},
}
