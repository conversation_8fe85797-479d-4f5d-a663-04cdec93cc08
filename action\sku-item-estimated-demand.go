package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetSkuItemEstDemand(query interface{}, offset, limit int64) *common.APIResponse {
	return model.SKUItemEstimationDemandDB.Query(query, offset, limit, &primitive.M{"_id": 1})
}

func GetSkuItemEstDemandCount() *common.APIResponse {
	return model.SKUItemEstimationDemandDB.Count(nil)
}
