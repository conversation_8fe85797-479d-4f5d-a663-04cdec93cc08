package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// CreateContact ...
func CreateContact(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Contact

	// parse input
	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.PurposeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Purpose code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Type == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ContactInfo == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Contact info is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if (enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO) && utils.IsNil(input.WarehouseCode) {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.CreateContact(input))
}

// UpdateContact ...
func UpdateContact(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Contact

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ContactCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Contact code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.PurposeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Purpose code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Type == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ContactInfo == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Contact info is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if (enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO) && utils.IsNil(input.WarehouseCode) {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.UpdateContact(input))
}

// DeleteContact ...
func DeleteContact(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		contactCode = req.GetParam("contactCode")
	)

	// validate input
	if contactCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Contact Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.DeleteContact(contactCode))
}

// GetContactByCode ...
func GetContactByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	contactCode := req.GetParam("contactCode")

	// validate input
	if contactCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Contact Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.GetContactByCode(contactCode))
}


// GetContactList ...
func GetContactList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// validate input
	var query model.Contact
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetContactList(&query, offset, limit, getTotal))
}

// GetContactMe ...
func GetContactMe(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)

	var query model.Contact
	// check permission
	if acc := getActionSource(req); acc != nil {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetContactList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}
