package utils

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
)

var Pointer = pointer{}

type pointer struct{}

func (p pointer) WithTime(i ...time.Time) *time.Time {
	if i == nil {
		_i := time.Now()
		return &_i
	}

	return &i[0]
}

func (p pointer) WithString(i ...string) *string {
	if i == nil {
		_i := ""
		return &_i
	}

	return &i[0]
}

func (p pointer) WithBool(i ...bool) *bool {
	if i == nil {
		_i := false
		return &_i
	}

	return &i[0]
}

func (p pointer) WithInt(i ...int) *int {
	if i == nil {
		_i := 0
		return &_i
	}

	return &i[0]
}

func (p pointer) WithInt64(i ...int64) *int64 {
	if i == nil {
		_i := int64(0)
		return &_i
	}

	return &i[0]
}

func (p pointer) WithFloat(i ...float32) *float32 {
	if i == nil {
		_i := float32(0)
		return &_i
	}

	return &i[0]
}

func (p pointer) WithFloat64(i ...float64) *float64 {
	if i == nil {
		_i := float64(0)
		return &_i
	}

	return &i[0]
}

func (p pointer) WithResponse(content string, template interface{}) error {
	err := json.Unmarshal([]byte(content), template)
	return err
}

func (p pointer) WithInvalidInput(e error) *common.APIResponse {
	rs := &common.APIResponse{
		Status:    common.APIStatus.Invalid,
		Message:   "Invalid input",
		ErrorCode: "INVALID_INPUT",
	}
	if e != nil {
		rs.Data = []string{e.Error()}
	}
	return rs
}
