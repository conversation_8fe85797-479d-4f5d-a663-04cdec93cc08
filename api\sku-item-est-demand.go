package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson"
)

func GetSkuItemEstDemand(req sdk.APIRequest, res sdk.APIResponder) error {
	var input model.SKUItemEstDemandQuery
	if err := req.GetContent(&input); err != nil {
		return res.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid data",
		})
	}

	if input.GetTotal {
		return res.Respond(action.GetSkuItemEstDemandCount())
	}

	var query bson.M
	if input.LastID != nil {
		query = bson.M{
			"_id": bson.M{
				"$gt": input.LastID,
			},
		}
	}

	if input.Limit > 1000 {
		input.Limit = 1000
	}

	return res.Respond(action.GetSkuItemEstDemand(query, input.Offset, input.Limit))
}
