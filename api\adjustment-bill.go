package api

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func GetListAdjustmentBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		query    model.AdjustmentBill
		qStr     = req.GetParam("q")
	)

	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// search
	if len(query.Hashtag) == 0 && len(query.Search) > 0 {
		searchTerm := utils.NormalizeString(query.Search)
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"hashtag": bson.M{"$regex": fmt.Sprintf(".*%s.*", searchTerm)},
		})
	}
	//  vendor codes
	if len(query.VendorCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"vendor_code": bson.M{"$in": query.VendorCodeIn},
		})
	}
	// ids
	if len(query.AdjustmentBillIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"adjustment_bill_id": bson.M{"$in": query.AdjustmentBillIDIn},
		})
	}
	// ids
	if len(query.InvoiceNumberIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"invoice_number": bson.M{"$in": query.InvoiceNumberIn},
		})
	}

	// type
	if len(query.AdjustTypeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"adjust_type": bson.M{"$in": query.VendorCodeIn},
		})
	}
	// duetime
	if query.DueTimeFrom != nil {
		dueTimeFrom := utils.GetFirstTimeOfDate(query.DueTimeFrom.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"due_time": bson.M{"$gte": dueTimeFrom},
		})
	}
	if query.DueTimeTo != nil {
		dueTimeTo := utils.GetLastTimeOfDate(query.DueTimeTo.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"due_time": bson.M{"$lte": dueTimeTo},
		})
	}

	// issuedtime
	if query.IssuedTimeFrom != nil {
		issuedTimeFrom := utils.GetFirstTimeOfDate(query.IssuedTimeFrom.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"issued_time": bson.M{"$gte": issuedTimeFrom},
		})
	}
	if query.IssuedTimeTo != nil {
		issuedTimeTo := utils.GetLastTimeOfDate(query.IssuedTimeTo.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"issued_time": bson.M{"$lte": issuedTimeTo},
		})
	}
	// origin issued time
	if query.OriginIssuedTimeFrom != nil || query.OriginIssuedTimeTo != nil {
		queryItem := model.AdjustmentBillItem{}
		if query.OriginIssuedTimeFrom != nil {
			originIssuedTimeFrom := utils.GetFirstTimeOfDate(query.OriginIssuedTimeFrom.In(utils.TimeZoneVN))
			queryItem.ComplexQuery = append(queryItem.ComplexQuery, bson.M{"origin_issued_time": bson.M{"$gte": originIssuedTimeFrom}})
		}
		if query.OriginIssuedTimeTo != nil {
			originIssuedTimeTo := utils.GetLastTimeOfDate(query.OriginIssuedTimeTo.In(utils.TimeZoneVN))
			queryItem.ComplexQuery = append(queryItem.ComplexQuery, bson.M{"origin_issued_time": bson.M{"$lte": originIssuedTimeTo}})
		}
		mainCodeIn := action.GetListAdjustmentBillMainCodeFromItem(&queryItem, 0, 200, &bson.M{"_id": -1})
		query.ComplexQuery = append(query.ComplexQuery, bson.M{"adjustment_bill_code": bson.M{"$in": mainCodeIn}})
	}
	// origin invoice number
	if len(query.OriginInvoiceNumberIn) > 0 {
		queryItem := model.AdjustmentBillItem{
			ComplexQuery: []bson.M{{"origin_invoice_number": bson.M{"$in": query.OriginInvoiceNumberIn}}},
		}
		mainCodeIn := action.GetListAdjustmentBillMainCodeFromItem(&queryItem, 0, 200, &bson.M{"_id": -1})
		query.ComplexQuery = append(query.ComplexQuery, bson.M{"adjustment_bill_code": bson.M{"$in": mainCodeIn}})
	}

	// product
	if len(query.ProductCodeIn) > 0 {
		queryItem := model.AdjustmentBillItem{
			ComplexQuery: []bson.M{{"product_code": bson.M{"$in": query.ProductCodeIn}}},
		}
		mainCodeIn := action.GetListAdjustmentBillMainCodeFromItem(&queryItem, 0, 200, &bson.M{"_id": -1})
		query.ComplexQuery = append(query.ComplexQuery, bson.M{"adjustment_bill_code": bson.M{"$in": mainCodeIn}})
	}
	if len(query.StatusIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{"status": bson.M{"$in": query.StatusIn}})
	}

	return resp.Respond(action.GetListAdjustmentBillMain(&query, offset, limit, getTotal, &bson.M{"_id": -1}))
}

// GetSingleAdjustmentBills ...
func GetSingleAdjustmentBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.AdjustmentBill
	)

	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// get main
	getMainResp := action.GetListAdjustmentBillMain(&query, 0, 1, false, nil)
	if getMainResp.Status != common.APIStatus.Ok {
		return resp.Respond(getMainResp)
	}
	iaMain := getMainResp.Data.([]*model.AdjustmentBill)[0]

	getItemResp := action.GetListAdjustmentBillItem(&model.AdjustmentBillItem{AdjustmentBillCode: iaMain.AdjustmentBillCode}, 0, 1000, false, nil)
	if getItemResp.Status != common.APIStatus.Ok {
		return resp.Respond(getItemResp)
	}

	items := getItemResp.Data.([]*model.AdjustmentBillItem)
	iaMain.Items = items
 
	// get history
	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.AdjustmentBill{iaMain},
	})
}

// GetListAdjustmentBillItem ...
func GetListAdjustmentBillItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		query    model.AdjustmentBillItem
	)

	// parse query
	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// search
	return resp.Respond(action.GetListAdjustmentBillItem(&query, offset, limit, getTotal, &bson.M{"_id": -1}))
}

// CreateAdjustmentBill ...
func CreateAdjustmentBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.AdjustmentBill
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// return resp.Respond(action.CreateAdjustmentBill(&input))
	return resp.Respond(action.CreateAdjustmentBill(&input))
}

// UpdateAdjustmentBill ...
func UpdateAdjustmentBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.AdjustmentBill
	)

	// parse input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.UpdateAdjustmentBill(&input))
}

// DeleteAdjustmentBill ...
func SwitchStatusAdjustmentBill(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.AdjustmentBill
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.SwitchStatusAdjustmentBill(input.AdjustmentBillCode, input.Status))
}
