package model

// type CallbackStock struct {
// 	SKU           string `json:"sku"`
// 	StockQuantity int64  `json:"stockQuantity"`
// }

// // CallbackOOSJob ...
// var CallbackStockJob = &job.Executor{
// 	ColName: "callback_stock",
// }

// func InitCallbackStockJob(database *mongo.Database, consumerFn job.ExecutionFn) {
// 	CallbackStockJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
// 		WaitForReadyTime:   true,
// 		ChannelCount:       10,
// 		OldVersionTimeoutS: 60 * 1,
// 		CurVersionTimeoutS: 60 * 5,
// 	})

// 	CallbackStockJob.SetTopicConsumer("CALLBACK_STOCK", consumerFn)
// }
