package integration

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var notificationClient *client.Client

const (
	postNotification = "POST::/notification"
)

func InitNotification(database *mongo.Database) {
	const notificationV1 = "/integration/notification/v1"
	headers := map[string]string{"Authorization": conf.Config.APIKey}
	notificationClient = client.NewClient(notificationV1, headers, 0)
	notificationClient.WithDatabase(database)
	notificationClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     postNotification,
				Name:     "notification__POST__create_notification",
				Database: database,
			},
		}...,
	)
}

func CreateNotification(opts ...client.APIOption) *common.APIResponse {
	requestOption := notificationClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := notificationClient.WithRequest(postNotification, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
