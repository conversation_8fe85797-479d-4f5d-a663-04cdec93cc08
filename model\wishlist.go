package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Wishlist ...
type Wishlist struct {
	ID              primitive.ObjectID `json:"-" bson:"_id,omitempty" `
	CreatedTime     *time.Time         `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time         `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`

	VendorCode    string    `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	ComplexQuery  []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductIDIn   []int64   `json:"productIDIn,omitempty" bson:"-"`
	ProductCodeIn []string  `json:"productCodeIn,omitempty" bson:"-"`
}

// WishlistDB ...
var WishlistDB = &db.Instance{
	ColName:        "wishlist",
	TemplateObject: &Wishlist{},
}

// InitWishlistModel is func init model wishlist
func InitWishlistModel(s *mongo.Database) {
	WishlistDB.ApplyDatabase(s)

	// t := true
	// _ = WishlistDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = WishlistDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
