package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/billing"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// GetSingleNewPaymentItem retrieves a single new payment item based on the provided query and options.
// It returns an APIResponse containing the payment item details.
//
// Parameters:
//   - query: A model.PaymentItemPlatform struct containing the query parameters for the payment item.
//   - option: A model.QueryOption struct containing additional query options.
//
// Returns:
//   - *common.APIResponse: A pointer to an APIResponse struct containing the payment item details.
func GetSingleNewPaymentItem(query model.PaymentItemPlatform, option model.QueryOption) *common.APIResponse {
	return GetPaymentItem(query, 0, 1, option)
}

// GetPaymentItem
// GetPaymentItem retrieves a list of payment items based on the provided query parameters.
// It processes various query fields to generate additional query conditions and then
// makes an API call to fetch the payment items.
//
// Parameters:
//   - query: model.PaymentItemPlatform - The query parameters for fetching payment items.
//   - offset: int64 - The offset for pagination.
//   - limit: int64 - The limit for pagination.
//   - option: model.QueryOption - Additional query options.
//
// Returns:
//   - *common.APIResponse - The API response containing the list of payment items or an error status.
func GetPaymentItem(query model.PaymentItemPlatform, offset, limit int64, option model.QueryOption) *common.APIResponse {
	// query AdjustmentBillCodeIn
	if len(query.AdjustmentBillCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(
			query.ExtraDataKeyValIn,
			query.GenQueryAdjustmentBillCode(query.AdjustmentBillCodeIn)...)
		query.AdjustmentBillCodeIn = nil
	}

	// query VendorBillCodeIn
	if len(query.VendorBillCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(
			query.ExtraDataKeyValIn,
			query.GenQueryVendorBillCode(query.VendorBillCodeIn)...)

		query.VendorBillCodeIn = nil
	}

	// query PaymentCodeIn
	if len(query.PartnerCode) > 0 {
		query.ExtraDataKeyValIn = append(
			query.ExtraDataKeyValIn,
			query.GenQueryPartnerCode([]string{query.PartnerCode})...)
		query.PartnerCode = ""
	}
	// query PaymentCodeIn
	if len(query.PartnerCodeIn) > 0 {
		query.ExtraDataKeyValIn = append(
			query.ExtraDataKeyValIn,
			query.GenQueryPartnerCode(query.PartnerCodeIn)...)
		query.PartnerCodeIn = nil
	}

	query.FillData()
	cliOption := client.APIOption{
		SaveLog: utils.Pointer.WithBool(false),
		Body: model.PaymentItemPlatformQuery{
			Query:  query,
			Option: option,
			Offset: offset,
			Limit:  limit,
		},
	}

	// Query data from client
	resp := payment_platform.GetPaymentItemList(cliOption)
	if resp.Status != common.APIStatus.Ok {
		return resp

	}
	// Fill support field
	items := resp.Data.([]*model.PaymentItemPlatform)
	for i := range items {
		items[i].FillSuportField()
	}
	resp.Data = items
	return resp
}

// thêm payment item chỉ có mục đích giảm dư nợ
// AddPaymentItem processes a payment item based on its type and returns an API response.
// It determines the type of the payment item by checking the length of specific fields
// and calls the appropriate function to handle the payment item.
//
// Parameters:
//   - paymentItem: A pointer to a PaymentItemPlatform struct containing the details of the payment item.
//
// Returns:
//   - A pointer to an APIResponse struct containing the status and message of the operation.
//
// The function handles the following cases:
//   - If VendorBillCode is present, it calls AddPaymentItemVB.
//   - If AdjustmentBillCode is present, it calls AddPaymentItemAB.
//   - If PaymentVoucherCode or ReceiptVoucherCode is present, it calls AddPaymentItemClearingDebt.
//   - If none of the above fields are present, it returns an API response with an Invalid status and a message indicating an invalid payment item type.
func AddPaymentItem(paymentItem *model.PaymentItemPlatform) *common.APIResponse {
	switch {
	case len(paymentItem.VendorBillCode) > 0:
		return AddPaymentItemVB(paymentItem)

	case len(paymentItem.AdjustmentBillCode) > 0:
		return AddPaymentItemAB(paymentItem)

	case len(paymentItem.PaymentVoucherCode) > 0 || len(paymentItem.ReceiptVoucherCode) > 0:
		return AddPaymentItemClearingDebt(paymentItem)

	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid payment item type",
		}
	}
}

// AddPaymentItem
// AddPaymentItemVB adds a payment item to a vendor bill.
// It validates the input data, checks the payment and vendor bill,
// updates the payment and vendor bill, and returns an API response.
//
// Parameters:
//   - input: A pointer to a model.PaymentItemPlatform containing the payment item details.
//
// Returns:
//   - A pointer to a common.APIResponse indicating the result of the operation.
//
// Possible error responses:
//   - Invalid input data
//   - PaymentCode is required
//   - VendorBillCode is required
//   - Invalid Amount
//   - NewRemainingMoney < 0
//   - Various errors from querying or updating the payment and vendor bill
func AddPaymentItemVB(input *model.PaymentItemPlatform) *common.APIResponse {
	// validate input data
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		}
	}

	// validate input PaymentCode
	if len(input.PaymentCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentCode is required",
		}
	}

	// validate input VendorBillCode
	if len(input.VendorBillCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "VendorBillCode is required",
		}
	}

	// validate input data
	if input.Amount == nil || *input.Amount == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Amount",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	// check Payment
	paymentQueryCheck := model.PaymentPlatform{
		PaymentCode: input.PaymentCode,
	}

	// get payment
	paymentResp := GetSingleNewPayment(paymentQueryCheck,
		model.QueryOption{Items: true})
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}
	payment := paymentResp.Data.([]*model.PaymentPlatform)[0]
	var (
		price float64 = 0
		// newBalance          float64 = 0
		newVBRemainingMoney float64 = 0
	)

	price = *input.Amount

	// if payment.Balance != nil {
	// 	newBalance = *payment.Balance
	// }
	// newBalance -= price
	// // double check
	// if newBalance < 0 {
	// 	return &common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "NewBalance < 0",
	// 		ErrorCode: string(enum.ErrorCodeInvalid.NewBalanceLTZero),
	// 	}
	// }

	// // round balance
	// if newBalance < conf.CURRENCY_ROUND {
	// 	newBalance = 0
	// }

	// check VendorBillì
	vbResp := model.VendorBillDB.QueryOne(model.VendorBill{
		VendorBillCode: input.VendorBillCode,
		VendorCode:     payment.PartnerCode,
		Status:         enum.VendorBillStatus.WAIT_TO_PAID,
	})
	if vbResp.Status != common.APIStatus.Ok {
		return vbResp
	}
	vb := vbResp.Data.([]*model.VendorBill)[0]

	// For VB
	if vb.RemainingMoney != nil {
		newVBRemainingMoney = *vb.RemainingMoney
	}

	// giảm remaining_money trong VB
	newVBRemainingMoney -= price

	// double check
	if newVBRemainingMoney < -conf.CURRENCY_ROUND {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "NewRemainingMoney < 0",
			ErrorCode: string(enum.ErrorCodeInvalid.NewRemainingMoneyLTZero),
		}
	}

	{ // payment main
		payment.FillSuportField()
		payment.PORelatedCodes = append(payment.PORelatedCodes, vb.POCode)
		// Append relate po, rm duplicate
		payment.FillData(model.OptionFillDataPaymentPlatform{
			ExtraData: true,
		})
	}

	{ // create payment item
		input.VendorBillID = vb.VendorBillID
		input.InvoiceNumber = vb.InvoiceNumber
		input.POCode = vb.POCode
		input.TransactionType = enum.Transaction.VENDOR_BILL
		input.ObjectType = enum.PaymentPlatformObjectType.VENDOR_BILL
		input.ObjectCode = input.VendorBillCode
		input.FillData(model.OptionFillDataPaymentPlatform{
			ExtraData: true,
		})
	}

	payment.Items = append(payment.Items, input)
	// update payments
	resp := updatePayment(*payment)

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	// update VendorBill
	status := vb.Status
	if newVBRemainingMoney < conf.CURRENCY_ROUND {
		status = enum.VendorBillStatus.PAID
	}

	// update VB
	updateVBResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{ID: vb.ID},
		model.VendorBill{
			RemainingMoney: &newVBRemainingMoney,
			Status:         status,
		},
	)
	if updateVBResp.Status != common.APIStatus.Ok {
		return updateVBResp
	}
	// build analyze
	if status == enum.VendorBillStatus.PAID {
		go billing.BuildVendorBillAnalyze(vb.VendorBillCode, vb.POCode)
	}
	return updateVBResp
}

// adjustment bill
// AddPaymentItemAB adds a payment item to a payment platform and updates the corresponding adjustment bill.
// It performs the following steps:
// 1. Validates the input data.
// 2. Checks if the payment code and adjustment bill code are provided.
// 3. Retrieves the payment platform based on the payment code.
// 4. Retrieves the adjustment bill based on the adjustment bill code and vendor code.
// 5. Calculates the new remaining money for the adjustment bill after deducting the payment amount.
// 6. Validates that the new remaining money is not less than zero.
// 7. Creates a new payment item and appends it to the payment platform.
// 8. Updates the payment platform with the new payment item.
// 9. Updates the adjustment bill's remaining money and status.
// 10. If the adjustment bill is fully paid, triggers an asynchronous analysis of the adjustment bill.
//
// Parameters:
// - input: A pointer to a PaymentItemPlatform model containing the payment item details.
//
// Returns:
// - A pointer to an APIResponse indicating the result of the operation.
func AddPaymentItemAB(input *model.PaymentItemPlatform) *common.APIResponse {
	// validate input data
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		}
	}

	// validate input PaymentCode
	if len(input.PaymentCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentCode is required",
		}
	}

	// validate input AdjustmentBillCode
	if len(input.AdjustmentBillCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "AdjustmentBillCode is required",
		}
	}

	// check Payment
	paymentQueryCheck := model.PaymentPlatform{PaymentCode: input.PaymentCode}
	paymentResp := GetSingleNewPayment(paymentQueryCheck, model.QueryOption{
		Items: true,
	})
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}

	// parse payment data
	payment := paymentResp.Data.([]*model.PaymentPlatform)[0]
	var (
		price               float64 = 0
		newABRemainingMoney float64 = 0
	)

	// get price
	if input.Amount != nil {
		price = *input.Amount
	}

	// check AdjustmentBill
	abResp := model.AdjustmentBillDB.QueryOne(model.AdjustmentBill{
		AdjustmentBillCode: input.AdjustmentBillCode,
		VendorCode:         payment.PartnerCode,
	})

	// get AB
	if abResp.Status != common.APIStatus.Ok {
		return abResp
	}
	ab := abResp.Data.([]*model.AdjustmentBill)[0]

	// For AB
	if ab.RemainingMoney != nil {
		newABRemainingMoney = *ab.RemainingMoney
	}

	// giảm remaining_money trong AB
	newABRemainingMoney -= price

	// chỉ chấp nhận [payment_item.total_price - ab.RemainingMoney >= ± 0.1]
	// double check
	if newABRemainingMoney < -conf.CURRENCY_ROUND {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "NewRemainingMoney < 0",
			ErrorCode: string(enum.ErrorCodeInvalid.NewRemainingMoneyLTZero),
		}
	}

	payment.FillSuportField()
	// create payment item
	{
		input.InvoiceNumber = ab.InvoiceNumber
		input.AdjustmentBillCode = ab.AdjustmentBillCode
		input.AdjustmentBillID = ab.AdjustmentBillID
		input.TransactionType = enum.Transaction.ADJUSTMENT_BILL
		input.ObjectCode = input.AdjustmentBillCode
		input.ObjectType = enum.PaymentPlatformObjectType.ADJUSMENT_BILL
		input.FillData(model.OptionFillDataPaymentPlatform{
			ExtraData: true,
		})
	}

	payment.Items = append(payment.Items, input)
	// update payments
	resp := updatePayment(*payment)

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	// trả hết thì update status
	status := ab.Status
	if newABRemainingMoney < conf.CURRENCY_ROUND {
		status = enum.AdjustmentBillStatus.PAID
	}

	// update AB
	updateABResp := model.AdjustmentBillDB.UpdateOne(
		model.AdjustmentBill{ID: ab.ID},
		model.AdjustmentBill{
			RemainingMoney: utils.Pointer.WithFloat64(newABRemainingMoney),
			Status:         status,
		},
	)

	if updateABResp.Status != common.APIStatus.Ok {
		return updateABResp
	}

	if status == enum.AdjustmentBillStatus.PAID {
		go billing.BuildABAnalyze(ab.AdjustmentBillCode)
	}
	return updateABResp
}

// AddPaymentItemClearingDebt adds a payment item to clear debt based on the provided input.
// It validates the input data and performs necessary checks before adding the payment item.
//
// Parameters:
//   - input: A pointer to model.PaymentItemPlatform containing the payment item details.
//
// Returns:
//   - *common.APIResponse: The API response indicating the result of the operation.
//
// The function performs the following validations:
//   - Checks if the input is nil.
//   - Checks if the PaymentCode is provided.
//   - Checks if the Amount is greater than 0.
//   - Checks if the PaidTime is provided.
//
// Depending on the provided voucher codes (PaymentVoucherCode or ReceiptVoucherCode),
// it validates the respective vouchers and updates the payment item accordingly.
//
// If the new balance after the operation is less than 0, it returns an error response.
//
// The function fills additional data for the payment item and updates the payment with the new item.
func AddPaymentItemClearingDebt(input *model.PaymentItemPlatform) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		}
	}

	if len(input.PaymentCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentCode is required",
		}
	}

	if input.Amount == nil || *input.Amount == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Total price must be greater than 0",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	if input.PaidTime == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Paid time is required",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	paymentResp := GetSingleNewPayment(
		model.PaymentPlatform{PaymentCode: input.PaymentCode},
		model.QueryOption{
			Items: true,
		})
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}
	payment := paymentResp.Data.([]*model.PaymentPlatform)[0]

	var price float64
	if input.Amount != nil {
		price = *input.Amount
	}

	switch {
	case len(input.PaymentVoucherCode) > 0:
		paymentChecklQuery := model.PaymentPlatform{
			PaymentCode: input.PaymentVoucherCode,
		}
		// validate respective payment voucher
		paymentVoucherResp := GetSingleNewPayment(paymentChecklQuery, model.QueryOption{Items: true})
		if paymentVoucherResp.Status != common.APIStatus.Ok {
			return paymentVoucherResp
		}
		input.ObjectType = enum.PaymentPlatformObjectType.PAYMENT
		input.ObjectCode = input.PaymentVoucherCode
		input.TransactionType = enum.Transaction.PAYMENT_VOUCHER

	case len(input.ReceiptVoucherCode) > 0:
		paymentChecklQuery := model.PaymentPlatform{
			PaymentCode: input.ReceiptVoucherCode,
		}
		// validate respective receipt voucher
		receiptVoucherResp := GetSingleNewPayment(paymentChecklQuery, model.QueryOption{Items: true})
		if receiptVoucherResp.Status != common.APIStatus.Ok {
			return receiptVoucherResp
		}

		input.ObjectType = enum.PaymentPlatformObjectType.PAYMENT
		input.ObjectCode = input.ReceiptVoucherCode
		input.TransactionType = enum.Transaction.RECEIPT_VOUCHER

		// calculate remain balance of payment voucher
		var newBalance float64
		newBalance = payment.Balance
		newBalance -= price

		if newBalance < 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "New balance is less than 0",
				ErrorCode: string(enum.ErrorCodeInvalid.NewBalanceLTZero),
			}
		}
		if newBalance < conf.CURRENCY_ROUND {
			input.Amount = utils.Pointer.WithFloat64(payment.Balance)
		}

		// updatePaymentResp := model.PaymentDB.UpdateOne(
		// 	model.Payment{ID: payment.ID},
		// 	model.Payment{Balance: &newBalance},
		// )
		// if updatePaymentResp.Status != common.APIStatus.Ok {
		// 	return updatePaymentResp
		// }

	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid payment or receipt voucher code",
		}
	}
	input.FillData(model.OptionFillDataPaymentPlatform{ExtraData: true})
	payment.Items = append(payment.Items, input)
	return updatePayment(*payment)
}

// CancelPaymentItem cancels a payment item based on its code. It first validates the input code,
// then retrieves the payment item details. Depending on the type of the payment item, it calls
// the appropriate cancellation function.
//
// Parameters:
// - paymentItemCode: A string representing the code of the payment item to be canceled.
//
// Returns:
// - *common.APIResponse: The response object containing the status and message of the cancellation process.
//
// Possible response statuses:
// - common.APIStatus.Invalid: If the paymentItemCode is empty or the payment item type is invalid.
// - common.APIStatus.Ok: If the payment item is successfully canceled.
// - Other statuses as returned by the GetSingleNewPaymentItem function or the specific cancellation functions.
func CancelPaymentItem(paymentItemCode string) *common.APIResponse {
	if len(paymentItemCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItemCode is required",
		}
	}

	// get payment item
	paymentItemResp := GetSingleNewPaymentItem(model.PaymentItemPlatform{PaymentItemCode: paymentItemCode}, model.QueryOption{})
	if paymentItemResp.Status != common.APIStatus.Ok {
		return paymentItemResp
	}
	paymentItem := paymentItemResp.Data.([]*model.PaymentItemPlatform)[0]

	switch {
	case len(paymentItem.VendorBillCode) > 0:
		return CancelPaymentItemVB(paymentItem)
	case len(paymentItem.AdjustmentBillCode) > 0:
		return CancelPaymentItemAB(paymentItem)
	case len(paymentItem.PaymentVoucherCode) > 0 || len(paymentItem.ReceiptVoucherCode) > 0:
		return CancelPaymentItemClearingDebt(paymentItem)
	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid payement item type",
		}
	}
}

// CancelPaymentItem
// CancelPaymentItemVB cancels a payment item for a vendor bill.
// It performs the following steps:
// 1. Validates the input payment item.
// 2. Checks if the vendor bill exists.
// 3. Calculates the new remaining money for the vendor bill.
// 4. Retrieves the payment associated with the payment item.
// 5. Finds the old payment item in the payment.
// 6. Updates the remaining money for the vendor bill.
// 7. Deletes the payment item.
// 8. Updates the vendor bill status if necessary.
// 9. Optionally triggers a rollback analysis for the vendor bill.
// 10. Updates the payment.
//
// Parameters:
// - paymentItem: A pointer to the PaymentItemPlatform model.
//
// Returns:
// - A pointer to an APIResponse indicating the result of the operation.
func CancelPaymentItemVB(paymentItem *model.PaymentItemPlatform) *common.APIResponse {

	// check PaymentItem
	if paymentItem == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItem is required",
		}
	}

	// check PaymentItemCode
	if len(paymentItem.PaymentItemCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItemCode is required",
		}
	}

	// check VendorBillCode
	if len(paymentItem.VendorBillCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "VendorBillCode is required",
		}
	}

	// check VendorBill
	vbResp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: paymentItem.VendorBillCode})
	if vbResp.Status != common.APIStatus.Ok {
		return vbResp
	}
	vb := vbResp.Data.([]*model.VendorBill)[0]

	// calc
	var price float64 = 0
	var newRemainingMoney float64 = 0
	// var newBalance float64 = 0
	if paymentItem.Amount != nil {
		price = *paymentItem.Amount
	}

	// For payment
	paymentResp := GetSingleNewPayment(model.PaymentPlatform{PaymentCode: paymentItem.PaymentCode},
		model.QueryOption{Items: true})
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}
	payment := paymentResp.Data.([]*model.PaymentPlatform)[0]

	var oldPaymentItem *model.PaymentItemPlatform
	for i := range payment.Items {
		item := payment.Items[i]
		if item.DecodeVendorBillCode() == paymentItem.VendorBillCode {
			oldPaymentItem = item
			break
		}

	}
	if oldPaymentItem == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Old payment item not found",
		}

	}

	// if payment.Balance != nil {
	// 	newBalance = *payment.Balance
	// }
	// newBalance += price

	// For VB
	if vb.RemainingMoney != nil {
		newRemainingMoney = *vb.RemainingMoney
	}
	newRemainingMoney += price
	// double check
	if newRemainingMoney > *vb.TotalPrice {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "NewRemainingMoney > TotalPrice",
			ErrorCode: string(enum.ErrorCodeInvalid.NewRemainingMoneyGTTotalPrice),
		}
	}

	payment.FillSuportField()
	// remove last index of po code of payment
	payment.PORelatedCodes = utils.RemoveLastIndexElement(paymentItem.POCode, payment.PORelatedCodes)
	if paymentResp.Status != common.APIStatus.Ok {
		return paymentResp
	}
	payment.FillData(model.OptionFillDataPaymentPlatform{
		ExtraData: true,
	})
	payment.Items = nil

	deleteRs := deletePaymentItem(model.PaymentItemPlatform{
		PaymentItemCode: paymentItem.PaymentItemCode,
	})
	if deleteRs.Status != common.APIStatus.Ok {
		return deleteRs
	}
	// update VendorBill
	status := vb.Status
	if status == enum.VendorBillStatus.PAID && newRemainingMoney > 0 {
		status = enum.VendorBillStatus.WAIT_TO_PAID
	}
	// update VB
	updateVBResp := model.VendorBillDB.UpdateOne(
		model.VendorBill{ID: vb.ID},
		model.VendorBill{
			RemainingMoney: &newRemainingMoney,
			Status:         status,
		},
	)
	if updateVBResp.Status != common.APIStatus.Ok {
		return updateVBResp
	}

	// delete PaymentItem
	if status != enum.VendorBillStatus.PAID {
		// go billing.BuildRollbackVBAnalyze(paymentItem.VendorBillCode)
	}

	resp := updatePayment(*payment)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	return deleteRs
}

// CancelPaymentItem AdjustmentBill
func CancelPaymentItemAB(paymentItem *model.PaymentItemPlatform) *common.APIResponse {
	// check PaymentItem
	if paymentItem == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItem is required",
		}
	}

	// check PaymentItemCode
	if len(paymentItem.PaymentItemCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItemCode is required",
		}
	}

	// check PaymentCode
	if len(paymentItem.PaymentCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentCode is required",
		}
	}

	// check AdjustmentBillCode
	if len(paymentItem.AdjustmentBillCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "AdjustmentBillCode is required",
		}
	}

	// check adjustmentBill
	abResp := model.AdjustmentBillDB.QueryOne(model.AdjustmentBill{
		AdjustmentBillCode: paymentItem.AdjustmentBillCode,
		Status:             enum.AdjustmentBillStatus.PAID,
		AdjustType:         enum.AdjustBill.INCREASE,
	})
	if abResp.Status != common.APIStatus.Ok {
		return abResp
	}
	ab := abResp.Data.([]*model.AdjustmentBill)[0]

	// calc
	var (
		price             float64 = 0
		newRemainingMoney float64 = 0
	)

	if paymentItem.Amount != nil {
		price = *paymentItem.Amount
	}

	// For payment
	paymentRep := GetSingleNewPayment(
		model.PaymentPlatform{PaymentCode: paymentItem.PaymentCode},
		model.QueryOption{Items: true})

	if paymentRep.Status != common.APIStatus.Ok {
		return paymentRep
	}
	payment := paymentRep.Data.([]*model.PaymentPlatform)[0]
	_ = payment
	// For AB
	if ab.RemainingMoney != nil {
		newRemainingMoney = *ab.RemainingMoney
	}
	newRemainingMoney += price
	// double check
	if newRemainingMoney > *ab.TotalAmountAfterVAT {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "NewRemainingMoney > TotalPriceAfterVAT",
			ErrorCode: string(enum.ErrorCodeInvalid.NewRemainingMoneyGTTotalPrice),
		}
	}

	// delete PaymentItem
	deleteItemResp := deletePaymentItem(model.PaymentItemPlatform{PaymentItemCode: paymentItem.PaymentItemCode})

	if deleteItemResp.Status != common.APIStatus.Ok {
		return deleteItemResp
	}

	// update AB
	status := ab.Status
	if status == enum.AdjustmentBillStatus.PAID && newRemainingMoney > 0 {
		status = enum.AdjustmentBillStatus.WAIT_TO_PAID
	}

	// update AdjustmentBill
	rsUpdate := model.AdjustmentBillDB.UpdateOne(
		model.AdjustmentBill{
			AdjustmentBillCode: ab.AdjustmentBillCode,
		},
		model.AdjustmentBill{
			Status:         status,
			RemainingMoney: &newRemainingMoney,
		},
	)

	// build analyze if update success and. status != PAID
	if rsUpdate.Status == common.APIStatus.Ok && status != enum.AdjustmentBillStatus.PAID {
		go billing.BuildRollbackABAnalyze(paymentItem.AdjustmentBillCode)
	}

	return rsUpdate
}

// CancelPaymentItemClearingDebt cancels the clearing debt of a payment item.
// It performs the following steps:
// 1. Checks if the corresponding payment voucher exists.
// 2. Checks if the receipt voucher exists in the corresponding payment voucher.
// 3. Deletes the payment item.
//
// Parameters:
// - paymentItem: A pointer to the PaymentItemPlatform model representing the payment item to be canceled.
//
// Returns:
// - *common.APIResponse: The API response indicating the result of the operation.
func CancelPaymentItemClearingDebt(paymentItem *model.PaymentItemPlatform) *common.APIResponse {
	// kiểm tra xem phiếu chi tương ứng có tồn tại hay không
	paymentVoucherResp := GetSingleNewPayment(
		model.PaymentPlatform{PaymentCode: paymentItem.PaymentVoucherCode},
		model.QueryOption{Items: true})
	if paymentVoucherResp.Status != common.APIStatus.Ok {
		return paymentVoucherResp
	}

	paymentVoucher := paymentVoucherResp.Data.([]*model.PaymentPlatform)[0]
	_ = paymentVoucher
	filter := model.PaymentItemPlatform{
		ReceiptVoucherCode: paymentItem.PaymentCode,
		PaymentCode:        paymentItem.PaymentVoucherCode,
	}

	// kiểm tra phiếu thu có tồn tại trong phiếu chi tương ứng không
	receiptVoucherItemResp := GetSingleNewPaymentItem(
		filter, model.QueryOption{})
	if receiptVoucherItemResp.Status != common.APIStatus.Ok {
		return receiptVoucherItemResp
	}

	// trả dư nợ về cho phiếu chi đó
	// if paymentVoucher.Balance != nil && paymentItem.TotalPrice != nil {
	// 	*paymentVoucher.Balance += *paymentItem.TotalPrice
	// }
	// updatePaymentResp := model.PaymentDB.UpdateOne(model.Payment{PaymentCode: paymentItem.PaymentVoucherCode},
	// 	model.Payment{Balance: paymentVoucher.Balance})
	// if updatePaymentResp.Status != common.APIStatus.Ok {
	// 	return updatePaymentResp
	// }

	return deletePaymentItem(filter)
	// // xoá phiếu thu trong phiếu chi tương ứng
	// cancelItemResp := model.PaymentItemDB.Delete(model.PaymentItem{
	// 	PaymentCode:        paymentItem.PaymentVoucherCode,
	// 	ReceiptVoucherCode: paymentItem.PaymentCode,
	// })
	// if cancelItemResp.Status != common.APIStatus.Ok {
	// 	return cancelItemResp
	// }

	// // xoá phiếu chi trong phiếu thu tương ứng
	// cancelItemResp = model.PaymentItemDB.Delete(model.PaymentItem{
	// 	PaymentCode:        paymentItem.PaymentCode,
	// 	PaymentVoucherCode: paymentItem.PaymentVoucherCode,
	// })
	// if cancelItemResp.Status != common.APIStatus.Ok {
	// 	return cancelItemResp
	// }

	// return cancelItemResp
}

// DeletePaymentItem
func DeletePaymentItem(paymentItemCode string) *common.APIResponse {
	// if len(paymentItemCode) == 0 {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Invalid,
	// 		Message: "PaymentItemCode is required",
	// 	}
	// }

	// paymentItemResp := model.PaymentItemDB.QueryOne(model.PaymentItem{PaymentItemCode: paymentItemCode})
	// if paymentItemResp.Status != common.APIStatus.Ok {
	// 	return paymentItemResp
	// }
	// paymentItem := paymentItemResp.Data.([]*model.PaymentItem)[0]

	// paymentResp := model.PaymentDB.QueryOne(model.Payment{PaymentCode: paymentItem.PaymentCode})
	// if paymentResp.Status != common.APIStatus.Ok {
	// 	return paymentResp
	// }
	// payment := paymentResp.Data.([]*model.Payment)[0]
	// if paymentItem.TotalPrice != nil {
	// 	if payment.Balance != nil {
	// 		*payment.Balance += *paymentItem.TotalPrice
	// 	} else {
	// 		payment.Balance = paymentItem.TotalPrice
	// 	}
	// }
	// updatePaymentResp := model.PaymentDB.UpdateOne(model.Payment{
	// 	ID: payment.ID,
	// }, model.Payment{
	// 	Balance: payment.Balance,
	// })
	// if updatePaymentResp.Status != common.APIStatus.Ok {
	// 	return updatePaymentResp
	// }

	// // delete PaymentItem
	// return model.PaymentItemDB.Delete(model.PaymentItem{
	// 	ID: paymentItem.ID,
	// })
	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "This api is deprecated",
	}
}

func deletePaymentItem(input model.PaymentItemPlatform) *common.APIResponse {
	if input.PaymentItemCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PaymentItemCode is required",
		}
	}
	paymentItemResp := GetSingleNewPaymentItem(
		input,
		model.QueryOption{})
	if paymentItemResp.Status != common.APIStatus.Ok {
		return paymentItemResp
	}
	paymentItem := paymentItemResp.Data.([]*model.PaymentItemPlatform)[0]

	return payment_platform.DeletePaymentItem(client.APIOption{
		SaveLog: utils.Pointer.WithBool(true),
		Params: map[string]string{
			"paymentCode":     paymentItem.PaymentCode,
			"paymentItemCode": paymentItem.PaymentItemCode,
		},
	})
}
