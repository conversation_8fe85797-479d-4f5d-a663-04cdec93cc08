package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Address struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// unique
	AddressID   int64  `json:"addressId" bson:"address_id,omitempty"`
	AddressCode string `json:"addressCode" bson:"address_code,omitempty"`

	VendorCode       string                `json:"vendorCode" bson:"vendor_code,omitempty"`
	PurposeCode      enum.PurposeCodeValue `json:"purposeCode" bson:"purpose_code,omitempty"`
	ProvinceCode     string                `json:"provinceCode,omitempty" bson:"province_code,omitempty"`
	DistrictCode     string                `json:"districtCode,omitempty" bson:"district_code,omitempty"`
	WardCode         string                `json:"wardCode,omitempty" bson:"ward_code,omitempty"`
	Street           string                `json:"street,omitempty" bson:"street,omitempty"`
	IsDefaultAddress *bool                 `json:"isDefaultAddress,omitempty" bson:"is_default_address,omitempty"`
	WarehouseCode    *string               `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []*bson.M `json:"-" bson:"$or,omitempty"`
}

// AddressDB ...
var AddressDB = &db.Instance{
	ColName:        "address",
	TemplateObject: &Address{},
}

// InitAddressMappingModel ...
func InitAddressMappingModel(s *mongo.Database) {
	AddressDB.ApplyDatabase(s)
}
