package main

import (
	"os"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/action/tools"
	"gitlab.com/thuocsi.vn/seller/purchasing/api"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/core"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/delivery"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/integration"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/monitoring"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/sso"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/workflow_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/zuellig"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/mongo"
)

type infoData struct {
	Service     string    `json:"service"`
	Environment string    `json:"environment"`
	Version     string    `json:"version"`
	StartTime   time.Time `json:"startTime"`
}

var globalInfo *infoData

func info(req sdk.APIRequest, res sdk.APIResponder) error {
	return res.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []interface{}{globalInfo},
	})
}

// onDBConnected function that handle on connected to DB event
func onDBConnected(s *mongo.Database) error {
	// Schedule various processes
	model.CreateConfigSchedule(s, map[string]schedule.Process{
		"MAIL_PAYMENT_TO_VENDOR": action.MailPayment,
	})

	// s.Collection("flag").Drop(context.TODO())
	// s.Collection("quotation_tracking").Indexes().DropOne(context.TODO(), "sku_1_warehouseCode_1_version_1")

	{
		// po
		model.InitPurchaseOrderItemModel(s)
		model.InitPurchaseOrderModel(s)
		// callback
		model.InitInboundCallbackModel(s)
		model.InitInboundCallbackStoreModel(s)
	}

	{
		// vendor bill
		model.InitVendorBillModel(s)
		model.InitVendorBillItemModel(s)

		// debt
		model.InitDebtModel(s)

		model.InitNewPaymentModel(s)
	}

	{
		// quotation
		model.InitQuotationModel(s)
		model.InitQuotationProductModel(s)
		model.InitQuotationProductVendorModel(s)
		model.InitQuotationPOHistoryModel(s)
		model.InitQuotationTrackingModel(s)
		model.InitQuotationExportModel(s)

		// manage quota
		model.InitQuotaManageModel(s)

		// bidding
		model.InitBiddingModel(s)
		model.InitBiddingHistoryModel(s)

		// bidding rate
		model.InitBiddingRateModel(s)
	}
	{
		// pushing group
		model.InitAdminPushingGroupModel(s)
		model.InitAdminPushingSettingModel(s)

		model.InitInitialStockDraftModel(s)

		model.InitInboundRequestItemModel(s)
	}

	// batch search
	// model.InitBatchUpdateDB(s)
	// cache.InitFlagModel(s)

	model.InitIDGenModel(s)

	model.InitSkuConfigModel(s)

	model.InitProductMappingModel(s)

	model.InitSettingModel(s)

	model.InitWishlistModel(s)

	model.InitLockActionModel(s)

	model.InitAdjustmentBillModel(s)

	model.InitPICMappingModel(s)

	model.InitSkipSOModel(s)

	model.InitContractPriceModel(s)

	model.InitAddressMappingModel(s)

	model.InitContactMappingModel(s)

	model.InitSkuTradingPriceDB(s)

	model.InitReasonTypeMappingModel(s)

	model.InitReasonMappingModel(s)

	model.InitBankingPaymentOrderModel(s)
	model.InitBankingPaymentOrderItemModel(s)

	model.InitSKUItemEstimatedDemandModel(s)

	return nil
}

func onReplicaDBConnected(s *mongo.Database) error {
	{ // PO
		model.InitPurchaseOrderReplicaModel(s)
	}

	{ // VB
		model.InitVendorBillReplicaModel(s)
	}

	{
		// sku config
		model.InitSkuConfigReadModel(s)
	}

	return nil
}

// onCacheConnected is func handle event connected to db cache
// func onCacheConnected(s *mongo.Database) error {
// 	// model.InitPurchasingCacheModel(model.PurchasingCacheDB, s)
// 	return nil
// }

func onDBJobConnected(s *mongo.Database) error {
	// model.InitCallbackStockJob(s, action.ProcessingCallbackStock)

	model.InitCrawlOrderItemJob(s, action.ProcessingCrawlOrderItem)

	model.InitImportQuotationJob(s, action.ProcessingImportQuotation)

	model.InitUpdateESJob(s)

	// promotion
	model.InitVendorPromotionJob(s)

	model.InitAssignPriceAfterRebate(s, action.ProcessAssignPriceAfterRebate)

	return nil
}

// onDBLogConnected ...
func onDBLogConnected(s *mongo.Database) error {
	// marketplace
	marketplace.InitProductV2Client(s)
	marketplace.InitOrderV2Client(s)

	// core
	core.InitCoreV1Client(s)
	core.InitMasterDataClient(s)
	core.InitAccountClient(s)

	// integration
	integration.InitNotification(s)
	integration.InitProductTextSearch(s)
	integration.InitMailProcessorClient(s)

	// seller
	seller.InitSellerCore(s)
	seller.InitSellerNotification(s)
	seller.InitSellerPromotion(s)
	seller.InitWisClient(s)
	seller.InitWisClient(s)
	seller.InitPurchasingWorkerClient(s)

	// warehouse
	warehouse.InitWarehouseCore(s)
	warehouse.InitWarehouseInbound(s)
	warehouse.InitWarehouseInventory(s)
	warehouse.InitTransportingClient(s)

	// SFTP
	if os.Getenv("env") == "prd" {
		zuellig.InitSFTPZuellig()
	}
	// zuellig.InitSFTPZuellig()

	// error
	model.InitErrorLogModel(s)
	model.InitLogQuotationExport(s)

	// payment
	payment.InitPaymentCore(s)

	// delivery
	delivery.InitTransportingClient(s)

	// sso
	sso.InitEntityClient(s)

	// monitoring - realtime supply
	monitoring.InitRealtimeSupply(s)

	action.GetValidWarehouses()

	payment_platform.InitPaymentPlatformClient(s)
	workflow_platform.InitWorkflowClient(s)
	return nil
}

func main() {
	globalInfo = &infoData{
		Service:     "Product",
		Version:     os.Getenv("version"),
		Environment: conf.Config.Env,
		StartTime:   time.Now(),
	}

	// setup new app
	app := sdk.NewApp("Product service")

	{
		// DB main
		dbConf := conf.Config.MainDBConf
		app.SetupDBClient(db.Configuration{
			Address:     dbConf.Addr,
			Username:    dbConf.User,
			Password:    dbConf.Password,
			DBName:      dbConf.DatabaseName,
			AuthDB:      dbConf.Auth,
			DoWriteTest: !dbConf.Readonly,
		}, onDBConnected)

		// DB replica
		app.SetupDBClient(db.Configuration{
			Address:            dbConf.Addr,
			Username:           dbConf.User,
			Password:           dbConf.Password,
			DBName:             dbConf.DatabaseName,
			AuthDB:             dbConf.Auth,
			DoWriteTest:        false,
			SecondaryPreferred: true,
		}, onReplicaDBConnected)
	}

	{
		// DB queue
		jobDBConf := conf.Config.JobDBConf
		app.SetupDBClient(db.Configuration{
			Address:     jobDBConf.Addr,
			Username:    jobDBConf.User,
			Password:    jobDBConf.Password,
			DBName:      jobDBConf.DatabaseName,
			AuthDB:      jobDBConf.Auth,
			DoWriteTest: !jobDBConf.Readonly,
		}, onDBJobConnected)

	}

	{
		// DB log
		logDBConf := conf.Config.LogDBConf
		app.SetupDBClient(db.Configuration{
			Address:     logDBConf.Addr,
			Username:    logDBConf.User,
			Password:    logDBConf.Password,
			DBName:      logDBConf.DatabaseName,
			AuthDB:      logDBConf.Auth,
			DoWriteTest: !logDBConf.Readonly,
		}, onDBLogConnected)
	}

	{
		// // setup second database
		// app.SetupDBClient(db.Configuration{
		// 	Address:  configMap["cacheAddr"],
		// 	Username: configMap["cacheUser"],
		// 	Password: configMap["cachePassword"],
		// 	DBName:   conf.Config.CacheDBName,
		// 	AuthDB:   "admin",
		// }, onCacheConnected)
	}

	// setup API Server
	protocol := os.Getenv("protocol")
	if protocol == "" {
		protocol = "THRIFT"
	}

	server, _ := app.SetupAPIServer(protocol)

	_ = server.SetHandler(common.APIMethod.GET, "/api-info", info)

	// ======================================== PO ========================================

	// purchase order

	_ = server.SetHandler(common.APIMethod.GET, "/purchase-order", api.GetPOList)
	_ = server.SetHandler(common.APIMethod.GET, "/vendor/purchase-order", api.GetVendorPOList)
	_ = server.SetHandler(common.APIMethod.PUT, "/vendor/purchase-order", api.VendorUpdatePO)
	_ = server.SetHandler(common.APIMethod.PUT, "/vendor/purchase-order/info-items", api.VendorUpdatePurchaseOrder) // update for po items
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/:sellerCode/:purchaserCode/purchase-order", api.GetSellerPOList)
	_ = server.SetHandler(common.APIMethod.POST, "/purchaser/:sellerCode/:purchaserCode/purchase-order", api.CreatePurchaseOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchaser/:sellerCode/:purchaserCode/purchase-order", api.UpdatePurchaseOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchaser/:sellerCode/:purchaserCode/purchase-order/info", api.UpdatePurchaseOrderInfo)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchaser/:sellerCode/:purchaserCode/purchase-order/switch-status", api.SwitchStatusPO)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchase-order/delete-promotion", api.DeletePromotionPO)
	_ = server.SetHandler(common.APIMethod.POST, "/purchase-order/notify", api.NotifyVendorPO)
	_ = server.SetHandler(common.APIMethod.POST, "/purchase-order/clone", api.ClonePurchaserOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchase-order/tag", api.UpdatePOTag)

	_ = server.SetHandler(common.APIMethod.PUT, "/purchaser/purchase-order/append-order-item", api.AppendPurchaseOrderItem)

	// purchase order item
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/:sellerCode/:purchaserCode/purchase-order-item", api.GetSellerPurchaseOrderItemList)
	_ = server.SetHandler(common.APIMethod.GET, "/purchase-order-item", api.GetPurchaseOrderItemList)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchase-order-item", api.UpdatePurchaseOrderItem)
	_ = server.SetHandler(common.APIMethod.GET, "/vendor/purchase-order-item", api.GetVendorPurchaseOrderItemList)

	// inbound callback
	_ = server.SetHandler(common.APIMethod.POST, "/callback/receipt", api.WarehouseCallbackLotDate)
	// _ = server.SetHandler(common.APIMethod.POST, "/callback/stock", api.WarehouseCallbackStock)
	// inbound-callback-store
	_ = server.SetHandler(common.APIMethod.GET, "/inbound-callback-store", api.GetInboundCallbackStore)

	// ======================================== inbound request ========================================

	_ = server.SetHandler(common.APIMethod.GET, "/:sellerCode/inbound-request", api.GetInboundRequestList)
	_ = server.SetHandler(common.APIMethod.POST, "/:sellerCode/inbound-request", api.CreateInboundRequest)
	_ = server.SetHandler(common.APIMethod.PUT, "/:sellerCode/inbound-request", api.UpdateInboundRequest)
	_ = server.SetHandler(common.APIMethod.PUT, "/:sellerCode/inbound-request/clone", api.CloneInboundRequest)
	_ = server.SetHandler(common.APIMethod.PUT, "/:sellerCode/inbound-request/switch-status", api.SwitchStatusInboundRequest)
	_ = server.SetHandler(common.APIMethod.GET, "/:sellerCode/inbound-request-item", api.GetInboundRequestItemList)
	_ = server.SetHandler(common.APIMethod.PUT, "/inbound-request/refresh-contract-price", api.RefreshInboundRequestContractPrice)
	_ = server.SetHandler(common.APIMethod.GET, "/inbound-request-item/contract-price", api.GetInboundRequestItemContractPrice)
	_ = server.SetHandler(common.APIMethod.POST, "/inbound-request/deal", api.GetInboundRequestDeal)
	_ = server.SetHandler(common.APIMethod.PUT, "/inbound-request-item/deal", api.UpdateInboundRequestItemDeal)
	_ = server.SetHandler(common.APIMethod.POST, "/inbound-request/purchase-order", api.CreateInboundRequestPurchaseOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/tool/inbound-request", api.ToolInboundRequest)
	_ = server.SetHandler(common.APIMethod.POST, "/inbound-request-item/validate-import", api.InboundRequestItemValidateImport)
	_ = server.SetHandler(common.APIMethod.POST, "/inbound-request-item/import", api.InboundRequestItemImport)
	_ = server.SetHandler(common.APIMethod.PUT, "/inbound-request-item/update-manual", api.InboundRequestItemManualUpdate)

	{ // ======================================== adjustment bill =======================================
		_ = server.SetHandler(common.APIMethod.GET, "/adjustment-bill", api.GetSingleAdjustmentBill)
		_ = server.SetHandler(common.APIMethod.GET, "/adjustment-bill/list", api.GetListAdjustmentBill)
		_ = server.SetHandler(common.APIMethod.GET, "/adjustment-bill-item/list", api.GetListAdjustmentBillItem)
		_ = server.SetHandler(common.APIMethod.POST, "/adjustment-bill", api.CreateAdjustmentBill)
		_ = server.SetHandler(common.APIMethod.PUT, "/adjustment-bill", api.UpdateAdjustmentBill)
		_ = server.SetHandler(common.APIMethod.PUT, "/adjustment-bill/switch-status", api.SwitchStatusAdjustmentBill)
	}

	// ======================================== initial stock draft ========================================

	_ = server.SetHandler(common.APIMethod.GET, "/:sellerCode/initial-stock-draft", api.GetInitialStockDraftList)
	_ = server.SetHandler(common.APIMethod.POST, "/:sellerCode/initial-stock-draft", api.CreateInitialStockDraft)
	_ = server.SetHandler(common.APIMethod.PUT, "/:sellerCode/initial-stock-draft", api.UpdateInitialStockDraft)

	// ======================================== QUOTATION ========================================
	// quotation hedging
	_ = server.SetHandler(common.APIMethod.POST, "/seller/quotation-hedging", api.CreateQuotationHedging)
	_ = server.SetHandler(common.APIMethod.GET, "/seller/quotation-hedging", api.GetQuotationHedgingByVenodor)
	_ = server.SetHandler(common.APIMethod.DELETE, "/seller/quotation-hedging", api.DeleteQuotationHedging)
	// quotation export
	_ = server.SetHandler(common.APIMethod.GET, "/seller/:sellerCode/quotation/export", api.GetQuotationExport)
	_ = server.SetHandler(common.APIMethod.GET, "/seller/:sellerCode/quotation/export-draft", api.GetQuotationExportDraft)
	// quotation
	_ = server.SetHandler(common.APIMethod.POST, "/quotation/search", api.SearchQuotation)
	// seller
	_ = server.SetHandler(common.APIMethod.POST, "/seller/:sellerCode/quotation/search", api.GetSellerQuotation)
	_ = server.SetHandler(common.APIMethod.POST, "/seller/:sellerCode/quotation", api.SellerCreateQuotations)
	_ = server.SetHandler(common.APIMethod.PUT, "/seller/:sellerCode/quotation", api.SellerUpdateQuotation)
	_ = server.SetHandler(common.APIMethod.DELETE, "/seller/:sellerCode/quotation", api.DeleteAllQuotations)

	// vendor
	_ = server.SetHandler(common.APIMethod.POST, "/vendor/quotation/search", api.GetVendorQuotation)

	// po of quotation
	_ = server.SetHandler(common.APIMethod.POST, "/vendor/quotation/purchase-order", api.VendorCreatePOFromV2Quotation)                     // create from supplier
	_ = server.SetHandler(common.APIMethod.POST, "/purchaser/:sellerCode/:purchaserCode/quotation/purchase-order", api.CreatePOFromBidding) // create from internal
	// quotation po history
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/:sellerCode/:purchaserCode/quotation/purchase-order/history", api.GetSellerQuotationPOHistory)

	// quotation product
	_ = server.SetHandler(common.APIMethod.POST, "/quotation-product/search", api.GetQuotationProduct)
	_ = server.SetHandler(common.APIMethod.DELETE, "/quotation-product", api.DeleteQuotationProduct)
	// quotation product vendor
	_ = server.SetHandler(common.APIMethod.POST, "/quotation-product-vendor/search", api.GetQuotationProductVendor)

	// wishlist for vendor
	_ = server.SetHandler(common.APIMethod.POST, "/wishlist", api.CreateWishlist)
	_ = server.SetHandler(common.APIMethod.GET, "/wishlist", api.GetWishList)
	_ = server.SetHandler(common.APIMethod.PUT, "/wishlist", api.DeleteWishlist)
	// ================================== TOOLS WISHLIST ========================================
	_ = server.SetHandler(common.APIMethod.PUT, "/tool/delete-wishlist-vendor", api.DeleteWishlistVendor)
	_ = server.SetHandler(common.APIMethod.PUT, "/tool/insert-wishlist-vendor", api.InsertWishlistVendor)

	// bidding
	// seller
	_ = server.SetHandler(common.APIMethod.GET, "/seller/:sellerCode/bidding", api.GetSellerBidding)
	_ = server.SetHandler(common.APIMethod.PUT, "/seller/:sellerCode/bidding", api.SellerUpdateBidding)
	// vendor
	_ = server.SetHandler(common.APIMethod.GET, "/vendor/bidding", api.GetVendorBidding)
	_ = server.SetHandler(common.APIMethod.POST, "/vendor/bidding", api.BiddingQuotation)
	_ = server.SetHandler(common.APIMethod.GET, "/vendor/bidding-history", api.GetVendorBiddingHistory)
	_ = server.SetHandler(common.APIMethod.GET, "/bidding-history", api.GetVendorBiddingHistory)

	// ======================================== BILL ========================================

	// vendor bill
	_ = server.SetHandler(common.APIMethod.GET, "/vendor-bill", api.GetVendorBillList)
	_ = server.SetHandler(common.APIMethod.POST, "/vendor-bill", api.CreateVendorBill)
	_ = server.SetHandler(common.APIMethod.PUT, "/vendor-bill", api.UpdateVendorBill)
	_ = server.SetHandler(common.APIMethod.PUT, "/vendor-bill/info", api.UpdateVendorBillInfo)
	_ = server.SetHandler(common.APIMethod.POST, "/vendor-bill/clone", api.CloneVendorBill)
	_ = server.SetHandler(common.APIMethod.PUT, "/vendor-bill/switch-status", api.SwitchStatusVendorBill)
	_ = server.SetHandler(common.APIMethod.GET, "/vendor-bill-item", api.GetVendorBillItemList)
	_ = server.SetHandler(common.APIMethod.POST, "/vendor-bill/create-by-po", api.CreateVendorBillByPO)

	// Debt
	_ = server.SetHandler(common.APIMethod.GET, "/debt", api.GetDebt)
	_ = server.SetHandler(common.APIMethod.GET, "/debt-platform", api.GetDebtPlatform)

	{
		//  payment
		server.SetHandler(common.APIMethod.GET, "/payment-reason-option", api.GetPaymentReason)
		server.SetHandler(common.APIMethod.GET, "/payment", api.GetPayment)
		server.SetHandler(common.APIMethod.POST, "/payment", api.CreatePayment)
		server.SetHandler(common.APIMethod.PUT, "/payment", api.UpdatePayment)
		server.SetHandler(common.APIMethod.DELETE, "/payment", api.DeletePayment)
		server.SetHandler(common.APIMethod.PUT, "/payment-info", api.UpdatePaymentInfo)
		// new payment item
		server.SetHandler(common.APIMethod.GET, "/payment-item", api.GetPaymentItem)
		server.SetHandler(common.APIMethod.POST, "/payment-item", api.AddPaymentItem)
		server.SetHandler(common.APIMethod.DELETE, "/payment-item", api.CancelPaymentItem)
		server.SetHandler(common.APIMethod.DELETE, "/force-payment-item", api.DeletePaymentItem)
		server.SetHandler(common.APIMethod.POST, "/payment-item/auto-apply", api.ApplyPaymentVoucherForVB)
	}

	// ======================================== CONFIG ========================================

	// sku config
	_ = server.SetHandler(common.APIMethod.GET, "/sku-config/list", api.GetSkuConfigList)
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/:sellerCode/:purchaserCode/sku-config", api.GetSkuConfig)
	_ = server.SetHandler(common.APIMethod.POST, "/purchase-order/sku-config", api.CreateSkuConfigWithCompletedPO)
	_ = server.SetHandler(common.APIMethod.POST, "/purchaser/:sellerCode/:purchaserCode/sku-config", api.CreateSkuConfig)
	_ = server.SetHandler(common.APIMethod.PUT, "/purchaser/:sellerCode/:purchaserCode/sku-config", api.UpdateSkuConfig)

	// contract price
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/contract-price-item", api.GetContractPriceItemV2)

	// get active contract price and fav vendor by product ID
	_ = server.SetHandler(common.APIMethod.GET, "/purchaser/sku-info", api.GetPurchaseSkuInfo)
	// product mapping
	_ = server.SetHandler(common.APIMethod.GET, "/product-mapping", api.GetProductMapping)
	_ = server.SetHandler(common.APIMethod.PUT, "/product-mapping", api.UpdateProductMapping)
	_ = server.SetHandler(common.APIMethod.DELETE, "/product-mapping", api.DeleteProductMapping)

	// admin-pushing-group
	_ = server.SetHandler(common.APIMethod.GET, "/seller/:sellerCode/admin-pushing-group", api.GetAdminPushingGroup)
	_ = server.SetHandler(common.APIMethod.PUT, "/seller/:sellerCode/admin-pushing-group/confirmed", api.ConfirmedPushingGroup)
	// Setting admin pushing
	_ = server.SetHandler(common.APIMethod.GET, "/seller/:sellerCode/admin-pushing-setting", api.GetAdminPushingSetting)
	_ = server.SetHandler(common.APIMethod.POST, "/seller/:sellerCode/admin-pushing-setting", api.CreateAdminPushingSetting)
	_ = server.SetHandler(common.APIMethod.PUT, "/seller/:sellerCode/admin-pushing-setting", api.UpdateAdminPushingSetting)
	_ = server.SetHandler(common.APIMethod.DELETE, "/seller/:sellerCode/admin-pushing-setting", api.DeleteAdminPushingSetting)

	// setting
	_ = server.SetHandler(common.APIMethod.GET, "/setting", api.GetSetting)
	_ = server.SetHandler(common.APIMethod.POST, "/setting", api.CreateSetting)
	_ = server.SetHandler(common.APIMethod.PUT, "/setting", api.UpdateSetting)

	//=========================== Zuellig ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/zuellig/upload-po/test", api.TestUploadFile)
	_ = server.SetHandler(common.APIMethod.POST, "/zuellig/send-po-file", api.SendPOFile)

	//=========================== PIC ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/pic", api.CreatePIC)
	_ = server.SetHandler(common.APIMethod.PUT, "/pic", api.UpdatePIC)
	_ = server.SetHandler(common.APIMethod.DELETE, "/pic", api.DeletePIC)
	_ = server.SetHandler(common.APIMethod.GET, "/pic/list", api.GetPICList)

	//=========================== ADDRESS ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/address", api.CreateAddress)
	_ = server.SetHandler(common.APIMethod.PUT, "/address", api.UpdateAddress)
	_ = server.SetHandler(common.APIMethod.DELETE, "/address", api.DeleteAddress)
	_ = server.SetHandler(common.APIMethod.GET, "/address", api.GetAddressByCode)
	_ = server.SetHandler(common.APIMethod.GET, "/address/list", api.GetAddressList)
	_ = server.SetHandler(common.APIMethod.GET, "/address/me", api.GetAddressMe)

	//=========================== CONTACT ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/contact", api.CreateContact)
	_ = server.SetHandler(common.APIMethod.PUT, "/contact", api.UpdateContact)
	_ = server.SetHandler(common.APIMethod.DELETE, "/contact", api.DeleteContact)
	_ = server.SetHandler(common.APIMethod.GET, "/contact", api.GetContactByCode)
	_ = server.SetHandler(common.APIMethod.GET, "/contact/list", api.GetContactList)
	_ = server.SetHandler(common.APIMethod.GET, "/contact/me", api.GetContactMe)

	// ==================== Skip Count Order SO =======================
	_ = server.SetHandler(common.APIMethod.GET, "/skip-so/list", api.GetListSkipSO)
	_ = server.SetHandler(common.APIMethod.POST, "/skip-so", api.CreateListSkipSO)
	_ = server.SetHandler(common.APIMethod.DELETE, "/skip-so", api.DeleteSkipSO)
	//=========================== Contract Price ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/contract-price", api.CreateContractPrice)
	_ = server.SetHandler(common.APIMethod.PUT, "/contract-price", api.UpdateContractPrice)
	_ = server.SetHandler(common.APIMethod.PUT, "/contract-price/switch-status", api.SwitchStatusContractPrice)
	_ = server.SetHandler(common.APIMethod.GET, "/contract-price", api.GetSingleContractPrice)
	_ = server.SetHandler(common.APIMethod.GET, "/contract-price/list", api.GetListContractPriceMain)
	_ = server.SetHandler(common.APIMethod.GET, "/contract-price-item/list", api.GetListContractPriceItem)
	_ = server.SetHandler(common.APIMethod.POST, "/contract-price/clone", api.CloneContractPrice)

	_ = server.SetHandler(common.APIMethod.POST, "/contract-price/verify", api.VerifyContractPrice)

	// ================ Sku Trading Price =================
	_ = server.SetHandler(common.APIMethod.GET, "/sku-trading-price", api.GetSkuTradingPrice)
	// ==================== Banking Payment Order =======================
	_ = server.SetHandler(common.APIMethod.GET, "/banking-payment-order", api.GetBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.POST, "/banking-payment-order", api.CreateBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/banking-payment-order", api.UpdateBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.POST, "/banking-payment-order/clone", api.CloneBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/banking-payment-order/switch-status", api.SwitchStatusBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/banking-payment-order/switch-status/confirm", api.ConfirmStatusBankingPaymentOrder)
	_ = server.SetHandler(common.APIMethod.PUT, "/banking-payment-order/switch-status/approve", api.ApproveSwitchStatusBankingPaymentOrder)

	_ = server.SetHandler(common.APIMethod.GET, "/banking-payment-order-item", api.GetBankingPaymentOrderItem)
	_ = server.SetHandler(common.APIMethod.POST, "/banking-payment-order-item/sync", api.SyncBankingPaymentOrderItem)

	// buymed
	{
		_ = server.SetHandler(common.APIMethod.POST, "/buymed/transfer-request", api.CreateTransferRequest)
		_ = server.SetHandler(common.APIMethod.POST, "/buymed/transfer-request/callback", api.CallbackTransferRequest)
	}

	//=========================== REASON TYPE ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/reason-type", api.CreateReasonType)
	_ = server.SetHandler(common.APIMethod.PUT, "/reason-type", api.UpdateReasonType)
	_ = server.SetHandler(common.APIMethod.DELETE, "/reason-type", api.DeleteReasonType)
	_ = server.SetHandler(common.APIMethod.GET, "/reason-type", api.GetReasonTypeByCode)
	_ = server.SetHandler(common.APIMethod.GET, "/reason-type/list", api.GetReasonTypeList)

	//=========================== REASON ==========================
	_ = server.SetHandler(common.APIMethod.POST, "/reason", api.CreateReason)
	_ = server.SetHandler(common.APIMethod.PUT, "/reason", api.UpdateReason)
	_ = server.SetHandler(common.APIMethod.DELETE, "/reason", api.DeleteReason)
	_ = server.SetHandler(common.APIMethod.GET, "/reason", api.GetReasonByCode)
	_ = server.SetHandler(common.APIMethod.GET, "/reason/list", api.GetReasonList)

	// ======================================== MANAGE QUOTA ========================================
	{
		_ = server.SetHandler(common.APIMethod.GET, "/quota", api.GetQuotaList)
		_ = server.SetHandler(common.APIMethod.POST, "/quota", api.CreateQuota)
		_ = server.SetHandler(common.APIMethod.PUT, "/quota", api.UpdateQuota)
		_ = server.SetHandler(common.APIMethod.POST, "/quota/calculate-actual-quantity", api.CalculateActualReceiptQty)
		_ = server.SetHandler(common.APIMethod.DELETE, "/quota", api.DeleteQuota)
	}

	// ======================================== BIDDING RATE ========================================
	{
		_ = server.SetHandler(common.APIMethod.POST, "/bidding-rate/search", api.GetSellerBiddingRate)
		_ = server.SetHandler(common.APIMethod.POST, "/bidding-rate", api.SellerCreateBiddingRates)
		_ = server.SetHandler(common.APIMethod.PUT, "/bidding-rate", api.SellerUpdateBiddingRates)
		_ = server.SetHandler(common.APIMethod.PUT, "/bidding-rate/note", api.PutBiddingRatesNote)
		_ = server.SetHandler(common.APIMethod.DELETE, "/bidding-rate", api.DeleteBiddingRates)
		_ = server.SetHandler(common.APIMethod.GET, "/bidding-rate/recount", api.SellerRecountBiddingRates)

		_ = server.SetHandler(common.APIMethod.GET, "/tool/bidding-rate/update-is-valid-field", api.UpdateIsValidField)

		// migrate data from quotation to bidding rate
		// add line PO
		// _ = server.SetHandler(common.APIMethod.POST, "/migrate/bidding-rate", api.MigrateBiddingRate)
		// _ = server.SetHandler(common.APIMethod.PUT, "/migrate/contract-price", api.MigrateContractPriceItem)
	}

	{
		// ======================================== API sp get VAT ========================================
		_ = server.SetHandler(common.APIMethod.POST, "/master-data/product-vat", tools.GetMasterDataProductVAT)
		_ = server.SetHandler(common.APIMethod.POST, "/tool/add-schedule-config", api.AddScheduleVendorRebate)
	}
	// ================================================ migrate ================================================
	{
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill/fill-name", tools.VendorBillFillInfo)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill/force-update", tools.VBForceUpdate)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/force-update", tools.POForceUpdate)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/delete", tools.MigrateDeletePO)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/fill-bill-status", tools.FillPOBillStatus)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/po-type", tools.MigratePOType)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order-item", tools.MigrateUpdatePOItem)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/fulfillment-percent", tools.MigrateFulfillmentPercentPO)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/purchase-order-item/delete", tools.MigrateDeletPOItem)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/product-mapping/sync", tools.MigrateNameMapping)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/es/remove-product", tools.DeleteESProductCode)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/payment/force", tools.ForceUpdatePayment)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/payment-item/delete", api.DeletePaymentItem)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/inbound-request/delete", tools.DeleteInboundRequest)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/inbound-request/force-update", tools.ForceInboundRequestItem)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-item/fix-data", tools.ForceUpdateVBItem)
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/purchase-order-item/bill-quantity", tools.FillBillQuantity)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/setting", tools.DeleteSetting)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/inbound-ticket/sync-po-to-wms", tools.SyncReceptToWarehouse)
		// _ = server.SetHandler(common.APIMethod.PUT, "/migrate/inbound-ticket/sync-wms-to-po", tools.SyncWarehouseToPO)
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/wms-binh-duon/:db", tools.MigrateBinhDuongWMS)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/admin-pushing-group/force", tools.ForceUpdatePushingGroup)
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/product-mapping-zuellig", tools.MigrateProductMappingForZuellig)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/delete-quotation", tools.DeleteQuoation)
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/promotion-queue", tools.MigratePromotionJobQueue)
		_ = server.SetHandler(common.APIMethod.DELETE, "/migrate/inbound-callback-store", api.DeleteInboundCallbackStore)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchase-order/reset-actual-po", api.ResetActualPO)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill/array", tools.VBUpdateDuetime)
		// add line PO
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/purchase-order-item/add", tools.MigrateUpdatePOItemAdd)
		_ = server.SetHandler(common.APIMethod.POST, "/migrate/purchase-order-item/add2", tools.MigratePOItemAdd)

		_ = server.SetHandler(common.APIMethod.POST, "/migrate/purchase-order-item/update", tools.MigrateUpdatePOItemUpdate)

		// merge seller
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/purchaser/:db", tools.MigratePurchaser)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/po-medxhn-to-medx", tools.MigratePOFromMEDXHNToMEDX2)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/pic", tools.MigratePIC)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/pic-seller", tools.MigratePICSellerCode)
		_ = server.SetHandler(common.APIMethod.PUT, "/revert-migrate/po-medxhn-to-medx", tools.RevertMigratePOFromMEDXHNToMEDX)
		// tools migrate data Duetime VB and PO follow new info paymentTerm vendor
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/sync-vendor-bill-follow-payment-term-vendor", tools.MigrateDuetimeFollowPaymentTermVendor)

		// payment v2
		server.SetHandler(common.APIMethod.PUT, "/migrate/payment-v2", tools.MigratePaymentV2)
		server.SetHandler(common.APIMethod.PUT, "/migrate/payment-po-paid", tools.MigratePaymentPOPaid)
		// update quotation
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/quotation-assignvendortime", tools.ForceQuotationAssignVendorTime)

		// migrate data for new feature
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-address", tools.MigrateAddressWarehouseCode)
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-contact", tools.MigrateContactWarehouseCode)

		// Migrate skuConfig for seller non trading
		server.SetHandler(common.APIMethod.PUT, "/migrate/sku-config-non-trading", tools.MigrateSkuConfigNoneTrading)
		// tools vb
		server.SetHandler(common.APIMethod.PUT, "/tool/recheck-vendor-bill-wrong-quantity", tools.ToolCheckVendorBillWrongQuantity)
		server.SetHandler(common.APIMethod.PUT, "/tool/update-purchaser-order-item", api.ToolUpdatePurchaserOrderItem)

		server.SetHandler(common.APIMethod.PUT, "/tool/migrate-contract-price", api.ToolMigrateContractPrice)

		server.SetHandler(common.APIMethod.GET, "/migrate/po-bill-qty", tools.MigrateBillQtyPO)
		server.SetHandler(common.APIMethod.PUT, "/migrate/po-status", tools.MigratePOStatus)
		// payment platform migrate

		server.SetHandler(common.APIMethod.PUT, "/migrate/payment-platform", tools.MigratePaymentPlatform)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/inbound-request-hedging", tools.MigrateInboundRequestHedging)
		_ = server.SetHandler(common.APIMethod.PUT, "/migrate/auto-apply-payment-item-vb", tools.MigrateAutoApplyPaymentVB)

		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-cancel-draft", tools.MigrateCancelVendorBill)
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-item-re-calc-price", tools.MigrateVendorBillItemRecalcPrice)

		// vendor bill tax
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-tax", tools.MigrateVendorBillTax)
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-po", tools.MigrateVendorBillPO)
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-price-without-vat", tools.MigrateUpdateVBpriceWithoutVat)
		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-total-vat-price", tools.MigrateUpdateVBpriceTotalVatPrice)

		server.SetHandler(common.APIMethod.PUT, "/migrate/vendor-bill-ensure-template-code", tools.MigrateVendorBillEnsureTemplateCode)
		server.SetHandler(common.APIMethod.PUT, "/debug/auto-add-payment", action.DebugAutoApplyPaymentByPM)
	}

	// ======================================== DEAL ========================================
	_ = server.SetHandler(common.APIMethod.POST, "/validation-deal", api.GetValidationDeal)
	_ = server.SetHandler(common.APIMethod.GET, "/price-for-deal", api.GetPriceForDeal)

	// ======================================== Product ========================================
	server.SetHandler(common.APIMethod.GET, "/product/last-bill-info", api.GetProductLastBillInfo)

	server.SetHandler(common.APIMethod.GET, "/tool/migrate-po", tools.MigratePO)
	server.SetHandler(common.APIMethod.GET, "/tool/migrate-completed-po", tools.MigrateCompletedPO)

	server.SetHandler(common.APIMethod.POST, "/sku-item-demand", api.GetSkuItemEstDemand)

	// expose
	server.Expose(80)

	// =============== WORKER JOBS ================
	workerRemoveQuotation := app.SetupWorker()
	workerRemoveQuotation.SetTask(action.WorkerRemoveQuotation)
	workerRemoveQuotation.SetRepeatPeriod(60 * 60)

	// worker warmup purchaser data
	workerWarmupPurchaser := app.SetupWorker()
	workerWarmupPurchaser.SetTask(action.WarmupWarehouseAndPurchaser)
	workerWarmupPurchaser.SetRepeatPeriod(60 * 60 * 24) // 1 ngày

	app.OnAllDBConnected(func() {
		// worker
		action.WarmupWarehouseAndPurchaser()

		// skip uat
		if os.Getenv("env") == "uat" {
			return
		}

		// schedule
		model.ConfigSchedule.Start()

		// start consume job
		model.CrawlOrderItemJob.StartConsume()
		model.ImportQuotationJob.StartConsume()
		model.AssignPriceAfterRebateJob.StartConsume()

		// =============== WORKER JOBS ================
		workerValidBiddingRate := app.SetupWorker()
		workerValidBiddingRate.SetTask(action.WorkerValidBiddingRate)
		workerValidBiddingRate.SetRepeatPeriod(60 * 10).SetDelay(60)
	})

	// launch app
	if err := app.Launch(); err != nil {
		panic(err)
	}
}
