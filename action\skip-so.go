package action

import (
	"errors"
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSkipSO retrieves a list of SkipSO records from the database based on the provided input criteria.
// It supports pagination through the offset and limit parameters, and can optionally include the total count of records.
//
// Parameters:
//   - input: A pointer to a model.SkipSO struct containing the search criteria.
//   - offset: An int64 representing the starting point for the records to be retrieved.
//   - limit: An int64 representing the maximum number of records to be retrieved.
//   - getTotal: A boolean indicating whether to include the total count of records in the response.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the query results and status.
func GetSkipSO(input *model.SkipSO, offset, limit int64, getTotal bool) *common.APIResponse {
	sort := &bson.M{"_id": -1}
	resp := model.SkipSODB.Query(input, offset, limit, sort)
	if getTotal && resp.Status == common.APIStatus.Ok {
		resp.Total = model.SkipSODB.Count(input).Total
	}

	return resp
}

// CreateSkipSO handles the creation or updating of a SkipSO record.
// It first validates the input using validateSkipSO function. If validation fails,
// it returns an API response with an invalid status and the error message.
// If validation passes, it generates a hashtag for the SkipSO input and then
// attempts to upsert the SkipSO record in the database.
//
// Parameters:
//   - input: A pointer to a model.SkipSO struct containing the details of the SkipSO to be created or updated.
//
// Returns:
//   - A pointer to a common.APIResponse struct indicating the result of the operation.
func CreateSkipSO(input *model.SkipSO) *common.APIResponse {

	if err := validateSkipSO(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	input.Hashtag = genSkipSOHashtag(input)

	// upsert
	return model.SkipSODB.Upsert(model.SkipSO{
		SKU:    input.SKU,
		SOCode: input.SOCode,
	}, input)
}

// CreateListSkipSO processes a list of SkipSO objects and creates each one by calling CreateSkipSO.
// If any creation fails, it returns the corresponding APIResponse with the error status.
// If all creations succeed, it returns an APIResponse indicating success.
//
// Parameters:
//   - input: A slice of SkipSO objects to be created.
//
// Returns:
//   - *common.APIResponse: The API response indicating the result of the operation.
func CreateListSkipSO(input []model.SkipSO) *common.APIResponse {
	for _, skipSO := range input {
		resp := CreateSkipSO(&skipSO)
		if resp.Status != common.APIStatus.Ok {
			return resp
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create skip SO success",
	}
}

// DeleteSkipSO handles the deletion of a SkipSO record based on the provided input.
func DeleteSkipSO(input *model.SkipSO) *common.APIResponse {
	if utils.HasZero(input.SKU, input.SOCode) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sku and so code are required",
		}
	}

	return model.SkipSODB.Delete(model.SkipSO{SKU: input.SKU, SOCode: input.SOCode})
}

// validateSkipSO validates the input SkipSO object to ensure that all required fields are present.
func validateSkipSO(input *model.SkipSO) error {

	if input == nil {
		return errors.New("input is nil")
	}

	if utils.HasZero(input.SKU, input.ProductCode, input.ProductID) {
		return errors.New("sku info is required")
	}

	if utils.HasZero(input.WarehouseCode, input.SOCode) {
		return errors.New("warehouse code and so code are required")
	}

	return nil

}

// genSkipSOHashtag generates a hashtag for a SkipSO object based on the SOCode, ProductCode, and ProductID.
func genSkipSOHashtag(input *model.SkipSO) string {
	return strings.Replace(utils.NormalizeString(fmt.Sprintf("%s %s %d", input.SOCode, input.ProductCode, input.ProductID)), " ", "-", -1)
}
