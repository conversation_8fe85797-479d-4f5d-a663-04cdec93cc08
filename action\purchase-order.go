package action

import (
	"fmt"
	"log"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/core"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// check the product is new expiration
func CheckNearExpiration(items []*model.PurchaseOrderItem) {
	var (
		// check po_item have lot_date & match with sku
		isPOItemSKUNearExpiration = func(poItem *model.PurchaseOrderItem, sku *model.SKUItem) bool {
			if !utils.HasZero(poItem, sku) && poItem.SKU != "" && sku.LotDates != nil && utils.IsZero(poItem.POItemID) && (sku.SKU == poItem.SKU) && (len(sku.LotDates) > 0) && sku.LotDates[0].IsNearExpired {
				return true
			}
			return false
		}
		// builder for effectively concat string
		listSKU []string
	)

	// concat for api will split string -> slice
	for _, item := range items {
		if !utils.IsZero(item.SKU) && utils.IsZero(item.POItemID) {
			listSKU = append(listSKU, item.SKU)
		}
	}

	// prevent get all sku
	if len(listSKU) == 0 {
		return
	}

	// get sku-item match list po_item.sku
	option := client.APIOption{
		Limit:  utils.Pointer.WithInt(1000),
		Params: map[string]string{"skuCodes": strings.Join(listSKU, ",")},
	}
	respCheckSKUNearExpire := marketplace.GetListSKUItem(option)

	if respCheckSKUNearExpire.Status != common.APIStatus.Ok {
		return
	}

	nearExpireSKUs := respCheckSKUNearExpire.Data.([]*model.SKUItem)
	// push to po_item matched sku
	for _, item := range items {
		for _, sku := range nearExpireSKUs {
			if isPOItemSKUNearExpiration(item, sku) {
				item.IsNearExpiration = utils.Pointer.WithBool(true)
				break
			}
		}
	}
}

// GetSellerPOList ...
// GetSellerPOList retrieves a list of seller purchase orders based on the provided query parameters.
// It supports pagination, sorting, and filtering by various fields.
//
// Parameters:
//   - query: A model.PurchaseOrder object containing the query parameters.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of records.
//   - getInboundRequest: A boolean indicating whether to include inbound request information.
//   - sort: A string specifying the field to sort by and the sort order.
//
// Returns:
//   - *common.APIResponse: An API response object containing the list of purchase orders and additional metadata.
func GetSellerPOList(query model.PurchaseOrder, offset, limit int64, getTotal, getInboundRequest bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}

	// sort
	switch sort {
	case "issuedTime":
		sortField = &primitive.M{"issued_time": 1}
	case "-issuedTime":
		sortField = &primitive.M{"issued_time": -1}
	case "vendorName":
		sortField = &primitive.M{"vendor_name": 1}
	case "-vendorName":
		sortField = &primitive.M{"vendor_name": -1}
	case "createdTime":
		sortField = &primitive.M{"created_time": 1}
	case "-createdTime":
		sortField = &primitive.M{"created_time": -1}
	case "id":
		sortField = &primitive.M{"_id": 1}
	case "-id":
		sortField = &primitive.M{"_id": -1}
	}

	// Add additional query conditions with IDFrom
	if query.IDFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$gt": query.IDFrom,
			},
		})
	}

	// Add additional query conditions with IDTo
	if query.IDTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"_id": bson.M{
				"$lt": query.IDTo,
			},
		})
	}

	// search by hash_tag: Replica DB
	for _, qM := range query.ComplexQuery {
		if _, ok := (*qM)["hash_tag"]; ok {

			// Rep DB
			opt := options.Find().SetLimit(limit).SetSkip(offset).SetSort(sortField)
			genResp := model.PurchaseOrderReplicaDB.QueryWithOptions(query, opt)
			if getTotal {
				countResp := model.PurchaseOrderReplicaDB.Count(query)
				genResp.Total = countResp.Total
			}

			// map inbound request info
			if getInboundRequest && genResp.Status == common.APIStatus.Ok {
				resultData := genResp.Data.([]*model.PurchaseOrder)
				mapInboundRequestInfo(resultData)
				genResp.Data = resultData
			}

			return genResp
		}
	}

	// query POs
	genResp := model.PurchaseOrderDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.PurchaseOrderDB.Count(query)
		genResp.Total = countResp.Total
	}

	// map inbound request info
	if getInboundRequest && genResp.Status == common.APIStatus.Ok {
		resultData := genResp.Data.([]*model.PurchaseOrder)
		mapInboundRequestInfo(resultData)
		genResp.Data = resultData
	}

	return genResp
}

// mapInboundRequestInfo maps inbound request information to each purchase order in the given slice.
// It iterates through each purchase order, retrieves the associated purchase order items,
// and then fetches the corresponding inbound requests based on the inbound request codes found in the items.
// If inbound requests are found, they are assigned to the respective purchase order.
//
// Parameters:
//
//	result ([]*model.PurchaseOrder): A slice of purchase orders to map inbound request information to.
func mapInboundRequestInfo(result []*model.PurchaseOrder) {
	// iterate through each purchase order
	for i := range result {
		irCodes := []string{}

		// get purchase order items
		poItemsResp := model.PurchaseOrderItemDB.Query(
			&model.PurchaseOrderItem{
				POCode: result[i].POCode,
			}, 0, 1000, nil)

		if poItemsResp.Status == common.APIStatus.Ok {
			poItems := poItemsResp.Data.([]*model.PurchaseOrderItem)
			for _, item := range poItems {
				irCodes = append(irCodes, item.InboundRequestCode)
			}
		}

		// map inbound request
		if len(irCodes) == 0 {
			continue
		}

		// query inbound requests
		irResp := model.InboundRequestDB.Query(model.InboundRequest{
			ComplexQuery: []*bson.M{
				{"inbound_request_code": bson.M{"$in": irCodes}},
			},
		}, 0, 1000, nil)

		// assign inbound requests to purchase order
		if irResp.Status == common.APIStatus.Ok {
			irs := irResp.Data.([]*model.InboundRequest)
			result[i].InboundRequests = irs
		}
	}
}

// CreatePurchaseOrder ...
// CreatePurchaseOrder creates a new purchase order based on the provided input and account information.
// It performs various validations and sets default values before creating the purchase order and its items.
//
// Parameters:
//   - input: model.PurchaseOrder - The purchase order details provided by the user.
//   - acc: *model.Account - The account information of the user creating the purchase order.
//
// Returns:
//   - *common.APIResponse - The response indicating the success or failure of the operation.
//
// The function performs the following steps:
//  1. Sets the initial status and bill status of the purchase order.
//  2. Generates a new POID for the purchase order.
//  3. Validates the warehouse code and seller code restrictions.
//  4. Ensures that the original PO codes are specified for replenishment type POs.
//  5. Sets the default PO type if not provided.
//  6. Validates the seller and purchaser codes.
//  7. Validates the warehouse codes and their association with the purchaser code.
//  8. Ensures that the seller and vendor have the same trading or non-trading type.
//  9. Checks if the purchase order items are provided.
//  10. Sets the PO code and planning time.
//  11. Adds estimated delivery time based on vendor information.
//  12. Validates if the SKUs are active.
//  13. Calculates the price of the purchase order.
//  14. Sets the default value for applying promotions.
//  15. Creates the purchase order in the database.
//  16. Checks for near expiration items.
//  17. Creates the purchase order items in the database.
//  18. Increases the quantity on bidding rate if the PO is created from bidding or confirmed quotation.
func CreatePurchaseOrder(input model.PurchaseOrder, acc *model.Account) *common.APIResponse {
	input.Status = enum.PurchaseOrderStatus.DRAFT
	// input.BillStatus = ""
	input.POID, _ = model.GetPOID()

	// TODO tạm chặn tạo PO từ kho HCM (sau này dynamic)
	if input.WarehouseCode == "HCM" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Không được tạo PO cho kho " + input.WarehouseCode,
		}
	}

	if input.SellerCode == "MEDX-HN" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: input.SellerCode + " không được tạo PO.",
		}
	}

	// Phải chỉ định PO nguồn của PO có phân loại là Bổ sung hàng
	if input.OriginalPOs == nil {
		input.OriginalPOs = &[]string{}
	}
	if input.OriginalPOs != nil && len(*input.OriginalPOs) > 0 && (*input.OriginalPOs)[0] == "" {
		input.OriginalPOs = &[]string{}
	}
	if input.Type == enum.POTypeVal.REPLENISHMENT && len(*input.OriginalPOs) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The original PO code must be specified to the PO with type of Replenishment",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidOriginalPO),
		}
	}

	if input.Type == "" {
		input.Type = enum.POTypeVal.NORMAL
	}

	if response, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return response
	}

	if input.WarehouseCode == "" || input.DeliveryWarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCode, DeliveryWarehouseCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// check warehouse có khớp với PUR chưa
	if purchaserCode, ok := utils.WarehouseCodeToPurchaserCode(input.WarehouseCode); !ok || purchaserCode != input.PurchaserCode {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   input.PurchaserCode + " do not manage warehouse " + input.WarehouseCode,
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseNotMatchPurchaser),
		}
	}

	if !utils.ValidWarehouseCode(input.DeliveryWarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}

	// if input.WarehouseCode != input.DeliveryWarehouseCode {
	// 	return &common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   fmt.Sprintf("WarehouseCode :%q not match DeliveryWarehouseCode :%q", input.WarehouseCode, input.DeliveryWarehouseCode),
	// 		ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
	// 	}
	// }

	// validate seller and vendor have the same trading or non-trading type
	validTypeResp := validSellerType(input.SellerCode, input.VendorCode)
	if validTypeResp.Status != common.APIStatus.Ok {
		return validTypeResp
	}

	// check po_items is empty
	if len(input.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Items required",
			ErrorCode: string(enum.ErrorCodeInvalid.POItems),
		}
	}

	// if isOK := CheckValidPOItems(input.Items); !isOK {
	// 	return &common.APIResponse{
	// 		Status:  common.APIStatus.Invalid,
	// 		Message: "Invalid PO items",
	// 	}
	// }

	now := utils.GetVietnamTimeNow()
	input.POCode = fmt.Sprintf("PO%v%v", input.WarehouseCode, input.POID)
	if input.WarehouseCode == "HCM" || input.WarehouseCode == "BD" {
		input.POCode = fmt.Sprintf("PO%v%v", "", input.POID)
	}

	if input.PlanningTime == nil {
		input.PlanningTime = &now
	}

	if acc != nil {
		input.CreatedByID = acc.AccountID
		input.CreatedByName = acc.Fullname
	}

	// Add filed estimatedDeliveryTime base on vendor info
	if input.VendorCode != "" {
		vendorResp := GetVendorInfo(input.VendorCode)
		if vendorResp.Status != common.APIStatus.Ok {
			return vendorResp
		}
		vendor := vendorResp.Data.([]*model.Seller)[0]

		// new hashtag
		input.HashTag = GenHashTagPOFromInfo(&input, vendor)

		if utils.IsNil(input.EstimatedDeliveryTime) {
			// nếu kho cần và kho giao trong PO khác nhau thì cần tính lại [total_leadtime = leadtime đến thuocsi + leadtime(từ kho giao -> kho cần) ]
			vendorLeadTime := selectVendorLeadTimes(vendor.InboundAddresses, input.DeliveryWarehouseCode)

			estimatedDeliveryTime := now.Add(time.Duration(int64(math.Ceil(vendorLeadTime*24))) * time.Hour)
			if estimatedDeliveryTime.Weekday() == time.Sunday {
				estimatedDeliveryTime = estimatedDeliveryTime.AddDate(0, 0, 1)
			}
			estimatedDeliveryTime = utils.GetLastTimeOfDate(estimatedDeliveryTime)
			input.EstimatedDeliveryTime = &estimatedDeliveryTime
		}

		// assign first-mile to PO
		FIRST_MILE_BY := enum.FirstMileBy.VENDOR
		if vendor.FirstMileBy != "" {
			FIRST_MILE_BY = vendor.FirstMileBy
		}
		input.FirstMileBy = FIRST_MILE_BY
	}

	// validate sku active
	activeSKUResp := checkIsActiveSKU(input.SellerCode, input.PurchaserCode, input.Items, enum.PurchaseOrderStatus.DRAFT)
	if activeSKUResp.Status != common.APIStatus.Ok {
		return activeSKUResp
	}

	// input.SourceDocument = input.POCode
	input = *calculatePricePO(&input)

	// default apply promotion po
	if input.ApplyPromotion == nil {
		input.ApplyPromotion = utils.Pointer.WithBool(true)
	}

	// create PurchaseOrder
	createResp := model.PurchaseOrderDB.Create(input)
	if createResp.Status != common.APIStatus.Ok {
		return createResp
	}

	CheckNearExpiration(input.Items)

	// insert field to items
	for i, item := range input.Items {
		item.POID = input.POID
		item.POCode = input.POCode
		item.DeliveryWarehouseCode = input.DeliveryWarehouseCode
		item.WarehouseCode = input.WarehouseCode
		item.SellerCode = input.SellerCode
		item.PurchaserCode = input.PurchaserCode
		item.VendorCode = input.VendorCode
		if item.POItemID == 0 {
			item.POItemID, _ = model.GetPOItemID()
		}
		input.Items[i] = item
	}

	// create po_items
	createItemResp := model.PurchaseOrderItemDB.CreateMany(input.Items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	// Nếu PO được tạo từ nguồn bidding hoặc confirm quotation => tăng số lượng trên bidding rate/
	if input.CreatedBySystem == string(enum.POCreateBySystem.SUPPLIER) ||
		input.CreatedBySystem == string(enum.POCreateBySystem.INTERNAL_CONFIRM_BIDDING) {
		if input.POCode != "" {
			go sdk.Execute(func() { IncreaseQuantityPoCodesToBiddingRate(input.POCode) })
		}
	}
	return createResp
}

// VendorUpdatePurchaseOrder ...
func VendorUpdatePurchaseOrder(input model.PurchaseOrder, isVendor bool) *common.APIResponse {
	// todo:
	// kiểm tra từng dòng items, nếu item có hedging và có thay đổi số lượng sản phẩm thì giảm số lương
	// thwus tự ưu tiên giảm số lương
	// giảm item của hedging trước
	// giảm item của hedging sau

	var newItems []*model.PurchaseOrderItem

	if len(input.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Item list is empty.",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Tìm danh sách items cũ, nếu chưa có items cũ thì xem như không được cập nhật.
	oldItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: input.POCode,
	}, 0, 1000, nil)

	if oldItemsResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Error when get old items",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}
	oldItems := oldItemsResp.Data.([]*model.PurchaseOrderItem)
	// sort oldItems theo InboundRequestCode tăng dần
	// mục tiêu: để items mua hàng bằng demand lên đầu.
	sort.Slice(oldItems, func(i, j int) bool {
		return oldItems[i].InboundRequestCode < oldItems[j].InboundRequestCode
	})

	// map old items key by SKU
	mapOldItems := make(map[string][]*model.PurchaseOrderItem)
	for _, oldItem := range oldItems {
		if oldItem.IsGift != nil && *oldItem.IsGift {
			continue
		}
		mapOldItems[oldItem.SKU] = append(mapOldItems[oldItem.SKU], oldItem)
	}

	for _, item := range input.Items {

		// hàng tặng thì không cần xử lý
		if item.IsGift != nil && *item.IsGift {
			newItems = append(newItems, item)
			continue
		}

		switch item.SourceQuotation {
		case enum.QuotationTypeVal.HEDGING:
			if oldItems, ok := mapOldItems[item.SKU]; ok {
				totalQuantityExpect := item.ExpectQuantity
				// duyện qua oldItems, giảm dần số lượng totalQuantityExpect cho đến khi totalQuantityExpect = 0
				for _, oldItem := range oldItems {
					if totalQuantityExpect == 0 {
						oldItem.ExpectQuantity = 0
						oldItem.ActualQuantity = 0
						newItems = append(newItems, oldItem)
					} else if oldItem.ExpectQuantity <= totalQuantityExpect {
						newItems = append(newItems, oldItem)
						totalQuantityExpect = totalQuantityExpect - oldItem.ExpectQuantity
					} else {
						oldItem.ExpectQuantity = totalQuantityExpect
						oldItem.ActualQuantity = totalQuantityExpect
						newItems = append(newItems, oldItem)
						totalQuantityExpect = 0
					}
				}
			}
		case enum.QuotationTypeVal.NORMAL:
			newItems = append(newItems, item)
		default:
		}
	}

	input.Items = newItems

	return UpdatePurchaseOrder(input, isVendor)
}

// UpdatePurchaseOrder ...
func UpdatePurchaseOrder(input model.PurchaseOrder, isVendor bool) *common.APIResponse {
	input.Status = ""
	var removedSkuCode []string
	// purchaser, seller
	if response, ok := utils.ValidSellerPurchaser(input.SellerCode, input.PurchaserCode); !ok {
		return response
	}
	// validate
	if len(input.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Item list is empty.",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Phải chỉ định PO nguồn của PO có phân loại là Bổ sung hàng
	if input.Type == enum.POTypeVal.REPLENISHMENT {
		if input.OriginalPOs == nil {
			input.OriginalPOs = &[]string{}
		}
		if input.OriginalPOs != nil && len(*input.OriginalPOs) == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The original PO code must be specified to the PO with type of Replenishment",
				ErrorCode: string(enum.ErrorCodeInvalid.InvalidOriginalPO),
			}
		}
	}

	oldItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: input.POCode,
	}, 0, 1000, nil)

	if oldItemResp.Status == common.APIStatus.Ok {
		oldItemList := oldItemResp.Data.([]*model.PurchaseOrderItem)
		vendorItems := []*model.PurchaseOrderItem{}
		// Duyệt trong vòng lặp:

		for _, oldItem := range oldItemList {
			var isExisted bool
			for _, newItem := range input.Items {
				if newItem.POItemID == oldItem.POItemID {
					if newItem.ExpectQuantity <= oldItem.ExpectQuantity {
						oldItem.ExpectQuantity = newItem.ExpectQuantity
					}
				}
				if newItem.SKU == oldItem.SKU {
					isExisted = true
				}
			}
			// Nếu oldItem không nằm trong input.Items thì thêm vào removedItems
			if !isExisted {
				removedSkuCode = append(removedSkuCode, oldItem.SKU)
			}
			vendorItems = append(vendorItems, oldItem)
		}

		if isVendor {
			input.Items = vendorItems
		}
	}

	// check: dòng mới phải nằm dưới cùng
	if input.Items[len(input.Items)-1].CreatedTime != nil {
		for _, item := range input.Items {
			if item.CreatedTime == nil {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "The new line must be at the bottom.",
					ErrorCode: string(enum.ErrorCodeInvalid.LastLineMustNew),
				}
			}
		}
	}
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}
	if !utils.ValidWarehouseCode(input.DeliveryWarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}

	// validate seller and vendor have the same trading or non-trading type
	validTypeResp := validSellerType(input.SellerCode, input.VendorCode)
	if validTypeResp.Status != common.APIStatus.Ok {
		return validTypeResp
	}

	// if input.WarehouseCode != input.DeliveryWarehouseCode {
	// 	return &common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   fmt.Sprintf("WarehouseCode :%q not match DeliveryWarehouseCode :%q", input.WarehouseCode, input.DeliveryWarehouseCode),
	// 		ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
	// 	}
	// }

	if input.POCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		}
	}

	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: input.POCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]

	if isOK := CheckValidPOItems(input.Items); !isOK && poCheck.Status != enum.PurchaseOrderStatus.DRAFT {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid PO items",
		}
	}

	// HashTag
	if input.VendorCode != poCheck.VendorCode {
		vendorResp := GetVendorInfo(input.VendorCode)
		if vendorResp.Status == common.APIStatus.Ok {
			vendor := vendorResp.Data.([]*model.Seller)[0]
			input.HashTag = GenHashTagPOFromInfo(&input, vendor)
		}
	}

	valid := CheckValidPOStatus(poCheck.Status, poCheck.Status)
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	// Không được thay đổi Kho Cần của PO
	if input.WarehouseCode != "" && input.WarehouseCode != poCheck.WarehouseCode {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not change PO's warehouse",
			ErrorCode: string(enum.ErrorCodeInvalid.NotChangeWarehouse),
		}
	}

	// validate sku active
	activeSKUResp := checkIsActiveSKU(input.SellerCode, input.PurchaserCode, input.Items, poCheck.Status)
	if activeSKUResp.Status != common.APIStatus.Ok {
		return activeSKUResp
	}

	// không được xóa line cũ
	oldItemsResp := model.PurchaseOrderItemDB.Query(
		model.PurchaseOrderItem{POCode: input.POCode},
		0, 1000, nil,
	)
	if oldItemsResp.Status == common.APIStatus.NotFound {
		// do nothing
	} else if oldItemsResp.Status != common.APIStatus.Ok {
		return oldItemsResp
	} else if oldItemsResp.Status == common.APIStatus.Ok {
		oldItems := oldItemsResp.Data.([]*model.PurchaseOrderItem)
		for _, old := range oldItems {
			if poCheck.Status != enum.PurchaseOrderStatus.DRAFT {
				isExisted := false
				for _, item := range input.Items {
					if !utils.IsNil(item.CreatedTime) && *old.CreatedTime == *item.CreatedTime || old.POItemID == item.POItemID {
						isExisted = true
						break
					}
				}

				if !isExisted {
					return &common.APIResponse{
						Status:    common.APIStatus.Invalid,
						Message:   "Do not delete old lines",
						ErrorCode: string(enum.ErrorCodeInvalid.NotAllowDeleteOldLine),
					}
				}
			}

			// TODO bỏ chặn tăng số lượng PO
			// không được tăng số lượng sp của po item tạo từ quotation
			for _, item := range input.Items {
				if item.ReferPrice > 0 && old.POItemID == item.POItemID && old.ExpectQuantity < item.ExpectQuantity {
					getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
						WarehouseCode: item.WarehouseCode,
						Version:       utils.GetCurrentVersionDDMMYYYY(),
						SKU:           item.SKU,
					})
					if getTrackingQuotationResp.Status == common.APIStatus.Ok {
						oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
						if oldTrackingQuotation.TotalQuantityConfirmed == nil {
							oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
						}
						newConfirmedQty := *oldTrackingQuotation.TotalQuantityConfirmed + item.ExpectQuantity - old.ExpectQuantity

						// update confirmed quantity PO tracking
						model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
							Version:       utils.GetCurrentVersionDDMMYYYY(),
							WarehouseCode: item.WarehouseCode,
							SKU:           item.SKU,
						}, model.QuotationTracking{
							TotalQuantityConfirmed: &newConfirmedQty,
						})
					}

				}
			}
		}
	}

	// Update est deliver time
	if input.EstimatedDeliveryTime != nil {
		tempEstimated := utils.GetLastTimeOfDate(*input.EstimatedDeliveryTime)
		input.EstimatedDeliveryTime = &tempEstimated
	}

	input = *calculatePricePO(&input)

	// revert PARTIALLY_RECEIVED if not fully received
	if poCheck.Status == enum.PurchaseOrderStatus.RECEIVED {
		for _, item := range input.Items {
			if item.ActualQuantity < item.ExpectQuantity {
				input.Status = enum.PurchaseOrderStatus.PARTIALLY_RECEIVED
			}
		}
	}

	// #1 update PurchaseOrder
	updateResp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: input.POCode,
		},
		input,
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}
	purchaseOrder := updateResp.Data.([]*model.PurchaseOrder)[0]

	// #2 scrollBackToQuotation khi trạng thái còn là nháp
	if purchaseOrder.Status == enum.PurchaseOrderStatus.DRAFT ||
		purchaseOrder.Status == enum.PurchaseOrderStatus.CONFIRMED || purchaseOrder.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED {
		oldItemsResp := model.PurchaseOrderItemDB.Query(
			model.PurchaseOrderItem{POCode: input.POCode},
			0, 1000, nil,
		)
		if oldItemsResp.Status == common.APIStatus.Error {
			return oldItemsResp
		}

		if oldItemsResp.Status == common.APIStatus.Ok {
			// sum nhiều line sku trùng nhau
			type poItemChange struct {
				decreaseQuantity int64
				oldItem          *model.PurchaseOrderItem
			}

			var (
				oldItems             = oldItemsResp.Data.([]*model.PurchaseOrderItem)
				mapSumSKUDecreaseQty = make(map[string]poItemChange, len(oldItems))
			)

			// -------------------------------------------------------------
			// X = SL tổng sku sẽ giảm
			// Y = SL mỗi sku po cũ
			// Z = SL mỗi sku po mới
			// X = [ Y1 +  Y2 +  Y3 +  Y4 + ...  Yn -  Z1 - Z2 - Z3 - Z4 - ...  Zn ]
			// sum decrease qty by sku, deleted line will be decrease
			for _, oldItem := range oldItems {
				// skip line không tạo từ quotation
				if oldItem.ReferPrice <= 0 {
					continue
				}
				if itemChange, ok := mapSumSKUDecreaseQty[oldItem.SKU]; ok {
					itemChange.decreaseQuantity += oldItem.ExpectQuantity
					mapSumSKUDecreaseQty[oldItem.SKU] = itemChange
				} else {
					mapSumSKUDecreaseQty[oldItem.SKU] = poItemChange{
						decreaseQuantity: oldItem.ExpectQuantity,
						oldItem:          oldItem,
					}
				}
			}

			for _, newItem := range input.Items {
				if newItem.ReferPrice <= 0 {
					continue
				}
				if itemChange, ok := mapSumSKUDecreaseQty[newItem.SKU]; ok {
					itemChange.decreaseQuantity -= newItem.ExpectQuantity
					mapSumSKUDecreaseQty[newItem.SKU] = itemChange
				}
			}

			validSystemType := []string{string(enum.POCreateBySystem.INTERNAL_CONFIRM_BIDDING), string(enum.POCreateBySystem.SUPPLIER)}
			for _, poItemChange := range mapSumSKUDecreaseQty {
				// roll back đúng bằng SL giảm trên po_item
				if poItemChange.decreaseQuantity > 0 {
					rollBackToQuotation(poCheck, poItemChange.oldItem, poItemChange.decreaseQuantity)

					// notify to PIC the updated of expected quantity of SKU & only PO created from Portal
					if isVendor && utils.IsContains(validSystemType, input.CreatedBySystem) {
						picResp := model.PICDB.QueryOne(model.PIC{
							ObjectType:    enum.ObjectType.SKU,
							ObjectCode:    poItemChange.oldItem.SKU,
							WarehouseCode: poItemChange.oldItem.WarehouseCode,
						})
						if picResp.Status != common.APIStatus.Ok {
							continue
						}
						pic := picResp.Data.([]*model.PIC)[0]

						// get account info of PIC
						if pic.PICAccountID != nil {
							resp := core.GetAccountList(client.APIOption{
								Q: model.Account{
									AccountID: *pic.PICAccountID,
								},
							})
							if resp.Status != common.APIStatus.Ok {
								continue
							}
							acc := resp.Data.([]*model.Account)[0]

							prodIdStr := strconv.Itoa(int(poItemChange.oldItem.ProductID))
							expectedQtyStr := strconv.Itoa(int(poItemChange.oldItem.ExpectQuantity))
							remainQty := strconv.Itoa(int(poItemChange.oldItem.ExpectQuantity - poItemChange.decreaseQuantity))
							CreateNotificationForUserAccount(*acc, model.Notification{
								Title: fmt.Sprintf("SERVER_NOTIFY_PO_CHANGE_QUANTITY_TO_PIC " + prodIdStr + " " + poItemChange.oldItem.POCode + " " + expectedQtyStr + " " + remainQty),
								Topic: "ANNOUNCEMENT",
								Link:  fmt.Sprintf("/internal-seller/%v/%v/purchase-order/edit?poCode=%v", poItemChange.oldItem.SellerCode, poItemChange.oldItem.PurchaserCode, poItemChange.oldItem.POCode),
							}, &enum.AccountType.EMPLOYEE)
						}
					}
				}
			}
		}

	}

	// #3 xóa/insert item
	// delete toàn bộ item
	deleteResp := model.PurchaseOrderItemDB.Delete(model.PurchaseOrderItem{POCode: purchaseOrder.POCode})
	if deleteResp.Status != common.APIStatus.Ok {
		return deleteResp
	}

	CheckNearExpiration(input.Items)

	// insert field to items
	for i, item := range input.Items {
		item.POID = purchaseOrder.POID
		item.POCode = purchaseOrder.POCode
		item.DeliveryWarehouseCode = purchaseOrder.DeliveryWarehouseCode
		item.WarehouseCode = purchaseOrder.WarehouseCode
		item.SellerCode = purchaseOrder.SellerCode
		item.PurchaserCode = purchaseOrder.PurchaserCode
		item.VendorCode = purchaseOrder.VendorCode
		if item.POItemID == 0 {
			item.POItemID, _ = model.GetPOItemID()
		}
		input.Items[i] = item
	}

	// create po_items
	createItemResp := model.PurchaseOrderItemDB.CreateMany(input.Items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	// #4 Check status, update receipt info & sync data warehouse
	if poCheck.Status == enum.PurchaseOrderStatus.CONFIRMED || poCheck.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
		// poCheck.Status == enum.PurchaseOrderStatus.PROCESSING ||
		poCheck.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || poCheck.Status == enum.PurchaseOrderStatus.RECEIVED ||
		// poCheck.Status == enum.PurchaseOrderStatus.SENT_PO ||
		poCheck.Status == enum.PurchaseOrderStatus.COMPLETED ||
		poCheck.Status == enum.PurchaseOrderStatus.AWAITING_BILLED || poCheck.Status == enum.PurchaseOrderStatus.BILLED {

		// // Tạo receipt qua wh
		// if mapSystem[poCheck.WarehouseCode] != "ERP" {
		createReceptResp := CreateReceptToWarehouse(purchaseOrder)
		if createReceptResp.Status != common.APIStatus.Ok {
			return createReceptResp
		}
		// }
	}

	// #5 sync money for vendor First mile ticket
	if purchaseOrder.Status == enum.PurchaseOrderStatus.CONFIRMED || purchaseOrder.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED {
		warehouse.UpdateValueShippingOrder(client.APIOption{
			Body: model.ShippingOrder{
				ParentReferenceCode: purchaseOrder.POCode,
				OrderValue:          purchaseOrder.TotalPrice,
			},
			Keys: []string{purchaseOrder.POCode},
		})
	}

	// #6 notify to employee when vendor update order quantity
	if isVendor {
		account := &model.Account{}
		if purchaseOrder.EmployeeCode != "" {
			resp := core.GetAccountList(client.APIOption{
				Q: model.Account{
					Username: purchaseOrder.EmployeeCode,
				},
			})
			if resp.Status == common.APIStatus.Ok {
				account = resp.Data.([]*model.Account)[0]
			}
		} else {
			var picID int64
			if purchaseOrder.VendorCode != "" {
				picResp := GetPICList(&model.PIC{ObjectType: enum.ObjectType.VENDOR, WarehouseCode: purchaseOrder.WarehouseCode, ObjectCode: purchaseOrder.VendorCode}, 0, 1, false)
				if picResp.Status == common.APIStatus.Ok {
					picAccountID := picResp.Data.([]*model.PIC)[0].PICAccountID
					if picAccountID != nil {
						picID = *picAccountID
					}
					resp := core.GetAccountList(client.APIOption{
						Q: model.Account{
							AccountID: picID,
						},
					})
					if resp.Status == common.APIStatus.Ok {
						account = resp.Data.([]*model.Account)[0]
					}
				}
			}
		}
		if account != nil {
			CreateNotificationForUserAccount(*account, model.Notification{
				Title: fmt.Sprintf("SERVER_NOTIFY_PO_CHANGE_QUANTITY " + purchaseOrder.POCode),
				Topic: "ANNOUNCEMENT",
				Link:  fmt.Sprintf("/internal-seller/%v/%v/purchase-order/edit?poCode=%v", purchaseOrder.SellerCode, purchaseOrder.PurchaserCode, purchaseOrder.POCode),
			}, &enum.AccountType.EMPLOYEE)
		}
	}
	if input.POCode != "" {
		go sdk.Execute(func() {
			RecountQuantityPoCodesToBiddingRate(input.POCode, removedSkuCode)
		})
	}

	return updateResp
}

// func getAccountInfo(code) {

// }

// UpdatePurchaseOrderInfo ...
func UpdatePurchaseOrderInfo(input model.PurchaseOrder) *common.APIResponse {
	updateResp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: input.POCode,
		},
		model.PurchaseOrder{SourceDocument: input.SourceDocument},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update success",
	}
}

// // ConfirmSellerPO ...
// func ConfirmSellerPO(poCode string) *common.APIResponse {
// 	// check valid status
// 	checkResp := model.PurchaseOrderDB.QueryOne(
// 		model.PurchaseOrder{
// 			POCode: poCode,
// 		})
// 	if checkResp.Status != common.APIStatus.Ok {
// 		return checkResp
// 	}
// 	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
// 	valid := CheckValidPOStatus(poCheck.Status, enum.PurchaseOrderStatus.CONFIRMED)
// 	if !valid {
// 		return &common.APIResponse{
// 			Status:  common.APIStatus.Invalid,
// 			Message: "Do not switch to this status.",
// 		}
// 	}

// 	// update PO
// 	updateResp := model.PurchaseOrderDB.UpdateOne(
// 		model.PurchaseOrder{
// 			POCode: poCode,
// 		},
// 		model.PurchaseOrder{
// 			Status: enum.PurchaseOrderStatus.CONFIRMED,
// 		},
// 	)

// 	return updateResp
// }

// SentSellerPO ...
func SentSellerPO(poCode string, acc *model.Account) *common.APIResponse {
	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: poCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}

	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
	valid := CheckValidPOStatus(poCheck.Status, enum.PurchaseOrderStatus.CONFIRMED) // SENT_PO
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	if poCheck.Type == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POType is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidPOType),
		}
	}

	if acc.Type != "EMPLOYEE" && poCheck.RequireConfirm != nil && *poCheck.RequireConfirm == enum.RequireConfirm.CHANGE_ESTIMATED_DELIVERY {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "PO needs to review changes before confirming",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, nil)
	if purchaseOrderItemResp.Status != common.APIStatus.Ok {
		return purchaseOrderItemResp
	}
	purchaseOrderItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)
	poCheck.Items = purchaseOrderItems

	// kiểm tra PO items
	if isOK := CheckValidPOItems(purchaseOrderItems); !isOK {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PO items invalid",
		}
	}

	// if poCheck.WarehouseCode != poCheck.DeliveryWarehouseCode {
	// 	return &common.APIResponse{
	// 		Status:    common.APIStatus.Invalid,
	// 		Message:   "Delivery stock and delivery warehouse can't be different.",
	// 		ErrorCode: string(enum.ErrorCodeInvalid.WarehouseMustBeSame),
	// 	}
	// }

	// if mapSystem[poCheck.DeliveryWarehouseCode] == "" {
	if !utils.ValidWarehouseCode(poCheck.DeliveryWarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}
	// if mapSystem[poCheck.WarehouseCode] == "" {
	if !utils.ValidWarehouseCode(poCheck.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}

	// count item
	var countItemQty int64 = 0
	for _, poItem := range purchaseOrderItems {
		countItemQty += poItem.ExpectQuantity
	}
	if countItemQty == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "PO is empty",
		}
	}

	// Call get info vendor
	vendorResp := GetVendorInfo(poCheck.VendorCode)
	if vendorResp.Status != common.APIStatus.Ok {
		return vendorResp
	}
	vendor := vendorResp.Data.([]*model.Seller)[0]

	// Auto create first mile flow
	if vendor.FirstMileConfig != nil {
		for _, config := range *vendor.FirstMileConfig {
			if config.IsAutoCreate != nil && config.WarehouseCode == poCheck.WarehouseCode {
				if *config.IsAutoCreate {
					opts := client.APIOption{
						SaveLog: utils.Pointer.WithBool(true),
						Keys:    []string{"CREATE_FIRST_MILE_BY_PO", poCode},
						Params: map[string]string{
							"poCode": poCode,
						},
					}
					go seller.CreateFirstMileTicket(opts)
				}
			}
			continue
		}
	}

	// call get PIC Info
	picResp := GetPICList(&model.PIC{
		ObjectType:    enum.ObjectType.VENDOR,
		ObjectCode:    poCheck.VendorCode,
		WarehouseCode: poCheck.WarehouseCode,
	}, 0, 100, false)

	if picResp.Status == common.APIStatus.Ok {

		pic := picResp.Data.([]*model.PIC)[0]
		if pic.MinOrderAmount != nil && poCheck.TotalPrice != nil && *poCheck.TotalPrice != 0 && *poCheck.TotalPrice < float64(*pic.MinOrderAmount) {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "The total amount of the PO must not be less than the minimum order value of the vendor",
				ErrorCode: string(enum.ErrorCodeInvalid.NotAllowTotalPriceLTVendorMOA),
			}
		}
	}

	// skip po with total price = 0

	// Tạo receipt qua wh

	sellerInfo := model.SellerInfo{
		// SSO Integration: remove accountID on vendor model
		// Use sellerID instead of accountID
		// Confirmed by @tri.dao & vinhthai.pham
		SellerId: vendor.SellerID,
		Name:     vendor.Name,
		Phone:    vendor.Phone,
	}
	poCheck.SellerInfo = sellerInfo
	createReceptResp := CreateReceptToWarehouse(poCheck)
	if createReceptResp.Status != common.APIStatus.Ok {
		return createReceptResp
	}
	// update PO
	now := time.Now()
	updatePOResp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: poCode,
		},
		model.PurchaseOrder{
			Status:        enum.PurchaseOrderStatus.CONFIRMED, // SENT_PO
			ConfirmedTime: &now,
		},
	)
	if updatePOResp.Status == common.APIStatus.Ok {

		// history
		model.QuotationPOHistoryDB.UpdateMany(
			model.QuotationPOHistory{
				POCode: poCode,
			},
			model.QuotationPOHistory{
				POCode:   poCode,
				POStatus: string(enum.PurchaseOrderStatus.CONFIRMED),
			},
		)

		// // check create first mile ticket
		// if poCheck.FirstMileBy == enum.FirstMileBy.BUYMED {
		// 	option := client.APIOption{
		// 		Params: map[string]string{
		// 			"poCode": poCheck.POCode,
		// 		},
		// 	}
		// 	go seller.CreateFirstMileTicket(option)
		// }

		// // update giá cho quotation khi PO đã confirmed
		// updateQuotationPriceByPOConfimred(purchaseOrderItems)

		// push in queue for calculate vendor promotion
		err := model.VendorPromotionJob.Push(model.PurchaseOrder{POCode: poCode}, &job.JobItemMetadata{
			Topic:     model.VENDOR_PROMOTION_TOPIC_JOB,
			Keys:      []string{poCode, string(enum.PurchaseOrderStatus.CONFIRMED)},
			UniqueKey: poCode,
			ReadyTime: utils.Pointer.WithTime(utils.GetVietnamTimeNow().Add(time.Second * 10)), // delay 10s để kho kịp xử lý
		})
		if err != nil {
			log.Println(err.Error())
		}
		// // Không cho phép PI
		// // Nếu PO được tạo từ quotation portal
		// // Cập nhật lại giá trị trong bidding rate
		// if poCheck.CreatedBySystem == string(enum.POCreateBySystem.SUPPLIER) || poCheck.CreatedBySystem == string(enum.POCreateBySystem.INTERNAL_CONFIRM_BIDDING) {
		// 	go sdk.Execute(func() { IncreaseQuantityPoCodesToBiddingRate(poCode) })
		// }
	}

	// send Zalo
	sendZNSConfirmedPO(poCheck)

	return updatePOResp
}

// func updateQuotationPriceByPOConfimred(poItems []*model.PurchaseOrderItem) {
// 	for _, poItem := range poItems {
// 		// skip item không tạo từ quotation
// 		if poItem.QuotationUnitPrice == 0 {
// 			continue
// 		}

// 		poItemPrice := int64(poItem.UnitPrice)
// 		var poItemVAT int64 = -1
// 		if poItem.VAT != nil {
// 			poItemVAT = int64(*poItem.VAT)
// 		}

// 		// get quotation
// 		quotationResp := model.QuotationDB.Query(
// 			model.Quotation{
// 				SKU: poItem.SKU,
// 			},
// 			0, 10, nil,
// 		)
// 		if quotationResp.Status != common.APIStatus.Ok {
// 			continue
// 		}
// 		quotations := quotationResp.Data.([]*model.Quotation)

// 		for _, quotation := range quotations {
// 			if poItemPrice < quotation.UnitPrice {

// 				// update price cho quotation
// 				model.QuotationDB.UpdateOne(
// 					model.Quotation{ID: quotation.ID},
// 					model.Quotation{
// 						UnitPrice: poItemPrice,
// 						VAT:       poItemVAT,
// 					},
// 				)

// 				// // update ES search
// 				// WarmupSearch(quotation.ProductCode, nil)
// 			}
// 		}
// 	}
// }

// // CompleteSellerPO ...
// func CompleteSellerPO(poCode string) *common.APIResponse {
// 	// check valid status
// 	checkResp := model.PurchaseOrderDB.QueryOne(
// 		model.PurchaseOrder{
// 			POCode: poCode,
// 		})
// 	if checkResp.Status != common.APIStatus.Ok {
// 		return checkResp
// 	}
// 	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
// 	valid := CheckValidPOStatus(poCheck.Status, enum.PurchaseOrderStatus.COMPLETED)
// 	if !valid {
// 		return &common.APIResponse{
// 			Status:  common.APIStatus.Invalid,
// 			Message: "Do not switch to this status.",
// 		}
// 	}

// 	// TODO mở ra khi không dùng erp
// 	// // nếu có receipt mới sync cancel qua warehouse
// 	// // call api sync status warehouse
// 	// warehouseResp := client.WarehouseClient.SyncStatus(model.InboundTicket{
// 	// 	WarehouseCode: poCheck.DeliveryWarehouseCode,
// 	// 	POID:          poCheck.POID,
// 	// 	ExternalType:  "PURCHASE_SYS",
// 	// 	Status:        enum.ReceiptStatus.COMPLETED,
// 	// })
// 	// if warehouseResp.ErrorCode == "TICKET_NOT_UPDATE" {
// 	// 	warehouseResp.Status = common.APIStatus.Ok
// 	// }
// 	// if warehouseResp.Status != common.APIStatus.Ok {
// 	// 	return warehouseResp
// 	// }

// 	// Chỉ update trạng thái receipt/receipt-item khi và chỉ khi call warehouse thành công
// 	updateResp := model.PurchaseOrderDB.UpdateOne(
// 		model.PurchaseOrder{
// 			POCode: poCode,
// 		},
// 		model.PurchaseOrder{
// 			Status: enum.PurchaseOrderStatus.COMPLETED,
// 		},
// 	)
// 	if updateResp.Status != common.APIStatus.Ok {
// 		return updateResp
// 	}

// 	CreateSkuConfigWithCompletedPO(poCode)

// 	return updateResp
// }

// LockPO ...
func LockPO(poCode string, createShortageTicket bool) *common.APIResponse {

	if poCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		}
	}

	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: poCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
	valid := CheckValidPOStatus(poCheck.Status, "LOCK")
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	// change PO status
	resp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{POCode: poCode},
		model.PurchaseOrder{
			Status:      enum.PurchaseOrderStatus.AWAITING_BILLED,
			MustInbound: utils.Pointer.WithBool(false),
		})
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	po := resp.Data.([]*model.PurchaseOrder)[0]

	// send lock event to warehouse
	option := client.APIOption{
		Body: model.InboundTicket{
			WarehouseCode: po.WarehouseCode,
			POCode:        po.POCode,
			POID:          po.POID,
			ExternalType:  "PURCHASE_SYS",
			Status:        enum.ReceiptStatus.COMPLETED,
		},
		Keys: []string{po.POCode, "COMPLETED"},
	}
	warehouseResp := warehouse.SyncStatus(option)
	if warehouseResp.Status != common.APIStatus.Ok {
		fmt.Println("SyncStatus: ", warehouseResp.Message)
	}

	// create ticket wrong ticket SHORTAGE
	if createShortageTicket {
		getPOItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
			POCode: po.POCode,
		}, 0, 1000, nil)
		if getPOItemsResp.Status == common.APIStatus.Ok {
			po.Items = getPOItemsResp.Data.([]*model.PurchaseOrderItem)
		}

		if needCreate, wrTicket := createdWrongTicketShortage(*po); needCreate && wrTicket != nil {
			option := client.APIOption{
				Body: wrTicket,
				Keys: []string{po.POCode, "SHORTAGE"},
			}
			createWT := seller.CreateWrongTicket(option)
			if createWT.Status != common.APIStatus.Ok {
				fmt.Println("CreateWrongTicket: ", createWT.Message)
			}
		}
	}

	return resp
}

// LockPO ...
func UnLockPO(poCode string, mustInbound *bool) *common.APIResponse {

	if poCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POCode required",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		}
	}

	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: poCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]

	if mustInbound != nil && *mustInbound {
		// no remove this code: make sure update mustInbound
		model.PurchaseOrderDB.UpdateOne(
			model.PurchaseOrder{ID: poCheck.ID},
			model.PurchaseOrder{
				MustInbound: mustInbound,
			},
		)
	}

	// if already unlock, do not unlock
	valid := CheckValidPOStatus(poCheck.Status, "LOCK")
	if valid {
		return checkResp
	}
	// if current status can not unlock
	valid = CheckValidPOStatus(poCheck.Status, "UNLOCK")
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	// total ActualQuantity, total ExpectQuantity
	var totalActualQuantity, totalExpectQuantity int64 = 0, 0

	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, &primitive.M{"_id": -1})
	if poItemResp.Status == common.APIStatus.Ok {
		poItems := poItemResp.Data.([]*model.PurchaseOrderItem)

		for _, poItem := range poItems {
			totalActualQuantity += poItem.ActualQuantity
			totalExpectQuantity += poItem.ExpectQuantity
		}
	}

	t := time.Now()
	poUpdate := model.PurchaseOrder{
		Status:         enum.PurchaseOrderStatus.PARTIALLY_RECEIVED,
		LastUnlockTime: &t,
		// MustInbound:    mustInbound,
	}
	if totalActualQuantity >= totalExpectQuantity {
		poUpdate.Status = enum.PurchaseOrderStatus.RECEIVED
	}

	// change PO status
	resp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{POCode: poCode},
		poUpdate)
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	po := resp.Data.([]*model.PurchaseOrder)[0]

	// send unlock event to warehouse
	option := client.APIOption{
		Body: model.InboundTicket{
			WarehouseCode: po.WarehouseCode,
			POID:          po.POID,
			POCode:        po.POCode,
			ExternalType:  "PURCHASE_SYS",
			Status:        "WAIT_TO_ADD",
		},
		Keys: []string{po.POCode, "WAIT_TO_ADD"},
	}
	warehouseResp := warehouse.SyncStatus(option)
	if warehouseResp.Status != common.APIStatus.Ok {
		fmt.Println("SyncStatus: ", warehouseResp.Message)
	}

	return resp
}

// ProcessingSellerPO ...
func ProcessingSellerPO(poCode string, status enum.PurchaseOrderStatusValue) *common.APIResponse {
	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: poCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
	// valid := CheckValidPOStatus(poCheck.Status, enum.PurchaseOrderStatus.PROCESSING)
	valid := CheckValidPOStatus(poCheck.Status, status)
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	updateResp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: poCode,
		},
		model.PurchaseOrder{
			// Status: enum.PurchaseOrderStatus.PROCESSING,
			Status: status,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	if poCheck.Status == enum.PurchaseOrderStatus.COMPLETED {
		model.VendorBillDB.UpdateMany(model.VendorBill{
			POCode: poCheck.POCode,
			Status: enum.VendorBillStatus.APPROVED,
		}, model.VendorBill{
			IsCompletedPO:   utils.Pointer.WithBool(false),
			IsWrongQuantity: utils.Pointer.WithBool(false),
			IsWrongLotDate:  utils.Pointer.WithBool(false),
		})
	}

	return updateResp
}

// CancelSellerPO ...
func CancelSellerPO(poCode string) *common.APIResponse {
	// check valid status
	checkResp := model.PurchaseOrderDB.QueryOne(
		model.PurchaseOrder{
			POCode: poCode,
		})
	if checkResp.Status != common.APIStatus.Ok {
		return checkResp
	}
	poCheck := checkResp.Data.([]*model.PurchaseOrder)[0]
	valid := CheckValidPOStatus(poCheck.Status, enum.PurchaseOrderStatus.CANCELED)
	if !valid {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Do not switch to this status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		}
	}

	// chỉ có VB huỷ hoặc chưa có VB mới được huỷ PO
	vendorBillResp := model.VendorBillDB.Query(model.VendorBill{POCode: poCode}, 0, 1000, nil)
	if vendorBillResp.Status == common.APIStatus.Ok {
		vendorBills := vendorBillResp.Data.([]*model.VendorBill)
		for _, vb := range vendorBills {
			if vb.Status != enum.VendorBillStatus.CANCEL {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "PO này đã có Hóa đơn",
					ErrorCode: string(enum.ErrorCodeInvalid.NotCancelPOHadPayment),
				}
			}
		}
	}

	// auto-cancel transport request flow
	opts := client.APIOption{
		SaveLog: utils.Pointer.WithBool(true),
		Keys:    []string{"GET_FIRST_MILE_TICKET", poCode},
		Offset:  utils.Pointer.WithInt(0),
		Limit:   utils.Pointer.WithInt(100),
		Q: model.FirstMileTicket{
			POCode: poCode,
		},
	}
	fmTicketResp := seller.GetFirstMileTicket(opts)

	if fmTicketResp.Status == common.APIStatus.Ok {
		fmList := fmTicketResp.Data.([]*model.FirstMileTicket)
		for _, fm := range fmList {
			if fm.Status != string(enum.FirstMileTicketStatusType.CANCELLED) {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   "Phải hủy phiếu Yêu cầu vận chuyển liên quan trước khi hủy PO",
					ErrorCode: string(enum.ErrorCodeInvalid.NotAllowPOCancelFMTicket),
				}
			}
		}
	}

	// call api sync status warehouse
	if poCheck.Status == enum.PurchaseOrderStatus.CONFIRMED || poCheck.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
		// poCheck.Status == enum.PurchaseOrderStatus.PROCESSING
		poCheck.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || poCheck.Status == enum.PurchaseOrderStatus.RECEIVED {

		option := client.APIOption{
			Body: model.InboundTicket{
				WarehouseCode: poCheck.WarehouseCode,
				POID:          poCheck.POID,
				ExternalType:  "PURCHASE_SYS",
				Status:        enum.ReceiptStatus.CANCEL,
			},
			Keys: []string{poCheck.POCode, "CANCEL"},
		}
		warehouseResp := warehouse.SyncStatus(option)

		if warehouseResp.ErrorCode == "TICKET_NOT_UPDATE" || warehouseResp.ErrorCode == "TICKET_NOT_FOUND" {
			warehouseResp.Status = common.APIStatus.Ok
		}
		if warehouseResp.Status != common.APIStatus.Ok {
			return warehouseResp
		}
	}

	updateResp := model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: poCode,
		},
		model.PurchaseOrder{
			Status: enum.PurchaseOrderStatus.CANCELED,
		},
	)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	// rollback SL cho quotation
	if poCheck.Status == enum.PurchaseOrderStatus.DRAFT || poCheck.Status == enum.PurchaseOrderStatus.CONFIRMED {

		if poCheck.CreatedBySystem == string(enum.POCreateBySystem.SUPPLIER) || poCheck.CreatedBySystem == string(enum.POCreateBySystem.INTERNAL_CONFIRM_BIDDING) {
			DecreaseQuantityPoCodesToBiddingRate(poCheck.POCode)
		}

		// get po item
		poItemResp := model.PurchaseOrderItemDB.Query(
			model.PurchaseOrderItem{
				POCode: poCheck.POCode,
			},
			0, 200, nil,
		)
		if poItemResp.Status == common.APIStatus.Ok {

			poItems := poItemResp.Data.([]*model.PurchaseOrderItem)

			for _, item := range poItems {
				if item.ReferPrice == 0 {
					continue
				}

				rollBackToQuotation(poCheck, item, item.ExpectQuantity)
			}
		}

		// cập nhật lại số lượng trong bidding rate

	}

	// send Zalo
	sendZNSCancelledPO(poCheck)

	return updateResp
}

func rollBackToQuotation(po *model.PurchaseOrder, item *model.PurchaseOrderItem, decreaseQty int64) {
	if utils.HasNil(po, item) {
		return
	}
	// get history
	quotationPOHistoryResp := model.QuotationPOHistoryDB.Query(
		model.QuotationPOHistory{
			Version: utils.GetCurrentVersionDDMMYYYY(),
			POCode:  po.POCode,
			SKU:     item.SKU,
		}, 0, 1000, nil,
	)
	if quotationPOHistoryResp.Status != common.APIStatus.Ok {
		return
	}
	quotationPOHistories := quotationPOHistoryResp.Data.([]*model.QuotationPOHistory)

	// đánh dấu po item đã rollback
	for _, history := range quotationPOHistories {
		if history.POStatus == string(enum.PurchaseOrderStatus.CANCELED) || po.Status == enum.PurchaseOrderStatus.CANCELED {
			continue
		}
		model.QuotationPOHistoryDB.UpdateOne(
			model.QuotationPOHistory{ID: history.ID},
			model.QuotationPOHistory{
				POStatus: string(enum.PurchaseOrderStatus.CANCELED),
			},
		)
	}

	// check sl rollback
	if decreaseQty < 1 {
		return
	}

	// ======================================================================

	getTrackingQuotationResp := model.QuotationTrackingDB.QueryOne(model.QuotationTracking{
		WarehouseCode: item.WarehouseCode,
		Version:       utils.GetCurrentVersionDDMMYYYY(),
		SKU:           item.SKU,
	})
	if getTrackingQuotationResp.Status != common.APIStatus.Ok {
		// không có QuotationTracking thì không cho rollback
		return
	}

	oldTrackingQuotation := getTrackingQuotationResp.Data.([]*model.QuotationTracking)[0]
	if oldTrackingQuotation.TotalQuantityConfirmed == nil {
		oldTrackingQuotation.TotalQuantityConfirmed = utils.Pointer.WithInt64(0)
	}
	newConfirmedQty := *oldTrackingQuotation.TotalQuantityConfirmed - decreaseQty

	//
	model.QuotationTrackingDB.UpdateOne(model.QuotationTracking{
		Version:       utils.GetCurrentVersionDDMMYYYY(),
		WarehouseCode: item.WarehouseCode,
		SKU:           item.SKU,
	}, model.QuotationTracking{
		TotalQuantityConfirmed: &newConfirmedQty,
	})

	// still enough qty
	// trừ số lượng confirmed ở tracking nhưng không đẩy ra bidding lại
	// có thể handle case doub dòng SP này ở PO khi loop đến dòng tiếp theo sẽ có số confirm mới so sánh
	if newConfirmedQty >= oldTrackingQuotation.TotalQuantityExpect {
		return
	}

	// ======================================================================

	newRollbackQty := oldTrackingQuotation.TotalQuantityExpect - newConfirmedQty // newConfirmedQty = *oldTrackingQuotation.QuantityConfirmed - decreaseQty
	// check sl rollback
	if newRollbackQty < 1 {
		return
	}

	// tạo lại quotation
	// quotationSample := quotationPOHistories[0]
	SellerCreateQuotations(
		CreateQuotationsInput{
			Mode: "OVERRITE",
			Items: []model.Quotation{
				{
					SellerCode:    item.SellerCode,
					WarehouseCode: item.WarehouseCode,

					ProductName: item.ProductName,
					ProductID:   item.ProductID,
					ProductCode: item.ProductCode,
					SKU:         item.SKU,

					Unit: item.Unit,

					QuantityExpect: utils.Pointer.WithInt64(newRollbackQty),
				},
			},
		},
		item.SellerCode,
		true,
		true,
	)
}

func updateLeadtimeInPO(input model.InboundCallback) {
	if input.POCode == "" {
		return
	}

	poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
	if poResp.Status == common.APIStatus.Ok {
		po := poResp.Data.([]*model.PurchaseOrder)[0]
		if po.FirstInboundTime != nil {
			return
		}
		var leadtime *float64
		// kiem tra nil cua CreatedTime and CheckInTime
		if po.CreatedTime != nil && input.CheckInTime != nil {
			// lay thoi gian created cua PO
			firstInTime := *po.CreatedTime
			checkInTime := input.CheckInTime
			tempLeadTime := float64(checkInTime.Sub(firstInTime).Hours())
			leadtime = &tempLeadTime
		}
		model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{
			POCode: input.POCode,
		},
			model.PurchaseOrder{
				Leadtime:         leadtime,
				FirstInboundTime: input.CheckInTime,
				// enum.PurchaseOrderStatus.PROCESSING,
				Status: enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
			},
		)

	}
}

func updateActualPOItems(input model.InboundCallback) {
	if input.POCode == "" || len(input.InboundCallbackItems) == 0 {
		return
	}
	inoundItems := make([]model.InboundCallbackItem, len(input.InboundCallbackItems))
	copy(inoundItems, input.InboundCallbackItems)

	t := time.Now()
	overFulfill := false

	// total ActualQuantity, total ExpectQuantity
	var totalActualQuantity, totalExpectQuantity int64 = 0, 0

	// #2 cập nhật actual cho PO item
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: input.POCode}, 0, 1000, &primitive.M{"_id": -1})
	if poItemResp.Status == common.APIStatus.Ok {
		poItems := poItemResp.Data.([]*model.PurchaseOrderItem)
		// put map
		mapPOItem := make(map[int64][]model.PurchaseOrderItem)
		for i := range poItems {
			poItem := poItems[i]
			mapPOItem[poItem.ProductID] = append(mapPOItem[poItem.ProductID], *poItem)
		}

		// loop productID
		for productID := range mapPOItem {
			poMapItems := mapPOItem[productID]

			for i := range poMapItems {
				poItem := poMapItems[i]
				oldActual := poItem.ActualQuantity

				// nếu đủ rồi thì không cần fill
				if poItem.ExpectQuantity <= poItem.ActualQuantity &&
					i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
					continue
				}

				// loop InboundCallbackItems
				for j, callbackItem := range inoundItems {
					// không quan tâm line
					if poItem.ProductID != callbackItem.ProductID {
						continue
					}
					if callbackItem.DoneQuantity <= 0 {
						continue
					}

					// tính actual mới
					newActual := poItem.ActualQuantity + callbackItem.DoneQuantity
					if newActual > poItem.ExpectQuantity &&
						i != len(poMapItems)-1 { // nếu dư thì cho fill dư vào poItem cũ nhất
						newActual = poItem.ExpectQuantity
					}

					// cập nhật lại InboundCallbackItems
					callbackItem.DoneQuantity -= (newActual - poItem.ActualQuantity)
					if callbackItem.DoneQuantity < 0 {
						continue
					}
					inoundItems[j] = callbackItem
					// cập nhật lại mapPOItem
					poItem.ActualQuantity = newActual
					mapPOItem[productID][i] = poItem
				}
				if poItem.ActualQuantity > poItem.ExpectQuantity {
					overFulfill = true
				}

				totalActualQuantity += poItem.ActualQuantity
				totalExpectQuantity += poItem.ExpectQuantity

				// do action: update ActualQuantity PurchaseOrderItem
				if oldActual != poItem.ActualQuantity {
					// sum when update quantity poItem
					model.PurchaseOrderItemDB.UpdateOne(
						model.PurchaseOrderItem{ID: poItem.ID},
						model.PurchaseOrderItem{
							ActualQuantity: poItem.ActualQuantity,
						},
					)
				}
			}
		}
	}

	poUpdate := model.PurchaseOrder{
		// enum.PurchaseOrderStatus.PROCESSING,
		LastInboundTime: &t,
		OverFulfill:     &overFulfill,
		MustInbound:     utils.Pointer.WithBool(false),
	}

	// update fulfillmentPercent PO
	poRes := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
	if poRes.Status == common.APIStatus.Ok {
		po := poRes.Data.([]*model.PurchaseOrder)[0]
		if totalExpectQuantity != 0 {
			newFulfill := (float64(totalActualQuantity) / float64(totalExpectQuantity)) * 100
			if newFulfill > 100 {
				newFulfill = 100
			}
			poUpdate.FulfillmentPercent = &newFulfill
		}

		// enum.PurchaseOrderStatus.PROCESSING
		poUpdate.Status = po.Status
		if po.Status == enum.PurchaseOrderStatus.CONFIRMED || po.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED {
			poUpdate.Status = enum.PurchaseOrderStatus.PARTIALLY_RECEIVED
		}
		if poUpdate.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED && totalActualQuantity >= totalExpectQuantity {
			poUpdate.Status = enum.PurchaseOrderStatus.RECEIVED
		}
	}

	// warmup wrong ticket shortage
	go sdk.Execute(func() {
		option := client.APIOption{
			Body: model.WrongTicket{
				POCodes: []string{input.POCode},
			},
			Keys: []string{input.POCode, "SHORTAGE"},
		}
		seller.WarmupWrongTicketShortage(option)
	})

	// // #3 cập nhật status PROCESSING/FirstInboundTime/LastInboundTime
	// // update FirstInboundTime PO
	// updateFirstTimeResp := model.PurchaseOrderDB.UpdateOne(
	// 	model.PurchaseOrder{
	// 		POCode: input.POCode,
	// 		ComplexQuery: []*bson.M{
	// 			{
	// 				"status": bson.M{
	// 					"$in": []enum.PurchaseOrderStatusValue{
	// 						enum.PurchaseOrderStatus.CONFIRMED,
	// 						// enum.PurchaseOrderStatus.SENT_PO,
	// 					},
	// 				},
	// 			},
	// 		},
	// 	},
	// 	model.PurchaseOrder{
	// 		Status:           enum.PurchaseOrderStatus.PROCESSING,
	// 		FirstInboundTime: &t,
	// 		LastInboundTime:  &t,
	// 	},
	// )
	// // update LastInboundTime PO
	// if updateFirstTimeResp.Status == common.APIStatus.NotFound {
	model.PurchaseOrderDB.UpdateOne(
		model.PurchaseOrder{
			POCode: input.POCode,
		},
		poUpdate,
	)
	// }
}

func VendorUpdatePO(input model.PurchaseOrder, vendor *model.Seller) *common.APIResponse {
	// Check vendor code
	if input.VendorCode != vendor.Code {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Cannot edit PO of other vendor",
			ErrorCode: string(enum.ErrorCodeInvalid.NotAllowEditPOOtherVendor),
		}
	}

	checkPurchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
		POCode: input.POCode,
	})
	if checkPurchaseOrderResp.Status != common.APIStatus.Ok {
		return checkPurchaseOrderResp
	}
	checkPurchaseOrder := checkPurchaseOrderResp.Data.([]*model.PurchaseOrder)[0]

	updater := model.PurchaseOrder{
		EstimatedDeliveryTime: input.EstimatedDeliveryTime,
		//...
	}

	if updater.EstimatedDeliveryTime != nil {

		tempEstimated := utils.GetLastTimeOfDate(*updater.EstimatedDeliveryTime)
		updater.EstimatedDeliveryTime = &tempEstimated

		// Get default filed estimatedDeliveryTime base on vendor info
		vendorEstimatedDeliveryTime := utils.GetLastTimeOfDate(*checkPurchaseOrder.CreatedTime)
		for _, leadtime := range vendor.InboundAddresses {
			if leadtime.WarehouseCode == input.DeliveryWarehouseCode {
				vendorEstimatedDeliveryTime = checkPurchaseOrder.CreatedTime.Add(time.Duration(int64(math.Ceil(leadtime.Leadtime*24))) * time.Hour)
				vendorEstimatedDeliveryTime = utils.GetLastTimeOfDate(vendorEstimatedDeliveryTime)
				break
			}
		}

		if vendorEstimatedDeliveryTime.Before(*updater.EstimatedDeliveryTime) {
			updater.RequireConfirm = &enum.RequireConfirm.CHANGE_ESTIMATED_DELIVERY
		} else {
			updater.RequireConfirm = &enum.RequireConfirm.BLANK
		}
	}

	return model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{
		POCode: input.POCode,
	}, updater)
}

// hàm tính giá PO dửa trên PO_items
func calculatePricePO(input *model.PurchaseOrder) *model.PurchaseOrder {
	var tempTotalWithoutVatPrice float64 = 0
	var tempTotalVatPrice float64 = 0
	var tempTotalPrice float64 = 0

	// poItem includes: UnitPrice, DiscountPercent, VAT, ExpectQuantity
	for _, poItem := range input.Items {

		if !utils.IsContains(input.SKUs, poItem.SKU) {
			input.SKUs = append(input.SKUs, poItem.SKU)
		}

		if poItem.UnitPrice == 0 {
			continue
		}

		// Discount price
		var discountPriceTemp float64 = 0
		if poItem.DiscountPercent != nil && *poItem.DiscountPercent > 0 {
			discountPriceTemp = poItem.UnitPrice * *poItem.DiscountPercent / 100
		}
		poItem.DiscountPrice = &discountPriceTemp

		// price after discount
		var tempPriceAfterDiscount float64 = 0
		tempPriceAfterDiscount = poItem.UnitPrice - *poItem.DiscountPrice
		poItem.PriceAfterDiscount = &tempPriceAfterDiscount

		// vat price
		var tempVATPrice float64 = 0
		if poItem.VAT != nil && *poItem.VAT > 0 {
			tempVATPrice = *poItem.PriceAfterDiscount * *poItem.VAT / 100
		}
		poItem.VATPrice = &tempVATPrice

		// Total price item
		tempTotalPriceItem := *poItem.PriceAfterDiscount*float64(poItem.ExpectQuantity) + *poItem.VATPrice*float64(poItem.ExpectQuantity)
		poItem.TotalPrice = &tempTotalPriceItem

		// PO Total
		tempTotalWithoutVatPrice += *poItem.PriceAfterDiscount * float64(poItem.ExpectQuantity)
		tempTotalVatPrice += *poItem.VATPrice * float64(poItem.ExpectQuantity)
	}
	tempTotalPrice = tempTotalWithoutVatPrice + tempTotalVatPrice

	input.TotalWithoutVatPrice = &tempTotalWithoutVatPrice
	input.TotalVatPrice = &tempTotalVatPrice
	input.TotalPrice = &tempTotalPrice

	return input
}

// Kiểm tra dòng mới có sku nào bị tắt mua hàng không
func checkIsActiveSKU(sellerCode, purchaserCode string, items []*model.PurchaseOrderItem, status enum.PurchaseOrderStatusValue) *common.APIResponse {
	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return response
	}

	var productIDs []int64
	var productIDErrors []string

	for _, item := range items {

		// nếu là SP tặng thì không check isGift
		if item.IsGift != nil && *item.IsGift == true {
			continue
		}

		// chỉ check những dòng mới
		if item.ProductID == 0 || item.ActualQuantity != 0 || (status != enum.PurchaseOrderStatus.DRAFT && item.POItemID != 0) {
			continue
		}

		productIDs = append(productIDs, item.ProductID)
	}

	skuConfigResp := model.SkuConfigDB.Query(model.SkuConfig{
		SellerCode:    sellerCode,
		PurchaserCode: purchaserCode,
		ComplexQuery: []*bson.M{
			{"product_id": bson.M{"$in": productIDs}},
		},
	}, 0, 1000, nil)

	if skuConfigResp.Status == common.APIStatus.Error {
		return skuConfigResp
	}
	if skuConfigResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Check sku active success",
		}
	}

	skuConfigs := skuConfigResp.Data.([]*model.SkuConfig)

	for _, skuConfig := range skuConfigs {
		if !utils.IsNil(skuConfig.IsActive) && !*skuConfig.IsActive {
			productIDErrors = append(productIDErrors, fmt.Sprintf("%v", skuConfig.ProductID))
		}
	}

	if len(productIDErrors) > 0 {
		idString := strings.Join(productIDErrors, ", ")

		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("Products %v are inactivated, cannot be added to PO", idString),
			ErrorCode: string(enum.ErrorCodeInvalid.NotAllowInActiveSKU),
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Check sku active success",
	}
}

func NotifyVendorPO(poCode string) *common.APIResponse {
	// query PO
	poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{
		POCode: poCode,
	})
	if poResp.Status != common.APIStatus.Ok {
		return poResp
	}
	po := poResp.Data.([]*model.PurchaseOrder)[0]

	if po.Status != enum.PurchaseOrderStatus.COMPLETED {
		return &common.APIResponse{
			Status:    common.APIStatus.Ok,
			Message:   "Notification can only be sent when PO completed",
			ErrorCode: string(enum.ErrorCodeInvalid.OnlyNotifyPOCompleted),
		}
	}

	now := utils.GetVietnamTimeNow()
	expired := now.AddDate(0, 1, 0)
	checkLockAcitonresp := CreateLockActionsWithExpiredTime("NOTIFY_VENDOR_WRONG_LOT_DATE", poCode, []string{po.VendorCode}, &expired)
	if checkLockAcitonresp.Status != common.APIStatus.Ok {
		checkLockAcitonresp.Message = "Notification can only be sent once for each PO"
		checkLockAcitonresp.ErrorCode = string(enum.ErrorCodeInvalid.OnlyNotifyOnceEachPO)
		return checkLockAcitonresp
	}

	createNotiResp := CreateNotificationVendor(po.VendorCode, model.Notification{
		Title: fmt.Sprintf("[Sai lệch lot-date] Lot-date sản phẩm trên hóa đơn của mã %v đang sai lệch với lot-date thực tế Thuocsi nhận.", po.POCode),
		Topic: "ANNOUNCEMENT",
		Link:  fmt.Sprintf("/order/detail?poCode=%v", po.POCode),
		// Description: "Lot-date trên hóa đơn sai lệch với lot-date thực tế Thuocsi nhận. Xem chi tiết tại đây...",
	})

	return createNotiResp
}

func ClonePurchaserOrder(poCode string, acc *model.Account) *common.APIResponse {
	purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if purchaseOrderResp.Status != common.APIStatus.Ok {
		return purchaseOrderResp
	}
	purchaseOrder := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]

	purchaseOrder.ID = nil
	purchaseOrder.CreatedTime = nil
	purchaseOrder.LastUpdatedTime = nil
	purchaseOrder.POID = 0
	purchaseOrder.POCode = ""

	now := time.Now()
	purchaseOrder.IssuedTime = &now
	purchaseOrder.PlanningTime = &now
	purchaseOrder.SourceDocument = nil
	purchaseOrder.Type = ""
	// purchaseOrder.OriginalPOs = &[]string{poCode}

	purchaseOrder.CreatedByID = 0
	purchaseOrder.CreatedByName = ""
	purchaseOrder.CreatedBySystem = "INTERNAL_MEDX"
	purchaseOrder.FirstInboundTime = nil
	purchaseOrder.LastInboundTime = nil
	purchaseOrder.EstimatedDeliveryTime = nil
	purchaseOrder.RequireConfirm = nil
	purchaseOrder.LastUnlockTime = nil

	purchaseOrder.Status = enum.PurchaseOrderStatus.DRAFT

	purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, nil)
	if purchaseOrderItemResp.Status != common.APIStatus.Ok {
		return purchaseOrderItemResp
	}

	var newListPOItem []*model.PurchaseOrderItem
	listPOItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)
	for _, purchaseOrderItem := range listPOItems {
		purchaseOrderItem.ID = nil
		purchaseOrderItem.CreatedTime = nil
		purchaseOrderItem.LastUpdatedTime = nil
		purchaseOrderItem.POItemID = 0
		purchaseOrderItem.ReferPrice = 0
		purchaseOrderItem.ActualQuantity = 0
		purchaseOrderItem.BillQuantity = nil

		purchaseOrderItem.InboundRequestCode = ""
		purchaseOrderItem.InboundRequestID = 0
		purchaseOrderItem.InboundRequestItemCode = ""

		purchaseOrderItem.WrongTicketID = 0
		purchaseOrderItem.WrongTicketCode = ""
		purchaseOrderItem.WrongTicketItemCode = ""
		purchaseOrderItem.WrongTicketItemID = 0

		purchaseOrderItem.WHOldSKU = ""
		purchaseOrderItem.WHErrorID = 0

		newListPOItem = append(newListPOItem, purchaseOrderItem)
	}

	purchaseOrder.Items = newListPOItem
	return CreatePurchaseOrder(*purchaseOrder, acc)
}

func DeletePromotionPO(poCode string) *common.APIResponse {
	// get PO
	purchaseOrderResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if purchaseOrderResp.Status != common.APIStatus.Ok {
		return purchaseOrderResp
	}
	poData := purchaseOrderResp.Data.([]*model.PurchaseOrder)[0]

	if !utils.IsContains(
		[]string{
			string(enum.PurchaseOrderStatus.DRAFT),
			string(enum.PurchaseOrderStatus.CONFIRMED),
			string(enum.PurchaseOrderStatus.HANDOVER_COMPLETED),
			// string(enum.PurchaseOrderStatus.PROCESSING),
			string(enum.PurchaseOrderStatus.PARTIALLY_RECEIVED), string(enum.PurchaseOrderStatus.RECEIVED),
		},
		string(poData.Status)) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   fmt.Sprintf("po status %q is not valid to clear promotion", poData.Status),
			ErrorCode: string(enum.ErrorCodeError.UpdatePO),
		}
	}

	// get PO item
	POItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: poCode}, 0, 1000, nil)
	if POItemsResp.Status != common.APIStatus.Ok {
		return POItemsResp
	}
	items := POItemsResp.Data.([]*model.PurchaseOrderItem)

	// xóa promotion ra khỏi item
	var ZERO float64 = 0.0
	newListItems := []*model.PurchaseOrderItem{}
	for _, item := range items {
		deleteThisItem := false
		if item.Metadata != nil && len(item.Metadata.ApplyPromotions) > 0 {

			// bỏ discount
			item.DiscountPercent = &ZERO

			// bỏ gift
			for _, promotion := range item.Metadata.ApplyPromotions {
				if promotion.Type == enum.VendorPromotionType.GIFT || promotion.Type == enum.VendorPromotionType.GIFT_BY_LEVEL {
					deleteThisItem = true
				}
			}

		}
		if !deleteThisItem {
			item.Metadata = &model.POItemMetadata{}
			newListItems = append(newListItems, item)
		}
	}
	poData.Items = newListItems

	// tính giá tiền
	newPO := *calculatePricePO(poData)

	// xóa promotion ra khỏi PO
	if newPO.Metadata != nil {
		newPO.Metadata.ApplyPromotionCodes = []string{}
	}

	// update PO
	updatePOResp := model.PurchaseOrderDB.UpdateOne(model.PurchaseOrder{POCode: poCode}, newPO)
	if updatePOResp.Status != common.APIStatus.Ok {
		return updatePOResp
	}

	// delete all po items
	deleteResp := model.PurchaseOrderItemDB.Delete(model.PurchaseOrderItem{POCode: poCode})
	if deleteResp.Status != common.APIStatus.Ok {
		return deleteResp
	}

	// create po_items
	createPOItemsResp := model.PurchaseOrderItemDB.CreateMany(newPO.Items)
	if createPOItemsResp.Status != common.APIStatus.Ok {
		return createPOItemsResp
	}

	updatePOResp.Data = []model.PurchaseOrder{newPO}

	// sync data warehouse
	if newPO.Status == enum.PurchaseOrderStatus.CONFIRMED ||
		newPO.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
		// newPO.Status == enum.PurchaseOrderStatus.PROCESSING ||
		newPO.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || newPO.Status == enum.PurchaseOrderStatus.RECEIVED ||
		newPO.Status == enum.PurchaseOrderStatus.COMPLETED ||
		newPO.Status == enum.PurchaseOrderStatus.AWAITING_BILLED || newPO.Status == enum.PurchaseOrderStatus.BILLED {

		// Tạo receipt qua wh
		createReceptResp := CreateReceptToWarehouse(&newPO)
		if createReceptResp.Status != common.APIStatus.Ok {
			return createReceptResp
		}
		// delay 1s để đảm bảo data wms đã được update
		time.Sleep(1 * time.Second)
	}

	return updatePOResp
}

func ResetActualPO(poCodes []string) *common.APIResponse {
	if len(poCodes) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "po code is required",
		}
	}

	listPOErr := []string{}

	type UpdateActualPOItem struct {
		ActualQuantity   *int64 `bson:"actual_quantity,omitempty"`
		IsResetActualQty bool   `bson:"is_reset_actual_qty,omitempty"`
	}

	listValidPO := []string{}

	for _, poCode := range poCodes {
		if len(poCode) == 0 {
			continue
		}
		// do reset actual po item
		var (
			updater      = UpdateActualPOItem{ActualQuantity: utils.Pointer.WithInt64(0), IsResetActualQty: true} // set 0
			filterPOItem = model.PurchaseOrderItem{POCode: poCode}
		)
		getListPOItem := model.PurchaseOrderItemDB.UpdateMany(filterPOItem, updater)
		if getListPOItem.Status != common.APIStatus.Ok {
			listPOErr = append(listPOErr, poCode)
			continue
		}

		// delete inbound callback store

		filterInboundCallbackStore := model.InboundCallbackStore{POCode: poCode}

		deleteCallbackStoreResp := DeleteInboundCallbackStore(&filterInboundCallbackStore)

		if deleteCallbackStoreResp.Status != common.APIStatus.Ok {
			listPOErr = append(listPOErr, poCode)
			continue
		}

		// success thì add valid
		listValidPO = append(listValidPO, poCode)
	}

	mapResp := map[string][]string{
		"Error":   listPOErr,
		"Success": listValidPO,
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Reset actual quantity po success",
		Data:    []map[string][]string{mapResp},
	}
}

func GenHashTagPOFromInfo(po *model.PurchaseOrder, vendor *model.Seller) string {
	sellerIDStr := ""
	if vendor != nil {
		sellerIDStr = strconv.FormatInt(vendor.SellerID, 10)
	}
	return strings.Replace(utils.NormalizeString(sellerIDStr+" "+po.VendorName), " ", "-", -1)
}

func UpdatePOTag(input *model.PurchaseOrder) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "input is required",
		}
	}

	if len(input.POCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "po code is required",
		}
	}
	poCheckResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{POCode: input.POCode}, 0, 1, nil)
	if poCheckResp.Status != common.APIStatus.Ok {
		return poCheckResp
	}
	poCheck := poCheckResp.Data.([]*model.PurchaseOrder)[0]

	switch poCheck.Status {
	case enum.PurchaseOrderStatus.DRAFT:
		// cập nhật cho confirmd bắn qua kho
		updatePOTagResp := model.PurchaseOrderDB.UpdateOne(
			model.PurchaseOrder{POCode: input.POCode},
			model.PurchaseOrder{Tags: input.Tags})
		return updatePOTagResp
	case enum.PurchaseOrderStatus.CONFIRMED, enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
		// enum.PurchaseOrderStatus.PROCESSING:
		enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED:
		updatePOTagResp := model.PurchaseOrderDB.UpdateOne(
			model.PurchaseOrder{POCode: input.POCode},
			model.PurchaseOrder{Tags: input.Tags})
		if updatePOTagResp.Status != common.APIStatus.Ok {
			return updatePOTagResp
		}
		// bắn tag qua kho

		poUpdated := updatePOTagResp.Data.([]*model.PurchaseOrder)[0]
		return CreateReceptToWarehouse(poUpdated)

	default:
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "po status is invalid",
		}
	}
}

func validSellerType(sellerCode, vendorCode string) *common.APIResponse {
	settingResp := model.SettingDB.Query(model.Setting{}, 0, 1, &primitive.M{"_id": -1})
	if settingResp.Status != common.APIStatus.Ok {
		return settingResp
	}
	setting := settingResp.Data.([]*model.Setting)[0]

	if setting.MapTradingNonTradingSellers == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Setting trading/non-trading respective to seller type not found",
		}
	}

	sellerTypes := []string{}
	for sellerType, sellerCodes := range setting.MapTradingNonTradingSellers {
		for _, code := range sellerCodes {
			if code == sellerCode {
				sellerTypes = append(sellerTypes, sellerType)
			}
		}
	}

	if len(sellerTypes) <= 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "The internal seller hasn't specified the seller type as trading/non-trading yet",
			ErrorCode: string(enum.ErrorCodeInvalid.InvalidSettingSellerType),
		}
	}

	allSellerTypes := len(setting.MapTradingNonTradingSellers)
	if len(sellerTypes) == allSellerTypes {
		return &common.APIResponse{Status: common.APIStatus.Ok}
	}

	vendorResp := seller.GetSellers(client.APIOption{
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
		},
		Params: map[string]string{
			"sellerCodes": vendorCode,
		},
	})
	if vendorResp.Status != common.APIStatus.Ok {
		return vendorResp
	}
	vendor := vendorResp.Data.([]*model.Seller)[0]

	vendorType := vendor.SellerType
	if vendorType == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Vendor type not found",
		}
	}

	sellerType := sellerTypes[0]
	if sellerType != string(vendorType) {
		switch sellerType {
		case string(enum.SellerTypeValue.TRADING):
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Only allow creating PO to purchase from trading vendors for the trading internal seller",
				ErrorCode: string(enum.ErrorCodeInvalid.InvalidTradingSeller),
			}

		case string(enum.SellerTypeValue.NON_TRADING):
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Only allow creating PO to purchase from non-trading vendors for the non-trading internal seller",
				ErrorCode: string(enum.ErrorCodeInvalid.InvalidNonTradingSeller),
			}
		}
	}

	return &common.APIResponse{Status: common.APIStatus.Ok}
}

func ToolUpdatePurchaserOrderItem(input *model.PurchaseOrderItem) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "input is required",
		}
	}

	if len(input.POCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "po item id is required",
		}
	}

	// get PO item
	poItemResp := model.PurchaseOrderItemDB.QueryOne(model.PurchaseOrderItem{
		POCode:    input.POCode,
		ProductID: input.ProductID,
	})
	if poItemResp.Status != common.APIStatus.Ok {
		return poItemResp
	}
	poItem := poItemResp.Data.([]*model.PurchaseOrderItem)[0]

	updater := bson.M{
		"expect_quantity": input.ExpectQuantity,
	}

	if input.BillQuantity != nil {
		updater["bill_quantity"] = *input.BillQuantity
	}

	// update PO item
	// updatePOResp := model.PurchaseOrderItemDB.UpdateOneWithOption(
	// 	model.PurchaseOrderItem{POCode: poItem.POCode, ProductID: poItem.ProductID},
	// 	bson.M{"$set": updater},
	// )
	//
	updatePOResp := model.PurchaseOrderItemDB.UpdateOne(model.PurchaseOrderItem{POCode: poItem.POCode, ProductID: poItem.ProductID}, input)

	if updatePOResp.Status != common.APIStatus.Ok {
		return updatePOResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Update po item success",
	}
}

// AppendPurchaseOrderItems ...
func AppendPurchaseOrderItems(input model.PurchaseOrder) *common.APIResponse {
	if input.POCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "PO code is required",
			ErrorCode: "INVALID_PO_CODE",
		}
	}

	// get PO
	poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
	if poResp.Status != common.APIStatus.Ok {
		return poResp
	}
	po := poResp.Data.([]*model.PurchaseOrder)[0]
	warehouseCode := po.WarehouseCode
	vendorCode := po.VendorCode
	// 1. Add existed PO items to input.Items

	// khởi tạo  map string -> WrongTicketItem
	mapWrongTicketItems := make(map[string]*model.WrongTicketItem)

	if len(input.Items) == 0 {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Items is required",
			ErrorCode: "INVALID_ITEMS",
		}
	}
	for i := range input.Items {
		item := input.Items[i]

		// validate
		if item.ProductID == 0 || item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Product ID, SKU is required",
				Data:      []*model.PurchaseOrderItem{item},
				ErrorCode: "INVALID_PRODUCT_ID_OR_SKU",
			}
		}
		if item.ExpectQuantity == 0 {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "ExpectQuantity is required",
				Data:      []*model.PurchaseOrderItem{item},
				ErrorCode: "INVALID_EXPECT_QUANTITY",
			}
		}

		// no-required
		if item.WrongTicketItemCode != "" {

			opts := client.APIOption{
				SaveLog: utils.Pointer.WithBool(false),
				Offset:  utils.Pointer.WithInt(0),
				Limit:   utils.Pointer.WithInt(1),
				Q: model.WrongTicketItem{
					WrongTicketItemCode: item.WrongTicketItemCode,
				},
			}
			wrongTicketItemResp := seller.GetWrongTicketItem(opts)
			if wrongTicketItemResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    wrongTicketItemResp.Status,
					Message:   wrongTicketItemResp.Message,
					ErrorCode: "WRONG_TICKET_ITEM_NOT_FOUND",
				}
			}
			wrongTicketItem := wrongTicketItemResp.Data.([]*model.WrongTicketItem)[0]

			// unknown product
			if wrongTicketItem.IsUnknownProduct {
				item.WHOldSKU = wrongTicketItem.UnknownSKU // MEDX.UNKNOWN_PO686818_4
			}
			item.WHErrorID = wrongTicketItem.ErrorId

			// issue line đã được thêm vào PO thì không được thêm tiếp
			if wrongTicketItem.PurchaseOrderCode != "" {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Wrong ticket item is added to PO. ProductID %v", item.ProductID),
					Data:      []*model.PurchaseOrderItem{item},
					ErrorCode: "WRONG_TICKET_ITEM_ADDED_TO_PO",
				}
			}
			if item.IsGift != nil && *item.IsGift {
				wrongTicketItem.IsGift = true
			}
			mapWrongTicketItems[item.WrongTicketItemCode] = wrongTicketItem
		}
	}

	// select right item from old PO
	for i := range input.Items {
		inputItem := input.Items[i]

		oldItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, nil)
		if oldItemResp.Status != common.APIStatus.Ok {
			return &common.APIResponse{
				Status:    oldItemResp.Status,
				Message:   oldItemResp.Message,
				ErrorCode: "PO_ITEM_NOT_FOUND",
			}
		}
		oldItems := oldItemResp.Data.([]*model.PurchaseOrderItem)

		newItem := &model.PurchaseOrderItem{
			ExpectQuantity:      inputItem.ExpectQuantity,
			ProductID:           inputItem.ProductID,
			SKU:                 inputItem.SKU,
			WrongTicketItemCode: inputItem.WrongTicketItemCode,
			WrongTicketItemID:   inputItem.WrongTicketItemID,

			// ProductCode:
			// ProductName:
			// VAT:
			// Unit:
			// UnitPrice:
			// PriceAfterRebate:

			WHOldSKU:  inputItem.WHOldSKU,
			WHErrorID: inputItem.WHErrorID,
			IsGift:    inputItem.IsGift,
		}

		// add wrong ticket code to po item
		if wrongTicket, isOk := mapWrongTicketItems[inputItem.WrongTicketItemCode]; isOk && wrongTicket != nil {
			newItem.WrongTicketCode = wrongTicket.WrongTicketCode
			newItem.WrongTicketID = wrongTicket.WrongTicketID
		}

		var itemPOWithPrice *model.PurchaseOrderItem
		var itemPOAny *model.PurchaseOrderItem

		for i := range oldItems {
			oldItem := oldItems[i]

			if oldItem.SKU != inputItem.SKU {
				continue
			}

			itemPOAny = oldItem
			if oldItem.UnitPrice > 0 {
				itemPOWithPrice = oldItem
			}
		}

		// GIFT
		existOnPO := false
		if inputItem.IsGift != nil && *inputItem.IsGift {
			if itemPOAny != nil {
				existOnPO = true

				newItem.ProductCode = itemPOAny.ProductCode
				newItem.ProductName = itemPOAny.ProductName
				newItem.VAT = itemPOAny.VAT
				newItem.Unit = itemPOAny.Unit
				newItem.UnitPrice = 0
				newItem.PriceAfterRebate = itemPOAny.PriceAfterRebate
			}

			// Purchase
		} else {
			if itemPOWithPrice != nil {
				existOnPO = true

				newItem.ProductCode = itemPOWithPrice.ProductCode
				newItem.ProductName = itemPOWithPrice.ProductName
				newItem.VAT = itemPOWithPrice.VAT
				newItem.Unit = itemPOWithPrice.Unit
				newItem.UnitPrice = itemPOWithPrice.UnitPrice
				newItem.PriceAfterRebate = itemPOWithPrice.PriceAfterRebate

				newItem.DiscountPercent = itemPOWithPrice.DiscountPercent
				newItem.DiscountPrice = itemPOWithPrice.DiscountPrice
			}
		}

		if !existOnPO {

			// get contract price
			contractPriceItemValid := GetContractPriceItemValid(inputItem.ProductID, warehouseCode, vendorCode, *po.CreatedTime)
			if contractPriceItemValid.Status != common.APIStatus.Ok {
				contractPriceItemValid.ErrorCode = "PRODUCT_NOT_FOUND_CONTRACT_PRICE"
				contractPriceItemValid.Data = []*model.PurchaseOrderItem{inputItem}
				contractPriceItemValid.Message = fmt.Sprintf("Product ID %v: not found contract price", inputItem.ProductID)
				return contractPriceItemValid
			}
			contractItem := contractPriceItemValid.Data.([]*model.ContractPriceItem)[0]

			existOnPO = true

			if contractItem.VAT == 0 {
				return &common.APIResponse{
					Status:    common.APIStatus.Invalid,
					Message:   fmt.Sprintf("Product ID %v: VAT = 0", inputItem.ProductID),
					ErrorCode: "PRODUCT_VAT_IS_ZERO",
				}
			}

			// get product
			productResp := marketplace.GetSingleProduct(
				client.APIOption{
					Q: model.Product{ProductID: inputItem.ProductID},
				},
			)

			if productResp.Status != common.APIStatus.Ok {
				return &common.APIResponse{
					Status:    productResp.Status,
					Message:   productResp.Message,
					ErrorCode: "PRODUCT_NOT_FOUND",
				}
			}

			product := productResp.Data.([]*model.Product)[0]

			newItem.ProductCode = product.Code
			newItem.ProductName = product.Name
			newItem.VAT = &contractItem.VAT
			newItem.Unit = product.Unit
			newItem.UnitPrice = contractItem.UnitPrice

			newItem.DiscountPercent = &contractItem.Discount

			if inputItem.IsGift != nil && *inputItem.IsGift {
				newItem.UnitPrice = 0
				newItem.DiscountPercent = nil
				newItem.DiscountPrice = nil
			}
		}

		if !existOnPO {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("Product ID %v: not found old PO item", inputItem.ProductID),
				Data:      []*model.PurchaseOrderItem{inputItem},
				ErrorCode: "PRODUCT_NOT_FOUND_CONTRACT_PRICE",
			}
		}

		input.Items[i] = newItem
	}

	// get Existed PO
	poItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, nil)
	if poItemResp.Status != common.APIStatus.Ok {
		return poItemResp
	}
	existedPOItems := poItemResp.Data.([]*model.PurchaseOrderItem)

	// 2. Call function unlock PO
	UnLockPO(po.POCode, input.MustInbound)
	po.MustInbound = nil

	// 3. Call Function Update PO to update PO
	po.Items = append(existedPOItems, input.Items...)
	updatePOResp := UpdatePurchaseOrder(*po, false)
	if updatePOResp.Status != common.APIStatus.Ok {
		response := &common.APIResponse{
			Status:    updatePOResp.Status,
			Message:   updatePOResp.Message,
			ErrorCode: updatePOResp.ErrorCode,
		}
		if updatePOResp.ErrorCode == "" {
			response.ErrorCode = "UPDATE_PO_FAILED"
		}

		return response
	}

	// 4. if new PO items have wrong ticket item code handle logic update wrong ticket item status
	for _, wrongTicketItem := range mapWrongTicketItems {

		option := client.APIOption{
			Body: model.WrongTicketItem{
				WrongTicketItemCode: wrongTicketItem.WrongTicketItemCode,
				WrongTicketCode:     wrongTicketItem.WrongTicketCode,
				PurchaseOrderCode:   po.POCode,
				Finish:              true,
				IsGift:              wrongTicketItem.IsGift,
			},
		}
		updateResp := seller.UpdateWrongTicketItem(option)
		if updateResp.Status != common.APIStatus.Ok {
			fmt.Println("update wrong ticket item failed: ", updateResp.Message)
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Append PO items success",
		Data:    updatePOResp.Data,
	}
}

func createdWrongTicketShortage(po model.PurchaseOrder) (isNeedCreatedTicket bool, wrTicket *model.WrongTicket) {

	// // valid PO status
	// if po.Status != enum.PurchaseOrderStatus.PARTIALLY_RECEIVED {
	// 	return
	// }

	// get inbound more
	opts := client.APIOption{
		Keys:   []string{"GET_WRONG_TICKET", po.POCode},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
		Q: bson.M{
			"poCodeIn":  []string{po.POCode},
			"errorType": "SHORTAGE",
		},
	}

	getWrongTicketResp := seller.GetWrongTicket(opts)
	if getWrongTicketResp.Status == common.APIStatus.Ok {
		return
	}

	mapSkuQtyInVP := make(map[string]int64)
	// get all VB
	vbResp := model.VendorBillDB.Query(model.VendorBill{POCode: po.POCode}, 0, 100, nil)
	if vbResp.Status == common.APIStatus.Ok {
		vbs := vbResp.Data.([]*model.VendorBill)
		for _, vb := range vbs {
			if vb.Status == enum.VendorBillStatus.CANCEL {
				continue
			}
			vpItemsResp := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillCode: vb.VendorBillCode}, 0, 1000, nil)
			if vpItemsResp.Status != common.APIStatus.Ok {
				continue
			}
			vpItems := vpItemsResp.Data.([]*model.VendorBillItem)
			// nếu quantity = 0 thì bỏ qua
			for _, vpItem := range vpItems {
				if vpItem.Quantity == nil || *vpItem.Quantity == 0 {
					continue
				}
				mapSkuQtyInVP[vpItem.ProductCode] += *vpItem.Quantity
			}
		}
	}

	mapSkuQtyInExpect := make(map[string]int64)
	mapSkuQtyInPO := make(map[string]*model.PurchaseOrderItem)
	for _, poItem := range po.Items {
		if item, isExisted := mapSkuQtyInPO[poItem.ProductCode]; isExisted {
			poItem.ActualQuantity += item.ActualQuantity
		}
		mapSkuQtyInPO[poItem.ProductCode] = poItem
		mapSkuQtyInExpect[poItem.ProductCode] += poItem.ExpectQuantity
	}

	wtItem := []*model.WrongTicketItem{}

	for _, item := range mapSkuQtyInPO {
		if mapSkuQtyInVP[item.ProductCode] <= item.ActualQuantity ||
			mapSkuQtyInExpect[item.ProductCode] <= item.ActualQuantity {
			continue
		}

		// Nếu số lượng trong bill nhiều hơn
		wtItem = append(wtItem, &model.WrongTicketItem{
			ProductID:     item.ProductID,
			ProductCode:   item.ProductCode,
			ProductName:   item.ProductName,
			VendorCode:    item.VendorCode,
			WarehouseCode: po.WarehouseCode,
			ErrorType:     "SHORTAGE",
			ErrorContent:  "thiếu",
			ImageUrls:     &[]string{},
			SKU:           item.SKU,
		})
	}

	// create ticket wrong ticket SHORTAGE
	if len(wtItem) > 0 {
		isNeedCreatedTicket = true
		wrTicket = &model.WrongTicket{
			SellerCode:    po.SellerCode,
			PurchaserCode: po.PurchaserCode,
			ErrorType:     "SHORTAGE",
			Type:          "REQUEST",
			VendorCode:    po.VendorCode,
			VendorName:    po.VendorName,
			POCodes:       []string{po.POCode},
			WarehouseCode: po.WarehouseCode,
		}
		wrTicket.Items = wtItem
	}

	return
}
