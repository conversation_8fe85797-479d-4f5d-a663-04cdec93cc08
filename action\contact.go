package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateOldContactDefault updates the default contact for a given vendor and purpose.
// If the input contact is marked as the default contact, it searches for existing default contacts
// with the same vendor code, purpose code, and type, and updates them to no longer be default.
// If the purpose code is either BUYMED_PICKUP or BUYMED_DELIVERS_TO, it also includes the warehouse code
// in the search query.
//
// Parameters:
//   - input: The contact information to be updated, including vendor code, purpose code, type, and default status.
//
// The function performs the following steps:
//   1. Checks if the input contact is marked as the default contact.
//   2. Constructs a query to find existing default contacts with the same vendor code, purpose code, and type.
//   3. If the purpose code is BUYMED_PICKUP or BUYMED_DELIVERS_TO, includes the warehouse code in the query.
//   4. Queries the database for existing default contacts.
//   5. If existing default contacts are found, iterates through them and updates their default status to false
//      if they are not the same as the input contact.
func UpdateOldContactDefault(input model.Contact) {
	if input.IsDefaultContact != nil && *input.IsDefaultContact {
		query := &model.Contact{
			VendorCode:       input.VendorCode,
			PurposeCode:      input.PurposeCode,
			Type:             input.Type,
			IsDefaultContact: utils.Pointer.WithBool(true),
		}

		if enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO {
			query.WarehouseCode = input.WarehouseCode
		}
		// check default contact exist
		contactsResp := model.ContactDB.Query(query, 0, 100, &primitive.M{"_id": -1})

		if contactsResp.Status == common.APIStatus.Ok {
			contactList := contactsResp.Data.([]*model.Contact)

			for _, contact := range contactList {
				if contact.ContactCode != input.ContactCode {
					contact.IsDefaultContact = utils.Pointer.WithBool(false)
					model.ContactDB.UpdateOne(
						model.Contact{
							ContactCode: contact.ContactCode,
						},
						contact)
				}
			}
		}
	}
}

// CreateContact creates a new contact in the database.
// It generates a new ContactID and ContactCode for the input contact,
// then attempts to create the contact in the database.
// If the creation is successful, it updates the old contact default status.
//
// Parameters:
//   - input: model.Contact - The contact information to be created.
//
// Returns:
//   - *common.APIResponse - The response from the database creation operation.
func CreateContact(input model.Contact) *common.APIResponse {
	// create
	input.ContactID, input.ContactCode = model.GetContactID()
	createResp := model.ContactDB.Create(input)

	if createResp.Status == common.APIStatus.Ok {
		createdData := createResp.Data.([]*model.Contact)[0]
		UpdateOldContactDefault(*createdData)
	}
	return createResp
}

// UpdateContact updates the contact information in the database.
// It first queries the database for an existing contact with the same ContactCode.
// If the contact is found, it updates the contact with the provided input data.
// If the update is successful, it calls UpdateOldContactDefault to handle any additional logic for the old contact.
// It returns an APIResponse indicating the status of the operation.
//
// Parameters:
//   - input: model.Contact - The contact information to be updated.
//
// Returns:
//   - *common.APIResponse - The response indicating the status of the update operation.
func UpdateContact(input model.Contact) *common.APIResponse {
	// query
	resp := model.ContactDB.QueryOne(&model.Contact{
		ContactCode: input.ContactCode,
	})

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	updateResp := model.ContactDB.UpdateOne(
		model.Contact{
			ContactCode: input.ContactCode,
		},
		input)

	if updateResp.Status == common.APIStatus.Ok {
		UpdateOldContactDefault(input)
	}

	return updateResp
}

// Delete Contact ...
// DeleteContact deletes a contact based on the provided contact code.
// It first retrieves the contact using the GetContactByCode function.
// If the contact is found, it proceeds to delete the contact from the database.
// If the contact is not found, it returns the response from GetContactByCode.
//
// Parameters:
//   - ContactCode: A string representing the unique code of the contact to be deleted.
//
// Returns:
//   - *common.APIResponse: The API response indicating the result of the delete operation.
func DeleteContact(ContactCode string) *common.APIResponse {
	contactResp := GetContactByCode(ContactCode)
	if contactResp.Status != common.APIStatus.Ok {
		return contactResp
	}
	return model.ContactDB.Delete(&model.Contact{ID: contactResp.Data.([]*model.Contact)[0].ID})
}

func GetContactByCode(ContactCode string) *common.APIResponse {
	return model.ContactDB.QueryOne(&model.Contact{ContactCode: ContactCode})
}

// GetContactList retrieves a list of contacts based on the provided query parameters.
// It supports pagination through the offset and limit parameters, and can optionally
// return the total count of matching contacts.
//
// Parameters:
//   - query: A pointer to a Contact model containing the query parameters.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of matching contacts.
//
// Returns:
//   - A pointer to an APIResponse containing the list of contacts and optionally the total count.
func GetContactList(query *model.Contact, offset, limit int64, getTotal bool) *common.APIResponse {

	result := model.ContactDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if getTotal {
		result.Total = model.ContactDB.Count(query).Total
	}

	return result
}
