package utils

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

var MAX_INDEX_DATE int64 = 99999
var MIN_INDEX_DATE int64 = -1

func GetAdminstrativeTime() *time.Time {
	admTime := 8
	minuteAdd := 0
	// get cache
	hourVal, okHour := MyCache.Get("TimeAdminstration_HOUR")
	minVal, okMin := MyCache.Get("TimeAdminstration_MINUTE")

	if okHour && okMin {
		admTime = hourVal.(int)
		minuteAdd = minVal.(int)
	} else {
		// get DB
		settingResp := model.SettingDB.QueryOne(nil)
		if settingResp.Status == common.APIStatus.Ok {
			setting := settingResp.Data.([]*model.Setting)[0]
			if setting.TimeAdminstration > 0 {
				admTime = int(setting.TimeAdminstration)
				minuteAdd = int((setting.TimeAdminstration - float64(admTime)) * 60)
			}
		}
		// put cache
		MyCache.Put("TimeAdminstration_HOUR", admTime)
		MyCache.Put("TimeAdminstration_MINUTE", minuteAdd)
	}

	// do action
	ictTime := time.Now().In(TimeZoneVN)
	startTime := time.Date(ictTime.Year(), ictTime.Month(), ictTime.Day(), admTime, minuteAdd, 0, 0, TimeZoneVN)
	if ictTime.Before(startTime) {
		return &startTime
	}

	return &ictTime
}

func GetHourDiffrenceDay(endTimeStr string, startTimeStr string) float64 {
	endTime, _ := time.Parse("2006-01-02", endTimeStr)
	endTime = endTime.AddDate(0, 0, 1)
	startTime, _ := time.Parse("2006-01-02", startTimeStr)

	return endTime.Sub(startTime).Hours()
}

func GetIndexNowTwoDate(endTimeStr string, startTimeStr string) int64 {
	endTime, _ := time.ParseInLocation("2006-01-02", endTimeStr, TimeZoneVN)
	startTime, _ := time.ParseInLocation("2006-01-02", startTimeStr, TimeZoneVN)

	now := time.Now()
	beginNowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, TimeZoneVN)

	if endTime.Before(beginNowDate) {
		return MAX_INDEX_DATE
	}
	if startTime.After(beginNowDate) {
		return MIN_INDEX_DATE
	}

	return int64(beginNowDate.Sub(startTime).Hours() / 24)
}

func GetDayOfWeek(weekday *time.Weekday) int {
	if weekday == nil {
		t := time.Now()
		// parse to time zone +7
		tz := time.FixedZone("UTC+7", +7*60*60)
		ictTime := t.In(tz)
		wd := ictTime.Weekday()
		weekday = &wd
	}

	switch *weekday {
	case time.Monday:
		return int(enum.DayOfWeekType.MONDAY)
	case time.Tuesday:
		return int(enum.DayOfWeekType.TUESDAY)
	case time.Wednesday:
		return int(enum.DayOfWeekType.WEDNESDAY)
	case time.Thursday:
		return int(enum.DayOfWeekType.THURSDAY)
	case time.Friday:
		return int(enum.DayOfWeekType.FRIDAY)
	case time.Saturday:
		return int(enum.DayOfWeekType.SATURDAY)
	case time.Sunday:
		return int(enum.DayOfWeekType.SUNDAY)
	}
	return -1
}

// ------------------

func GetVietnamTimeNow() time.Time {
	t := time.Now()
	return t.In(TimeZoneVN)
}

func GetCurrentVersionMMSS() string {
	t := time.Now()
	ictTime := t.In(TimeZoneVN)
	return ictTime.Format("150405")
}
func GetHourToNow(input time.Time) float64 {
	now := time.Now()
	return now.Sub(input).Hours()
}

func GetMinuteToNow(input time.Time) float64 {
	now := time.Now()
	return now.Sub(input).Minutes()
}

func GetTimeNow() int {
	ictTime := GetVietnamTimeNow()
	hour := ictTime.Hour()
	minute := ictTime.Minute()
	timeMinute := hour*60 + minute
	return timeMinute
}

func BeginAdminstrativeTime() *time.Time {
	admTime := 8
	// get cache
	val, ok := MyCache.Get("TimeAdminstration")
	if ok {
		admTime = val.(int)
	} else {
		// get DB
		settingResp := model.SettingDB.QueryOne(nil)
		if settingResp.Status == common.APIStatus.Ok {
			setting := settingResp.Data.([]*model.Setting)[0]
			if setting.TimeAdminstration > 0 {
				admTime = int(setting.TimeAdminstration)
			}
		}
		// put cache
		MyCache.Put("TimeAdminstration", admTime)
	}

	// do action
	ictTime := GetVietnamTimeNow()
	startTime := time.Date(ictTime.Year(), ictTime.Month(), ictTime.Day(), admTime, 0, 0, 0, TimeZoneVN)
	return &startTime
}

func ConvertTimeToStringDDMMYYYY(input *time.Time) string {
	ictTime := input.In(TimeZoneVN)
	return ictTime.Format("02012006")
}

func ConvertTimeToStringYYYYMMDD(input *time.Time, layout string) string {
	if layout == "" {
		layout = "20060102"
	}
	ictTime := input.In(TimeZoneVN)
	return ictTime.Format(layout)
}

func GetCurrentVersionDDMMYYYY() string {
	ictTime := GetVietnamTimeNow()
	return ictTime.Format("02012006")
}

func GetCurrentVersionYYYYMMDD() string {
	ictTime := GetVietnamTimeNow()
	return ictTime.Format(YYYYMMDD)
}

// // CheckDateEqual ...
// func CheckDateEqual(date1 time.Time, date2 time.Time) bool {
// 	y1, m1, d1 := date1.Date()
// 	y2, m2, d2 := date2.Date()
// 	return y1 == y2 && m1 == m2 && d1 == d2
// }

// func GetTimeEndDate(input time.Time) time.Time {
// 	y, m, d := input.In(TimeZoneVN).Date()
// 	endDate := time.Date(y, m, d, 23, 59, 59, 0, TimeZoneVN)
// 	return endDate
// }

func GetFirstTimeOfDate(inputTime time.Time) time.Time {
	y, m, d := inputTime.In(TimeZoneVN).Date()
	return time.Date(y, m, d, 0, 0, 0, 0, TimeZoneVN)
}

func GetLastTimeOfDate(inputTime time.Time) time.Time {
	y, m, d := inputTime.In(TimeZoneVN).Date()
	return time.Date(y, m, d, 23, 59, 59, 0, TimeZoneVN)
}

func GetTimeZone7(input time.Time) time.Time {
	return input.In(TimeZoneVN)
}
