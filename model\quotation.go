package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Quotation struct {
	ID                 *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime        *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime    *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	AssignVendorTime   *time.Time          `json:"assignVendorTime,omitempty" bson:"assign_vendor_time,omitempty"`
	PublishBiddingTime *time.Time          `json:"publishBiddingTime,omitempty" bson:"publish_bidding_time,omitempty"`

	// Version string `json:"version,omitempty" bson:"version,omitempty"`
	QuotationStatus enum.QuotationStatusValue `json:"quotationStatus" bson:"quotation_status,omitempty"`

	PurchaserCode  string     `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	WarehouseCode  string     `json:"warehouseCode" bson:"warehouse_code,omitempty"`
	SellerCode     string     `json:"sellerCode" bson:"seller_code,omitempty"`
	ActionByName   string     `json:"actionByName" bson:"action_by_name,omitempty"`
	ActionByID     int64      `json:"actionByID" bson:"action_by_id,omitempty"`
	LastActionTime *time.Time `json:"lastActionTime,omitempty" bson:"last_action_time,omitempty"`
	// Status
	// GroupID   int64  `json:"groupID,omitempty" bson:"group_id,omitempty"`
	// GroupCode string `json:"groupCode,omitempty" bson:"group_code,omitempty"`

	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	Hashtag string `json:"hashtag,omitempty" bson:"hashtag,omitempty"`

	BlockReleaseToBiddingSession *bool `json:"blockReleaseToBiddingSession,omitempty" bson:"block_release_to_bidding_session,omitempty"`
	BlockReleaseToFavSession     *bool `json:"blockReleaseToFavSession,omitempty" bson:"block_release_to_fav_session,omitempty"`

	// VendorCode          *string         `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorCodes *[]string `json:"vendorCodes,omitempty" bson:"vendor_codes,omitempty"`
	// Vendors     *[]VendorConfig `json:"vendors,omitempty" bson:"-"`
	// CurrentVendorConfig *VendorConfig   `json:"currentVendorConfig,omitempty" bson:"current_vendor_config,omitempty"`

	Unit string `json:"unit,omitempty" bson:"unit,omitempty"`
	// UnitPrice float64  `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	// VAT       int64  `json:"vat,omitempty" bson:"vat,omitempty"`
	// TotalNoneVATPrice float64  `json:"totalNoneVATPrice,omitempty" bson:"total_none_vat_price,omitempty"`
	// TotalPrice        float64  `json:"totalPrice,omitempty" bson:"total_price,omitempty"`

	// Packaging string `json:"packaging,omitempty" bson:"packaging,omitempty"`
	// ManufacturerCode string `json:"manufacturerCode" bson:"manufacturer_code,omitempty"`
	Origin string `json:"origin" bson:"origin,omitempty"`

	Type enum.QuotationType `json:"type" bson:"type,omitempty"`

	QuantityExpect       *int64 `json:"quantityExpect,omitempty" bson:"quantity_expect,omitempty"`
	VendorExpectQuantity *int64 `json:"vendorExpectQuantity,omitempty" bson:"vendor_expect_quantity,omitempty"`

	InboundRequestCode     string `json:"inboundRequestCode,omitempty" bson:"inbound_request_code,omitempty"`
	InboundRequestID       int64  `json:"inboundRequestID,omitempty" bson:"inbound_request_id,omitempty"`
	InboundRequestItemCode string `json:"inboundRequestItemCode,omitempty" bson:"inbound_request_item_code,omitempty"`
	InboundRequestName     string `json:"inboundRequestName,omitempty" bson:"inbound_request_name,omitempty"`

	// QuantityOrder   int64  `json:"quantityOrder,omitempty" bson:"quantity_order,omitempty"`      // Total count sku in order
	// QuantityIncoming int64  `json:"quantityIncoming,omitempty" bson:"quantity_incoming,omitempty"`  // Total count sku po
	// QuantityInStock int64  `json:"quantityInStock,omitempty" bson:"quantity_in_stock,omitempty"` // Total count sku in stock

	// FE internal
	QuantityConfirmed int64 `json:"quantityConfirmed,omitempty" bson:"quantity_confirmed,omitempty"`

	// MinInStock int64  `json:"minInStock,omitempty" bson:"min_in_stock,omitempty"`
	// MinPerStep int64 `json:"minPerStep,omitempty" bson:"min_per_step,omitempty"`
	// Formula    string `json:"formula,omitempty" bson:"formula,omitempty"`

	// SOCodes []string `json:"soCodes,omitempty" bson:"so_codes,omitempty"`
	// POCodes []string `json:"poCodes,omitempty" bson:"po_codes,omitempty"`

	PrWhHashtag string `json:"prWhHashtag,omitempty" bson:"pr_wh_hashtag,omitempty"`

	// Logs []string `json:"logs" bson:"logs,omitempty"` // For trace

	IsBidding *bool `json:"isBidding" bson:"is_bidding,omitempty"`
	// IsNewProduct *bool `json:"isNewProduct" bson:"is_new_product,omitempty"`
	// IsLowPriority   *bool `json:"isLowPriority" bson:"is_low_priority,omitempty"`

	// BI
	// ActionTrackingLastest enum.QuotationActionTrackingValue `json:"actionTracking,omitempty" bson:"action_tracking,omitempty"`
	// For query
	ComplexQuery     []*bson.M `json:"-" bson:"$and,omitempty"`
	WarehouseCodes   []string  `json:"warehouseCodes,omitempty" bson:"-"`
	PrWhHashtagIn    []string  `json:"prWhHashtagIn,omitempty" bson:"-"`
	PrWhHashtagNotIn []string  `json:"prWhHashtagNotIn,omitempty" bson:"-"`
	PurchaserCodeIn  []string  `json:"purchaserCodeIn,omitempty" bson:"-"`
	Search           string    `json:"search,omitempty" bson:"-"`
	SellerCodeIn     []string  `json:"sellerCodeIn,omitempty" bson:"-"`
	// QuantityLte      *int64    `json:"quantityLte,omitempty" bson:"-"`

	ProductIDIn []int64 `json:"productIDIn,omitempty" bson:"-"`

	//For log
	CurrentQuantityConfirmed int64 `json:"currentQuantityConfirmed,omitempty" bson:"-"`

	// for UI
	SkuPIC              int64   `json:"skuPIC,omitempty" bson:"sku_pic,omitempty"`
	VendorPIC           int64   `json:"vendorPIC,omitempty" bson:"vendor_pic,omitempty"`
	FavoriteVendorID    int64   `json:"favoriteVendorID,omitempty" bson:"favorite_vendor_id,omitempty"`
	FavoriteVendorName  string  `json:"favoriteVendorName,omitempty" bson:"favorite_vendor_name,omitempty"`
	FavoriteVendorPhone string  `json:"favoriteVendorPhone,omitempty" bson:"favorite_vendor_phone,omitempty"`
	PaymentTerm         float64 `json:"paymentTerm,omitempty" bson:"payment_term,omitempty"`
	TotalPublishPrice   int64   `json:"totalPublishPrice,omitempty" bson:"total_publish_price,omitempty"`

	// For queue
	// SOCodes []string `json:"soCodes,omitempty" bson:"so_codes,omitempty"`
	Logs []string `json:"logs" bson:"logs,omitempty"` // For trace
}

// QuotationDB ...
var QuotationDB = &db.Instance{
	ColName:        "quotation",
	TemplateObject: &Quotation{},
}

var QuotationHedgingDB = &db.Instance{
	ColName:        "quotation_hedging",
	TemplateObject: &Quotation{},
}

func InitQuotationModel(s *mongo.Database) {
	QuotationDB.ApplyDatabase(s)
	QuotationHedgingDB.ApplyDatabase(s)

	// t := true
	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "quantity", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "vendor_codes", Value: 1},
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "pr_wh_hashtag", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "vendor_codes", Value: 1},
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// 	primitive.E{Key: "pr_wh_hashtag", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_codes", Value: 1},
	// 	primitive.E{Key: "assign_vendor_time", Value: 1},
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_codes", Value: 1},
	// 	primitive.E{Key: "publish_bidding_time", Value: 1},
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// expire := int32((30 * 24 * time.Hour).Seconds())
	// _ = QuotationDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })

	// // fulltext search
	// _ = QuotationDB.CreateIndex(bson.D{
	// 	{"hashtag", "text"},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
