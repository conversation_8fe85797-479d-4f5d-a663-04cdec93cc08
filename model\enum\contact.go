package enum

type VendorContactPurposeValue string
type vendorContactPurposeValue struct {
	BUYMED_PICKUP        VendorContactPurposeValue
	BUYMED_DELIVERS_TO   VendorContactPurposeValue
	BUSINESS_INFORMATION VendorContactPurposeValue
	OTHER                VendorContactPurposeValue
}

var VendorContactPurposeCode = &vendorContactPurposeValue{
	"BUYMED_PICKUP",
	"BUYMED_DELIVERS_TO",
	"BUSINESS_INFORMATION",
	"OTHER",
}

type VendorContactTypeValue string
type vendorContactTypeValue struct {
	PHONE VendorContactTypeValue
	EMAIL VendorContactTypeValue
	FAX   VendorContactTypeValue
}

var VendorContactTypeCode = &vendorContactTypeValue{
	"PHONE",
	"EMAIL",
	"FAX",
}
