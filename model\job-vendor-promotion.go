package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"go.mongodb.org/mongo-driver/mongo"
)

// check promotion for confirm PO
var (
	VendorPromotionJob = &job.Executor{
		ColName: "vendor_promotion_job",
	}

	// topic key
	VENDOR_PROMOTION_TOPIC_JOB = "VENDOR_PROMOTION_TOPIC_JOB"
)

func InitVendorPromotionJob(database *mongo.Database) {
	VendorPromotionJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:   true,
		ChannelCount:       1,
		UniqueItem:         true,
		CurVersionTimeoutS: 60 * 30,
	})
}
