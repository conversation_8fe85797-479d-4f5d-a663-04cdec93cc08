package action

import (
	"errors"
	"fmt"
	"strconv"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

const VENDOR_REPLACE_NAME = "quý Nhà cung cấp"

// sendZNSConfirmedPO sends a ZNS confirmation message for a purchase order to the vendor.
// It retrieves vendor information using the vendor code from the purchase order.
// If the vendor name exceeds 30 characters, it replaces it with a predefined constant.
// If the vendor is valid for receiving ZNS messages, it sends a confirmation message to each phone number
// listed in the vendor's received message phones.
// The message includes the vendor name and purchase order code, and uses a predefined template.
// If any message sending fails, it returns an error with the failure message.
//
// Parameters:
//   - purchaseOrder: A pointer to the PurchaseOrder model containing the purchase order details.
//
// Returns:
//   - error: An error if any step in the process fails, otherwise nil.
func sendZNSConfirmedPO(purchaseOrder *model.PurchaseOrder) error {
	// Get vendor info
	vendorInfoResp := GetVendorInfo(purchaseOrder.VendorCode)
	if vendorInfoResp.Status != common.APIStatus.Ok {
		return errors.New(vendorInfoResp.Message)
	}
	vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]

	vendorName := vendorInfo.Name
	if len(vendorName) > 30 {
		vendorName = VENDOR_REPLACE_NAME
	}

	// Check if the vendor is valid for receiving ZNS messages
	if validVendorSentMessageZNS(vendorInfo) {
		for _, phone := range *vendorInfo.ReceivedMessagePhones {
			keys := []string{"PO_COMFIRMED", vendorName, purchaseOrder.POCode, strconv.Itoa(int(vendorInfo.SellerID))}
			msg := model.ZNSMessage{
				Topic:       "PO_COMFIRMED",
				Source:      "seller/purchasing",
				Receiver:    phone,
				UseTemplate: true,
				Dictionary: map[string]interface{}{
					"vendor_name": vendorName,
					"po_code":     purchaseOrder.POCode,
				},
				ReadyTime: utils.GetVietnamTimeNow(),
				Keys:      keys,
			}

			if resp := seller.SendZNSMessage(client.APIOption{Body: msg, Keys: keys}); resp.Status != common.APIStatus.Ok {
				return errors.New(resp.Message)
			}
		}
	}

	return nil
}

// sendZNSCancelledPO sends a Zalo Notification Service (ZNS) message to notify the vendor that a purchase order has been cancelled.
// It retrieves vendor information using the vendor code from the purchase order, validates if the vendor is eligible to receive ZNS messages,
// and sends the cancellation message to each phone number associated with the vendor.
//
// Parameters:
//   - purchaseOrder: A pointer to the PurchaseOrder model containing details of the purchase order.
//
// Returns:
//   - error: An error if there is any issue in retrieving vendor information or sending the ZNS message, otherwise nil.
func sendZNSCancelledPO(purchaseOrder *model.PurchaseOrder) error {
	// // Get vendor info
	vendorInfoResp := GetVendorInfo(purchaseOrder.VendorCode)
	if vendorInfoResp.Status != common.APIStatus.Ok {
		return errors.New(vendorInfoResp.Message)
	}
	vendorInfo := vendorInfoResp.Data.([]*model.Seller)[0]

	vendorName := vendorInfo.Name
	if len(vendorName) > 30 {
		vendorName = VENDOR_REPLACE_NAME
	}

	// Check if the vendor is valid for receiving ZNS messages
	if validVendorSentMessageZNS(vendorInfo) {
		for _, phone := range *vendorInfo.ReceivedMessagePhones {
			keys := []string{"PO_CANCELLED", vendorName, purchaseOrder.POCode, strconv.Itoa(int(vendorInfo.SellerID))}
			msg := model.ZNSMessage{
				Topic:       "PO_CANCELLED",
				Source:      "seller/purchasing",
				Receiver:    phone,
				UseTemplate: true,
				Dictionary: map[string]interface{}{
					"customer_name": vendorName,
					"Ma_PO":         purchaseOrder.POCode,
					"Day":           purchaseOrder.CreatedTime.In(utils.TimeZoneVN).Format("02/01/2006"),
				},
				ReadyTime: utils.GetVietnamTimeNow(),
				Keys:      keys,
			}

			if resp := seller.SendZNSMessage(client.APIOption{Body: msg, Keys: keys}); resp.Status != common.APIStatus.Ok {
				return errors.New(resp.Message)
			}
		}
	}

	return nil
}

// sendZNSVendorCreateQuotation sends a ZNS message to a vendor when a quotation is created.
// It performs the following steps:
// 1. Validates the vendor code.
// 2. Locks the database to prevent duplicate notifications.
// 3. Retrieves vendor information.
// 4. Sends a ZNS message to the vendor if they are eligible to receive messages.
//
// Parameters:
// - vendorCode: The code of the vendor to send the message to.
//
// Returns:
// - An error if any step fails, otherwise nil.
func sendZNSVendorCreateQuotation(vendorCode string) error {
	if len(vendorCode) == 0 {
		return errors.New("vendor code is required")
	}
	// add lock database
	key := fmt.Sprintf("CREATE_QUOTATION_NOTIFY_ZNS_%s", vendorCode)
	now := utils.GetVietnamTimeNow()
	tomorrow := utils.GetFirstTimeOfDate(now.AddDate(0, 0, 1))
	resp := model.LockActionDB.Create(model.LockAction{Key: key, CreatedTime: &tomorrow})
	if resp.Status != common.APIStatus.Ok {
		return nil
	}

	// Get vendor info
	getVendorInfoResp := GetVendorInfo(vendorCode)
	if getVendorInfoResp.Status != common.APIStatus.Ok {
		return errors.New(getVendorInfoResp.Message)
	}
	vendorInfo := getVendorInfoResp.Data.([]*model.Seller)[0]

	vendorName := vendorInfo.Name
	if len(vendorName) > 30 {
		vendorName = VENDOR_REPLACE_NAME
	}


	// Check if the vendor is valid for receiving ZNS messages
	if validVendorSentMessageZNS(vendorInfo) {
		for _, phone := range *vendorInfo.ReceivedMessagePhones {
			keys := []string{"CREATE_QUOTATION_VER_2", vendorName, strconv.Itoa(int(vendorInfo.SellerID))}
			msg := model.ZNSMessage{
				Topic:       "CREATE_QUOTATION_VER_2",
				Source:      "seller/purchasing",
				Receiver:    phone,
				UseTemplate: true,
				Dictionary: map[string]interface{}{
					"vendor_name": vendorName,
					"ID_vendor":   vendorInfo.SellerID,
					"number":      10,
				},
				ReadyTime: *utils.GetAdminstrativeTime(),
				Keys:      keys,
			}
			if resp := seller.SendZNSMessage(client.APIOption{Body: msg, Keys: keys}); resp.Status != common.APIStatus.Ok {
				return errors.New(resp.Message)
			}
		}
	}

	return nil
}

func validVendorSentMessageZNS(vendorInfo *model.Seller) bool {
	if vendorInfo != nil && vendorInfo.IsActiveZNS != nil && *vendorInfo.IsActiveZNS == true && vendorInfo.ReceivedMessagePhones != nil {
		return true
	}

	return false
}
