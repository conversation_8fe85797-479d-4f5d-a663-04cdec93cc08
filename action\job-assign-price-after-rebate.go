package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdatePOPriceAfterRebate struct {
	PriceAfterRebate             *float64 `bson:"price_after_rebate"`               // chấp nhận null
	PriceAfterRebateWithoutAdhoc *float64 `bson:"price_after_rebate_without_adhoc"` // chấp nhận null
	ContractPriceCode            *string  `bson:"contract_price_code"`
}

type ContractProductPrice struct {
	ContractPriceCode string
	Price             float64
	PriceWithoutAdhoc float64
}

// ProcessAssignPriceAfterRebate processes the assignment of prices after rebate for a given job item.
// It performs the following steps:
// 1. Checks if the job item has failed or been repushed more than 3 times, and returns if true.
// 2. Marshals the job item data into a BSON format and unmarshals it into a WorkerAssignPriceAfterRebateVariable struct.
// 3. Validates the VendorCode and Version fields of the input data.
// 4. Parses the Version field to get the start time.
// 5. Determines the target vendor code and its child vendor codes.
// 6. Retrieves the list of prices after rebate for the target and child vendors based on the version.
// 7. Creates a map of distinct contract prices and retrieves the contract price details.
// 8. Generates a map of product prices after rebate, considering the latest start time for intersecting prices.
// 9. Retrieves purchase orders within the specified time range and updates their items with the new prices after rebate.
//
// Parameters:
// - item: A pointer to the job.JobItem struct containing the job item data.
//
// Returns:
// - error: An error if any step fails, otherwise nil.
func ProcessAssignPriceAfterRebate(item *job.JobItem) error {
	if item.FailCount > 3 || item.RepushCount > 3 {
		return nil
	}

	// Unmarshal data
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.WorkerAssignPriceAfterRebateVariable

	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	// Validate input
	if input.VendorCode == "" {
		return &common.Error{Type: "INVALID", Message: "VendorCode is empty"}
	}

	// Parse version
	if input.Version == "" {
		return &common.Error{Type: "INVALID", Message: "Version is empty"}
	}

	startTime, err := time.Parse(utils.YYYYMMDD, input.Version)
	if err != nil {
		return err
	}

	// target sẽ luôn là vendor cha
	targetVendorCode := input.VendorCode
	childVendorCodes := []string{}

	//check child vendor
	opts := client.APIOption{
		Keys: []string{targetVendorCode, "PARRENT_VENDOR"},
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
			LineManager: targetVendorCode,
		},
	}

	// get child vendor
	getVendorResp := seller.GetSellers(opts)
	if getVendorResp.Status == common.APIStatus.Ok { // target is parrent vendor
		childVendors := getVendorResp.Data.([]*model.Seller)
		for _, vendor := range childVendors {
			if len(vendor.Code) != 0 {
				childVendorCodes = append(childVendorCodes, vendor.Code)
			}
		}
	} else { // target is child vendor or normal vendor
		opts := client.APIOption{
			Keys: []string{targetVendorCode, "CHILD_VENDOR"},
			Q: model.Seller{
				SellerClass: model.CLASS_VENDOR,
			},
			Params: map[string]string{
				"sellerCodes": targetVendorCode,
			},
		}

		// get child vendor
		getVendorResp := seller.GetSellers(opts)
		if getVendorResp.Status != common.APIStatus.Ok {
			return &common.Error{Type: "INVALID", Data: targetVendorCode, Message: "VendorCode is invalid"}
		}
		vendorChild := getVendorResp.Data.([]*model.Seller)[0]
		childVendorCodes = []string{vendorChild.Code}
		if len(vendorChild.LineManager) > 0 {
			targetVendorCode = vendorChild.LineManager
		}
	}

	// lấy giá sau rebate theo version
	priceAfterRebateList := make([]*model.PriceAfterRebate, 0)
	for offset, limit := 0, 1000; ; offset += limit {
		otps := client.APIOption{
			Keys: append(childVendorCodes, targetVendorCode, input.Version),
			Q: model.PriceAfterRebate{
				VendorCode: targetVendorCode,
				Version:    input.Version,
			},
			Offset: utils.Pointer.WithInt(offset),
			Limit:  utils.Pointer.WithInt(1000),
		}

		// get price after rebate
		getPriceAfterRebateResp := seller.GetPriceAfterRebate(otps)
		if getPriceAfterRebateResp.Status != common.APIStatus.Ok {
			break
		}

		priceAfterRebateList = append(priceAfterRebateList, getPriceAfterRebateResp.Data.([]*model.PriceAfterRebate)...)
	}

	// get distinct contract price
	mapDistinctContractPrice := make(map[string]struct{})
	for _, v := range priceAfterRebateList {
		if _, ok := mapDistinctContractPrice[v.ContractPriceCode]; !ok {
			mapDistinctContractPrice[v.ContractPriceCode] = struct{}{}
		}
	}

	// get contract price
	mapContractPrice := make(map[string]*model.ContractPrice)
	for contractPriceCode := range mapDistinctContractPrice {
		getContractPriceResp := model.ContractPriceDB.QueryOne(model.ContractPrice{ContractPriceCode: contractPriceCode})
		if getContractPriceResp.Status != common.APIStatus.Ok {
			continue
		}
		mapContractPrice[contractPriceCode] = getContractPriceResp.Data.([]*model.ContractPrice)[0]
	}

	// get price after rebate
	mapProductPrice := make(map[string]ContractProductPrice)
	for _, priceAfterRebate := range priceAfterRebateList {

		if priceAfterRebate.PriceAfterRebateWithAdhocPromotion == nil {
			continue
		}

		// @@ check nếu 2 bảng giá cùng intersect 1 sp thì lấy giá của bảng giá có ngày bắt đầu mới nhất
		key := genKeyWarehouseProduct(priceAfterRebate.WarehouseCode, priceAfterRebate.ProductCode)
		checkContractPrdPrice, ok := mapProductPrice[key]
		if !ok {
			checkContractPrdPrice.ContractPriceCode = priceAfterRebate.ContractPriceCode
			checkContractPrdPrice.Price = *priceAfterRebate.PriceAfterRebateWithAdhocPromotion
			checkContractPrdPrice.PriceWithoutAdhoc = *priceAfterRebate.PriceAfterRebate

			mapProductPrice[key] = checkContractPrdPrice
			continue
		}

		// check intersect
		oldContractPrice, ok := mapContractPrice[checkContractPrdPrice.ContractPriceCode]
		if !ok {
			continue
		}

		// check intersect
		newContractPrice, ok := mapContractPrice[priceAfterRebate.ContractPriceCode]
		if !ok {
			continue
		}
		//overide
		if newContractPrice.StartTime.After(*oldContractPrice.StartTime) {
			checkContractPrdPrice.ContractPriceCode = priceAfterRebate.ContractPriceCode
			checkContractPrdPrice.Price = *priceAfterRebate.PriceAfterRebateWithAdhocPromotion
			checkContractPrdPrice.PriceWithoutAdhoc = *priceAfterRebate.PriceAfterRebate

			mapProductPrice[key] = checkContractPrdPrice
		}

	}

	// get price after rebate
	startTime = utils.GetFirstTimeOfDate(startTime)
	endTime := utils.GetLastTimeOfDate(startTime)
	poValidStatus := []enum.PurchaseOrderStatusValue{
		enum.PurchaseOrderStatus.CONFIRMED, enum.PurchaseOrderStatus.HANDOVER_COMPLETED,
		// enum.PurchaseOrderStatus.PROCESSING
		enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED,
		enum.PurchaseOrderStatus.COMPLETED,
		enum.PurchaseOrderStatus.AWAITING_BILLED, enum.PurchaseOrderStatus.BILLED,
	}

	_id := primitive.NilObjectID

	// get po
	vedorCodePO := append(childVendorCodes, targetVendorCode)
	for {

		// get po
		getPOResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
			ComplexQuery: []*bson.M{
				{"issued_time": bson.M{"$gte": startTime}},
				{"issued_time": bson.M{"$lte": endTime}},
				{"status": bson.M{"$in": poValidStatus}},
				{"vendor_code": bson.M{"$in": vedorCodePO}},
				{"_id": bson.M{"$gt": _id}},
			}},
			0, 1000, &bson.M{"_id": 1})
		if getPOResp.Status != common.APIStatus.Ok {
			break
		}

		poList := getPOResp.Data.([]*model.PurchaseOrder)

		// update po item
		for _, poMain := range poList {
			// offset
			_id = *poMain.ID

			// get po item
			poItemResp := model.PurchaseOrderItemDB.Query(
				model.PurchaseOrderItem{
					POCode: poMain.POCode,
				}, 0, 1000, nil)

			if poItemResp.Status != common.APIStatus.Ok {
				continue
			}

			// update po item
			poItemList := poItemResp.Data.([]*model.PurchaseOrderItem)
			for _, poItem := range poItemList {
				price := (*float64)(nil)
				contractPriceCode := (*string)(nil)
				priceWithoutAdhoc := (*float64)(nil)

				key := genKeyWarehouseProduct(poItem.WarehouseCode, poItem.ProductCode)
				if contractProductPrice, ok := mapProductPrice[key]; ok {
					price = utils.Pointer.WithFloat64(contractProductPrice.Price)
					contractPriceCode = utils.Pointer.WithString(contractProductPrice.ContractPriceCode)
					priceWithoutAdhoc = utils.Pointer.WithFloat64(contractProductPrice.PriceWithoutAdhoc)
				}

				// assign tất cả nếu giá sau rebate bị xoá thì xoá field luôn
				model.PurchaseOrderItemDB.UpdateOne(
					model.PurchaseOrderItem{ID: poItem.ID},
					UpdatePOPriceAfterRebate{
						PriceAfterRebate:             price,
						ContractPriceCode:            contractPriceCode,
						PriceAfterRebateWithoutAdhoc: priceWithoutAdhoc,
					},
				)
			}

		}

	}

	return nil
}

// genKeyWarehouseProduct generates a unique key for a warehouse product
// by concatenating the warehouse code and product code with an underscore.
//
// Parameters:
//   - warehouseCode: A string representing the code of the warehouse.
//   - productCode: A string representing the code of the product.
//
// Returns:
//
//	A string that combines the warehouse code and product code separated by an underscore.
func genKeyWarehouseProduct(warehouseCode, productCode string) string {
	return warehouseCode + "_" + productCode
}
