package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotaManage struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`

	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorName string `json:"vendorName,omitempty" bson:"vendor_name,omitempty"`

	QuotaManageCode string `json:"quotaManageCode,omitempty" bson:"quota_manage_code,omitempty"`
	QuotaManageID   int64  `json:"quotaManageID,omitempty" bson:"quota_manage_id,omitempty"`

	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`

	CICAccountVendor  *int64 `json:"cic_account_vendor,omitempty" bson:"cic_account_vendor,omitempty"`
	CICAccountProduct *int64 `json:"cic_account_product,omitempty" bson:"cic_account_product,omitempty"`

	Quota          *int64 `json:"quota,omitempty" bson:"quota,omitempty"`
	ActualQuantity *int64 `json:"actualQuantity,omitempty" bson:"actual_quantity,omitempty"`

	Period string `json:"period,omitempty" bson:"period,omitempty"`

	IsNotification *bool `json:"isNotification,omitempty" bson:"is_notification,omitempty"`

	// search for client
	VendorCodeIn        []string `json:"vendorCodeIn,omitempty" bson:"-"`
	ProductIDIn         []string `json:"productIDIn,omitempty" bson:"-"`
	WarehouseCodeIn     []string `json:"warehouseCodeIn,omitempty" bson:"-"`
	CICAccountVendorIn  []string `json:"cicAccountVendorIn,omitempty" bson:"-"`
	CICAccountProductIn []string `json:"cicAccountProductIn,omitempty" bson:"-"`

	// internal query
	ComplexQuery []*bson.M           `json:"-" bson:"$and,omitempty"`
	IDFrom       *primitive.ObjectID `json:"idFrom,omitempty" bson:"-"`
	IDTo         *primitive.ObjectID `json:"idTo,omitempty" bson:"-"`
}

var QuotaManageDB = &db.Instance{
	ColName:        "quota_manage",
	TemplateObject: &QuotaManage{},
}

func InitQuotaManageModel(s *mongo.Database) {
	QuotaManageDB.ApplyDatabase(s)
}
