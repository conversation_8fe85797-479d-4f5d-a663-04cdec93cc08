package api

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

var DEFAULT_SELLER_CODE = "MEDX"

func GetSingleContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query model.ContractPrice
	)

	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	getMainResp := action.GetContractPriceMain(&query, 0, 1, false, nil)
	if getMainResp.Status != common.APIStatus.Ok {
		return resp.Respond(getMainResp)
	}
	main := getMainResp.Data.([]*model.ContractPrice)[0]

	getItemResp := action.GetContractPriceItem(&model.ContractPriceItem{ContractPriceCode: main.ContractPriceCode}, 0, 1000, false, nil)
	if getItemResp.Status != common.APIStatus.Ok {
		return resp.Respond(getItemResp)
	}
	items := getItemResp.Data.([]*model.ContractPriceItem)
	main.Items = items

	return resp.Respond(&common.APIResponse{
		Status: common.APIStatus.Ok,
		Data:   []*model.ContractPrice{main},
	})
}

func GetListContractPriceMain(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		now      = utils.GetVietnamTimeNow()
		query    model.ContractPrice
	)

	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// search
	if len(query.Search) > 0 {
		searchTerm := utils.NormalizeString(query.Search)
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"hash_tag": bson.M{"$regex": searchTerm},
		})
	}

	// search vendor codes
	if len(query.VendorCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"vendor_codes": bson.M{"$in": query.VendorCodeIn},
		})
	}

	// search warehouse code
	if len(query.WarehouseCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"warehouse_code": bson.M{"$in": query.WarehouseCodeIn},
		})
	}

	if query.TimeIn != nil {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"start_time": bson.M{"$lte": query.TimeIn},
			"end_time":   bson.M{"$gte": query.TimeIn},
		})
	}

	if query.TimeIntersectFrom != nil && query.TimeIntersectTo != nil {
		timeIn := []bson.M{
			{
				"start_time": bson.M{"$lte": query.TimeIntersectTo},
				"end_time":   bson.M{"$gte": query.TimeIntersectTo},
			},
			{
				"start_time": bson.M{"$lte": query.TimeIntersectFrom},
				"end_time":   bson.M{"$gte": query.TimeIntersectFrom},
			},
			{
				"start_time": bson.M{"$gte": query.TimeIntersectFrom},
				"end_time":   bson.M{"$lte": query.TimeIntersectTo},
			},
			{
				"start_time": bson.M{"$lte": query.TimeIntersectFrom},
				"end_time":   bson.M{"$gte": query.TimeIntersectTo},
			},
		}

		query.OrQuery = append(query.OrQuery, timeIn...)
	}

	// search time
	if query.TimeFrom != nil {
		*query.TimeFrom = utils.GetFirstTimeOfDate(query.TimeFrom.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"start_time": bson.M{"$gte": query.TimeFrom},
		})
	}
	if query.TimeTo != nil {
		*query.TimeTo = utils.GetLastTimeOfDate(query.TimeTo.In(utils.TimeZoneVN))
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"end_time": bson.M{"$lte": query.TimeTo},
		})
	}

	// search product code
	if len(query.ProductCodeIn) > 0 {
		// get product code item
		queryItem := model.ContractPriceItem{
			ComplexQuery: []bson.M{
				{
					"product_code": bson.M{"$in": query.ProductCodeIn},
				},
			},
		}

		listMainCode := action.GetContractPriceMainCodeFromItem(&queryItem)
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"contract_price_code": bson.M{"$in": listMainCode},
		})
	}

	// active status
	switch query.ActiveStatus {
	case enum.ContractPriceActiveStatus.UPCOMING:
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"start_time": bson.M{"$gte": now},
		})
	case enum.ContractPriceActiveStatus.OCCURRING:
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"start_time": bson.M{"$lte": now},
			"end_time":   bson.M{"$gte": now},
		})
	case enum.ContractPriceActiveStatus.FINISHED:
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"end_time": bson.M{"$lte": now},
		})
	}

	return resp.Respond(action.GetContractPriceMain(&query, offset, limit, getTotal, nil))
}

func GetListContractPriceItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
		query    model.ContractPriceItem
	)

	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}
	if limit > 1000 {
		limit = 1000
	}

	return resp.Respond(action.GetContractPriceItem(&query, offset, limit, getTotal, &bson.M{"_id": -1}))
}

func CreateContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.ContractPrice
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.CreateContractPrice(&input))
}

func UpdateContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.ContractPrice
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.UpdateContractPrice(&input))
}

func SwitchStatusContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.ContractPrice
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.SwitchStatusContractPrice(input.ContractPriceCode, input.Status))
}

func CloneContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var contractPriceCode = req.GetParam("contractPriceCode")

	if contractPriceCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid ContractPriceCode",
			ErrorCode: string(enum.ErrorCodeInvalid.ContractPriceCode),
		})
	}

	return resp.Respond(action.CloneContractPrice(contractPriceCode))
}

// GetContractPrice ...
func GetContractPriceItemV2(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		productID    = sdk.ParseInt64(req.GetParam("productID"), 0)
		warehouse    = req.GetParam("warehouse")
		vendorCode   = req.GetParam("vendorCode")
		checkingTime = req.GetParam("checkingTime")
	)
	if checkingTime != "" {
		convertCheckingTime, err := time.Parse("2006-01-02", checkingTime)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid checkingTime",
			})
		}
		return resp.Respond(action.GetContractPriceItemValid(productID, warehouse, vendorCode, convertCheckingTime))
	}

	// do action
	return resp.Respond(action.GetContractPriceItemV2(productID, warehouse, vendorCode))
}

// VerifyContractPrice ...
func VerifyContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input model.ContractPrice
	)
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	return resp.Respond(action.VerifyContractPrice(&input))
}

// GetPurchaseSkuInfo
// lấy thông tin config của sku theo product id, warehouse, vendor code
func GetPurchaseSkuInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		productID  = sdk.ParseInt64(req.GetParam("productID"), 0)
		warehouse  = req.GetParam("warehouse")
		sellerCode = req.GetParam("sellerCode")
	)

	if strings.TrimSpace(sellerCode) == "" {
		sellerCode = utils.DEFAULT_SELLER_CODE
	}
	if productID == 0 || warehouse == "" || sellerCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid input data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.GetPurchaseSkuInfo(productID, warehouse, sellerCode))
}

// 	if input.ContractPriceCode == "" && input.ContractPriceID == 0 {
// 		return resp.Respond(&common.APIResponse{
// 			Status:    common.APIStatus.Invalid,
// 			Message:   "ContractPriceCode or ContractPriceID is required.",
// 			ErrorCode: string(enum.ErrorCodeInvalid.ContractPriceCode),
// 		})
// 	}
// 	return resp.Respond(action.MigrateContractPriceItem(&input))
// }

// ToolMigrateContractPrice
func ToolMigrateContractPrice(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var contractPriceCode = req.GetParam("contractPriceCode")

	// do action
	return resp.Respond(action.ToolMigrateContractPrice(contractPriceCode))
}
