package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ProductMapping struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductID int64 `json:"productID,omitempty" bson:"product_id,omitempty"`

	ProductVendors *[]ProductVendor `json:"productVendors,omitempty" bson:"product_vendors,omitempty"`

	UnitVendors *[]UnitVendor `json:"unitVendors,omitempty" bson:"unit_vendors,omitempty"`

	// For query
	ComplexQuery      []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductVendorName string    `json:"productVendorName,omitempty" bson:"-"`
	ProductIDIn       []int64   `json:"productIDIn,omitempty" bson:"-"` // Dùng để kiểm tra sku-mapping đã được import chưa(File import không có productCode)
}

type ProductVendor struct {
	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	ProductVendorNames []string `json:"productVendorNames,omitempty" bson:"product_vendor_names,omitempty"`
	ProductVendorSlugs []string `json:"productVendorSlugs,omitempty" bson:"product_vendor_slugs,omitempty"`
}

type UnitVendor struct {
	Value string   `json:"value,omitempty" bson:"value,omitempty"`
	Names []string `json:"names,omitempty" bson:"names,omitempty"`
	Slugs []string `json:"slugs,omitempty" bson:"slugs,omitempty"`
}

// ProductMappingDB ...
var ProductMappingDB = &db.Instance{
	ColName:        "product_mapping",
	TemplateObject: &ProductMapping{},
}

// InitProductMappingModel ...
func InitProductMappingModel(s *mongo.Database) {
	ProductMappingDB.ApplyDatabase(s)

	// t := true
	// _ = ProductMappingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = ProductMappingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = ProductMappingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_slug", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = ProductMappingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_vendors.product_vendor_slugs", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // Worker
	// _ = ProductMappingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_vendors.vendor_code", Value: 1},
	// 	primitive.E{Key: "product_vendors.product_vendor_slugs", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
