package action

import (
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BillDetailOfLastPOQuery struct {
	ProductID     int64  `json:"productID"`
	WarehouseCode string `json:"warehouseCode"`
	SellerCode    string `json:"sellerCode"`
	VendorCode    string `json:"vendorCode"`

	VendorCodes string `json:"vendorCodes"`
}

// GetBillDetailOfLastPO retrieves the bill details of the last purchase order (PO) based on the provided query parameters.
// It validates the query parameters and fetches the relevant purchase order items and their associated vendor bills.
//
// Parameters:
//   - query: BillDetailOfLastPOQuery containing the search criteria.
//
// Returns:
//   - *common.APIResponse: API response containing the status, message, and data (if any).
//
// The function performs the following steps:
//  1. Validates the query parameters (ProductID, WarehouseCode, SellerCode).
//  2. Queries the purchase order items based on the query parameters.
//  3. Retrieves the purchase orders associated with the queried purchase order items.
//  4. Filters the purchase orders based on the confirmed time (within the last 6 months).
//  5. Checks the bill status of the filtered purchase orders.
//  6. Retrieves the vendor bill items associated with the purchase orders.
//  7. Returns the vendor bill items if found, otherwise returns a "Not found" response.
func GetBillDetailOfLastPO(query BillDetailOfLastPOQuery) *common.APIResponse {

	if query.ProductID == 0 || query.WarehouseCode == "" || query.SellerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid query",
		}
	}
	// Get All PO items

	// TODO: FIXME: create index warehouse_code, product_id, seller_code for purchase_order_item
	// db.getCollection("purchase_order_item").createIndex({ "warehouse_code": 1, "seller_code": 1,"product_id": 1 })
	// HACK: Only check last 100 PO items

	q := model.PurchaseOrderItem{
		ProductID:     query.ProductID,
		WarehouseCode: query.WarehouseCode,
		SellerCode:    query.SellerCode,
		VendorCode:    query.VendorCode,
	}

	if query.VendorCodes != "" && query.VendorCode == "" {
		vendorCodes := strings.Split(query.VendorCodes, ",")
		q.ComplexQuery = []*primitive.M{
			{
				"vendor_code": bson.M{
					"$in": vendorCodes,
				},
			},
		}
	}

	poItemResp := model.PurchaseOrderItemDB.Query(q, 0, 100, &primitive.M{
		"_id": -1,
	})

	// Check if PO item not found
	if poItemResp.Status != common.APIStatus.Ok {
		return poItemResp
	}

	poCodes := []string{}
	for _, poItem := range poItemResp.Data.([]*model.PurchaseOrderItem) {
		poCodes = append(poCodes, poItem.POCode)
	}

	// Get all PO With PO Code and Bill Status = BILL_RECEIVED
	poCodes = utils.UniqueStringSlice(poCodes)
	poResp := model.PurchaseOrderDB.Query(model.PurchaseOrder{
		// BillStatus: enum.POBillStatus.BILL_RECEIVED // chỉ lấy các PO đã nhận hóa đơn
		Status: enum.PurchaseOrderStatus.COMPLETED,
		ComplexQuery: []*primitive.M{
			{
				"po_code": bson.M{
					"$in": poCodes,
				},
			},
		},
	}, 0, int64(len(poCodes)), &primitive.M{"confirmed_time": -1}) // sort theo confirmed_time giảm dần

	if poResp.Status != common.APIStatus.Ok {
		return poResp
	}

	// Get POs
	selectedPOs := poResp.Data.([]*model.PurchaseOrder)

	// Get all vendor bill items
	limitTime := time.Now().AddDate(0, -6, 0) // 6 tháng trước
	for _, selectedPO := range selectedPOs {

		// nếu confirmed_time < 6 tháng trước thì dừng
		if selectedPO.ConfirmedTime.Before(limitTime) {
			break
		}
		// check bill status
		vendorBillResp := model.VendorBillDB.Query(model.VendorBill{
			POCode: selectedPO.POCode,
			ComplexQuery: []*primitive.M{
				{
					"status": bson.M{
						"$in": []string{
							string(enum.VendorBillStatus.APPROVED),
							string(enum.VendorBillStatus.PAID),
							string(enum.VendorBillStatus.WAIT_TO_PAID),
						},
					},
				}},
		}, 0, 20, nil)

		// Check if vendor bill not found
		if vendorBillResp.Status != common.APIStatus.Ok {
			continue
		}

		// Get all vendor bill item
		vendorBills := vendorBillResp.Data.([]*model.VendorBill)

		vendorBillItems := []*model.VendorBillItem{}
		// Get all vendor bill items
		for _, vendorBill := range vendorBills {

			// Get all vendor bill detail
			itemResp := model.VendorBillItemDB.Query(model.VendorBillItem{
				VendorBillID: vendorBill.VendorBillID,
				ProductID:    query.ProductID,
			}, 0, 100, nil)

			// Check if vendor bill item available
			if itemResp.Status == common.APIStatus.Ok {

				billItems := itemResp.Data.([]*model.VendorBillItem)
				for idx := range billItems {
					item := billItems[idx]
					item.IssuedTime = vendorBill.IssuedTime
					vendorBillItems = append(vendorBillItems, item)
				}
			}
		}

		// Return vendor bill items if found
		if len(vendorBillItems) > 0 {
			return &common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Success",
				Data:    vendorBillItems,
			}
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.NotFound,
		Message: "Not found",
	}
}
