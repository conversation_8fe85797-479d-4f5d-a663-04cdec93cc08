package action

import (
	"os"
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// WorkerValidBiddingRate updates the bidding rates in the database to mark them as valid.
// It first checks the current environment and exits if it is "uat".
// Then, it checks the current time and exits if it is before 10:45 AM, sleeping for 5 minutes before returning.
// If the conditions are met, it updates the bidding rates in the database where the "is_valid" field is either
// non-existent or set to false, marking them as valid.
func WorkerValidBiddingRate() {
	timeNow := utils.GetTimeNow()

	if os.Getenv("env") == "uat" {
		return
	}

	// trước 10:30 sẽ return
	if timeNow < 10*60+45 {
		time.Sleep(5 * time.Minute)
		return
	}

	// update bidding rate
	model.BiddingRateDB.UpdateMany(
		&model.BiddingRate{
			DaySpan: utils.GetCurrentVersionDDMMYYYY(),
			ComplexQuery: []*bson.M{{
				"$or": []bson.M{
					{"is_valid": bson.M{"$exists": false}},
					{"is_valid": false},
				},
			}},
		},
		&model.BiddingRate{
			IsValid: true,
		},
	)
}
