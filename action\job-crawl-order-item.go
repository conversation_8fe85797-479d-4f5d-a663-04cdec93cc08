package action

import (
	"errors"
	"fmt"
	"log"
	"math"
	"sort"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ProcessingCrawlOrderItem processes a job item by marshaling its data into BSON format,
// unmarshaling it into a CrawlOrderItem model, and then performing calculations based on the CrawlType.
// It returns an error if any step in the process fails.
//
// Parameters:
//   - item: A pointer to a job.JobItem containing the data to be processed.
//
// Returns:
//   - error: An error if any step in the process fails, otherwise nil.
func ProcessingCrawlOrderItem(item *job.JobItem) error {
	data, err := bson.Marshal(item.Data)
	if err != nil {
		return err
	}
	var input model.CrawlOrderItem

	err = bson.Unmarshal(data, &input)
	if err != nil {
		return err
	}

	// Perform calculations based on the CrawlType
	if input.CrawlType == "INITIAL_STOCK" {
		return calcQuotationOrder(input)
	} else if input.CrawlType == "ORDER" {
		return calcQuotationOrder(input)
	}

	return nil
}

// calcQuotationOrder ...
func calcQuotationOrder(input model.CrawlOrderItem) error {
	// get config
	var (
		skuConfig      = input.SkuConfig
		vendorInfo     = input.VendorInfo
		workerCounting = input.WorkerCountingVariable

		// fav vendor cho yêu cầu tăng tồn theo vendor
		favVendorCode  string
		favVendor      *model.VendorConfig
		avgDemand      float64
		skuMaxStockDay float64
	)

	// get initial stock draft
	if len(input.SkuConfig.SKU) > 0 {
		initialStockDraftResp := model.InitialStockDraftDB.QueryOne(model.InitialStockDraft{SKU: skuConfig.SKU, WarehouseCode: workerCounting.WarehouseCode})
		if initialStockDraftResp.Status == common.APIStatus.Ok {
			initialStockDraft := initialStockDraftResp.Data.([]*model.InitialStockDraft)[0]
			avgDemand = initialStockDraft.AVGDemand
			if initialStockDraft.MaxStockDay != nil && *initialStockDraft.MaxStockDay > 0 {
				skuMaxStockDay = *initialStockDraft.MaxStockDay
			}
		}
	}

	// CrawlType=INITIAL_STOCK không có
	saleOrder := input.SaleOrder
	orderItem := input.OrderItem
	needQuantity := orderItem.Quantity
	if orderItem.ReservedQuantity != nil {
		needQuantity -= *orderItem.ReservedQuantity
	}

	// get fav vendor
	var leadtime float64 = 1
	if skuConfig.VendorSelected != nil && skuConfig.VendorSelected.Priority != nil &&
		utils.IsInt64Contains(utils.FAV_PRIORITY, *skuConfig.VendorSelected.Priority) &&
		skuConfig.VendorSelected.VendorCode == vendorInfo.Code {
		leadtime = selectVendorLeadTimes(vendorInfo.InboundAddresses, workerCounting.WarehouseCode)
		favVendorCode = skuConfig.VendorSelected.VendorCode
		favVendor = skuConfig.VendorSelected
	}

	// check quotation export draft
	quotationResp := model.QuotationExportDraftDB.QueryOne(model.QuotationExport{
		Version:       workerCounting.Version,
		SellerCode:    workerCounting.SellerCode,
		ProductCode:   orderItem.ProductCode,
		WarehouseCode: workerCounting.WarehouseCode,
	})

	if quotationResp.Status == common.APIStatus.Error {
		return errors.New(quotationResp.Message)
	}
	// Tạo mới nếu không tồn tại trong DB
	if quotationResp.Status == common.APIStatus.NotFound {

		var (
			now       = time.Now()
			quotation = &model.QuotationExport{
				WarehouseCode:       workerCounting.WarehouseCode,
				SellerCode:          workerCounting.SellerCode,
				ProductID:           orderItem.ProductID,
				ProductCode:         orderItem.ProductCode,
				SKU:                 orderItem.Sku,
				QuantityOrderWithSO: needQuantity,
				QuantityIncoming:    input.QuantityIncoming, // Để dễ trace
				Leadtime:            leadtime,               // Để dễ trace
				Formula:             workerCounting.Setting.CalcPOExpectFormula,
				LastUpdatedTime:     &now,
				Version:             workerCounting.Version,
				Type:                enum.QuotationTypeVal.NORMAL,

				// SOCodes: []string{saleOrder.SaleOrderCode},
				// SOCodesAll: []string{saleOrder.SaleOrderCode},
				// QuantityOrderAllSO:  needQuantity,
			}
		)

		// SOCodes
		if saleOrder.SaleOrderCode != "" {
			quotation.SOCodes = append(quotation.SOCodes, saleOrder.SaleOrderCode)
		}

		// // SOCodesInput
		// if len(input.WorkerCountingVariable.SOCodesInput) == 0 {
		//     quotation.SOCodes = quotation.SOCodesAll
		// } else if !utils.IsContains(workerCounting.SOCodesInput, saleOrder.SaleOrderCode) {
		//     quotation.SOCodes = []string{saleOrder.SaleOrderCode}
		// }

		// để không bị nil pointer
		// quotation.Unit = skuConfig.Unit
		if skuConfig.VendorSelected != nil && skuConfig.VendorSelected.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *skuConfig.VendorSelected.Priority) {
			quotation.UnitPrice = skuConfig.VendorSelected.UnitPrice
			quotation.VAT = skuConfig.VendorSelected.VAT
			// input.VendorPromotionInfo chỉ tồn tại chỉ khi có VendorSelected
			quotation.MinPerStep = selectQuotationMinPerstep(&skuConfig)
			quotation.VendorCode = skuConfig.VendorSelected.VendorCode
		}

		// get InitialStockDraft
		initialStockDraft := &model.InitialStockDraft{}
		initialStockDraftResp := model.InitialStockDraftDB.QueryOne(model.InitialStockDraft{SKU: orderItem.Sku, WarehouseCode: input.WorkerCountingVariable.WarehouseCode})
		if initialStockDraftResp.Status == common.APIStatus.Ok {
			initialStockDraft = initialStockDraftResp.Data.([]*model.InitialStockDraft)[0]
		}

		// lấy tồn kho inbound
		inventorySkuLocation := &model.InventorySkuLocation{}
		// lấy tồn kho
		inventorySku := &model.InventorySku{}
		option := client.APIOption{
			Q: model.InventorySkuLocation{
				SKU:           orderItem.Sku,
				WarehouseCode: input.WorkerCountingVariable.WarehouseCode,
				LocationCode:  model.INVENTORY_LOCATION_INBOUND_CODE,
			},
			Keys: []string{orderItem.Sku, input.WorkerCountingVariable.WarehouseCode},
		}

		// lấy tồn kho inbound
		inventorySkuLocationResp := warehouse.GetInventorySKULocation(option)
		if inventorySkuLocationResp.Status == common.APIStatus.Ok {
			inventorySkuLocation = inventorySkuLocationResp.Data.([]*model.InventorySkuLocation)[0]
			inventorySku = &inventorySkuLocation.SKUInfo
		}
		if inventorySkuLocationResp.Status != common.APIStatus.Ok {
			quotation.Logs = append(quotation.Logs, "GET_INVENTORY_LOCATION_ERROR")
		}

		// lấy tồn kho
		if inventorySkuLocationResp.Status != common.APIStatus.Ok || inventorySku.Sku == "" {
			option := client.APIOption{
				Q: model.InventorySku{
					Sku:           orderItem.Sku,
					WarehouseCode: input.WorkerCountingVariable.WarehouseCode,
				},
				Keys: []string{orderItem.Sku, input.WorkerCountingVariable.WarehouseCode},
			}
			inventorySkuResp := warehouse.GetInventorySKU(option)

			if inventorySkuResp.Status == common.APIStatus.Ok {
				inventorySku = inventorySkuResp.Data.([]*model.InventorySku)[0]
			}
			if inventorySkuResp.Status != common.APIStatus.Ok {
				quotation.Logs = append(quotation.Logs, "GET_INVENTORY_ERROR")
			}
		}

		if initialStockDraft.InitialStock == nil {
			// nếu không có initial stock thì set mặc định là 0
			initialStockDraft.InitialStock = utils.Pointer.WithInt64(0)
		}

		if initialStockDraft.MaxQuantity == nil {
			// nếu không có max quantity thì set mặc định là 0
			initialStockDraft.MaxQuantity = utils.Pointer.WithInt64(0)
		}

		// set data
		quotation.ProductName = inventorySku.Name
		quotation.Packaging = inventorySku.Packaging
		quotation.QuantityInbound = inventorySkuLocation.StockQuantity
		quotation.QuantityAvailable = inventorySku.AvailableQuantity
		quotation.MinInStock = *initialStockDraft.InitialStock // initial stock
		quotation.MaxInStock = *initialStockDraft.MaxQuantity  // max initial stock
		quotation.SKUVendorType = selectSKUVendorType(vendorInfo, skuConfig.VendorSelected)

		// Calculate promotion balance
		quantityPromotion, quantityPromotionVendor, promotionApplieds := getQuantityPromotionSKU(quotation.SKU, quotation.WarehouseCode, favVendorCode)

		// xoá việc tính toán quotation dựa vào hedging
		// quantityRequest, quantityRequestVendor, requestApplieds := getQuantityRequestSKU(quotation.SKU, quotation.WarehouseCode, favVendorCode, *quotation)
		quantitySaveInitialStock, saveInitialApplieds, beginDayByInitials := getQuantitySaveInitialStock(int64(workerCounting.Setting.POArrivedDay), leadtime, quotation.WarehouseCode, workerCounting.SellerCode, orderItem.Sku, vendorInfo, skuConfig.VendorSelected, *initialStockDraft)
		// logs
		quotation.Logs = append(quotation.Logs, beginDayByInitials...)

		quotation.InboundRequestItemApplieds = append(quotation.InboundRequestItemApplieds, promotionApplieds...)
		// quotation.InboundRequestItemApplieds = append(quotation.InboundRequestItemApplieds, requestApplieds...)
		quotation.InboundRequestItemApplieds = append(quotation.InboundRequestItemApplieds, saveInitialApplieds...)

		// inbound request
		quotation.QuantityPromotionIR = quantityPromotion
		// xoá việc tính toán quotation dựa vào hedging
		// quotation.QuantityOtherIR = quantityRequest
		quotation.QuantitySaveInitialStock = quantitySaveInitialStock

		// vendor inbound request
		quotation.QuantityPromotionWithVendorIR = quantityPromotionVendor
		// xoá việc tính toán quotation dựa vào hedging
		// quotation.QuantityOtherWithVendorIR = quantityRequestVendor

		isFavVendor := len(favVendorCode) > 0 && favVendor != nil
		temp := calcExpectQuantity(quotation, isFavVendor)
		quotation.QuantityExpect = utils.Pointer.WithInt64(temp)
		// xoá việc tính toán quotation dựa vào hedging
		// temp += quotation.QuantityOtherIR
		// temp += quotation.QuantityOtherWithVendorIR

		// tính quotation hedging cho sku
		mapHedging, totalHedgingFav := createHedgingQuotationExportDraftFromInboundRequest(orderItem.Sku, workerCounting.WarehouseCode, quotation, favVendor)
		// biến này sẽ được + vào quantityExpect để đi ctkm/multiple/MOA
		quotation.QuantityHedging = totalHedgingFav

		var totalHedgingExpected int64
		var isHasDemand bool

		// tính promotion cho vendor
		if isFavVendor {
			// tính promotion cho vendor
			promotionExpect := calcExpectVendorFav(input.VendorPromotions, quotation, skuConfig, *favVendor, avgDemand, skuMaxStockDay)
			if promotionExpect != nil {
				// trường hợp có mua hàng demand
				// So lượng VendorExpectQuantity sau cùng sẽ là SuggestQty - totalHedgingFav
				if quotation.QuantityExpect != nil && *quotation.QuantityExpect > 0 {
					tempVendorExpectQuantity := int64(promotionExpect.SuggestQty) - totalHedgingFav
					quotation.VendorExpectQuantity = utils.Pointer.WithInt64(int64(tempVendorExpectQuantity))
					quotation.TrackVendorPromotionExpect = promotionExpect
					isHasDemand = true
				} else {
					// trường hợp khônng có mua hàng demand thì VendorExpectQuantity sẽ được thêm vào ở quotation hedging.
					totalHedgingExpected = int64(promotionExpect.SuggestQty)
				}
			}
		}

		// Luu hedging vào DB
		if len(mapHedging) > 0 {
			createHedgingQuotationExportDraft(mapHedging, favVendor, isHasDemand, totalHedgingFav, totalHedgingExpected)
		}

		// skip create quotation for calc by CrawlType=INITIAL_STOCK
		if input.CrawlType == "INITIAL_STOCK" {
			if quotation.VendorExpectQuantity == nil || *quotation.VendorExpectQuantity == 0 {
				if quotation.QuantityExpect == nil || *quotation.QuantityExpect == 0 {
					return nil
				}
			}
		}

		go logQuotationHaveMinGreaterThanMax(quotation)

		// create quotation
		resp := model.QuotationExportDraftDB.Create(quotation)
		if resp.Status != common.APIStatus.Ok {
			return errors.New(resp.Message)
		}

	} else if quotationResp.Status == common.APIStatus.Ok {

		// skip for calc by CrawlType=INITIAL_STOCK
		if input.CrawlType == "INITIAL_STOCK" {
			return nil
		}

		quotation := quotationResp.Data.([]*model.QuotationExport)[0]

		// SOCodes
		if !utils.IsContains(workerCounting.SOCodesInput, saleOrder.SaleOrderCode) {
			quotation.SOCodes = append(quotation.SOCodes, saleOrder.SaleOrderCode)
		}

		// Cập nhật lại số lượng count trong DB
		isFavVendor := len(favVendorCode) > 0 && favVendor != nil
		quotation.QuantityOrderWithSO += needQuantity
		temp := calcExpectQuantity(quotation, isFavVendor)
		temp += quotation.QuantityOtherIR
		temp += quotation.QuantityOtherWithVendorIR
		quotation.QuantityExpect = &temp

		// tính quotation hedging cho sku
		mapHedging, totalHedgingFav := createHedgingQuotationExportDraftFromInboundRequest(orderItem.Sku, workerCounting.WarehouseCode, quotation, favVendor)
		quotation.QuantityHedging = totalHedgingFav
		quotation.QuantityExpect = utils.Pointer.WithInt64(temp)

		var totalHedgingExpected int64
		var isHasDemand bool

		// if fav vendor
		if isFavVendor {

			// tính promotion cho vendor
			promotionExpect := calcExpectVendorFav(input.VendorPromotions, quotation, skuConfig, *favVendor, avgDemand, skuMaxStockDay)
			if promotionExpect != nil {
				// trường hợp có mua hàng demand
				if quotation.QuantityExpect != nil && *quotation.QuantityExpect > 0 {
					tempVendorExpectQuantity := int64(promotionExpect.SuggestQty) - totalHedgingFav
					quotation.VendorExpectQuantity = utils.Pointer.WithInt64(int64(tempVendorExpectQuantity))
					quotation.TrackVendorPromotionExpect = promotionExpect
					isHasDemand = true
				} else {
					// trường hợp khônng có mua hàng demand thì VendorExpectQuantity sẽ được thêm vào ở quotation hedging.
					totalHedgingExpected = int64(promotionExpect.SuggestQty)
				}
			}
		}

		go logQuotationHaveMinGreaterThanMax(quotation)

		// Luu hedging vào DB
		model.QuotationExportDraftDB.UpdateOne(model.Quotation{ID: quotation.ID}, quotation)

		if len(mapHedging) > 0 {
			createHedgingQuotationExportDraft(mapHedging, favVendor, isHasDemand, totalHedgingFav, totalHedgingExpected)
		}

	}
	return nil
}

// getQuantityPromotionSKU ...
func getQuantityPromotionSKU(sku, warehouseCode, favVendorCode string) (promotionAllBalance int64, promotionVendorBalance int64, inboundRequestItemApplieds []string) {
	// Calculate promotion balance
	now := time.Now()
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)

	// get Yêu cầu tăng tồn có type là promotion
	inboundPromotionResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		SKU:                sku,
		WarehouseCode:      warehouseCode,
		InboundRequestType: enum.InboundRequestType.PROMOTION,
		Status:             enum.InboundRequestStatus.CONFIRMED,
		ComplexQuery: []*bson.M{{
			"end_time": bson.M{"$gte": nowYYYYMMDD},
		}},
	}, 0, 1000, nil)
	if inboundPromotionResp.Status == common.APIStatus.Ok {
		inboundPromotions := inboundPromotionResp.Data.([]*model.InboundRequestItem)

	LOOP_PROMOTION:
		// Calculate promotion balance
		for _, inboundPromotion := range inboundPromotions {
			if inboundPromotion.StartTime == "" || inboundPromotion.EndTime == "" ||
				inboundPromotion.IsActive == nil || *inboundPromotion.IsActive == false {
				continue LOOP_PROMOTION
			}

			// Calculate promotion balance
			indexDate := utils.GetIndexNowTwoDate(inboundPromotion.EndTime, inboundPromotion.StartTime)
			if indexDate >= utils.MAX_INDEX_DATE {
				continue LOOP_PROMOTION
			}

			// tất cả hoặc vendor fav
			if inboundPromotion.VendorCode == nil || len(*inboundPromotion.VendorCode) == 0 {
				// tất cả
				if indexDate <= utils.MIN_INDEX_DATE {
					promotionAllBalance += int64(inboundPromotion.ExpectQuantity)
				} else {
					datePromotion := utils.GetHourDiffrenceDay(inboundPromotion.EndTime, inboundPromotion.StartTime) / 24
					averagePromotion := math.Ceil(float64(inboundPromotion.ExpectQuantity) / datePromotion)
					promotionAllBalance += inboundPromotion.ExpectQuantity - indexDate*int64(averagePromotion)
				}

				inboundRequestItemApplieds = append(inboundRequestItemApplieds, inboundPromotion.InboundRequestItemCode)

			} else if inboundPromotion.VendorCode != nil && *inboundPromotion.VendorCode == favVendorCode {
				// vendor fav
				if indexDate <= utils.MIN_INDEX_DATE {
					promotionVendorBalance += int64(inboundPromotion.ExpectQuantity)
				} else {
					datePromotion := utils.GetHourDiffrenceDay(inboundPromotion.EndTime, inboundPromotion.StartTime) / 24
					averagePromotion := math.Ceil(float64(inboundPromotion.ExpectQuantity) / datePromotion)
					promotionVendorBalance += inboundPromotion.ExpectQuantity - indexDate*int64(averagePromotion)
				}

				inboundRequestItemApplieds = append(inboundRequestItemApplieds, inboundPromotion.InboundRequestItemCode)
			}
		}
	}

	return promotionAllBalance, promotionVendorBalance, inboundRequestItemApplieds
}

// createHedgingQuotationExportDraftFromInboundRequest ...
func createHedgingQuotationExportDraftFromInboundRequest(sku, warehouseCode string, quotation *model.QuotationExport, favVendor *model.VendorConfig) (mapHedging map[string]model.QuotationExport, totalHedging int64) {
	// Calculate promotion balance
	now := time.Now()
	nowYYYYMMDD := utils.ConvertTimeToStringYYYYMMDD(&now, utils.YYYYMMDD_ENDASH)
	mapQuotationFromHedging := make(map[string]model.QuotationExport)
	otherResp := model.InboundRequestItemDB.Query(model.InboundRequestItem{
		SKU:                sku,
		WarehouseCode:      warehouseCode,
		InboundRequestType: enum.InboundRequestType.OTHER,
		Status:             enum.InboundRequestStatus.CONFIRMED,
		ApplyTime:          nowYYYYMMDD,
	}, 0, 1000, nil)

	// get all inbound request
	if otherResp.Status == common.APIStatus.Ok {
		others := otherResp.Data.([]*model.InboundRequestItem)
		purchaserOrderCodes := []string{}
		for _, item := range others {
			purchaserOrderCodes = append(purchaserOrderCodes, item.POCode)
		}

		purchaseOrderMap := make(map[string]struct{})

		// get just for PO != CANCELED
		getPurchaseOrders := model.PurchaseOrderDB.Query(model.PurchaseOrder{
			ComplexQuery: []*bson.M{
				{"po_code": bson.M{"$in": purchaserOrderCodes}},
			},
		}, 0, 1000, nil)
		if getPurchaseOrders.Status == common.APIStatus.Ok {
			purchaseOrders := getPurchaseOrders.Data.([]*model.PurchaseOrder)
			for _, purchaseOrder := range purchaseOrders {

				// Nếu PO đã bị hủy thì bỏ qua
				if purchaseOrder.Status == enum.PurchaseOrderStatus.CANCELED {
					continue
				}

				purchaseOrderMap[purchaseOrder.POCode] = struct{}{}
			}
		}

		// Duyệt qua từng other
		for _, req := range others {
			// This is a case where the PO was created by tool, so that we need to ignore these items
			if _, exists := purchaseOrderMap[req.POCode]; exists {
				continue
			}

			// Nếu inbound request đã được tinh toán rồi thì bỏ qua
			if req.IsActive == nil || *req.IsActive == false {
				continue
			}

			var isExisted bool = false
			// kiểm tra xem đã có hedging nháp chưa, nếu đã có thì return
			hedgingResp := model.QuotationExportHedgingDraftDB.QueryOne(
				model.QuotationExport{
					Version:                quotation.Version,
					WarehouseCode:          warehouseCode,
					Type:                   enum.QuotationTypeVal.HEDGING,
					InboundRequestCode:     req.InboundRequestCode,
					InboundRequestItemCode: req.InboundRequestItemCode,
					ProductCode:            quotation.ProductCode,
				})

			if hedgingResp.Status == common.APIStatus.Ok {
				isExisted = true
			}

			if favVendor != nil && req.VendorCode != nil && *req.VendorCode == favVendor.VendorCode {
				// tổng số lượng hedging
				totalHedging += req.ExpectQuantity
			}

			vendorCode := *req.VendorCode
			key := genKeyVendorSKUWarehouseIR(vendorCode, sku, warehouseCode, req.InboundRequestItemCode)

			// nếu chưa có thì tạo mới
			hedging, ok := mapQuotationFromHedging[key]
			if !ok {
				hedging = model.QuotationExport{
					VendorCode:             *req.VendorCode,
					SKU:                    sku,
					WarehouseCode:          warehouseCode,
					Version:                quotation.Version,
					ProductCode:            quotation.ProductCode,
					ProductID:              quotation.ProductID,
					ProductName:            quotation.ProductName,
					SellerCode:             quotation.SellerCode,
					Unit:                   quotation.Unit,
					Type:                   enum.QuotationTypeVal.HEDGING,
					InboundRequestCode:     req.InboundRequestCode,
					InboundRequestID:       req.InboundRequestID,
					InboundRequestItemCode: req.InboundRequestItemCode,
					UnitPrice:              req.UnitPrice,
					VAT:                    int64(req.VAT),
					IsExisted:              isExisted,
				}

				// Nếu QuantityExpect == nil thì QuantityExpect = 0
				if hedging.QuantityExpect == nil {
					hedging.QuantityExpect = new(int64)
				}
				*hedging.QuantityExpect += req.ExpectQuantity
				hedging.InboundRequestCode = req.InboundRequestCode
				hedging.InboundRequestItemApplieds = append(hedging.InboundRequestItemApplieds, req.InboundRequestItemCode)
				// lưu vào map
				mapQuotationFromHedging[key] = hedging
			}
		}
	}

	mapHedging = mapQuotationFromHedging

	return
}

// getQuantitySaveInitialStock ...
// getQuantitySaveInitialStock calculates the quantity to save for initial stock based on various parameters and conditions.
// It returns the calculated quantity, a list of applied inbound request item codes, and a list of begin days by initials.
//
// Parameters:
// - poArrivedDay: The day the purchase order is expected to arrive.
// - leadtime: The lead time for the vendor.
// - warehouseCode: The code of the warehouse.
// - sellerCode: The code of the seller.
// - sku: The SKU code of the product.
// - vendorInfo: Information about the seller.
// - vendorSelected: Configuration of the selected vendor.
// - initialStockDraft: Draft information about the initial stock.
//
// Returns:
// - int64: The calculated quantity to save for initial stock.
// - []string: A list of applied inbound request item codes.
// - []string: A list of begin days by initials.
func getQuantitySaveInitialStock(poArrivedDay int64, leadtime float64, warehouseCode, sellerCode, sku string, vendorInfo model.Seller, vendorSelected *model.VendorConfig, initialStockDraft model.InitialStockDraft) (int64, []string, []string) {
	// logs
	beginDayByInitials := []string{}

	if leadtime <= 0 {
		leadtime = 1
	}

	// check vendor fav trading
	isVendorFavTrading := false
	if vendorInfo.SellerType == "TRADING" && vendorSelected != nil && vendorSelected.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendorSelected.Priority) {
		isVendorFavTrading = true
	}

	// get initial stock
	nowWeekDay := utils.GetDayOfWeek(nil)
	if nowWeekDay == -1 { // initialStock == 0 ||
		return 0, nil, beginDayByInitials
	}

	var saveInitialStock int64 = 0
	inboundRequestItemApplieds := []string{}
	// get inbound request
	inboundRequestResp := model.InboundRequestDB.Query(model.InboundRequest{
		SellerCode:         sellerCode,
		WarehouseCode:      warehouseCode,
		InboundRequestType: enum.InboundRequestType.SAVE_INITIAL_STOCK,
		Status:             enum.InboundRequestStatus.CONFIRMED,
	}, 0, 1000, &primitive.M{"_id": -1})
	if inboundRequestResp.Status == common.APIStatus.Ok {
		inboundRequests := inboundRequestResp.Data.([]*model.InboundRequest)

		//
		for _, inboundRequest := range inboundRequests {

			// Nếu không có IsActive hoặc IsActive == false thì bỏ qua
			if inboundRequest.IsActive == nil || !*inboundRequest.IsActive {
				continue
			}

			// Nếu không có StartTime hoặc EndTime thì bỏ qua
			if inboundRequest.EndWeekday != 2 && inboundRequest.EndWeekday != 3 {
				if nowWeekDay >= inboundRequest.EndWeekday {
					continue
				}
			}

			// Nếu không có SkuCodes hoặc SkuCodes không chứa sku thì bỏ qua
			if inboundRequest.SkuCodesSkip != nil && utils.IsContains(*inboundRequest.SkuCodesSkip, sku) {
				continue
			}

			if inboundRequest.EndWeekday == 2 {
				inboundRequest.EndWeekday = 7
			}
			if inboundRequest.EndWeekday == 3 {
				inboundRequest.EndWeekday = 8
			}
			// nếu ngày trước T7,CN = 1
			var N int64 = 1
			// Trường hợp mua bù cho t7,cn giao hàng trễ nhất là vào ngày `poArrivedDay`(Mặc định poArrivedDay: 6)
			if inboundRequest.EndWeekday == 8 || inboundRequest.EndWeekday == 7 {
				N = int64(inboundRequest.EndWeekday) - poArrivedDay

				// Nếu là trading giao hàng trễ nhất là vào ngày thứ 5(Trước non-trading 1 ngày)
				if isVendorFavTrading {
					N += 1
				}

			}

			beginDay := float64(inboundRequest.EndWeekday) - float64(N)
			if isVendorFavTrading {
				// Nếu là trading giao hàng trễ nhất là vào ngày thứ 5
				beginDay -= leadtime
			} else {
				// Trường hợp không có vendor config trong sku-config
				// Hoặc vendor trong sku-config không phải là fav 0
				// Hoặc vendor fav 0 không phải là trading
				// => Mặc định sẽ mua vào thứ 6 - Không cần trừ cho leadtime
			}

			if float64(nowWeekDay) >= beginDay {
				// // Trường hợp không có vendorFav0 || vendorFav0 == NON-TRADING || không có leadtime => leadtime là 1(để không bị lỗi)
				// // Ngược lại, leadtime = vendorFav0.leadtime
				// saveInitialStock += int64(float64(initialStock) / leadtime)

				// get avgDemand
				saveInitialStock += int64(initialStockDraft.AVGDemand)

				// else {
				// 	avgDemand = int64(float64(initialStock) / leadtime)
				// }

				inboundRequestItemApplieds = append(inboundRequestItemApplieds, inboundRequest.InboundRequestCode)
			}
			beginDayByInitials = append(beginDayByInitials, fmt.Sprintf("Ngày bắt đầu mua là %v", beginDay))
		}
	}

	return saveInitialStock, inboundRequestItemApplieds, beginDayByInitials
}

// calcExpectQuantity ...
// calcExpectQuantity calculates the expected quantity of an order item based on the given quotation and vendor preference.
// It uses various parameters from the quotation to compute the quantity using a formula.
//
// Parameters:
// - quotation: A model.QuotationExport object containing details about the quotation.
// - isFavVendor: A boolean indicating whether the vendor is a favorite vendor.
//
// Returns:
// - int64: The calculated expected quantity. Returns 0 if there is an error in the formula parsing or if the result is invalid.
func calcExpectQuantity(quotation *model.QuotationExport, isFavVendor bool) int64 {
	parameters := make(map[string]interface{})
	parameters["ORDER_QUANTITY"] = quotation.QuantityOrderWithSO
	parameters["AVAILABLE_QUANTITY"] = quotation.QuantityAvailable
	parameters["INCOMING_QUANTITY"] = quotation.QuantityIncoming
	// mặc định lấy min_in_stock là tồn min
	parameters["MIN_IN_STOCK"] = quotation.MinInStock
	parameters["INBOUND_QUANTITY"] = quotation.QuantityInbound
	parameters["PROMOTION_QUANTITY"] = quotation.QuantityPromotionIR
	parameters["SAVE_INITIAL_STOCK_QUANTITY"] = quotation.QuantitySaveInitialStock
	parameters["VENDOR_PROMOTION_QUANTITY_IR"] = 0

	if isFavVendor {
		parameters["VENDOR_PROMOTION_QUANTITY_IR"] = quotation.QuantityPromotionWithVendorIR
	}

	parameters["MIN_QUANTITY"] = quotation.MinInStock

	quotation.LogFormulaData = parameters

	// formula
	result, err := utils.ParseFormulaToInterface(quotation.Formula, parameters)
	if err != nil {
		return 0
	}
	valMin, ok := result.(float64)
	if !ok || math.IsInf(valMin, 0) || math.IsNaN(valMin) || valMin < 0 {
		return 0
	}
	// tồn max không được config hoặc tồn min nhỏ hơn hoặc không có số lượng đi mua
	if quotation.MaxInStock == 0 || quotation.MinInStock <= quotation.MaxInStock || valMin <= 0 {
		return int64(math.Ceil(valMin))
	}

	//------------------------------------------------------------//
	// sử dụng tồn max thay min để tính lại số lượng cần đi mua
	parameters["MIN_IN_STOCK"] = quotation.MaxInStock

	// support tracelog
	parameters["MAX_QUANTITY"] = quotation.MaxInStock

	quotation.LogFormulaData = parameters
	result, err = utils.ParseFormulaToInterface(quotation.Formula, parameters)
	if err != nil {
		return 0
	}
	valMax, ok := result.(float64)
	if !ok || math.IsInf(valMax, 0) || math.IsNaN(valMax) || valMax < 0 {
		return 0
	}
	return int64(math.Ceil(valMax))
}

// calcExpectByVendorStep ...
// calcExpectByVendorStep calculates the expected quantity by vendor step.
// It takes the total expected quantity from the vendor, the vendor step value,
// and an optional minimum per order quantity. If the optional minimum per order
// quantity is provided and greater than zero, it will be used in the calculation.
// The function returns the calculated expected quantity rounded up to the nearest
// multiple of the vendor step.
//
// Parameters:
// - totalVendorExpectQty: The total expected quantity from the vendor.
// - vendorStep: The step value for the vendor.
// - minPerOrderOtp: Optional minimum per order quantity.
//
// Returns:
//   - result: The calculated expected quantity rounded up to the nearest multiple
//     of the vendor step.
func calcExpectByVendorStep(totalVendorExpectQty, vendorStep float64, minPerOrderOtp ...float64) (result float64) {
	minPerOrder := float64(0)
	if len(minPerOrderOtp) > 0 && minPerOrderOtp[0] > 0 {
		minPerOrder = minPerOrderOtp[0]
	}
	return math.Ceil(utils.MaxFloat64(totalVendorExpectQty, minPerOrder)/utils.MaxFloat64(vendorStep, 1)) * utils.MaxFloat64(vendorStep, 1)
}

// lấy leadtime chính xác hoặc leadtime cùng purchaser
// selectVendorLeadTimes selects the lead time for a vendor based on the provided warehouse code.
// It iterates through the list of vendor lead times and checks if the warehouse code matches
// the provided warehouse code or if the purchaser code of the warehouse matches the purchaser code
// of the vendor lead time's warehouse. If a match is found and the lead time is greater than zero,
// it returns the lead time. If no match is found, it returns a default lead time of 1.
//
// Parameters:
// - vendorLeadtimes: A slice of InboundAddress containing vendor lead times and warehouse codes.
// - warehouseCode: A string representing the warehouse code to match.
//
// Returns:
// - A float64 representing the selected lead time.
func selectVendorLeadTimes(vendorLeadtimes []model.InboundAddress, warehouseCode string) float64 {
	leadtime := float64(1)
	purchaserOfWarehouse, ok := utils.WarehouseCodeToPurchaserCode(warehouseCode)
	if !ok {
		return 1
	}

	// iterate through vendor lead times
	for _, vLeadtime := range vendorLeadtimes {
		vLeadTimeWarehouseCode := vLeadtime.WarehouseCode

		if vLeadTimeWarehouseCode == warehouseCode && vLeadtime.Leadtime > 0 {
			leadtime = vLeadtime.Leadtime
			break
		}

		// check if the purchaser code of the warehouse matches the purchaser code of the vendor lead time's warehouse
		vLeadTimePurchaser, ok := utils.WarehouseCodeToPurchaserCode(vLeadTimeWarehouseCode)
		if !ok || vLeadTimePurchaser != purchaserOfWarehouse {
			continue
		}

		leadtime = vLeadtime.Leadtime
	}

	return leadtime
}

// selectSKUVendorType determines the SKU vendor type based on the given vendor and vendor configuration.
//
// Parameters:
// - vendor: The seller information of type model.Seller.
// - vendorConfig: A pointer to the vendor configuration of type *model.VendorConfig.
//
// Returns:
// - enum.SKUVendorType: The SKU vendor type which can be either TRADING or NON_TRADING.
//
// The function returns NON_TRADING if the vendorConfig is nil or if the Priority field in vendorConfig is nil.
// If the vendor's SellerType is TRADING and the Priority is in the FAV_PRIORITY list, it returns TRADING.
// Otherwise, it returns NON_TRADING.
func selectSKUVendorType(vendor model.Seller, vendorConfig *model.VendorConfig) enum.SKUVendorType {
	if vendorConfig == nil || vendorConfig.Priority == nil {
		return enum.SKUVendorValue.NON_TRADING
	}

	if vendor.SellerType == enum.SellerTypeValue.TRADING && utils.IsInt64Contains(utils.FAV_PRIORITY, *vendorConfig.Priority) {
		return enum.SKUVendorValue.TRADING
	}

	return enum.SKUVendorValue.NON_TRADING
}

// selectQuotationMinPerstep determines the minimum quantity per step for a given SKU configuration.
// It returns the minimum quantity per step if the SKU configuration, selected vendor, and vendor priority are valid and meet the criteria.
// Otherwise, it returns 0.
//
// Parameters:
// - skuConfig: A pointer to the SkuConfig struct containing SKU configuration details.
//
// Returns:
// - int64: The minimum quantity per step.
func selectQuotationMinPerstep(skuConfig *model.SkuConfig) int64 {
	minPerStep := int64(0)

	if skuConfig != nil && skuConfig.VendorSelected != nil && skuConfig.VendorSelected.Priority != nil && utils.IsInt64Contains(utils.FAV_PRIORITY, *skuConfig.VendorSelected.Priority) {
		minPerStep = skuConfig.VendorSelected.MinPerStep
	}

	return minPerStep
}

type promotionItemMetadata struct {
	isGift     bool
	isDiscount bool

	VendorPromotionItemID int64

	giftItem model.VendorGiftItem

	// quy cách khuyến mãi
	promotionStep float64

	// quy cách đề xuất
	suggestStep float64

	// số lượng tặng
	giftQuantity float64

	// track cùng sản phẩm điều kiện - tặng
	xSameY bool

	// số lượng dự kiến nhận
	newExpectQuantity float64

	// số lượng tặng dự kiến
	giftExpectQty float64 // major

	// số lượng đề xuất
	suggestQuantity float64 // major

	// current stock day
	currentStockDay float64

	promotionCode string
}

// calcExpectVendorFav calculates the expected vendor promotion based on various parameters such as promotions, quotation export, SKU configuration, vendor configuration, average demand, and maximum stock day for the SKU.
// It returns a pointer to a VendorPromotionExpect model.
//
// Parameters:
// - promotions: A slice of VendorPromotion pointers representing the available promotions.
// - quotationExport: A pointer to a QuotationExport model containing the quotation export details.
// - skuConf: An SkuConfig model representing the SKU configuration.
// - vendorFav: A VendorConfig model representing the vendor configuration.
// - avgDemand: A float64 representing the average demand for the SKU.
// - skuMaxStockDay: A float64 representing the maximum stock day for the SKU.
//
// Returns:
// - A pointer to a VendorPromotionExpect model representing the expected vendor promotion, or nil if certain conditions are not met.
//
// The function performs the following steps:
// 1. Validates the input parameters and logs errors if necessary.
// 2. Calculates the expected quantity to be purchased based on the quotation export details.
// 3. Retrieves the maximum stock day setting from the database.
// 4. Iterates through the promotions and calculates the suggested quantity and stock day for each promotion item.
// 5. Checks if the calculated stock day satisfies the maximum stock day constraint.
// 6. If the stock day constraint is not satisfied, recalculates the suggested quantity based on vendor step and promotion step.
// 7. Returns the expected vendor promotion if the stock day constraint is satisfied, otherwise returns a default promotion expect based on vendor step.
func calcExpectVendorFav(promotions []*model.VendorPromotion, quotationExport *model.QuotationExport, skuConf model.SkuConfig, vendorFav model.VendorConfig, avgDemand, skuMaxStockDay float64) *model.VendorPromotionExpect {
	if quotationExport == nil {
		model.ErrorLogDB.Create(model.ErrorLog{
			Keys: []string{"calcExpectVendorFav", "selectReferPrice"},
			Type: "calcExpectVendorFav",
			Data: "quotationExport is nil",
		})

		return nil
	}

	// số lượng mua kiến nhận
	var qtyExpect int64
	if quotationExport.QuantityExpect != nil && *quotationExport.QuantityExpect > 0 {
		qtyExpect += *quotationExport.QuantityExpect
	}
	if quotationExport.QuantityHedging != 0 {
		qtyExpect += quotationExport.QuantityHedging
	}

	var (
		// số lượng tối thiếu mỗi lần mua (ưu tiên 2)
		vendorMinPerOrder = float64(vendorFav.MinPerOrder)

		// quy cách sản phẩm (ưu tiên 1)
		vendorStep = float64(vendorFav.MinPerStep)

		// demand
		orderQty = float64(quotationExport.QuantityOrderWithSO)

		// config stock day
		useMinSuggest bool

		// max stock day (ngày)
		maxDayInstock float64

		// unavailable quantity
		originalUnavailableQty = float64(qtyExpect)

		// vendor unavailable quantity
		vendorUnavailableQty = originalUnavailableQty // đã cộng hedging vendor

		listPromotionExpectQty []model.VendorPromotionExpect

		// giá tham chiếu
		referPrice = selectReferPrice(skuConf, vendorFav)

		// thoả mãn và không thoả mãn để tính lại
		listPromotionItemMetadata = []*promotionItemMetadata{}

		// tổng tồn kho
		inventoryQty float64
	)

	if vendorStep < 1 {
		vendorStep = 1
	}

	// không mua thì k cần tính
	if vendorUnavailableQty <= 0 {
		return nil
	}

	// inventory qty
	inventoryQty = float64(utils.MaxInt64(quotationExport.QuantityAvailable + quotationExport.QuantityIncoming + quotationExport.QuantityInbound))

	// get setting max day instock
	getSettingResp := model.SettingDB.QueryOne(nil)
	if getSettingResp.Status != common.APIStatus.Ok {
		model.ErrorLogDB.Create(model.ErrorLog{
			Keys: []string{"calcExpectVendorFav", "get setting error"},
			Type: "calcExpectVendorFav",
			Data: getSettingResp.Message,
		})

		return nil
	}
	setting := getSettingResp.Data.([]*model.Setting)[0]
	for _, dayInStock := range *setting.StockDays {
		if dayInStock.WarehouseCode == quotationExport.WarehouseCode {
			// ưu tiên stock day (2)
			maxDayInstock = float64(dayInStock.MaxStockDay)
			if dayInStock.UseMinSuggestQuantity != nil && *dayInStock.UseMinSuggestQuantity {
				useMinSuggest = true
			}
			break
		}
	}

	// ưu tiên sku stock day (1)
	if skuMaxStockDay > 0 {
		maxDayInstock = skuMaxStockDay
	}

	// tính lần 1:
	// - ưu tiên 1: quy cách promotion
	// - ưu  tiên 2: lấy quy cách sản phẩm theo vendor
LOOP_PROMOTION:
	for _, promotion := range promotions {
		switch promotion.PromotionType {
		case enum.VendorPromotionType.DISCOUNT:
			for _, discountItem := range promotion.VendorDiscountItems { // len(list) == 1
			LOOP_DISCOUNT_REWARD:
				for _, discountProduct := range discountItem.DiscountRewardProducts {
					if discountProduct.ProductCode != skuConf.ProductCode {
						continue LOOP_DISCOUNT_REWARD
					}

					tempReferprice := referPrice

					if discountItem.DiscountRangeType == enum.VendorDiscountRangeType.QUANTITY {
						tempReferprice = 1
					}

					// số lượng mua đề xuất theo từng bậc
					for _, rangeReward := range discountItem.DiscountRangeRewards {
						discountMetadata := promotionItemMetadata{isDiscount: true}
						discountMetadata.promotionCode = promotion.VendorPromotionCode
						tempExpectVendor := calcExpectByVendorStep(vendorUnavailableQty, vendorStep)
						tempExpectPromtion := math.Ceil(rangeReward.From/(vendorStep*utils.MaxFloat64(tempReferprice, 1))) * vendorStep
						// ROUND_UP (mua từ / QC sản phẩm * giá mua )
						discountMetadata.suggestQuantity = utils.MaxFloat64(tempExpectVendor, tempExpectPromtion)
						discountMetadata.newExpectQuantity = discountMetadata.suggestQuantity
						discountMetadata.currentStockDay = computeCurrentStockday(discountMetadata.suggestQuantity, avgDemand, orderQty, inventoryQty)

						discountMetadata.VendorPromotionItemID = discountItem.VendorPromotionItemID // log

						listPromotionItemMetadata = append(listPromotionItemMetadata, &discountMetadata)
					}
				}
			}

		case enum.VendorPromotionType.GIFT:
		LOOP_GIFT_ITEM:
			for _, giftItem := range promotion.VendorGiftItems {
				giftMetadata := promotionItemMetadata{isGift: true}

			LOOP_GIFT_CONDITION:
				for _, condition := range giftItem.GiftConditions { // len(list) == 1
					if condition.ProductCode != skuConf.ProductCode {
						continue LOOP_GIFT_CONDITION
					}

					switch giftItem.GiftConditionType {
					case enum.VendorGiftConditionType.QUANTITY:
						giftMetadata.promotionStep = float64(giftItem.TotalPerStepVendor)
					case enum.VendorGiftConditionType.AMOUNT:
						giftMetadata.promotionStep = math.Ceil(float64(giftItem.TotalPerStepVendor) / utils.MaxFloat64(referPrice, 1)) // ROUND_UP(QC khuyến mãi, giá mua)
					default:
						continue LOOP_GIFT_ITEM
					}

					giftMetadata.giftItem = *giftItem

					giftMetadata.suggestStep = float64(utils.LCM(int64(vendorStep), int64(giftMetadata.promotionStep)))

					// không bao giờ xảy ra
					if giftMetadata.suggestStep < 1 {
						model.ErrorLogDB.Create(model.ErrorLog{
							Keys: []string{"calcExpectVendorFav", "suggest step < 1"},
							Type: "calcExpectVendorFav",
							Data: fmt.Sprintf("suggestStep: %f, sku: %s", giftMetadata.suggestStep, skuConf.SKU),
						})
						continue LOOP_GIFT_CONDITION
					}

				LOOP_REWARD:
					for _, reward := range giftItem.GiftRewards {
						giftMetadata.giftQuantity = float64(reward.Quantity)
						if reward.ProductCode == skuConf.ProductCode {
							giftMetadata.xSameY = true
							break LOOP_REWARD
						}
					}

					// rẽ nhánh theo loại gift (x == y)
					if giftMetadata.xSameY {
						// 1. sp tặng chính bằng sp mua (x == y)
						// ROUND_UP(demand,(QC đề xuất + SL tặng)) * QC đề xuất
						giftMetadata.suggestQuantity = math.Ceil(vendorUnavailableQty/(giftMetadata.suggestStep+giftMetadata.giftQuantity)) * giftMetadata.suggestStep

						// SL mua đề xuất / QC khuyến mãi * SL tặng
						giftMetadata.giftExpectQty = float64(int64(giftMetadata.suggestQuantity) / int64(utils.MaxFloat64(giftMetadata.promotionStep, 1)) * int64(giftMetadata.giftQuantity))
					} else {
						// 2. sp tặng chính khác sp mua (x != y)
						// ROUND_UP(demand, QC đề xuất) * QC đề xuất
						giftMetadata.suggestQuantity = math.Ceil(vendorUnavailableQty/giftMetadata.suggestStep) * giftMetadata.suggestStep

						// TODO : hỗ trợ tặng X khác Y cộng vào gift quantity
						giftMetadata.giftExpectQty = 0
					}

					// tổng dự kiến nhận
					giftMetadata.newExpectQuantity = giftMetadata.suggestQuantity + giftMetadata.giftExpectQty
					giftMetadata.currentStockDay = computeCurrentStockday(giftMetadata.newExpectQuantity, avgDemand, orderQty, inventoryQty)
					giftMetadata.promotionCode = promotion.VendorPromotionCode

					giftMetadata.VendorPromotionItemID = giftItem.VendorPromotionItemID // log

					listPromotionItemMetadata = append(listPromotionItemMetadata, &giftMetadata)
				}

			}

		default:
			// chưa hỗ trợ loại promotion
			continue LOOP_PROMOTION
		}
	}

	for _, itemMetadata := range listPromotionItemMetadata {
		// thoả mãn stock day
		if itemMetadata.currentStockDay <= maxDayInstock {
			listPromotionExpectQty = append(listPromotionExpectQty, model.VendorPromotionExpect{
				PromotionCode:         itemMetadata.promotionCode,
				GiftExpectQty:         itemMetadata.giftExpectQty,
				SuggestQty:            itemMetadata.suggestQuantity,
				CurrentStockDay:       itemMetadata.currentStockDay,
				VendorPromotionItemID: itemMetadata.VendorPromotionItemID,
				QuantityHedging:       quotationExport.QuantityHedging,
			})
		}
	}

	if promotionExpect, ok := checkAllowStockday(skuMaxStockDay, useMinSuggest, listPromotionExpectQty, vendorMinPerOrder, 1); ok {
		// đã thoã stock day
		return promotionExpect
	}

	// tính lần 2
	// - ưu tiên 1: quy cách sản phẩm theo vendor
	// - ưu  tiên 2: vẫn cố lấy quy cách promotion
	for _, itemMetadata := range listPromotionItemMetadata {
		switch {
		case itemMetadata.isGift:
			// tính lại tối ưu sl mua cho loại promotion gift
			if itemMetadata.xSameY {
				// MAX (ROUND_UP((SL yêu cầu mua - SL tặng) /QC sản phẩm × QC sản phẩm)) , ROUND_UP(Quy cách KM/QC sản phẩm) × QC sản phẩm)
				tempExpectVendor := calcExpectByVendorStep(vendorUnavailableQty-itemMetadata.giftQuantity, vendorStep)
				tempExpectRoundPromotionStep := calcExpectByVendorStep(itemMetadata.promotionStep, vendorStep)
				itemMetadata.suggestQuantity = utils.MaxFloat64(tempExpectVendor, tempExpectRoundPromotionStep)

				// (ROUND_DOWN(SL mua đề xuất / QC khuyến mãi) * SL tặng)
				itemMetadata.giftExpectQty = math.Floor(itemMetadata.suggestQuantity/itemMetadata.promotionStep) * itemMetadata.giftQuantity
				itemMetadata.newExpectQuantity = itemMetadata.suggestQuantity + itemMetadata.giftExpectQty
				itemMetadata.currentStockDay = computeCurrentStockday(itemMetadata.newExpectQuantity, avgDemand, orderQty, inventoryQty)

				if itemMetadata.currentStockDay <= maxDayInstock {
					listPromotionExpectQty = append(listPromotionExpectQty, model.VendorPromotionExpect{
						PromotionCode:         itemMetadata.promotionCode,
						GiftExpectQty:         itemMetadata.giftExpectQty,
						SuggestQty:            itemMetadata.suggestQuantity,
						CurrentStockDay:       itemMetadata.currentStockDay,
						VendorPromotionItemID: itemMetadata.VendorPromotionItemID,
						QuantityHedging:       quotationExport.QuantityHedging,
					})
				}
			} else {
				// MAX( (ROUNDUP(QC khuyến mãi theo SL / QC sản phẩm) * QC sản phẩm), (ROUNDUP(SL yêu cầu mua / QC sản phẩm , 0) * QC sản phẩm))
				tempExpectVendor := calcExpectByVendorStep(vendorUnavailableQty, vendorStep)
				tempExpectVendorWithPromotion := calcExpectByVendorStep(itemMetadata.promotionStep, vendorStep)
				itemMetadata.suggestQuantity = utils.MaxFloat64(tempExpectVendor, tempExpectVendorWithPromotion)

				itemMetadata.giftExpectQty = 0
				itemMetadata.newExpectQuantity = itemMetadata.suggestQuantity + itemMetadata.giftExpectQty
				itemMetadata.currentStockDay = computeCurrentStockday(itemMetadata.newExpectQuantity, avgDemand, orderQty, inventoryQty)

				listPromotionExpectQty = append(listPromotionExpectQty, model.VendorPromotionExpect{
					PromotionCode:         itemMetadata.promotionCode,
					GiftExpectQty:         itemMetadata.giftExpectQty,
					SuggestQty:            itemMetadata.suggestQuantity,
					CurrentStockDay:       itemMetadata.currentStockDay,
					VendorPromotionItemID: itemMetadata.VendorPromotionItemID,
					QuantityHedging:       quotationExport.QuantityHedging,
				})

			}
		}
	}

	// check stock day lần 2
	if promotionExpect, ok := checkAllowStockday(skuMaxStockDay, useMinSuggest, listPromotionExpectQty, vendorMinPerOrder, 2); ok {
		// đã thoã stock day
		return promotionExpect
	}

	// tính lẩn 3
	// chỉ tính theo quy cách sản phẩm theo vendor
	promotionExpect := new(model.VendorPromotionExpect)
	promotionExpect.PromotionRound = 3
	promotionExpect.SuggestQty = calcExpectByVendorStep(vendorUnavailableQty, vendorStep, vendorMinPerOrder)

	return promotionExpect
}

// chọn giá mua tham chiếu
// selectReferPrice selects the reference price for a given SKU configuration and vendor configuration.
// The function prioritizes the reference price in the following order:
// 1. Vendor's reference price after VAT (if greater than 0).
// 2. SKU configuration's reference price (if greater than 0).
// If the reference price includes VAT, it is converted to the unit price excluding VAT.
//
// Parameters:
// - skuConf: The SKU configuration containing the reference price and purchase VAT.
// - vendor: The vendor configuration containing the reference price after VAT and VAT.
//
// Returns:
// - referPrice: The selected reference price. If no valid reference price is found, returns 0.
func selectReferPrice(skuConf model.SkuConfig, vendor model.VendorConfig) (referPrice float64) {
	// TODO get bảng giá (ưu tiên 1)

	// giá mua tham chiếu vendor fav (ưu tiên 2)
	if vendor.ReferPriceAfterVAT > 0 {
		referPrice = vendor.ReferPriceAfterVAT
		if vendor.VAT > 0 {
			referPrice = utils.UnitPriceFromVAT(vendor.ReferPriceAfterVAT, float64(vendor.VAT))
		}
	} else {
		// giá mua tham chiếu chung sku-config (ưu tiên 3)
		if skuConf.ReferPrice != nil && *skuConf.ReferPrice > 0 {
			referPrice = *skuConf.ReferPrice
			if skuConf.PurchaseVAT != nil && *skuConf.PurchaseVAT > 0 {
				referPrice = utils.UnitPriceFromVAT(*skuConf.ReferPrice, float64(*skuConf.PurchaseVAT))
			}
		}
	}

	if referPrice <= 0 {
		referPrice = 0
	}

	return referPrice
}

// tính
// computeCurrentStockday calculates the current stock day based on the new expected stock,
// average demand, initial order quantity, and current inventory.
//
// Parameters:
//   - newExpect: The new expected stock level.
//   - avgDemand: The average demand for the item.
//   - order: The initial order quantity.
//   - inventory: The current inventory level.
//
// Returns:
//   - The computed current stock day, ensuring it is not less than zero.
func computeCurrentStockday(newExpect, avgDemand, order, inventory float64) float64 {
	// (tổng dự kiến - SL mua ban đầu) / demand trung bình
	temp := (newExpect + inventory - order) / utils.MaxFloat64(avgDemand, 1)
	return utils.MaxFloat64(temp, 0)
}

// checkAllowStockday checks if any promotion in the list meets the stock day criteria and returns the first or last valid promotion based on the useMinSuggest flag.
//
// Parameters:
// - maxStockDay: The maximum allowed stock day. If 0, stock day is not considered.
// - useMinSuggest: A flag indicating whether to use the minimum suggested quantity.
// - listPromotionExpectQty: A list of VendorPromotionExpect objects to check against the criteria.
// - minPerOrder: The minimum quantity per order.
// - round: The current promotion round.
//
// Returns:
// - *model.VendorPromotionExpect: The selected promotion that meets the criteria, or nil if none found.
// - bool: A boolean indicating whether a valid promotion was found.
func checkAllowStockday(maxStockDay float64, useMinSuggest bool, listPromotionExpectQty []model.VendorPromotionExpect, minPerOrder float64, round int64) (*model.VendorPromotionExpect, bool) {
	if len(listPromotionExpectQty) == 0 {
		return nil, false
	}

	sort.Slice(listPromotionExpectQty, func(i, j int) bool {
		return listPromotionExpectQty[i].CurrentStockDay < listPromotionExpectQty[j].CurrentStockDay
	})

	var promotionExpect *model.VendorPromotionExpect

LOOP_LIST_PROMOTION_EXPECT:
	// lấy thoả mã
	for _, promo := range listPromotionExpectQty {

		if maxStockDay == 0 { // không hỗ trợ
		} else if promo.CurrentStockDay > maxStockDay {
			continue LOOP_LIST_PROMOTION_EXPECT
		}

		switch useMinSuggest {
		case true:
			if allowMinPerOrder(minPerOrder, promo.SuggestQty) && promotionExpect == nil { // (get first)
				// lấy thoả mản stock day nhỏ nhất
				promo.PromotionRound = round
				promotionExpect = &promo
				break LOOP_LIST_PROMOTION_EXPECT
			}

		default:
			// lấy thoả mãn stock day lớn nhất
			if allowMinPerOrder(minPerOrder, promo.SuggestQty) { // override (get last)
				promo.PromotionRound = round
				promotionExpect = &promo
			}
		}
	}

	if promotionExpect == nil {
		return nil, false
	}

	return promotionExpect, true
}

func allowMinPerOrder(minPerOrder, suggestQty float64) bool {
	return suggestQty >= minPerOrder
}

// createHedgingQuotationExportDraft creates hedging quotation export drafts based on the provided map of quotations.
// It checks if a draft already exists for each quotation and creates a new draft if it does not exist and the expected quantity is greater than zero.
// If there is no demand and the favorite vendor matches the quotation's vendor code, it adjusts the expected quantity based on the total hedging and vendor hedging expected values.
//
// Parameters:
// - mapQuotationHedging: A map of quotation exports keyed by a string identifier.
// - favVendor: A pointer to the favorite vendor configuration.
// - isHasDemand: A boolean indicating if there is demand.
// - totalHedging: The total hedging value.
// - totalVendorHedgingExpected: The total vendor hedging expected value.
func createHedgingQuotationExportDraft(mapQuotationHedging map[string]model.QuotationExport, favVendor *model.VendorConfig, isHasDemand bool, totalHedging, totalVendorHedgingExpected int64) {
	isChecked := false
	for _, quotation := range mapQuotationHedging {

		// kiểm tra lại trước khi tạo
		quotationResp := model.QuotationExportHedgingDraftDB.QueryOne(
			model.QuotationExport{
				Version:                quotation.Version,
				WarehouseCode:          quotation.WarehouseCode,
				Type:                   enum.QuotationTypeVal.HEDGING,
				InboundRequestCode:     quotation.InboundRequestCode,
				InboundRequestItemCode: quotation.InboundRequestItemCode,
				ProductID:              quotation.ProductID,
			})

		if quotationResp.Status == common.APIStatus.NotFound && quotation.QuantityExpect != nil && *quotation.QuantityExpect > 0 {
			// print ra quotation info
			quotation.VendorExpectQuantity = quotation.QuantityExpect
			quotation.Type = enum.QuotationTypeVal.HEDGING

			// Nếu khoong có demand:
			// Sản phẩm được suggest dựa trên ctkm/multiple/MOA sẽ được + vào VendorExpectQuantity đầu tiên của favVendor
			if !isChecked && favVendor != nil && favVendor.VendorCode == quotation.VendorCode && !isHasDemand && totalVendorHedgingExpected > 0 {
				temp := *quotation.QuantityExpect + totalVendorHedgingExpected - totalHedging
				if temp > 0 {
					quotation.VendorExpectQuantity = &temp
					isChecked = true
				}
			}
			model.QuotationExportHedgingDraftDB.Create(quotation)
		}
	}
}

func genKeyVendorSKUWarehouse(vendor, sku, warehouse string) string {
	return fmt.Sprintf("%s_%s_%s", vendor, sku, warehouse)
}

func genKeyVendorSKUWarehouseIR(vendor, sku, warehouse, IRcode string) string {
	return fmt.Sprintf("%s_%s_%s_%s", IRcode, vendor, sku, warehouse)
}

func logQuotationHaveMinGreaterThanMax(quotation *model.QuotationExport) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered in logQuotationHaveMinGreaterThanMax", r)
		}
	}()

	if quotation.LogFormulaData == nil {
		return
	}

	log := quotation.LogFormulaData

	max, ok1 := log["MAX_QUANTITY"].(int64)
	min, ok2 := log["MIN_QUANTITY"].(int64)

	if ok1 && ok2 && min > max {
		model.LogQuotationExportDB.Upsert(model.LogQuotationExport{
			Version:       quotation.Version,
			SellerCode:    quotation.SellerCode,
			WarehouseCode: quotation.WarehouseCode,
			ProductID:     quotation.ProductID,
		}, model.LogQuotationExport{
			Version:        quotation.Version,
			SellerCode:     quotation.SellerCode,
			WarehouseCode:  quotation.WarehouseCode,
			ProductID:      quotation.ProductID,
			ProductName:    quotation.ProductName,
			ProductCode:    quotation.ProductCode,
			SKU:            quotation.SKU,
			LogFormulaData: quotation.LogFormulaData,
		})
	}
}
