package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetSellerQuotationPOHistory ...
func GetSellerQuotationPOHistory(query model.QuotationPOHistory, offset, limit int64, getTotal bool) *common.APIResponse {

	query.Version = utils.GetCurrentVersionDDMMYYYY()
	historyResp := model.QuotationPOHistoryDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResp := model.QuotationPOHistoryDB.Count(query)
		historyResp.Total = countResp.Total
	}

	return historyResp
}
