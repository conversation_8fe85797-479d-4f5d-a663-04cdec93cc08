package enum

type ErrorCodeValue string

type errorCodeErrorValue struct {
	// Common
	InsertQueue    ErrorCodeValue
	TypeConversion ErrorCodeValue
	// Create PO from Bidding
	CreatePO ErrorCodeValue
	UpdatePO ErrorCodeValue
}

var ErrorCodeError = &errorCodeErrorValue{
	// Common
	InsertQueue:    "ERROR_INSERT_QUEUE",
	TypeConversion: "ERROR_TYPE_CONVERSION",

	// Create PO from Bidding
	CreatePO: "ERROR_CREATE_PO",
	UpdatePO: "ERROR_UPDATE_PO",
}

type errorCodeInvalidValue struct {
	// Common
	ParseData      ErrorCodeValue
	Action         ErrorCodeValue
	RequiredFields ErrorCodeValue
	InputData      ErrorCodeValue
	LockAction     ErrorCodeValue
	Purchaser      ErrorCodeValue
	TypeAssertion  ErrorCodeValue
	Vendor         ErrorCodeValue
	// admin-pushing-group
	GroupCode             ErrorCodeValue
	VersionAdminGroup     ErrorCodeValue
	TimeConfirmAdminGroup ErrorCodeValue
	GroupConfirmed        ErrorCodeValue
	// admin-pushing-setting
	SellerCode         ErrorCodeValue
	PushingGens        ErrorCodeValue
	PushingSettingCode ErrorCodeValue
	PushingSettingID   ErrorCodeValue

	//quotation
	PurchaserCode ErrorCodeValue

	// bidding
	VendorCode ErrorCodeValue
	ProductID  ErrorCodeValue

	// callback-stock
	SKU ErrorCodeValue

	// inbound-callback-store
	POCode    ErrorCodeValue
	RequestID ErrorCodeValue

	// payment-item
	PaymentCode                   ErrorCodeValue
	VendorBillCode                ErrorCodeValue
	TotalPrice                    ErrorCodeValue
	PaymentItemCode               ErrorCodeValue
	NewBalanceLTZero              ErrorCodeValue
	NewRemainingMoneyLTZero       ErrorCodeValue
	NewRemainingMoneyGTTotalPrice ErrorCodeValue

	// payment
	CancelPaymentPaid ErrorCodeValue

	// product-mapping
	ProductCode ErrorCodeValue

	// sku-config
	VendorCodes     ErrorCodeValue
	ExistedBefore   ErrorCodeValue
	SkuInactive     ErrorCodeValue
	SkuPublishPrice ErrorCodeValue

	// PO
	Status                        ErrorCodeValue
	WarehouseCreatePO             ErrorCodeValue
	WarehouseNotMatchPurchaser    ErrorCodeValue
	WarehouseMustBeSame           ErrorCodeValue
	LastLineMustNew               ErrorCodeValue
	NotChangeWarehouse            ErrorCodeValue
	NotAllowDeleteOldLine         ErrorCodeValue
	NotCancelPOHadPayment         ErrorCodeValue
	NotAllowPOCancelFMTicket      ErrorCodeValue
	NotAllowEditPOOtherVendor     ErrorCodeValue
	NeedApproveBeforeConfirm      ErrorCodeValue
	NotAllowTotalPriceLTVendorMOA ErrorCodeValue
	NotAllowInActiveSKU           ErrorCodeValue
	POItems                       ErrorCodeValue
	InvalidSettingSellerType      ErrorCodeValue
	InvalidTradingSeller          ErrorCodeValue
	InvalidNonTradingSeller       ErrorCodeValue
	InvalidOriginalPO             ErrorCodeValue
	InvalidPOType                 ErrorCodeValue

	// VB
	NotCancelVBHadPayment    ErrorCodeValue
	NotApproveAffterCancelPO ErrorCodeValue
	NotEmptyItems            ErrorCodeValue
	NotMappingItem           ErrorCodeValue
	QuantityVBGtPO           ErrorCodeValue
	EnoughQuantity           ErrorCodeValue

	// Create PO from Bidding
	Warehouse                       ErrorCodeValue
	Info                            ErrorCodeValue
	QuantityConfirmGTQuantityExpect ErrorCodeValue
	QuantityExpect                  ErrorCodeValue
	QuantityExpectGTQuantityBidding ErrorCodeValue
	QuantityBidding                 ErrorCodeValue

	// Seller
	VendorInactivated ErrorCodeValue

	// inbound request
	InboundRequestCode                     ErrorCodeValue
	InboundRequestItemCode                 ErrorCodeValue
	NotCreateNowLTEStartTime               ErrorCodeValue
	NotCreateNowGTEApplyTime               ErrorCodeValue
	NotApproveNowLTEStartTime              ErrorCodeValue
	NotApproveNowLTEApplyTime              ErrorCodeValue
	NotAllowItemGT1000                     ErrorCodeValue
	InvalidInboundRequestPromotionCode     ErrorCodeValue
	InvalidInboundRequestWarehouseCode     ErrorCodeValue
	InvalidInboundRequestVendorCode        ErrorCodeValue
	InvalidInboundRequestContractPriceCode ErrorCodeValue
	InvalidInboundRequestSKUs              ErrorCodeValue
	InvalidInboundRequestDealTicketCode    ErrorCodeValue
	InvalidInboundRequestSKU               ErrorCodeValue

	InboundRequestItemRequestType ErrorCodeValue
	InboundRequestItemApplyTime   ErrorCodeValue
	InboundRequestWarehouseCode   ErrorCodeValue

	// notify
	OnlyNotifyPOCompleted ErrorCodeValue
	OnlyNotifyOnceEachPO  ErrorCodeValue
	AccountID             ErrorCodeValue
	EntityID              ErrorCodeValue

	// Wishlist
	WishlistGT1000 ErrorCodeValue

	// Wishlist
	PICObjectExist ErrorCodeValue

	//Contract price
	ContractPriceCode ErrorCodeValue
	// Wishlist
	InitialStockDaftExist ErrorCodeValue

	AddressNotFound ErrorCodeValue
	// Payment order
	BankingPaymentOrderType ErrorCodeValue
	VPNotConfig             ErrorCodeValue
	Amount                  ErrorCodeValue
	BankNotFound            ErrorCodeValue

	// Reason
	ReasonExist     ErrorCodeValue
	ReasonTypeExist ErrorCodeValue
}

var ErrorCodeInvalid = &errorCodeInvalidValue{

	// Common
	ParseData:      "INVALID_PARSE_DATA",
	Action:         "INVALID_ACTION",
	RequiredFields: "INVALID_REQUIRED_FIELDS",
	InputData:      "INVALID_INPUT_DATA",
	LockAction:     "INVALID_LOCK_ACTION",
	Purchaser:      "INVALID_PURCHASER",
	TypeAssertion:  "INVALID_TYPE_ASSERTION",
	Vendor:         "INVALID_VENDOR",

	// admin-pushing-group
	GroupCode:             "INVALID_GROUP_CODE",
	VersionAdminGroup:     "INVALID_VERSION_ADMIN_GROUP",
	TimeConfirmAdminGroup: "INVALID_TIME_CONFIRM_ADMIN_GROUP",
	GroupConfirmed:        "INVALID_GROUP_CONFIRMED",

	// admin-pushing-setting
	SellerCode:         "INVALID_SELLER_CODE",
	PushingGens:        "INVALID_PUSHING_GEN",
	PushingSettingCode: "INVALID_PUSHING_SETTING_CODE",
	PushingSettingID:   "INVALID_PUSHING_SETTING_ID",

	//quotation
	PurchaserCode: "INVALID_PURCHASER_CODE",
	// bidding
	VendorCode: "INVALID_VENDOR_CODE",
	ProductID:  "INVALID_PRODUCT_ID",

	// callback-stock
	SKU: "INVALID_SKU",

	// inbound-callback-store
	POCode:    "INVALID_PO_CODE",
	RequestID: "INVALID_REQUEST_ID",

	// payment-item
	PaymentCode:                   "INVALID_PAYMENT_CODE",
	VendorBillCode:                "INVALID_VENDOR_BILL_CODE",
	TotalPrice:                    "INVALID_TOTAL_PRICE_CODE",
	PaymentItemCode:               "INVALID_PAYMENT_ITEM_CODE",
	NewBalanceLTZero:              "INVALID_NEW_BALANCE_LT_ZERO",
	NewRemainingMoneyLTZero:       "INVALID_NEW_REMAINING_MONEY_LT_ZERO",
	NewRemainingMoneyGTTotalPrice: "INVALID_NEW_REMAINING_MONEY_GT_TOTAL_PRICE",

	// payment
	CancelPaymentPaid: "INVALID_CANCEL_PAYMENT_PAID",

	// product-mapping
	ProductCode: "INVALID_PRODUCT_CODE",

	// PO
	Status:                        "INVALID_STATUS",
	WarehouseCreatePO:             "INVALID_WAREHOUSE_CREATE_PO",
	WarehouseNotMatchPurchaser:    "INVALID_WAREHOUSE_NOT_MATCH_PURCHASER",
	WarehouseMustBeSame:           "INVALID_WAREHOUSE_AND_DELIVERY_WAREHOUSE_MUST_SAME",
	LastLineMustNew:               "INVALID_LAST_LINE_MUST_NEW",
	NotChangeWarehouse:            "INVALID_NOT_CHANGE_WAREHOUSE",
	NotAllowDeleteOldLine:         "INVALID_NOT_ALLOW_DELETE_OLD_LINE",
	NotCancelPOHadPayment:         "INVALID_NOT_CANCEL_PO_HAD_PAYMENT",
	NotAllowPOCancelFMTicket:      "INVALID_NOT_ALLOW_PO_CANCEL_FM_TICKET",
	NotAllowEditPOOtherVendor:     "INVALID_NOT_ALLOW_EDIT_PO_OTHER_VENDOR",
	NeedApproveBeforeConfirm:      "INVALID_NEED_APPROVED_BEFORE_CONFIRM",
	NotAllowTotalPriceLTVendorMOA: "INVALID_NOT_ALLOW_TOTAL_PRICE_LT_VENDOR_MOA",
	NotAllowInActiveSKU:           "INVALID_NOT_ALLOW_IN_ACTIVE_SKU",
	POItems:                       "INVALID_PURCHASER_ORDER_ITEMS",
	InvalidSettingSellerType:      "INVALID_SETTING_SELLER_TYPE",
	InvalidTradingSeller:          "INVALID_TRADING_SELLER",
	InvalidNonTradingSeller:       "INVALID_NON_TRADING_SELLER",
	InvalidOriginalPO:             "INVALID_ORIGINAL_PO",
	InvalidPOType:                 "INVALID_PO_TYPE",

	// VB
	NotCancelVBHadPayment:    "INVALID_NOT_CANCEL_VB_HAD_PAYMENT",
	NotApproveAffterCancelPO: "INVALID_NOT_APPROVE_AFFTER_CANCEL_PO",
	NotEmptyItems:            "INVALID_NOT_EMPTY_ITEMS",
	NotMappingItem:           "INVALID_NOT_MAPPING_ITEM",
	QuantityVBGtPO:           "INVALID_QUANTITY_VB_GT_PO",
	EnoughQuantity:           "INVALID_ENOUGH_QUANTITY",

	// sku-config
	VendorCodes:     "INVALID_VENDOR_CODES",
	ExistedBefore:   "INVALID_EXISTSED_BEFORE",
	SkuInactive:     "INVALID_SKU_INACTIVE",
	SkuPublishPrice: "INVALID_PUBLISH_PRICE",

	// Create PO from Bidding
	Warehouse:                       "INVALID_WAREHOUSE",
	Info:                            "INVALID_INFO",
	QuantityConfirmGTQuantityExpect: "INVALID_QUANTITY_CONFIRMED_GT_QUANTITY_EXPECT",
	QuantityExpect:                  "INVALID_QUANTITY_EXPECT",
	QuantityExpectGTQuantityBidding: "INVALID_QUANTITY_EXPECT_GT_QUANTITY_BIDDING",
	QuantityBidding:                 "INVALID_QUANTITY_BIDDING",

	// Seller
	VendorInactivated: "INVALID_VENDOR_INACTIVATED",

	// inbound request
	InboundRequestCode:                     "INVALID_INBOUND_REQUEST_CODE",
	InboundRequestItemCode:                 "INVALID_INBOUND_REQUEST_ITEM_CODE",
	NotCreateNowLTEStartTime:               "INVALID_CREATE_LTE_START_TIME",
	NotCreateNowGTEApplyTime:               "INVALID_CREATE_GTE_APPLY_TIME",
	NotApproveNowLTEStartTime:              "INVALID_APPROVE_LTE_START_TIME",
	NotApproveNowLTEApplyTime:              "INVALID_APPROVE_LTE_APPLY_TIME",
	NotAllowItemGT1000:                     "INVALID_ITEM_GT_1000",
	InvalidInboundRequestPromotionCode:     "INVALID_INBOUND_REQUEST_PROMOTION_CODE",
	InvalidInboundRequestWarehouseCode:     "INVALID_INBOUND_REQUEST_WAREHOUSE_CODE",
	InvalidInboundRequestVendorCode:        "INVALID_INBOUND_REQUEST_VENDOR_CODE",
	InvalidInboundRequestContractPriceCode: "INVALID_INBOUND_REQUEST_CONTRACT_PRICE_CODE",
	InvalidInboundRequestSKUs:              "INVALID_INBOUND_REQUEST_PRODUCT_SKUS",
	InvalidInboundRequestDealTicketCode:    "INVALID_INBOUND_REQUEST_DEAL_TICKET_CODE",
	InvalidInboundRequestSKU:               "INVALID_INBOUND_REQUEST_SKU",

	InboundRequestItemRequestType: "INVALID_INBOUND_REQUEST_ITEM_REQUEST_TYPE",
	InboundRequestItemApplyTime:   "INVALID_INBOUND_REQUEST_ITEM_APPLY_TIME",
	InboundRequestWarehouseCode:   "INVALID_INBOUND_REQUEST_WAREHOUSE_CODE",

	// notify
	OnlyNotifyPOCompleted: "INVALID_ONLY_NOTIFY_PO_COMPLETED",
	OnlyNotifyOnceEachPO:  "INVALID_ONLY_NOTIFY_ONCE_EACH_PO",
	AccountID:             "INVALID_ACCOUNT_ID",
	EntityID:              "INVALID_ENTITY_ID",

	// Wishlist
	WishlistGT1000: "WISHLIST_GT_1000",

	// Wishlist
	PICObjectExist: "PIC_OBJECT_EXIST",

	//Contract price
	ContractPriceCode:     "CONTRACT_PRICE_CODE",
	InitialStockDaftExist: "INITIAL_STOCK_DAFT_EXIST",

	// Vendor address
	AddressNotFound: "ADDRESS_NOT_FOUND",

	// Payment order
	BankingPaymentOrderType: "BANKING_PAYMENT_ORDER_TYPE",
	VPNotConfig:             "VP_NOT_CONFIG",
	Amount:                  "INVALID_AMOUNT",
	BankNotFound:            "BANK_NOT_FOUND",

	// Reason
	ReasonExist:     "REASON_EXIST",
	ReasonTypeExist: "REASON_TYPE_EXIST",
}

type errorCodeNotFoundValue struct {
	// Create PO from Bidding
	Quotations        ErrorCodeValue
	Biddings          ErrorCodeValue
	AdminPushingGroup ErrorCodeValue
	Vendor            ErrorCodeValue
	PurchaseOrder     ErrorCodeValue
	PurchaseOrderItem ErrorCodeValue
	SKUConfig         ErrorCodeValue
	VendorConfig      ErrorCodeValue
}

var ErrorCodeNotFound = &errorCodeNotFoundValue{
	// Create PO from Bidding
	Quotations:        "NOT_FOUND_QUOTATIONS",
	Biddings:          "NOT_FOUND_BIDDINGS",
	AdminPushingGroup: "NOT_FOUND_ADMIN_PUSHING_GROUP",
	Vendor:            "NOT_FOUND_VENDOR",
	PurchaseOrder:     "NOT_FOUND_PURCHASE_ORDER",
	PurchaseOrderItem: "NOT_FOUND_PURCHASE_ORDER_ITEM",
	SKUConfig:         "NOT_FOUND_SKU_CONFIG",
	VendorConfig:      "NOT_FOUND_VENDOR_CONFIG",
}
