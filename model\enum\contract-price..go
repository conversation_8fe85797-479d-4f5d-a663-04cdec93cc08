package enum

/**
 * Status type
 */
type (
	ContractPriceStatusValue string
	contractPriceStatusValue struct {
		DRAFT     ContractPriceStatusValue
		CONFIRMED ContractPriceStatusValue
		CANCELED  ContractPriceStatusValue
	}
)

var ContractPriceStatus = &contractPriceStatusValue{
	DRAFT:     "DRAFT",
	CONFIRMED: "CONFIRMED",
	CANCELED:  "CANCELED",
}

/**
 * Contract Price active status
 */
type (
	PriceActiveStatusValue string
	priceActiveStatusValue struct {
		UPCOMING  PriceActiveStatusValue
		OCCURRING PriceActiveStatusValue
		FINISHED  PriceActiveStatusValue
	}
)

var ContractPriceActiveStatus = &priceActiveStatusValue{
	UPCOMING:  "UPCOMING",
	OCCURRING: "OCCURRING",
	FINISHED:  "FINISHED",
}

/**
 * Status type
 */
type (
	ContractPriceActionValue string
	contractPriceActionValue struct {
		CHANGE_STATUS    ContractPriceActionValue
		UPDATE_CONFIRMED ContractPriceActionValue
	}
)

var ContractPriceAction = &contractPriceActionValue{
	CHANGE_STATUS:    "CHANGE_STATUS",
	UPDATE_CONFIRMED: "UPDATE_CONFIRMED",
}
