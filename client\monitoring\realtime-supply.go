package monitoring

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var realtimeSupplyClient *client.Client

const (
	getSKUInventoryInfo = "GET::/sku-inventory-info"
)

func InitRealtimeSupply(database *mongo.Database) {
	const realtimeSupplyV1 = "/monitoring/realtime-supply/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	realtimeSupplyClient = client.NewClient(realtimeSupplyV1, headers, 0)
	realtimeSupplyClient.WithDatabase(database)
	realtimeSupplyClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     getSKUInventoryInfo,
				Name:     "monitoring_realtime_supply__GET__sku_inventory_info",
				Database: database,
			},
		}...,
	)
}

func GetSKUInventoryInfo(opts ...client.APIOption) *common.APIResponse {
	requestOption := realtimeSupplyClient.WithAPIOption(opts...)
	var resp SKUInventoryInfoResponse
	_, err := realtimeSupplyClient.WithRequest(getSKUInventoryInfo, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
