package model

import (
	"go.mongodb.org/mongo-driver/bson"
	"log"
)

func getIndexKeys(input bson.D) (finalResult bson.D) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered in getIndexKeys", r)
		}
	}()
	finalResult = bson.D{}
	for _, e := range input {
		if e.Key == "key" {
			value := e.Value.(bson.D)
			var result bson.D
			for _, e2 := range value {
				result = append(result, bson.E{
					Key:   e2.Key,
					Value: int(e2.Value.(int32)),
				})
			}
			finalResult = result
			return
		}
	}
	return finalResult
}

func getIndexUniqueOpt(input bson.D) (finalResult bool) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered in getIndexUniqueOpt", r)
		}
	}()
	finalResult = false
	for _, e := range input {
		if e.Key == "unique" {
			finalResult = e.Value.(bool)
			return
		}
	}
	return finalResult
}

type Index struct {
	Keys     bson.D
	IsUnique bool
}

func (idx Index) Equal(idx2 Index) bool {
	if idx.IsUnique != idx2.IsUnique {
		return false
	}
	if len(idx.Keys) != len(idx2.Keys) {
		return false
	}
	for i := range idx.Keys {
		if idx.Keys[i].Key != idx2.Keys[i].Key {
			return false
		}
		if idx.Keys[i].Value != idx2.Keys[i].Value {
			return false
		}
	}
	return true
}
