package action

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateWishlist(input model.Wishlist) *common.APIResponse {
	qResult := model.WishlistDB.QueryOne(&model.Wishlist{VendorCode: input.VendorCode, ProductID: input.ProductID})
	if qResult.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Existed,
			Message:   "The wishlist already has this product.",
			ErrorCode: string(enum.ErrorCodeInvalid.ExistedBefore),
		}
	}

	t := time.Now()
	input.CreatedTime = &t
	input.LastUpdatedTime = &t
	return model.WishlistDB.Create(input)
}

// GetWishList is func to get wishlist
func GetWishList(query *model.Wishlist, offset, limit int64, getTotal bool) *common.APIResponse {
	result := model.WishlistDB.Query(query, offset, limit, &primitive.M{"_id": -1})
	if getTotal {
		countResult := model.WishlistDB.Count(query)
		result.Total = countResult.Total
	}
	return result
}

// DeleteWishlist is func to delete wishlist
func DeleteWishlist(input model.Wishlist) *common.APIResponse {
	return model.WishlistDB.Delete(&model.Wishlist{ProductID: input.ProductID, VendorCode: input.VendorCode})
}

// GetVendorsWishListProduct...
func GetVendorsWishListProduct(productID int64) []string {
	// Query all wishlish base on product id
	var vendorCodesFilter []string
	wishListResp := model.WishlistDB.Query(model.Wishlist{
		ProductID: productID,
	}, 0, 1000, nil)
	if wishListResp.Status == common.APIStatus.Ok {
		wishLists := wishListResp.Data.([]*model.Wishlist)
		for _, wishList := range wishLists {
			vendorCodesFilter = append(vendorCodesFilter, wishList.VendorCode)
		}
	}

	return vendorCodesFilter
}
