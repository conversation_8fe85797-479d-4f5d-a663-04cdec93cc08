package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Contact struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// unique
	ContactID   int64  `json:"contactId" bson:"contact_id,omitempty"`
	ContactCode string `json:"contactCode" bson:"contact_code,omitempty"`

	VendorCode       string                         `json:"vendorCode" bson:"vendor_code,omitempty"`
	Type             enum.VendorContactTypeValue    `json:"type" bson:"type,omitempty"`
	PurposeCode      enum.VendorContactPurposeValue `json:"purposeCode" bson:"purpose_code,omitempty"`
	ContactInfo      string                         `json:"contactInfo" bson:"contact_info,omitempty"`
	Description      string                         `json:"description" bson:"description,omitempty"`
	IsDefaultContact *bool                          `json:"isDefaultContact" bson:"is_default_contact,omitempty"`
	WarehouseCode    *string                        `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []*bson.M `json:"-" bson:"$or,omitempty"`
}

// ContactDB ...
var ContactDB = &db.Instance{
	ColName:        "contact",
	TemplateObject: &Contact{},
}

// InitContactMappingModel ...
func InitContactMappingModel(s *mongo.Database) {
	ContactDB.ApplyDatabase(s)
}
