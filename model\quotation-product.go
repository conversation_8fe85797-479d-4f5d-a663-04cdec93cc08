package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotationProduct struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`
	SellerCode  string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`

	// ManufacturerCode string `json:"manufacturerCode" bson:"manufacturer_code,omitempty"`
	Origin string `json:"origin" bson:"origin,omitempty"`

	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	PrWhHashtag string `json:"prWhHashtag,omitempty" bson:"pr_wh_hashtag,omitempty"`

	// For sort
	CurrentQuantityExpect     *int64   `json:"currentQuantityExpect,omitempty" bson:"current_quantity_expect,omitempty"`
	CurrentPriceAfterDiscount *float64 `json:"currentPriceAfterDiscount,omitempty" bson:"current_price_after_discount,omitempty"`
	CurrentTotalPrice         *float64 `json:"currentTotalPrice,omitempty" bson:"current_total_price,omitempty"`

	BlockReleaseToBiddingSession *bool `json:"blockReleaseToBiddingSession,omitempty" bson:"block_release_to_bidding_session,omitempty"`
	BlockReleaseToFavSession     *bool `json:"blockReleaseToFavSession,omitempty" bson:"block_release_to_fav_session,omitempty"`

	// For query
	ComplexQuery     []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductIDIn      []int64   `json:"productIDIn,omitempty" bson:"-"`
	ProductIDNotIn   []int64   `json:"productIDNotIn,omitempty" bson:"-"`
	PrWhHashtagIn    []string  `json:"prWhHashtagIn,omitempty" bson:"-"`
	PrWhHashtagNotIn []string  `json:"prWhHashtagNotIn,omitempty" bson:"-"`
	WarehouseCodes   []string  `json:"warehouseCodes,omitempty" bson:"-"`
	// ManufacturerCodeIn []string  `json:"manufacturerCodeIn" bson:"-"`
	OriginIn        []string `json:"originIn" bson:"-"`
	PurchaserCodeIn []string `json:"purchaserCodeIn,omitempty" bson:"-"`
	SellerCodeIn    []string `json:"sellerCodeIn,omitempty" bson:"-"`
}

// QuotationProductDB ...
var QuotationProductDB = &db.Instance{
	ColName:        "quotation_product",
	TemplateObject: &QuotationProduct{},
}

func InitQuotationProductModel(s *mongo.Database) {
	QuotationProductDB.ApplyDatabase(s)

	// t := true
	// _ = QuotationProductDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// _ = QuotationProductDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
