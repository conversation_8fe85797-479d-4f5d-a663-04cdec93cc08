package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePIC creates a new PIC (Product Information Code) entry in the database.
// It validates the input data and returns an API response indicating the result of the operation.
//
// Parameters:
//   - input: model.PIC - The PIC data to be created.
//
// Returns:
//   - *common.APIResponse - The API response containing the status, message, and error code (if any).
//
// Validation:
//   - Checks if ObjectCode is provided.
//   - Checks if ObjectType is provided.
//   - Checks if SellerCode is provided when ObjectType is SKU.
//   - Checks if the PIC already exists in the database.
//   - Checks if WarehouseCode is provided and valid.
//   - Checks if at least one of PICAccountID or CICAccountID is provided.
//
// Possible Error Codes:
//   - enum.ErrorCodeInvalid.InputData: When required input data is missing or invalid.
//   - enum.ErrorCodeInvalid.PICObjectExist: When the PIC already exists in the database.
//   - enum.ErrorCodeInvalid.Warehouse: When WarehouseCode is missing.
//   - enum.ErrorCodeInvalid.WarehouseCreatePO: When WarehouseCode is invalid.
//   - enum.ErrorCodeInvalid.AccountID: When neither PICAccountID nor CICAccountID is provided.
func CreatePIC(input model.PIC) *common.APIResponse {


	// Check if ObjectCode is provided.
	if input.ObjectCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Object code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check if ObjectType is provided.
	if input.ObjectType == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Object type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check if SellerCode is provided when ObjectType is SKU.
	if input.SellerCode == "" && input.ObjectType == enum.ObjectType.SKU {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Seller code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// Check if the PIC already exists in the database.
	checkPICResp := model.PICDB.QueryOne(&model.PIC{
		ObjectCode:    input.ObjectCode,
		ObjectType:    input.ObjectType,
		WarehouseCode: input.WarehouseCode,
	})

	// Check if the PIC already exists in the database.
	if checkPICResp.Status == common.APIStatus.Ok {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Object code does exist!",
			ErrorCode: string(enum.ErrorCodeInvalid.PICObjectExist),
		}
	}


	// Check if WarehouseCode is provided and valid.
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCoderequired",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}

	// Check if WarehouseCode is valid.
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}

	// Check if at least one of PICAccountID or CICAccountID is provided.
	if input.PICAccountID == nil && input.CICAccountID == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Account ID is required",
			ErrorCode: string(enum.ErrorCodeInvalid.AccountID),
		}
	}

	// create
	createResp := model.PICDB.Create(input)
	return createResp
}

// UpdatePIC updates the Person In Charge (PIC) information based on the provided input.
// It validates the input fields and checks if the PIC exists in the database before updating.
//
// Parameters:
//   - input (model.PIC): The PIC information to be updated.
//
// Returns:
//   - *common.APIResponse: The response containing the status of the update operation.
//
// Validation:
//   - ObjectCode: Must not be empty.
//   - ObjectType: Must not be empty.
//   - WarehouseCode: Must not be empty and must be valid.
//   - PICAccountID or CICAccountID: At least one must be provided.
//
// If the input is invalid, an appropriate error response is returned.
// If the PIC does not exist, the response from the query is returned.
// If the update is successful, the response from the update operation is returned.
func UpdatePIC(input model.PIC) *common.APIResponse {

	// validate ObjectCode required
	if input.ObjectCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Object code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// validate ObjectType required
	if input.ObjectType == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Object type is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		}
	}

	// query
	checkPICResp := model.PICDB.QueryOne(&model.PIC{
		ObjectCode:    input.ObjectCode,
		ObjectType:    input.ObjectType,
		WarehouseCode: input.WarehouseCode,
	})

	if checkPICResp.Status != common.APIStatus.Ok {
		return checkPICResp
	}

	// get data
	picData := checkPICResp.Data.([]*model.PIC)[0]

	// validate WarehouseCode required
	if input.WarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCoderequired",
			ErrorCode: string(enum.ErrorCodeInvalid.Warehouse),
		}
	}
	// validate WarehouseCode valid
	if !utils.ValidWarehouseCode(input.WarehouseCode) {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid warehouse.",
			ErrorCode: string(enum.ErrorCodeInvalid.WarehouseCreatePO),
		}
	}

	// validate AccountID required
	if input.PICAccountID == nil && input.CICAccountID == nil {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Account ID is required",
			ErrorCode: string(enum.ErrorCodeInvalid.AccountID),
		}
	}
	// do not update seller code
	input.SellerCode = ""

	// updat
	updateResp := model.PICDB.UpdateOne(
		model.PIC{
			ObjectCode:    picData.ObjectCode,
			ObjectType:    input.ObjectType,
			WarehouseCode: input.WarehouseCode,
		},
		input)
	return updateResp
}

// DeletePIC ...
func DeletePIC(objectCode string, objectType enum.ObjectTypeValue, warehouseCode string) *common.APIResponse {
	return model.PICDB.Delete(&model.PIC{ObjectCode: objectCode, ObjectType: objectType, WarehouseCode: warehouseCode})
}

// GetPICList retrieves a list of PIC (Person In Charge) records from the database based on the provided query.
// It supports pagination through the offset and limit parameters, and can optionally return the total count of matching records.
//
// Parameters:
//   - query: A pointer to a model.PIC struct containing the query criteria.
//   - offset: The number of records to skip before starting to return records.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of matching records in the response.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the query results and optionally the total count.
func GetPICList(query *model.PIC, offset, limit int64, getTotal bool) *common.APIResponse {

	result := model.PICDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if getTotal {
		result.Total = model.PICDB.Count(query).Total
	}

	return result
}
