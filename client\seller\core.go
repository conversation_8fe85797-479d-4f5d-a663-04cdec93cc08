package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var sellerCoreClient *client.Client

const (
	getSellerAccountList = "GET::/account/list"
	getSellerMe          = "GET::/me"
	getBankConfig        = "GET::/bank"
)

func InitSellerCore(database *mongo.Database) {
	const sellerCoreV1 = "/seller/core/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	sellerCoreClient = client.NewClient(sellerCoreV1, headers, 0)
	sellerCoreClient.WithDatabase(database)
	sellerCoreClient.WithConfiguration(
		[]client.Configuration{
			// {
			// 	Path:     getSellerAccountList,
			// 	Name:     "seller__GET__seller_list",
			// 	Database: database,
			// },
			{
				Path:     getSellerMe,
				Name:     "seller__GET__seller_me",
				Database: database,
			},
			{
				Path:     getBankConfig,
				Name:     "seller__GET__bank_config",
				Database: database,
			},
		}...,
	)
}

// get seller type : [ 1: seller, 2: vendor, 3: purchaser ]
func GetSellers(opts ...client.APIOption) *common.APIResponse {
	requestOption := sellerCoreClient.WithAPIOption(opts...)
	var resp SellerResponse
	_, err := sellerCoreClient.WithRequest(getSellerAccountList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetVendorMe(opts ...client.APIOption) *common.APIResponse {
	requestOption := sellerCoreClient.WithAPIOption(opts...)
	var resp SellerResponse
	_, err := sellerCoreClient.WithRequest(getSellerMe, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetBankConfig(opts ...client.APIOption) *common.APIResponse {
	requestOption := sellerCoreClient.WithAPIOption(opts...)
	var resp BankResponse
	_, err := sellerCoreClient.WithRequest(getBankConfig, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
