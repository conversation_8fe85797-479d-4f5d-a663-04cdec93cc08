package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotationPOHistory struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	CreatedBySystem string              `json:"createdBySystem,omitempty" bson:"created_by_system,omitempty"`
	CreatedByName   string              `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`

	PurchaserCode string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"`
	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	Version string `json:"version,omitempty" bson:"version,omitempty"`

	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	// FE internal
	POCode            string `json:"poCode,omitempty" bson:"po_code,omitempty"`
	POStatus          string `json:"poStatus,omitempty" bson:"po_status,omitempty"`
	QuantityConfirmed int64  `json:"quantityConfirmed,omitempty" bson:"quantity_confirmed,omitempty"`

	//
	ReferPrice float64 `json:"referPrice,omitempty" bson:"refer_price,omitempty"` // đã bao gồm VAT
	// QuotationVAT       int64 `json:"quotationVAT,omitempty" bson:"quotation_vat,omitempty"`

	// For query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
	// QuantityLte  *float64    `json:"quantityLte,omitempty" bson:"-"`
}

// QuotationPOHistoryDB ...
var QuotationPOHistoryDB = &db.Instance{
	ColName:        "quotation_po_history",
	TemplateObject: &QuotationPOHistory{},
}

func InitQuotationPOHistoryModel(s *mongo.Database) {
	QuotationPOHistoryDB.ApplyDatabase(s)

	// t := true
	// _ = QuotationPOHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// expire := int32((30 * 24 * time.Hour).Seconds())
	// _ = QuotationPOHistoryDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })
}
