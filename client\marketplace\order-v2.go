package marketplace

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var orderV2Client *client.Client

const (
	getInternalOrderList     = "GET::/internal/order/list"
	getInternalOrderItemList = "GET::/order-item/list"
)

func InitOrderV2Client(database *mongo.Database) {
	const productV2 = "/marketplace/order/v2"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	orderV2Client = client.NewClient(productV2, headers, 0)
	orderV2Client.WithDatabase(database)
	orderV2Client.WithConfiguration(
		[]client.Configuration{
			{
				Path:     getListSKUItem,
				Name:     "order__GET__internal_order_list",
				Database: database,
			},
		}...,
	)
}

func GetInternalOrderList(opts ...client.APIOption) *common.APIResponse {
	requestOption := orderV2Client.WithAPIOption(opts...)
	var resp OrderResponse
	_, err := orderV2Client.WithRequest(getInternalOrderList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetOrderItems(opts ...client.APIOption) *common.APIResponse {
	requestOption := orderV2Client.WithAPIOption(opts...)
	var resp OrderItemResponse
	_, err := orderV2Client.WithRequest(getInternalOrderItemList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
