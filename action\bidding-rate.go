package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CreateBiddingRateInput struct {
	Mode string `json:"mode,omitempty"` // OVERRITE, REMOVE-AND-WRITE
	// WarehouseCodes []string            `json:"warehouseCodes,omitempty"`
	Items []model.BiddingRate `json:"items,omitempty"`
}

// Function convert from CreateQuotationInput to CreateBiddingRateInput
// ConvertCreateQuotationInputToCreateBiddingRateInput converts a CreateQuotationsInput object
// to a CreateBiddingRateInput object. It maps the fields from each item in the input to a new
// BiddingRate object and returns a CreateBiddingRateInput containing these items.
//
// Parameters:
//   - input: CreateQuotationsInput containing the data to be converted.
//
// Returns:
//   - CreateBiddingRateInput containing the converted data.
func ConvertCreateQuotationInputToCreateBiddingRateInput(input CreateQuotationsInput) CreateBiddingRateInput {
	items := []model.BiddingRate{}
	for _, item := range input.Items {
		// Create a new BiddingRate object and populate it with the data from the input items
		items = append(items, model.BiddingRate{
			SellerCode:          item.SellerCode,
			ProductID:           item.ProductID,
			ProductCode:         item.ProductCode,
			SKU:                 item.SKU,
			WarehouseCode:       item.WarehouseCode,
			PurchaserCode:       item.PurchaserCode,
			DaySpan:             utils.GetCurrentVersionDDMMYYYY(),
			SkuPIC:              item.SkuPIC,
			ActionByID:          item.ActionByID,
			ActionByName:        item.ActionByName,
			FavoriteVendorID:    item.FavoriteVendorID,
			FavoriteVendorName:  item.FavoriteVendorName,
			FavoriteVendorPhone: item.FavoriteVendorPhone,
			ExpectedQuantity:    item.QuantityExpect,
			ProductName:         item.ProductName,
		})
	}
	return CreateBiddingRateInput{
		Mode:  input.Mode,
		Items: items,
	}
}

// Function convert from CreateQuotationInput to CreateBiddingRateInput
// ConvertQuotationToBiddingRateInput converts a slice of Quotation items to a slice of BiddingRate items.
// It iterates over each Quotation item and maps its fields to a new BiddingRate item.
//
// Parameters:
//   - items: A slice of model.Quotation items to be converted.
//
// Returns:
//   - A slice of model.BiddingRate items.
func ConvertQuotationToBiddingRateInput(items []model.Quotation) []model.BiddingRate {
	var result []model.BiddingRate
	for _, item := range items {

		// Create a new BiddingRate object and populate it with the data from the input items
		result = append(result, model.BiddingRate{
			SellerCode:          item.SellerCode,
			ProductID:           item.ProductID,
			ProductName:         item.ProductName,
			ProductCode:         item.ProductCode,
			SKU:                 item.SKU,
			WarehouseCode:       item.WarehouseCode,
			PurchaserCode:       item.PurchaserCode,
			DaySpan:             utils.GetCurrentVersionDDMMYYYY(),
			SkuPIC:              item.SkuPIC,
			ActionByID:          item.ActionByID,
			ActionByName:        item.ActionByName,
			FavoriteVendorID:    item.FavoriteVendorID,
			FavoriteVendorName:  item.FavoriteVendorName,
			FavoriteVendorPhone: item.FavoriteVendorPhone,
			ExpectedQuantity:    item.QuantityExpect,
		})
	}
	return result
}

// SellerCreateBiddingRate
// Khởi tạo và cập nhật lại bidding rate đã tồn tại trước đó

// SellerCreateBiddingRate handles the creation of bidding rates for a seller.
// It validates the input mode and seller code, processes each item in the input list,
// and either updates existing bidding rates or creates new ones based on the mode.
//
// Parameters:
//   - input: CreateBiddingRateInput containing the details of the bidding rates to be created.
//   - sellerCode: string representing the seller's unique code.
//
// Returns:
//   - *common.APIResponse: The response containing the status, message, and any relevant data or errors.
//
// The function performs the following steps:
//  1. Validates the input mode and seller code.
//  2. Iterates through the list of items in the input.
//  3. For each item, generates a bidding rate key and checks if a bidding rate with that key already exists.
//  4. If a bidding rate exists, updates its expected quantity based on the mode and existing data.
//  5. If a bidding rate does not exist, creates a new bidding rate and sets its initial values.
//  6. Collects any errors encountered during processing and includes them in the response if any exist.
//  7. Returns a success response with the created or updated bidding rates if no errors occurred.
func SellerCreateBiddingRate(input CreateBiddingRateInput, sellerCode string) *common.APIResponse {

	// Validate input
	var biddindRateMode = []string{"OVERRITE", "REMOVE-AND-WRITE"}
	if input.Mode == "" || !utils.IsContains(biddindRateMode, input.Mode) {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "mode là bắt buộc và phải là OVERRITE hoặc REMOVE-AND-WRITE",
		}
	}
	// Validate seller code
	if sellerCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "sellerCode là bắt buộc",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		}
	}

	var (
		successCreatedBiddingRates []model.BiddingRate
		now                        = utils.GetVietnamTimeNow()
		errorList                  []model.BiddingRate
	)

	// thực hiện với data trong list
	// xử lý cả mode OVERRITE và REMOVE-AND-WRITE
	// nếu mode là OVERRITE thì cập nhật lại ExpectedQuantity
	for i := range input.Items {

		newBiddingRate := input.Items[i]
		if genKeyErr := genBiddingRateKey(&newBiddingRate); genKeyErr.Status != common.APIStatus.Ok {
			errorList = append(errorList, newBiddingRate)
			continue
		}

		biddingRateResp := model.BiddingRateDB.QueryOne(model.BiddingRate{Key: newBiddingRate.Key})
		switch biddingRateResp.Status {
		case common.APIStatus.Ok:
			// Xử lý bidding rate đã tồn tại
			// nếu tồn tại POCodes:
			// nếu tổng số lượng của POCode trong POCodes * ( sum(ExpectQuantity) + newEpect) > current ExpectedQuantity thì cập nhật ExpectedQuantity
			oldBiddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]
			var newQuantity int64

			if oldBiddingRate.POCodes != nil && len(*oldBiddingRate.POCodes) > 0 {
				newQuantity = CountTotalQuantityByPOCodesInBiddingRate(*oldBiddingRate.POCodes, oldBiddingRate.WarehouseCode, oldBiddingRate.SKU) + *newBiddingRate.ExpectedQuantity
				if newQuantity > *oldBiddingRate.ExpectedQuantity {
					model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: oldBiddingRate.ID}, model.BiddingRate{ExpectedQuantity: &newQuantity, IsValid: true})
				}
			} else {
				newQuantity = *newBiddingRate.ExpectedQuantity
				model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: oldBiddingRate.ID}, model.BiddingRate{ExpectedQuantity: &newQuantity})
			}

			successCreatedBiddingRates = append(successCreatedBiddingRates, *oldBiddingRate)

		case common.APIStatus.NotFound:
			// nếu không tồn tại thì tạo mới bidding rate
			newBiddingRate.LastActionTime = &now
			genBiddingRateInfo(&newBiddingRate, nil)
			// query bidding heaging from warehouse and sku
			// if not exist get quantity and plus to newBiddingRate.expectedQuantity
			quotaHeging := model.QuotationHedgingDB.Query(
				model.Quotation{WarehouseCode: newBiddingRate.WarehouseCode, SKU: newBiddingRate.SKU, Type: enum.QuotationTypeVal.HEDGING}, 0, 0, nil)
			if quotaHeging.Status == common.APIStatus.Ok {
				newData := *newBiddingRate.ExpectedQuantity
				quotas := quotaHeging.Data.([]*model.Quotation)
				for _, quota := range quotas {
					if quota.QuantityExpect != nil {
						newData = newData + *quota.QuantityExpect
					}
				}
				newBiddingRate.ExpectedQuantity = &newData
			}
			// get sku item
			skuItem := GetSkuItemFromSKU(newBiddingRate.ProductID, newBiddingRate.SellerCode, newBiddingRate.WarehouseCode)
			if skuItem != nil {
				newBiddingRate.ItemCode = skuItem.ItemCode
				newBiddingRate.OldStatus = string(*skuItem.Status)
			}
			if createResp := model.BiddingRateDB.Create(&newBiddingRate); createResp.Status != common.APIStatus.Ok {
				errorList = append(errorList, newBiddingRate)
				continue
			}
			successCreatedBiddingRates = append(successCreatedBiddingRates, newBiddingRate)

		default:
			errorList = append(errorList, newBiddingRate)
		}
	}

	// nếu có lỗi thì trả về lỗi
	if len(errorList) > 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Đã xảy ra lỗi trong quá trình xử lý một số bidding rate.",
			Data:    errorList,
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Tạo bidding rates thành công.",
		Data:    successCreatedBiddingRates,
	}
}

// function generate key for bidding rate

// genBiddingRateKey generates a unique key for a bidding rate based on the
// ProductCode, SKU, and WarehouseCode fields of the provided BiddingRate object.
// The key is formatted as "ProductCode_SKU_WarehouseCode_CurrentDate" where
// CurrentDate is in the format DDMMYYYY.
//
// Parameters:
// - bidRate: A pointer to a BiddingRate object containing the necessary fields.
//
// Returns:
//   - A pointer to an APIResponse object containing the generated key if the
//     input is valid, or an error message if any of the required fields are missing.
func genBiddingRateKey(bidRate *model.BiddingRate) *common.APIResponse {
	// validation input
	if bidRate.ProductCode != "" && bidRate.SKU != "" && bidRate.WarehouseCode != "" {
		bidRate.Key = bidRate.ProductCode + "_" + bidRate.SKU + "_" + bidRate.WarehouseCode + "_" + utils.GetCurrentVersionDDMMYYYY()
		return &common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: bidRate.Key,
		}
	}
	return &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "ProductCode, SKU, WarehouseCode required",
	}
}

// genBiddingRateInfo generates bidding rate information for a given quota and SKU configuration.
// It updates the quota with the SKU PIC, vendor PIC, and favorite vendor details.
//
// Parameters:
//   - quota: A pointer to a BiddingRate model which will be updated with the bidding rate information.
//   - skuConfig: A pointer to a SkuConfig model which contains the SKU configuration details.
//
// The function performs the following steps:
//  1. Checks if the quota or skuConfig is nil, or if the quota SKU is empty, and returns early if any of these conditions are met.
//  2. Queries the PIC database for the SKU PIC and updates the quota with the PIC account ID if found.
//  3. Checks if the skuConfig vendors list is nil or empty, and returns early if true.
//  4. Iterates through the vendors to find the one with the highest priority and retrieves its vendor code.
//  5. Queries the PIC database for the vendor PIC using the vendor code and updates the quota with the PIC account ID if found.
//  6. Queries the seller API for the vendor details using the vendor code and updates the quota with the favorite vendor ID, name, and phone number.
func genBiddingRateInfo(quota *model.BiddingRate, skuConfig *model.SkuConfig) {
	if quota == nil || skuConfig == nil {
		return
	}

	if len(quota.SKU) == 0 {
		return
	}

	getPicSKUResp := model.PICDB.QueryOne(model.PIC{
		ObjectCode:    quota.SKU,
		ObjectType:    enum.ObjectType.SKU,
		WarehouseCode: quota.WarehouseCode,
	})

	if getPicSKUResp.Status == common.APIStatus.Ok {
		picSKU := getPicSKUResp.Data.([]*model.PIC)[0]
		if picSKU.PICAccountID != nil {
			quota.SkuPIC = *picSKU.PICAccountID
		}
	}

	if skuConfig.Vendors == nil || len(*skuConfig.Vendors) == 0 {
		return
	}

	// get pic vendor
	vendorCode := ""
	for _, vendor := range *skuConfig.Vendors {
		if vendor.Priority == nil {
			continue
		}
		if utils.IsInt64Contains(utils.FAV_PRIORITY, *vendor.Priority) {
			vendorCode = vendor.VendorCode
			break
		}
	}

	if len(vendorCode) == 0 {
		return
	}

	// get pic vendor
	getPicVendorResp := model.PICDB.QueryOne(model.PIC{
		ObjectCode:    vendorCode,
		ObjectType:    enum.ObjectType.VENDOR,
		WarehouseCode: quota.WarehouseCode,
	})
	if getPicVendorResp.Status == common.APIStatus.Ok {
		picVendor := getPicVendorResp.Data.([]*model.PIC)[0]
		if picVendor.PICAccountID != nil {
			quota.VendorPIC = *picVendor.PICAccountID
		}
	}

	// get vendor
	opts := client.APIOption{
		Q: model.Seller{
			SellerClass: model.CLASS_VENDOR,
		},
		Limit: utils.Pointer.WithInt(1),
		Params: map[string]string{
			"sellerCode": vendorCode,
		},
	}
	getVendorResp := seller.GetSellers(opts)
	if getVendorResp.Status != common.APIStatus.Ok {
		return
	}

	vendor := getVendorResp.Data.([]*model.Seller)[0]
	// update quota
	quota.FavoriteVendorID = vendor.SellerID
	quota.FavoriteVendorName = vendor.Name
	quota.FavoriteVendorPhone = vendor.Phone
}

// Đếm số lượng sản phẩm có trong POs
// CountTotalQuantityByPOCodesInBiddingRate calculates the total expected quantity of items
// in purchase orders that match the given purchase order codes, warehouse code, and SKU.
//
// Parameters:
// - poCodes: A slice of purchase order codes to filter the purchase order items.
// - warehouseCode: The code of the warehouse to filter the purchase order items.
// - sku: The SKU of the items to filter the purchase order items.
//
// Returns:
// - total: The total expected quantity of items that match the given criteria.
func CountTotalQuantityByPOCodesInBiddingRate(poCodes []string, warehouseCode, sku string) (total int64) {
	query := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		ComplexQuery: []*bson.M{
			{"po_code": bson.M{"$in": poCodes}},
		},
	}, 0, 1000, nil)
	if query.Status != common.APIStatus.Ok {
		return
	}

	POItems := query.Data.([]*model.PurchaseOrderItem)
	for _, item := range POItems {
		if item.SKU == sku && item.WarehouseCode == warehouseCode {
			total = total + item.ExpectQuantity
		}
	}

	return
}

// DeleteAllBiddingRates deletes all bidding rates for a given seller and warehouse.
// If isForceDeleteAll is true, it will forcefully delete all bidding rates that match the criteria.
// It performs the following steps:
// 1. Validates the sellerCode.
// 2. Logs the deletion process.
// 3. If isForceDeleteAll is true, it iterates through all bidding rates in batches of 100.
// 4. For each bidding rate, it checks if there are any associated PO codes.
//   - If there are no PO codes, it deletes the bidding rate.
//   - If there are PO codes, it checks if the PO codes exist in the PurchaseOrderDB.
//   - If none of the PO codes exist, it deletes the bidding rate.
//
// 5. Logs the completion of the deletion process.
// 6. Returns an API response indicating the success or failure of the operation.
//
// Parameters:
// - isForceDeleteAll: A boolean indicating whether to forcefully delete all bidding rates.
// - sellerCode: A string representing the seller's code.
// - codesArr: An array of strings representing codes (not used in the function).
// - skusArr: An array of strings representing SKUs to filter the bidding rates.
// - warehouseCode: A string representing the warehouse code.
//
// Returns:
// - A pointer to a common.APIResponse indicating the result of the operation.
func DeleteAllBiddingRates(isForceDeleteAll bool, sellerCode string, codesArr, skusArr []string, warehouseCode string) *common.APIResponse {
	if sellerCode == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "sellerCode required",
		}
	}
	_id := primitive.NilObjectID

	fmt.Println("DELETE ALL BIDDING RATE" + sellerCode + " " + warehouseCode + " ")

	// nếu isForceDeleteAll = true thì thực hiện xóa tất cả bidding rate
	if isForceDeleteAll {

		var limit int64 = 100
		for {
			biddingRatesQuery := model.BiddingRate{
				SellerCode:    sellerCode,
				WarehouseCode: warehouseCode,
				DaySpan:       utils.GetCurrentVersionDDMMYYYY(),
				ComplexQuery: []*bson.M{
					{"_id": bson.M{"$gt": _id}},
				},
			}
			if len(skusArr) > 0 {
				biddingRatesQuery.ComplexQuery = append(biddingRatesQuery.ComplexQuery, &bson.M{
					"sku": &bson.M{
						"$in": skusArr,
					},
				})
			}

			biddingRateResp := model.BiddingRateDB.Query(biddingRatesQuery, 0, limit, &primitive.M{"_id": 1})
			if biddingRateResp.Status != common.APIStatus.Ok {
				break
			}

			BDs := biddingRateResp.Data.([]*model.BiddingRate)
			for _, bd := range BDs {
				_id = *bd.ID
				// Thực hiện kiểm tra xem bibding rate hien tại có POCode nào không
				// nếu không có thì xóa
				// nếu có thì không làm gì cả
				if bd.POCodes == nil || len(*bd.POCodes) == 0 {
					model.BiddingRateDB.Delete(model.BiddingRate{ID: bd.ID})
				}

				// nếu PO code trong POCode không tồn tại thì xoá bidding rate này
				if bd.POCodes != nil && len(*bd.POCodes) > 0 {
					isExisted := false
					for _, poCode := range *bd.POCodes {
						queryPO := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
						if queryPO.Status == common.APIStatus.Ok {
							isExisted = true
							break
						}
					}
					if !isExisted {
						model.BiddingRateDB.Delete(model.BiddingRate{ID: bd.ID})
					}
				}
			}

			if len(BDs) < int(limit) {
				break
			}
			time.Sleep(50 * time.Millisecond)
		}
	}

	fmt.Println(sellerCode + " DELETE DONE")

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Delete data success",
	}
}

// Đếm số lượng sản phẩm có trong POs
// IncreaseQuantityPoCodesToBiddingRate increases the quantity of purchase order codes to the bidding rate.
// It takes a purchase order code as input, verifies if the purchase order exists, and then updates the bidding rate
// with the quantity of items in the purchase order.
//
// Parameters:
//   - poCode: The purchase order code to be processed.
//
// The function performs the following steps:
//  1. Verifies if the purchase order code is not empty.
//  2. Checks if the purchase order exists in the database.
//  3. Retrieves the items associated with the purchase order.
//  4. Aggregates the expected quantities of items by SKU and warehouse code.
//  5. Updates the bidding rate with the aggregated quantities and purchase order codes.
//  6. If the bidding rate does not have an item code, it retrieves the item code and updates the bidding rate.
//  7. If the current time is within the valid time range, it updates the SKU status in the marketplace.
//
// Note: The function uses goroutines to update the SKU status asynchronously.
func IncreaseQuantityPoCodesToBiddingRate(poCode string) {

	if poCode == "" {
		return
	}

	// verify PO existed
	poCheckResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if poCheckResp.Status != common.APIStatus.Ok {
		return
	}
	poCheck := poCheckResp.Data.([]*model.PurchaseOrder)[0]

	// convert time to string
	daySpan := utils.ConvertTimeToStringDDMMYYYY(poCheck.CreatedTime)

	// query POItems
	query := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: poCode,
	}, 0, 1000, nil)
	if query.Status != common.APIStatus.Ok {
		return
	}

	POItems := query.Data.([]*model.PurchaseOrderItem)

	mapBiddingRate := make(map[string]int64)

	// tạo map với key là SKU và WarehouseCode
	for _, item := range POItems {
		key := item.SKU + "@@@" + item.WarehouseCode
		if _, ok := mapBiddingRate[key]; ok {
			mapBiddingRate[key] = mapBiddingRate[key] + item.ExpectQuantity
		} else {
			mapBiddingRate[key] = item.ExpectQuantity
		}
	}
	// tìm bibdding rate đang tồn tại
	// Cập nhật số lượng đang có trong POCode
	// Cập nhật POCode
	// Cập nhật số lượng đang có trong POCode
	for key, value := range mapBiddingRate {

		sliptKey := strings.Split(key, "@@@")
		sku := sliptKey[0]
		warehouseCode := sliptKey[1]

		biddingRateQuery := model.BiddingRate{
			SKU:           sku,
			WarehouseCode: warehouseCode,
			DaySpan:       daySpan,
		}
		// query bidding rate
		biddingRateResp := model.BiddingRateDB.QueryOne(biddingRateQuery)
		if biddingRateResp.Status != common.APIStatus.Ok {
			continue
		}
		biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]

		if biddingRate.POCodes == nil {
			biddingRate.POCodes = &[]string{}
		}
		// nếu poCode đã tồn tại trong POCodes thì không thêm vào
		isExisted := false
		for _, po := range *biddingRate.POCodes {
			if po == poCode {
				isExisted = true
				break
			}
		}
		if isExisted {
			continue
		}
		newPos := []string{}
		if biddingRate.POCodes != nil {
			newPos = append(*biddingRate.POCodes, poCode)
		} else {
			newPos = []string{poCode}
		}
		var posQuantity = value
		if biddingRate.POsQuantity != nil {
			posQuantity = posQuantity + *biddingRate.POsQuantity
		}
		// update bidding rate
		resp := model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{POCodes: &newPos, POsQuantity: &posQuantity, IsValid: true})
		if resp.Status == common.APIStatus.Ok {

			if biddingRate.ItemCode == "" {
				skuItem := GetSkuItemFromSKU(biddingRate.ProductID, biddingRate.SellerCode, biddingRate.WarehouseCode)
				if skuItem != nil && skuItem.ItemCode != "" {
					biddingRate.ItemCode = skuItem.ItemCode
					model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{ItemCode: skuItem.ItemCode})
				}
			}
			// Thời gian hiện tại không nằm trong khung giờ cho phép cập nhât
			if IsValidTime() {
				// Gọi qua marketplace để cập nhật lại sku status
				go marketplace.UpdateSKUStatus(
					client.APIOption{
						Body: model.UpdateSKUStatusRequest{
							SKU:           biddingRate.SKU,
							ProductID:     biddingRate.ProductID,
							ProductCode:   biddingRate.ProductCode,
							WarehouseCode: biddingRate.WarehouseCode,
							ItemCode:      biddingRate.ItemCode,
						},
						Keys: []string{biddingRate.SKU, "UPDATE_SKU_STATUS"},
					},
				)
			}
		}
	}
}

// // Đếm số lượng sản phẩm có trong POs
// DecreaseQuantityPoCodesToBiddingRate decreases the quantity of purchase order codes in the bidding rate.
// It verifies if the purchase order (PO) exists, retrieves the PO items, and updates the bidding rate accordingly.
// If the PO code is already present in the bidding rate, it recalculates the quantity and updates the bidding rate.
// Additionally, it updates the SKU status in the marketplace if the time is valid.
//
// Parameters:
//   - poCode: The purchase order code to be processed.
//
// The function performs the following steps:
//  1. Verifies if the PO code is not empty.
//  2. Checks if the PO exists in the database.
//  3. Retrieves the PO items associated with the given PO code.
//  4. Aggregates the expected quantities of the PO items by SKU and warehouse code.
//  5. For each unique SKU and warehouse code combination, it checks if the bidding rate exists.
//  6. If the PO code is already present in the bidding rate, it recalculates the quantity and updates the bidding rate.
//  7. Updates the SKU status in the marketplace if the time is valid.
func DecreaseQuantityPoCodesToBiddingRate(poCode string) {
	if poCode == "" {
		return
	}

	// verify PO existed
	poCheckResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if poCheckResp.Status != common.APIStatus.Ok {
		return
	}
	poCheck := poCheckResp.Data.([]*model.PurchaseOrder)[0]
	daySpan := utils.ConvertTimeToStringDDMMYYYY(poCheck.CreatedTime)
	// query POItems
	query := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: poCode,
	}, 0, 1000, nil)
	if query.Status != common.APIStatus.Ok {
		return
	}

	POItems := query.Data.([]*model.PurchaseOrderItem)

	mapBiddingRate := make(map[string]int64)

	// tạo map với key là SKU và WarehouseCode
	for _, item := range POItems {
		key := item.SKU + "@@@" + item.WarehouseCode
		if _, ok := mapBiddingRate[key]; ok {
			mapBiddingRate[key] = mapBiddingRate[key] + item.ExpectQuantity
		} else {
			mapBiddingRate[key] = item.ExpectQuantity
		}
	}
	// tìm bibdding rate đang tồn tại
	// Cập nhật số lượng đang có trong POCode
	// Cập nhật POCode
	for key, _ := range mapBiddingRate {
		sliptKey := strings.Split(key, "@@@")
		sku := sliptKey[0]
		warehouseCode := sliptKey[1]
		biddingRateQuery := model.BiddingRate{
			SKU:           sku,
			WarehouseCode: warehouseCode,
			DaySpan:       daySpan,
		}
		biddingRateResp := model.BiddingRateDB.QueryOne(biddingRateQuery)
		if biddingRateResp.Status != common.APIStatus.Ok {
			continue
		}
		biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]

		// nếu poCode đã tồn tại trong POCodes thì không thêm vào
		isExisted := false
		POCodes := biddingRate.POCodes
		NewPoCodes := []string{}
		if POCodes == nil || biddingRate.POsQuantity == nil || *biddingRate.POsQuantity == 0 {
			continue
		}
		for _, po := range *POCodes {
			// nếu POCode tồn tại trong POCodes thì không thêm vào
			if po == poCode {
				isExisted = true
				continue
			}
			NewPoCodes = append(NewPoCodes, po)
		}
		// // không tồn tại POCode trong POCodes thì không làm gì
		if !isExisted {
			continue
		}

		var value int64 = 0
		if len(NewPoCodes) > 0 {
			poCodeIn := []string{}
			for _, po := range NewPoCodes {
				checkPoResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: po})
				if checkPoResp.Status == common.APIStatus.Ok {
					poCodeIn = append(poCodeIn, po)
				}
			}
			queryPoItems := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
				SKU:           sku,
				WarehouseCode: warehouseCode,
				ComplexQuery: []*bson.M{
					{"po_code": bson.M{"$in": poCodeIn}},
				},
			}, 0, 1000, nil)
			if queryPoItems.Status == common.APIStatus.Ok {
				POs := queryPoItems.Data.([]*model.PurchaseOrderItem)
				for _, po := range POs {
					value = value + po.ExpectQuantity
				}
			}
		}
		// update bidding rate
		resp := model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{POCodes: &NewPoCodes, POsQuantity: &value, IsValid: true})
		if resp.Status == common.APIStatus.Ok {

			if biddingRate.ItemCode == "" {
				skuItem := GetSkuItemFromSKU(biddingRate.ProductID, biddingRate.SellerCode, biddingRate.WarehouseCode)
				if skuItem != nil && skuItem.ItemCode != "" {
					biddingRate.ItemCode = skuItem.ItemCode
					model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{ItemCode: skuItem.ItemCode})
				}

			}
			// Thời gian hiện tại không nằm trong khung giờ cho phép cập nhât
			if IsValidTime() {
				// Gọi qua marketplace để cập nhật lại sku status
				marketplace.UpdateSKUStatus(
					client.APIOption{
						Body: model.UpdateSKUStatusRequest{
							SKU:           biddingRate.SKU,
							ProductID:     biddingRate.ProductID,
							ProductCode:   biddingRate.ProductCode,
							WarehouseCode: biddingRate.WarehouseCode,
							ItemCode:      biddingRate.ItemCode,
						},
						Keys: []string{biddingRate.SKU, "UPDATE_SKU_STATUS"},
					},
				)
			}
		}
	}
}

// RecountQuantityPoCodesToBiddingRate recalculates the quantities of purchase order (PO) items
// associated with a given PO code and updates the corresponding bidding rates.
//
// The function performs the following steps:
// 1. Finds the purchase order (PO) by the given PO code.
// 2. Retrieves each SKU and warehouse pair in the PO.
// 3. Finds the bidding rate for each SKU and warehouse pair.
// 4. Recounts the quantities of PO items for the given PO codes and updates the bidding rates if there are changes.
//
// Parameters:
// - poCode: The code of the purchase order to be processed.
// - removedSkuCode: A list of SKU codes that have been removed and need to be recounted.
//
// The function verifies the existence of the PO and retrieves its creation time.
// It then queries the PO items associated with the given PO code and constructs a map of SKU and warehouse pairs.
// For each pair, it queries the corresponding bidding rate and recounts the quantities of PO items within the same day.
// The function updates the bidding rate with the new quantities and PO codes if there are changes.
// If the bidding rate's item code is empty, it retrieves the SKU item and updates the item code.
// Finally, it calls the marketplace to update the SKU status if the current time is within the allowed update time frame.
func RecountQuantityPoCodesToBiddingRate(poCode string, removedSkuCode []string) {
	// step 1: Tìm Po theo poCode
	// step 2: Tim từng cặp sku+warehoue trong PO
	// step 3: Tìm bidding rate theo sku+warehouse trên
	// step 3.1: theo từng bibddingrate, đếm lại tất cả số lượng quantity trong POitems của POCodes. (queru PO by, in POCodes and sku+warehouse)
	// step 3.2: cập nhật lại số lượng trong bidding rate (nếu cos thay đổi)

	if poCode == "" {
		return
	}

	// verify PO existed
	poCheckResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: poCode})
	if poCheckResp.Status != common.APIStatus.Ok {
		return
	}
	poCheck := poCheckResp.Data.([]*model.PurchaseOrder)[0]
	daySpan := utils.ConvertTimeToStringDDMMYYYY(poCheck.CreatedTime)

	// query POItems
	query := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{
		POCode: poCode,
	}, 0, 1000, nil)

	if query.Status != common.APIStatus.Ok {
		return
	}

	POItems := query.Data.([]*model.PurchaseOrderItem)
	mapBiddingRate := make(map[string]bool)

	for _, item := range POItems {
		key := item.SKU + "@@@" + item.WarehouseCode
		mapBiddingRate[key] = true
	}

	var mapPo = map[string]*model.PurchaseOrder{}
	// thêm danh sách SKU đã xoá vào map, để đi đếm lại số lượng quantity trong PO
	for _, sku := range removedSkuCode {
		key := sku + "@@@" + poCheck.WarehouseCode
		mapBiddingRate[key] = true
	}
	for key := range mapBiddingRate {
		sliptKey := strings.Split(key, "@@@")
		sku := sliptKey[0]
		warehouseCode := sliptKey[1]

		biddingRateQuery := model.BiddingRate{
			SKU:           sku,
			WarehouseCode: warehouseCode,
			DaySpan:       daySpan,
		}

		// query bidding rate
		biddingRateResp := model.BiddingRateDB.QueryOne(biddingRateQuery)
		if biddingRateResp.Status != common.APIStatus.Ok {
			continue
		}

		biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]

		var posQuantity int64
		var newPOCodes []string

		// query POItems
		startDate := time.Date(poCheck.CreatedTime.Year(), poCheck.CreatedTime.Month(), poCheck.CreatedTime.Day(), 0, 0, 0, 0, utils.TimeZoneVN)
		endDate := time.Date(poCheck.CreatedTime.Year(), poCheck.CreatedTime.Month(), poCheck.CreatedTime.Day(), 23, 59, 59, 0, utils.TimeZoneVN)

		q := model.PurchaseOrderItem{
			SKU:           sku,
			WarehouseCode: warehouseCode,
			ComplexQuery: []*bson.M{
				{"created_time": bson.M{"$gte": startDate, "$lte": endDate}},
				// {"po_code": bson.M{"$in": *biddingRate.POCodes}},
			}}

		poItemsResp := model.PurchaseOrderItemDB.Query(q, 0, 1000, nil)
		// nếu không có POItems thì không cần xử lý
		if poItemsResp.Status == common.APIStatus.Ok {
			items := poItemsResp.Data.([]*model.PurchaseOrderItem)

			for _, item := range items {
				// kiểm tra POItem có valid không
				// laays thông tin PO dựa vào POCode của item.PoCode
				var po *model.PurchaseOrder
				if poCache, ok := mapPo[item.POCode]; ok && poCache != nil {
					po = poCache
				} else {
					poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: item.POCode})
					if poResp.Status != common.APIStatus.Ok {
						continue
					}
					po = poResp.Data.([]*model.PurchaseOrder)[0]
					mapPo[po.POCode] = po
				}
				// kiểm tra trạng thái của PO
				if po == nil {
					continue
				}
				// nếu PO đã bị hủy thì không cần xử lý
				if po.Status == enum.PurchaseOrderStatus.CANCELED {
					continue
				}
				// nếu PO không phải là PO của supplier hoặc internal confirm bidding thì không cần xử lý
				if po.CreatedBySystem != string(enum.POCreateBySystem.SUPPLIER) && po.CreatedBySystem != string(enum.POCreateBySystem.INTERNAL_CONFIRM_BIDDING) {
					continue
				}
				posQuantity += item.ExpectQuantity

				// nếu item.POCode chưa nằm trong newPOCodes thì thêm vào
				isExisted := false
				for _, po := range newPOCodes {
					if po == item.POCode {
						isExisted = true
						break
					}
				}

				if !isExisted {
					newPOCodes = append(newPOCodes, item.POCode)
				}
			}
		}

		// nếu POCode đã tồn tại trong POCodes thì không thêm vào
		resp := model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{POsQuantity: &posQuantity, POCodes: &newPOCodes, IsValid: true})
		if resp.Status == common.APIStatus.Ok {
			// Gọi qua marketplace để cập nhật lại sku status

			if biddingRate.ItemCode == "" {
				skuItem := GetSkuItemFromSKU(biddingRate.ProductID, biddingRate.SellerCode, biddingRate.WarehouseCode)
				if skuItem != nil && skuItem.ItemCode != "" {
					biddingRate.ItemCode = skuItem.ItemCode
					model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{ItemCode: skuItem.ItemCode})
				}
			}
			// Thời gian hiện tại không nằm trong khung giờ cho phép cập nhât
			if IsValidTime() {
				go marketplace.UpdateSKUStatus(
					client.APIOption{
						Body: model.UpdateSKUStatusRequest{
							SKU:           biddingRate.SKU,
							ProductID:     biddingRate.ProductID,
							ProductCode:   biddingRate.ProductCode,
							WarehouseCode: biddingRate.WarehouseCode,
							ItemCode:      biddingRate.ItemCode,
						},
						Keys: []string{biddingRate.SKU, "UPDATE_SKU_STATUS"},
					},
				)
			}
		}
	}

}

// GetBiddingRateSearch ...
// GetBiddingRateSearch retrieves bidding rate data based on the provided query parameters.
// It supports pagination, sorting, and additional options for fetching total count, SKU configuration, and today's bidding rates.
//
// Parameters:
//   - query: The BiddingRate model containing the search criteria.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to fetch the total count of matching records.
//   - getSkuconfig: A boolean indicating whether to fetch SKU configuration for each bidding rate.
//   - getToday: A boolean indicating whether to filter results based on today's valid time range.
//   - sort: The sorting criteria.
//
// Returns:
//   - *common.APIResponse: The API response containing the status, message, and data (list of BiddingRate).
func GetBiddingRateSearch(query model.BiddingRate, offset, limit int64, getTotal bool, getSkuconfig bool, getToday bool, sort string) *common.APIResponse {
	resp := model.BiddingRateDB.Query(query, offset, limit, &bson.M{"_id": -1})
	if resp.Status != common.APIStatus.Ok {
		return resp
	}
	var bds []*model.BiddingRate

	// getToday : để phân biệt API gọi từ product worker để đi kiểm tra bidding rate
	if getToday {

		// Nếu không có config thì thời gian valid là 6h - 16h30
		var validStartTime = 570 // 6h
		var validEndTime = 990   // 16h30

		settingResp := model.SettingDB.Query(model.Setting{}, 0, 1, &primitive.M{"_id": -1})
		if settingResp.Status != common.APIStatus.Ok {
			return settingResp
		}
		setting := settingResp.Data.([]*model.Setting)[0]

		if setting.TimeUpdateSKUStatusByBiddingRate != nil {
			if setting.TimeUpdateSKUStatusByBiddingRate.StartTime != nil {
				validStartTime = *setting.TimeUpdateSKUStatusByBiddingRate.StartTime
			}
			if setting.TimeUpdateSKUStatusByBiddingRate.EndTime != nil {
				validEndTime = *setting.TimeUpdateSKUStatusByBiddingRate.EndTime
			}
		}

		// thời gian gọi API nằm ngoài thời gian valid thì không trả về item này
		timeNow := utils.GetTimeNow()
		if timeNow < validStartTime || timeNow > validEndTime {
			return &common.APIResponse{
				Status:  common.APIStatus.NotFound,
				Message: "bidding rate not found",
			}
		}
	}

	// tính toán bidding rate percent
	for _, item := range resp.Data.([]*model.BiddingRate) {
		if item.ExpectedQuantity != nil && *item.ExpectedQuantity != 0 && item.POsQuantity != nil && *item.POsQuantity != 0 {
			item.BiddingRatePercent = (float32(*item.POsQuantity) / float32(*item.ExpectedQuantity)) * 100
		}
		if getSkuconfig {
			if skuConfigResp := model.SkuConfigDB.QueryOne(model.SkuConfig{
				ProductCode:   item.ProductCode,
				SellerCode:    item.SellerCode,
				PurchaserCode: item.PurchaserCode,
				SKU:           item.SKU,
			}); skuConfigResp.Status == common.APIStatus.Ok {
				skuConfig := skuConfigResp.Data.([]*model.SkuConfig)[0]
				item.SkuConfig = skuConfig
			}
		}

		bds = append(bds, item)
	}
	if getTotal {
		countResp := model.BiddingRateDB.Count(query)
		resp.Total = countResp.Total
	}
	resp.Data = bds
	return resp
}

// SellerUpdateBiddingRate
// Khởi tạo và cập nhật lại bidding rate đã tồn tại trước đó
// SellerUpdateBiddingRate updates the bidding rate for a seller based on the provided input.
// It validates the input SKU and checks if the bidding rate exists in the database.
// If the bidding rate is found, it updates the bidding rate with the new input values.
//
// Parameters:
//   - input: model.BiddingRate - The bidding rate details to be updated.
//
// Returns:
//   - *common.APIResponse: The response containing the status and message of the update operation.
//
// Possible response statuses:
//   - common.APIStatus.Invalid: If the input SKU is empty.
//   - common.APIStatus.NotFound: If the bidding rate is not found in the database.
//   - common.APIStatus.Ok: If the bidding rate is successfully updated.
func SellerUpdateBiddingRate(input model.BiddingRate) *common.APIResponse {
	if input.SKU == "" {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "mode là bắt buộc và phải là OVERRITE hoặc REMOVE-AND-WRITE",
		}
	}
	biddingRateResp := model.BiddingRateDB.QueryOne(model.BiddingRate{SKU: input.SKU, WarehouseCode: input.WarehouseCode, DaySpan: utils.GetCurrentVersionDDMMYYYY()})
	if biddingRateResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Bidding rate not found",
		}
	}
	biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]

	input.IsValid = true

	return model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, input)
}

// PUTBiddingRateNote
// Cập nhật note cho bidding rate
// PUTBiddingRateNote updates the notes of a bidding rate based on the provided input.
// It first queries the database for a bidding rate matching the SKU, WarehouseCode, and current date.
// If the bidding rate is not found, it returns a NotFound API response.
// If found, it appends a new note to the existing notes, indicating the status change from OldStatus to NewStatus.
// Finally, it updates the bidding rate in the database with the new notes.
//
// Parameters:
//   - input: model.PUTBiddingRateNoteRequest containing the SKU, WarehouseCode, OldStatus, and NewStatus.
//
// Returns:
//   - *common.APIResponse: The API response indicating the result of the operation.
func PUTBiddingRateNote(input model.PUTBiddingRateNoteRequest) *common.APIResponse {
	biddingRateResp := model.BiddingRateDB.QueryOne(model.BiddingRate{
		SKU:           input.SKU,
		WarehouseCode: input.WarehouseCode,
		DaySpan:       utils.GetCurrentVersionDDMMYYYY()})

	if biddingRateResp.Status != common.APIStatus.Ok {
		return &common.APIResponse{
			Status:  common.APIStatus.NotFound,
			Message: "Bidding rate not found",
		}
	}

	// lấy ra bidding rate
	biddingRate := biddingRateResp.Data.([]*model.BiddingRate)[0]

	// nếu notes = nil thì tạo mới (Notes lưu lại trạng thái của SKU được cập nhật trong ngày)
	notes := biddingRate.Notes
	if notes == nil {
		notes = &[]string{}
	}

	*notes = append([]string{fmt.Sprintf("%s/%s", input.OldStatus, input.NewStatus)}, *notes...)
	return model.BiddingRateDB.UpdateOne(model.BiddingRate{ID: biddingRate.ID}, model.BiddingRate{Notes: notes})
}


// IsValidTime checks if the current time falls within a valid time range.
// The default valid time range is from 9:30 AM (570 minutes) to 4:30 PM (990 minutes).
// If there is a configuration available in the database, it will override the default time range.
// The function returns true if the current time is within the valid time range, otherwise false.
func IsValidTime() (isValid bool) {
	// Nếu không có config thì thời gian valid là 6h45 - 16h30
	var validStartTime = 570 // 9h 30
	var validEndTime = 990   // 16h30

	settingResp := model.SettingDB.Query(model.Setting{}, 0, 1, &primitive.M{"_id": -1})
	if settingResp.Status == common.APIStatus.Ok {
		setting := settingResp.Data.([]*model.Setting)[0]

		if setting.TimeUpdateSKUStatusByBiddingRate != nil {
			if setting.TimeUpdateSKUStatusByBiddingRate.StartTime != nil {
				validStartTime = *setting.TimeUpdateSKUStatusByBiddingRate.StartTime
			}
			if setting.TimeUpdateSKUStatusByBiddingRate.EndTime != nil {
				validEndTime = *setting.TimeUpdateSKUStatusByBiddingRate.EndTime
			}
		}
	}

	// thời gian gọi API nằm ngoài thời gian valid thì không trả về item này
	timeNow := utils.GetTimeNow()
	if timeNow >= validStartTime && timeNow <= validEndTime {
		isValid = true
	}

	return
}
