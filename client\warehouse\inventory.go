package warehouse

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var warehouseInventoryClient *client.Client

const (
	getListInventoryLocation = "GET::/sku-location/list"
	getInventoryLocation     = "GET::/sku-location"
	getInventorySKU          = "GET::/sku"
)

func InitWarehouseInventory(database *mongo.Database) {
	const warehouseInventoryV1 = "/warehouse/inventory/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	warehouseInventoryClient = client.NewClient(warehouseInventoryV1, headers, 0)
	warehouseInventoryClient.WithDatabase(database)
	warehouseInventoryClient.WithConfiguration(
		[]client.Configuration{
			// {
			// 	Path:     getInventoryLocation,
			// 	Name:     "warehouse_inventory__GET__sku_location",
			// 	Database: database,
			// },
			// {
			// 	Path:     getInventorySKU,
			// 	Name:     "warehouse_inventory__GET__sku",
			// 	Database: database,
			// },
		}...,
	)
}

func GetListInventorySKULocation(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInventoryClient.WithAPIOption(opts...)
	var resp InventorySkuLocationResponse
	_, err := warehouseInventoryClient.WithRequest(getListInventoryLocation, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetInventorySKULocation(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInventoryClient.WithAPIOption(opts...)
	var resp InventorySkuLocationResponse
	_, err := warehouseInventoryClient.WithRequest(getInventoryLocation, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetInventorySKU(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInventoryClient.WithAPIOption(opts...)
	var resp InventorySkuResponse
	_, err := warehouseInventoryClient.WithRequest(getInventorySKU, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
