package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type InitialStockDraft struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	SKU string `json:"sku,omitempty" bson:"sku,omitempty"`

	InitialStock       *int64   `json:"initialStock,omitempty" bson:"initial_stock,omitempty"`
	OriginInitialStock int64    `json:"originInitialStock,omitempty" bson:"origin_initial_stock,omitempty"`
	DayDemand          float64  `json:"dayDemand,omitempty" bson:"day_demand,omitempty"`               // For trace
	DayAvailable       float64  `json:"dayAvailable,omitempty" bson:"day_available,omitempty"`         // For trace
	Leadtime           float64  `json:"leadtime,omitempty" bson:"leadtime,omitempty"`                  // For trace
	SkuDemand          float64  `json:"skuDemand,omitempty" bson:"sku_demand,omitempty"`               // For trace
	AVGDemand          float64  `json:"avgDemand,omitempty" bson:"avg_demand,omitempty"`               // For trace
	AveragePromotion   float64  `json:"averagePromotion,omitempty" bson:"average_promotion,omitempty"` // For trace
	MaxStockDay        *float64 `json:"maxStockDay,omitempty" bson:"max_stock_day,omitempty"`          // điều kiện tồn kho
	// MapStatusPerDaySKUItem map[string]map[string][]*Sku `json:"mapStatusPerDay,omitempty" bson:"map_status_per_day,omitempty"` // For trace

	StockDay    *float64 `json:"stockDay,omitempty" bson:"stock_day,omitempty"` // -1 not use, use leadtime | 0 stockday = 0 | > 0 stockday > 0
	MaxQuantity *int64   `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`

	// new sku demand logic
	CleaningSkuDemand           *float64           `json:"cleaningSkuDemand,omitempty" bson:"cleaning_sku_demand,omitempty"`                       // For trace
	CleaningAVGDemand           *float64           `json:"cleaningAVGDemand,omitempty" bson:"cleaning_avg_demand,omitempty"`                       // For trace
	CleaningDayAvailableSKUItem map[string]float64 `json:"cleaningDayAvailableSKUItem,omitempty" bson:"cleaning_day_available_sku_item,omitempty"` // For trace

	// logic demand for hedging
	HedgingDayDemand *float64 `json:"hedgingDayDemand,omitempty" bson:"hedging_day_demand,omitempty"` // For trace
	HedgingSkuDemand *float64 `json:"hedgingSkuDemand,omitempty" bson:"hedging_sku_demand,omitempty"` // For trace
	HedgingAVGDemand float64  `json:"hedgingAVGDemand,omitempty" bson:"hedging_avg_demand,omitempty"` // For trace

	Total30DaysDemand        float64 `json:"total30DaysDemand,omitempty" bson:"total_30_days_demand,omitempty"` // For trace
	DemandClassificationCode string  `json:"demandClassificationCode,omitempty" bson:"demand_classification_code,omitempty"`

	SKUIn        []string  `json:"skuIn,omitempty" bson:"-"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`

	CreatedTimeFrom *time.Time          `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo   *time.Time          `json:"createdTimeTo,omitempty" bson:"-"`
	IDFrom          *primitive.ObjectID `json:"idFrom" bson:"-"`
	IDTo            *primitive.ObjectID `json:"idTo" bson:"-"`
}

// InitialStockDraftDB ...
var InitialStockDraftDB = &db.Instance{
	ColName:        "initial_stock_draft" + conf.Config.UATSuffix,
	TemplateObject: &InitialStockDraft{},
}

func InitInitialStockDraftModel(s *mongo.Database) {
	InitialStockDraftDB.ApplyDatabase(s)

	// WORKER
	// t := true
	// _ = InitialStockDraftDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = InitialStockDraftDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
