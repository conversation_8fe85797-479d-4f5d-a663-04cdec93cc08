package model

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type WrongTicket struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ActionTime      *time.Time          `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ActionBy        string              `json:"actionBy,omitempty" bson:"action_by,omitempty"`
	CreatedByName   string              `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID     int64               `json:"createdByID,omitempty" bson:"created_by_id,omitempty"`

	WrongTicketID   int64  `json:"wrongTicketID" bson:"wrong_ticket_id,omitempty"`
	WrongTicketCode string `json:"wrongTicketCode" bson:"wrong_ticket_code,omitempty"`

	WarehouseTicketID        int64  `json:"warehouseTicketID,omitempty" bson:"warehouse_ticket_id,omitempty"`
	WarehouseTicketCode      string `json:"warehouseTicketCode,omitempty" bson:"warehouse_ticket_code,omitempty"`
	WarehouseReturnOrderCode string `json:"warehouseReturnOrderCode,omitempty" bson:"warehouse_return_order_code,omitempty"`
	//sync to wh
	Code          string             `json:"code,omitempty" bson:"-"`
	ErrorItems    []*WrongTicketItem `json:"errorItems,omitempty" bson:"-"`
	UpdatedByName string             `json:"updatedByName,omitempty" bson:"-"`

	Type          string          `json:"type,omitempty" bson:"type,omitempty"`
	WarehouseCode string          `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	SellerCode    string          `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	SellerType    enum.SellerType `json:"sellerType,omitempty" bson:"seller_type,omitempty"`
	PurchaserCode string          `json:"purchaserCode" bson:"purchaser_code,omitempty"`
	PICAccountID  int64           `json:"picAccountID,omitempty" bson:"pic_account_id,omitempty"`

	ErrorType  string `json:"errorType,omitempty" bson:"error_type,omitempty"`
	Inspection string `json:"inspection,omitempty" bson:"inspection,omitempty"`

	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorName string `json:"vendorName,omitempty" bson:"vendor_name,omitempty"`
	HashTag    string `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`

	POCodes        []string           `json:"poCodes,omitempty" bson:"po_codes,omitempty"`
	ROCodes        []string           `json:"roCodes,omitempty" bson:"ro_codes,omitempty"`
	SourceLocation string             `json:"sourceLocation,omitempty" bson:"source_location,omitempty"`
	Status         string             `json:"status,omitempty" bson:"status,omitempty"`
	Note           *string            `json:"note,omitempty" bson:"note,omitempty"`
	Items          []*WrongTicketItem `json:"items,omitempty" bson:"-"`

	ComplexQuery      []*bson.M  `json:"-" bson:"$and,omitempty"`
	WrongTicketCodeIn []string   `json:"wrongTicketCodeIn,omitempty" bson:"-"`
	POCodeIn          []string   `json:"poCodeIn,omitempty" bson:"-"`
	ROCodeIn          []string   `json:"roCodeIn,omitempty" bson:"-"`
	WrongTicketIDIn   []int64    `json:"wrongTicketIDIn" bson:"-"`
	CreatedTimeFrom   *time.Time `json:"createdTimeFrom,omitempty" bson:"-"`
	CreatedTimeTo     *time.Time `json:"createdTimeTo,omitempty" bson:"-"`
}

type WrongTicketItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ActionTime      *time.Time          `json:"actionTime,omitempty" bson:"action_time,omitempty"`
	ActionBy        string              `json:"actionBy,omitempty" bson:"action_by,omitempty"`

	WrongTicketItemID   int64  `json:"wrongTicketItemID" bson:"wrong_ticket_item_id,omitempty"`
	WrongTicketItemCode string `json:"wrongTicketItemCode" bson:"wrong_ticket_item_code,omitempty"`

	SKU           string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductID     int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode   string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductName   string `json:"productName,omitempty" bson:"product_name,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	ErrorId int64 `json:"errorId,omitempty" bson:"error_id,omitempty"`

	ErrorType    string `json:"errorType,omitempty" bson:"error_type,omitempty"`
	ErrorContent string `json:"errorContent,omitempty" bson:"error_content,omitempty"`

	WrongTicketCode          string `json:"wrongTicketCode" bson:"wrong_ticket_code,omitempty"`
	WrongTicketID            int64  `json:"wrongTicketID" bson:"wrong_ticket_id,omitempty"`
	WarehouseReturnOrderCode string `json:"warehouseReturnOrderCode,omitempty" bson:"warehouse_return_order_code,omitempty"`
	//sp wh switch status
	WarehouseTicketCode string `json:"warehouseTicketCode,omitempty" bson:"warehouse_ticket_code,omitempty"`

	ReturnOrderCode   string `json:"returnOrderCode,omitempty" bson:"return_order_code,omitempty"`
	PurchaseOrderCode string `json:"purchaseOrderCode,omitempty" bson:"purchase_order_code,omitempty"` // have value when updated PO

	Quantity  int64     `json:"quantity,omitempty" bson:"quantity,omitempty"`
	ImageUrls *[]string `json:"imageUrls,omitempty" bson:"image_urls,omitempty"`

	// unknown product
	// IsUpdateUnknownProduct *bool   `json:"isUpdateUnknownProduct,omitempty" bson:"-"`
	IsUnknownProduct bool   `json:"isUnknownProduct,omitempty" bson:"is_unknown_product,omitempty"`
	UnknownSKU       string `json:"unknownSKU,omitempty" bson:"unknown_sku,omitempty"`
	// UnknownProductUpdatedTime       *time.Time `json:"unknownProductUpdatedTime,omitempty" bson:"unknown_product_updated_time,omitempty"`
	// UnknownProductUpdatedByUsername string     `json:"unknownProductUpdatedByUsername,omitempty" bson:"unknown_product_updated_by_username,omitempty"`

	Status string  `json:"status,omitempty" bson:"status,omitempty"`
	Finish bool    `json:"finish,omitempty" bson:"finish,omitempty"`
	Note   *string `json:"note,omitempty" bson:"note,omitempty"`

	IsGift       bool      `json:"isGift,omitempty" bson:"is_gift,omitempty"`
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}
