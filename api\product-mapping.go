package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetProductMapping
func GetProductMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// parse input
	var query model.ProductMapping
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})
		}
	}

	// validate input
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// validate input
	if query.ProductVendorName != "" {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_vendors.product_vendor_slugs": primitive.Regex{Pattern: utils.NormalizeString(query.ProductVendorName)},
		})
	}

	// do action
	return resp.Respond(action.GetProductMapping(&query, offset, limit, getTotal))
}

// UpdateProductMapping
func UpdateProductMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		input []*model.ProductMapping
	)

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if len(input) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Name",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.UpdateProductMapping(input))
}

// DeleteProductMapping
func DeleteProductMapping(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		productID = sdk.ParseInt64(req.GetParam("productID"), 0)
	)

	if productID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid productID",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}

	// do action
	return resp.Respond(action.DeleteProductMapping(productID))

}
