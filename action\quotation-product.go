package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetQuotationProduct ...
// GetQuotationProduct retrieves a list of quotation products based on the provided query parameters.
// It supports pagination, sorting, and an option to get the total count of matching records.
//
// Parameters:
//   - query: The filter criteria for querying quotation products.
//   - offset: The number of records to skip for pagination.
//   - limit: The maximum number of records to return for pagination.
//   - getTotal: A boolean indicating whether to include the total count of matching records.
//   - sort: The field by which to sort the results. Supported values are:
//     - "currentQuantityExpect": Sort by current quantity expect in ascending order.
//     - "-currentQuantityExpect": Sort by current quantity expect in descending order.
//     - "currentPriceAfterDiscount": Sort by current price after discount in ascending order.
//     - "-currentPriceAfterDiscount": Sort by current price after discount in descending order.
//     - "currentTotalPrice": Sort by current total price in ascending order.
//     - "-currentTotalPrice": Sort by current total price in descending order.
//     - "sku": Sort by SKU in ascending order.
//     - "-sku": Sort by SKU in descending order.
//
// Returns:
//   - *common.APIResponse: The API response containing the list of quotation products and optionally the total count.
func GetQuotationProduct(query model.QuotationProduct, offset, limit int64, getTotal bool, sort string) *common.APIResponse {

	sortField := &primitive.M{"product_id": -1}
	switch sort {
	case "currentQuantityExpect":
		sortField = &primitive.M{"current_quantity_expect": 1}
	case "-currentQuantityExpect":
		sortField = &primitive.M{"current_quantity_expect": -1}
	case "currentPriceAfterDiscount":
		sortField = &primitive.M{"current_price_after_discount": 1}
	case "-currentPriceAfterDiscount":
		sortField = &primitive.M{"current_price_after_discount": -1}
	case "currentTotalPrice":
		sortField = &primitive.M{"current_total_price": 1}
	case "-currentTotalPrice":
		sortField = &primitive.M{"current_total_price": -1}
	case "sku":
		sortField = &primitive.M{"sku": 1}
	case "-sku":
		sortField = &primitive.M{"sku": -1}
	}

	// Query primary DB
	quotationProductResp := model.QuotationProductDB.Query(query, offset, limit, sortField)
	if getTotal {
		countResp := model.QuotationProductDB.Count(query)
		quotationProductResp.Total = countResp.Total
	}

	return quotationProductResp
}

// GetQuotationProductVendor ...
func GetQuotationProductVendor(query model.QuotationProductVendor, offset, limit int64, getTotal bool) *common.APIResponse {
	quotationProductVendorResp := model.QuotationProductVendorDB.Query(query, offset, limit, &primitive.M{"product_id": -1})
	if getTotal {
		countResp := model.QuotationProductVendorDB.Count(query)
		quotationProductVendorResp.Total = countResp.Total
	}

	return quotationProductVendorResp
}

// DeleteQuotationProduct ...
func DeleteQuotationProduct(productCode string) *common.APIResponse {
	model.QuotationProductDB.Delete(&model.QuotationProduct{ProductCode: productCode})
	model.QuotationProductVendorDB.Delete(&model.QuotationProductVendor{ProductCode: productCode})
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Delete success",
	}
}
