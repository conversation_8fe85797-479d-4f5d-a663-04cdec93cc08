package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type PaymentLegacy struct {
	// primitive
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// meatadata
	PaidTime *time.Time `json:"paidTime,omitempty" bson:"paid_time,omitempty"`

	// status type
	Type    enum.PaymentType        `json:"type,omitempty" bson:"type,omitempty"`       // loại thanh toán (thu, chi)
	Purpose enum.PaymentPurposeType `json:"purpose,omitempty" bson:"purpose,omitempty"` // mục đích thanh toán

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdById,omitempty" bson:"created_by_id,omitempty"`

	// basic
	PaymentID   int64  `json:"paymentID,omitempty" bson:"payment_id,omitempty"`
	PaymentCode string `json:"paymentCode,omitempty" bson:"payment_code,omitempty"`

	// info
	// SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`
	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	// fields chỉ dành thanh toán có dư nợ
	PrePaidPOCodes     []string `json:"prePaidPOCodes,omitempty" bson:"pre_paid_po_codes,omitempty"`        // PO (trả tạm ứng)
	RefundBillCode     string   `json:"refundBillCode,omitempty" bson:"refund_bill_code,omitempty"`         // link to refund bill
	AdjustmentBillCode string   `json:"adjustmentBillCode,omitempty" bson:"adjustment_bill_code,omitempty"` // hoá đơn điều chỉnh (loại giảm)

	PaymentMethod string `json:"paymentMethod,omitempty" bson:"payment_method,omitempty"` // hình thức thanh toán

	// amount
	TotalPrice *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	Balance    *float64 `json:"balance,omitempty" bson:"balance,omitempty"`

	// other
	Note string `json:"note,omitempty" bson:"note,omitempty"`

	// For FE
	Items   []PaymentItemLegacy `json:"items,omitempty" bson:"-"`
	Token   string              `json:"token,omitempty" bson:"-"`
	POCodes []string            `json:"poCodes,omitempty" bson:"po_codes,omitempty"` // PO đã thanh toán cho hoá đơn (search, tracking)
	// query at backend
	ComplexQuery     []*bson.M  `json:"-" bson:"$and,omitempty"`
	OrQuery          []*bson.M  `json:"-" bson:"$or,omitempty"`
	PaidFrom         *time.Time `json:"paidFrom,omitempty" bson:"-"`
	PaidTo           *time.Time `json:"paidTo,omitempty" bson:"-"`
	PaymentCodeIn    []string   `json:"paymentCodeIn,omitempty" bson:"-"`
	POCodeIn         []string   `json:"poCodeIn,omitempty" bson:"-"`
	VendorCodeIn     []string   `json:"vendorCodeIn,omitempty" bson:"-"`
	RefundBillCodeIn []string   `json:"refundBillCodeIn,omitempty" bson:"-"` // link to refund bill
}

type PaymentItemLegacy struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	CreatedByName string `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64  `json:"createdById,omitempty" bson:"created_by_id,omitempty"`

	PaidTime *time.Time `json:"paidTime,omitempty" bson:"paid_time,omitempty"`

	PaymentID   int64  `json:"paymentID,omitempty" bson:"payment_id,omitempty"`
	PaymentCode string `json:"paymentCode,omitempty" bson:"payment_code,omitempty"`

	// SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`
	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	// thanh toán cho
	VendorBillID       int64  `json:"vendorBillID,omitempty" bson:"vendor_bill_id,omitempty"`
	VendorBillCode     string `json:"vendorBillCode,omitempty" bson:"vendor_bill_code,omitempty"`
	AdjustmentBillID   int64  `json:"adjustmentBillID,omitempty" bson:"adjustment_bill_id,omitempty"`
	AdjustmentBillCode string `json:"adjustmentBillCode,omitempty" bson:"adjustment_bill_code,omitempty"`
	POCode             string `json:"poCode,omitempty" bson:"po_code,omitempty"`
	InvoiceNumber      string `json:"invoiceNumber,omitempty" bson:"invoice_number,omitempty"`
	PaymentVoucherCode string `json:"paymentVoucherCode,omitempty" bson:"payment_voucher_code,omitempty"`
	ReceiptVoucherCode string `json:"receiptVoucherCode,omitempty" bson:"receipt_voucher_code,omitempty"`

	TransactionType string `json:"transactionType,omitempty" bson:"transaction_type,omitempty"`

	PaymentItemCode string `json:"paymentItemCode,omitempty" bson:"payment_item_code,omitempty"`

	TotalPrice *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`

	// query
	ComplexQuery         []*bson.M `json:"-" bson:"$and,omitempty"`
	VendorBillCodeIn     []string  `json:"vendorBillCodeIn,omitempty" bson:"-"`
	AdjustmentBillCodeIn []string  `json:"adjustmentBillCodeIn,omitempty" bson:"-"`
	PaymentCodeIn        []string  `json:"paymentCodeIn,omitempty" bson:"-"`
}

var (
	PaymentLegacyDB = &db.Instance{
		ColName:        "payment",
		TemplateObject: &PaymentLegacy{},
	}
	PaymentItemLegacyDB = &db.Instance{
		ColName:        "payment_item",
		TemplateObject: &PaymentItemLegacy{},
	}
)

func InitNewPaymentModel(s *mongo.Database) {
	PaymentLegacyDB.ApplyDatabase(s)
	PaymentItemLegacyDB.ApplyDatabase(s)
}
