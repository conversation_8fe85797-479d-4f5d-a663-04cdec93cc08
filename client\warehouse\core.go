package warehouse

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var warehouseCoreClient *client.Client

const (
	getWarehouse = "GET::/warehouse"
	putIndexSO   = "PUT::/sale-orders/index"
)

func InitWarehouseCore(database *mongo.Database) {
	const warehouseCoreV1 = "/warehouse/core/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	warehouseCoreClient = client.NewClient(warehouseCoreV1, headers, 0)
	warehouseCoreClient.WithDatabase(database)
	warehouseCoreClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     getWarehouse,
				Name:     "warehouse_core__GET__warehouse_list",
				Database: database,
			},
			{
				Path:     putIndexSO,
				Name:     "warehouse_core__PUT__update_index_so",
				Database: database,
			},
		}...,
	)
}

func GetWarehouse(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseCoreClient.WithAPIOption(opts...)
	var resp WarehouseResponse
	_, err := warehouseCoreClient.WithRequest(getWarehouse, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func UpdateIndexSO(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseCoreClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := warehouseCoreClient.WithRequest(putIndexSO, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
