package model

import (
	"time"

	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// ActionSource ...
type ActionSource struct {
	Account *Account `json:"account"`
}

// Account is model define response from register account
type Account struct {
	AccountID int64  `json:"accountId"`
	Type      string `json:"type"`
	Fullname  string `json:"fullname"`
	Username  string `json:"username"`
}

type FileInfo struct {
	ModifiedTime *time.Time `json:"modTime"`
	Name         string     `json:"name"`
	Size         int64      `json:"size"`
	IsDir        bool       `json:"isDir"`
}

type FileUpload struct {
	Data        string `json:"data"`
	Destination string `json:"destination"`
}

type ProductCategoryInCharge struct {
	CreatedTime     *time.Time `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Code        string `json:"code,omitempty" bson:"code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`

	CategoryInChargeData []*ProductCategoryInChargeData `json:"categoryInChargeData,omitempty" bson:"category_in_charge_data,omitempty"`

	// for filter
	WarehouseCode      string  `json:"warehouseCode,omitempty" bson:"-"`
	CategoryManagement *string `json:"categoryManagement,omitempty" bson:"-"`
	CategoryInCharge   *int64  `json:"categoryInCharge,omitempty" bson:"-"`
}

type ProductCategoryInChargeData struct {
	WarehouseCode      string  `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	CategoryManagement *string `json:"categoryManagement,omitempty" bson:"category_management,omitempty"`
	CategoryInCharge   *int64  `json:"categoryInCharge,omitempty" bson:"category_in_charge,omitempty"`
}

type DealResponse struct {
	SellerCode    string  `json:"sellerCode"`
	WarehouseCode string  `json:"warehouseCode"`
	SKU           string  `json:"sku,omitempty"`
	AVGDemand     float64 `json:"avgDemand,omitempty"`
	MaxQuantity   *int64  `json:"maxQuantity,omitempty" bson:"max_quantity,omitempty"`

	// SkuConfig        *SkuConfig           `json:"skuConfig,omitempty"`
	Seller                             *Seller              `json:"seller,omitempty"`
	SellerLineManager                  *Seller              `json:"sellerLineManager,omitempty"`
	IsValid                            *bool                `json:"isValid"`
	IsShow                             *bool                `json:"isShow"`
	ProductCode                        string               `json:"productCode,omitempty"`
	ProductID                          int64                `json:"productID,omitempty"`
	PurchaserCode                      string               `json:"purchaserCode,omitempty"`
	Status                             *enum.SkuStatusValue `json:"status,omitempty"`
	LotDates                           []LotDates           `json:"lotDates,omitempty" bson:"lot_dates,omitempty"`
	SellerClass                        string               `json:"seller_class,omitempty"`
	RetailPriceType                    string               `json:"retailPriceType,omitempty"`
	RetailPriceValue                   int64                `json:"retailPriceValue,omitempty"`
	ItemCode                           string               `json:"itemCode,omitempty"`
	PriceAfterRebateWithAdhocPromotion *float64             `json:"priceAfterRebateWithAdhocPromotion,omitempty"`

	DealCode       string `json:"dealCode,omitempty"`
	DealTicketCode string `json:"dealTicketCode,omitempty"`

	// Thông tin ngày tạo deal, ngày kết thúc deal
	StartTime *time.Time `json:"startTime,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty"`
}

type Region struct {
	RegionID      int64    `json:"regionID,omitempty" bson:"region_id,omitempty"`
	Code          string   `json:"code,omitempty" bson:"code,omitempty"`
	Name          string   `json:"name" bson:"name,omitempty"`
	ProvinceCodes []string `json:"provinceCodes,omitempty" bson:"province_codes,omitempty"`
}

type InboundRequestItemResponse struct {
	ApplyTime              string                         `json:"applyTime,omitempty"`
	StartTime              string                         `json:"startTime,omitempty"`
	EndTime                string                         `json:"endTime,omitempty"`
	ProductCode            string                         `json:"productCode,omitempty"`
	ProductID              int64                          `json:"productID,omitempty"`
	SKU                    string                         `json:"sku,omitempty"`
	SellerCode             string                         `json:"sellerCode,omitempty"`
	WarehouseCode          string                         `json:"warehouseCode,omitempty"`
	InboundRequestID       int64                          `json:"inboundRequestID,omitempty"`
	InboundRequestCode     string                         `json:"inboundRequestCode,omitempty"`
	InboundRequestItemID   int64                          `json:"inboundRequestItemID,omitempty"`
	InboundRequestItemCode string                         `json:"inboundRequestItemCode,omitempty"`
	InboundRequestType     enum.InboundRequestTypeValue   `json:"inboundRequestType,omitempty"`
	ExpectQuantity         int64                          `json:"expectQuantity,omitempty"`
	Status                 enum.InboundRequestStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	IsActive               *bool                          `json:"isActive,omitempty" bson:"is_active,omitempty"`
	VendorCode             *string                        `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	PoCode                 *string                        `json:"poCode,omitempty" bson:"po_code,omitempty"`
	PurchaseOrderItemCode  string                         `json:"purchaseOrderItemCode,omitempty"`
	// Contract price
	UnitPrice              float64    `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	VAT                    float64    `json:"vat,omitempty" bson:"vat,omitempty"`
	Discount               *float64   `json:"discount,omitempty" bson:"discount,omitempty"`
	ContractPriceCode      string     `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	ContractPriceStartTime *time.Time `json:"contractPriceStartTime,omitempty" bson:"contract_price_start_time,omitempty"`
	ContractPriceEndTime   *time.Time `json:"contractPriceEndTime,omitempty" bson:"contract_price_end_time,omitempty"`

	// Promotion - Deal
	DealTicketCode string `json:"dealTicketCode,omitempty" bson:"deal_ticket_code,omitempty"`

	NewUnitPrice              float64    `json:"newUnitPrice,omitempty"`
	NewVAT                    float64    `json:"newVAT,omitempty"`
	NewDiscount               float64    `json:"newDiscount,omitempty"`
	NewContractPriceCode      string     `json:"newContractPriceCode,omitempty"`
	NewContractPriceStartTime *time.Time `json:"newContractPriceStartTime,omitempty"`
	NewContractPriceEndTime   *time.Time `json:"newContractPriceEndTime,omitempty"`

	PurchasedProductCode string                        `json:"purchasedProductCode,omitempty"`
	PurchasedProductID   int64                         `json:"purchasedProductID,omitempty"`
	PurchaserFrom        float64                       `json:"purchaserFrom,omitempty"`
	PurchaserTo          float64                       `json:"purchaserTo,omitempty"`
	IsIntoCOGS           *bool                         `json:"isIntoCOGS,omitempty"`
	PromotionID          int64                         `json:"promotionID,omitempty"`
	PromotionCode        string                        `json:"promotionCode,omitempty"`
	PromotionName        string                        `json:"promotionName,omitempty"`
	PromotionType        enum.VendorPromotionTypeValue `json:"promotionType,omitempty"`
	PromotionGroup       string                        `json:"promotionGroup,omitempty"`
	PromotionStartDate   *time.Time                    `json:"promotionStartDate,omitempty"`
	PromotionEndDate     *time.Time                    `json:"promotionEndDate,omitempty"`
	GiftProductCode      string                        `json:"giftProductCode,omitempty"`
	GiftProductID        int64                         `json:"giftProductID,omitempty"`
	GiftQty              int64                         `json:"giftQty,omitempty"`
	TotalPerStepVendor   int64                         `json:"totalPerStepVendor,omitempty"`

	AVGDemand             float64 `json:"avgDemand"`
	IncomingQty           int64   `json:"incomingQty"`
	WaitToReserveQty      int64   `json:"waitToReserveQty"`
	InboundQuantity       int64   `json:"inboundQuantity"`
	AvailableQuantity     int64   `json:"availableQuantity"`
	DealCreation          *bool   `json:"dealCreation,omitempty"`
	SameWithHedgingVendor *bool   `json:"sameWithHedgingVendor,omitempty"`
	OrderQty              int64   `json:"orderQty"`
	SalesQtyPerDeal       int64   `json:"salesQtyPerDeal"`

	PriceAfterRebateWithAdhocPromotion *float64 `json:"priceAfterRebateWithAdhocPromotion,omitempty"`
}

type ContractPriceItemInboundRequestResponse struct {
	ProductCode            string     `json:"productCode,omitempty"`
	ProductName            string     `json:"productName,omitempty"`
	ProductID              int64      `json:"productID,omitempty"`
	ContractPriceCode      string     `json:"contractPriceCode,omitempty"`
	ContractPriceID        int64      `json:"contractPriceID,omitempty"`
	WarehouseCode          string     `json:"warehouseCode,omitempty"`
	UnitPrice              float64    `json:"unitPrice,omitempty"`
	VAT                    float64    `json:"vat,omitempty"`
	Discount               float64    `json:"discount,omitempty"`
	ContractPriceStartTime *time.Time `json:"contractPriceStartTime,omitempty"`
	ContractPriceEndTime   *time.Time `json:"contractPriceEndTime,omitempty"`
	SKU                    string     `json:"sku,omitempty"`
	Seller                 *Seller    `json:"seller,omitempty" bson:"-"`

	Kind                        string   `json:"kind,omitempty"`
	AvgDemand                   float64  `json:"avgDemand,omitempty"`
	NumDayInStock               int64    `json:"numDayInStock"`
	ExpectQuantity              int64    `json:"expectQuantity"`
	TotalAvailableQuantity      int64    `json:"totalAvailableQuantity,omitempty"`
	AvailableQuantityWHInbound  int64    `json:"availableQuantityWHInbound,omitempty"`
	QuantityIncoming            int64    `json:"quantityIncoming,omitempty"`
	WaitingConfirmOrderQuantity int64    `json:"waitingConfirmOrderQuantity,omitempty"`
	PoCodes                     []string `json:"poCodes,omitempty"`

	// for demand hedging
	AvgHedgingDemand         float64 `json:"avgHedgingDemand,omitempty"`
	DemandClassificationCode string  `json:"demandClassificationCode,omitempty"`
	Total30DaysDemand        float64 `json:"total30DaysDemand,omitempty"`
}

type DemandHedgingResponse struct {
	SKU                      string  `json:"sku,omitempty"`
	WarehouseCode            string  `json:"warehouseCode,omitempty"`
	DemandClassificationCode string  `json:"demandClassificationCode,omitempty"`
	Total30DaysDemand        float64 `json:"total30DaysDemand,omitempty"`
	HedgingDayDemand         float64 `json:"hedgingDayDemand,omitempty"` // For trace
	HedgingSkuDemand         float64 `json:"hedgingSkuDemand,omitempty"` // For trace
	HedgingAVGDemand         float64 `json:"hedgingAVGDemand,omitempty"` // For trace
}
type CalcPriceAfterRebateResp struct {
	PriceAfterRebate                   *float64 `json:"priceAfterRebate,omitempty"`
	PriceAfterRebateWithAdhocPromotion *float64 `json:"priceAfterRebateWithAdhocPromotion,omitempty"`
}

type InboundRequestItemValidateImportResponse struct {
	ContractPriceItemInboundRequestResponse
	Leadtime                              float64 `json:"leadtime,omitempty"`
	MinQuantity                           int64   `json:"minQuantity,omitempty"` // minQuantity is originInitialStock
	InventorySkuAvailableQuantity         int64   `json:"inventorySkuAvailableQuantity"`
	InventorySkuLocationAvailableQuantity int64   `json:"inventorySkuLocationAvailableQuantity"`
	TotalIncoming                         int64   `json:"totalIncoming"`
	InboundRequestCode                    string  `json:"inboundRequestCode,omitempty"`
	VendorCode                            string  `json:"vendorCode,omitempty"`
	PromotionCode                         string  `json:"promotionCode,omitempty"`

	AvgDemand                   float64 `json:"avgDemand,omitempty"`
	NumDayInStock               int64   `json:"numDayInStock"`
	ExpectQuantity              int64   `json:"expectQuantity"`
	TotalAvailableQuantity      int64   `json:"totalAvailableQuantity,omitempty"`
	AvailableQuantityWHInbound  int64   `json:"availableQuantityWHInbound,omitempty"`
	QuantityIncoming            int64   `json:"quantityIncoming,omitempty"`
	WaitingConfirmOrderQuantity int64   `json:"waitingConfirmOrderQuantity,omitempty"`
}
type PriceForDeal struct {
	Price   *float64          `json:"price,omitempty"`
	Source  string            `json:"source,omitempty"`
	IsCombo bool              `json:"isCombo,omitempty"`
	SubSku  []SubPriceForDeal `json:"subSku,omitempty"`
}

type SubPriceForDeal struct {
	ProductID  int64    `json:"productID,omitempty"`
	ItemCode   string   `json:"itemCode,omitempty"`
	SKU        string   `json:"sku,omitempty"`
	Price      *float64 `json:"price,omitempty"`
	Source     string   `json:"source,omitempty"`
	Quantity   int      `json:"quantity,omitempty"`
	TotalPrice *float64 `json:"totalPrice,omitempty"`
}
type SKUInventoryInfoResponse struct {
	Sku           string `json:"sku,omitempty"`
	ItemCode      string `json:"itemCode,omitempty"`
	WarehouseCode string `json:"warehouseCode,omitempty"`
	SellerCode    string `json:"sellerCode,omitempty"`

	TotalQuantity               int64 `json:"totalQuantity,omitempty"`
	AvailableQuantity           int64 `json:"availableQuantity,omitempty"`
	ReseverdQuantity            int64 `json:"reseverdQuantity,omitempty"`
	MissingQuantity             int64 `json:"missingQuantity,omitempty"`
	WaitingHoldQuantity         int64 `json:"waitingHoldQuantity,omitempty"`
	WaitingInboundQuantity      int64 `json:"waitingInboundQuantity,omitempty"`
	WaitingPutQuantity          int64 `json:"waitingPutQuantity,omitempty"`
	WaitingConfirmOrderQuantity int64 `json:"waitingConfirmOrderQuantity,omitempty"`
}

type DetermineStockAvailabilityResponse struct {
	AvgDemand                   float64  `json:"avgDemand,omitempty"`
	TotalAvailableQuantity      int64    `json:"totalAvailableQuantity,omitempty"`
	AvailableQuantityWHInbound  int64    `json:"availableQuantityWHInbound,omitempty"`
	QuantityIncoming            int64    `json:"quantityIncoming,omitempty"`
	WaitingConfirmOrderQuantity int64    `json:"waitingConfirmOrderQuantity,omitempty"`
	NumDayInStock               int64    `json:"numDayInStock,omitempty"`
	ExpectQuantity              int64    `json:"expectQuantity,omitempty"`
	PoCodes                     []string `json:"poCodes,omitempty"`
	Status                      string   `json:"status,omitempty"`
	WaitingHoldQuantity         int64    `json:"waitingHoldQuantity,omitempty"`
}
