package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SkipSO struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productId,omitempty" bson:"product_id,omitempty"`

	WarehouseCode string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	SOCode        string `json:"soCode,omitempty" bson:"so_code,omitempty"`

	Hashtag string `json:"hashtag,omitempty" bson:"hashtag,omitempty"`

	SOCodeIn []string `json:"soCodeIn,omitempty" bson:"-"`
	// OrderTimeFrom *time.Time `json:"orderTimeFrom,omitempty" bson:"-"`
	// OrderTimeTo   *time.Time `json:"orderTimeTo,omitempty" bson:"-"`
	Search string `json:"search,omitempty" bson:"-"`

	// query at backend
	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`
}

// SkuConfigDB ...
var SkipSODB = &db.Instance{
	ColName:        "skip_so",
	TemplateObject: &SkipSO{},
}

func InitSkipSOModel(s *mongo.Database) {
	SkipSODB.ApplyDatabase(s)
}
