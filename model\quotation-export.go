package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type QuotationExport struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	Version string `json:"version,omitempty" bson:"version,omitempty"`

	WarehouseCode string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	SellerCode string `json:"sellerCode" bson:"seller_code,omitempty"`
	VendorCode string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	SKUVendorType enum.SKUVendorType `json:"skuVendorType,omitempty" bson:"sku_vendor_type,omitempty"`
	Type          enum.QuotationType `json:"type" bson:"type,omitempty"`

	UnitPrice float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	VAT       int64   `json:"vat,omitempty" bson:"vat,omitempty"`
	Unit      string  `json:"unit,omitempty" bson:"unit,omitempty"`

	Packaging string `json:"packaging,omitempty" bson:"packaging,omitempty"`

	// QuantityOrderAllSO  int64 `json:"quantityOrderAllSO,omitempty" bson:"quantity_order_all_so,omitempty"`   // QuantityOrder = Quantity - ReservedQuantity
	QuantityOrderWithSO int64 `json:"quantityOrderWithSO,omitempty" bson:"quantity_order_with_so,omitempty"` // QuantityOrder = Quantity - ReservedQuantity
	QuantityIncoming    int64 `json:"quantityIncoming,omitempty" bson:"quantity_incoming,omitempty"`         // Total count sku po
	QuantityAvailable   int64 `json:"quantityAvailable,omitempty" bson:"quantity_available,omitempty"`       // Total count sku in stock
	QuantityInbound     int64 `json:"quantityInbound,omitempty" bson:"quantity_inbound,omitempty"`           // Total count sku inbounding

	// inbound request
	QuantityPromotionIR           int64  `json:"quantityPromotionIR,omitempty" bson:"quantity_promotion_ir,omitempty"`                       // IR promotion
	QuantityPromotionWithVendorIR int64  `json:"quantityPromotionWithVendorIR,omitempty" bson:"quantity_promotion_with_vendor_ir,omitempty"` // IR promotion with vendor
	QuantityOtherIR               int64  `json:"quantityOtherIR,omitempty" bson:"quantity_other_ir,omitempty"`                               // hedging
	QuantityOtherWithVendorIR     int64  `json:"quantityOtherWithVendorIR,omitempty" bson:"quantity_other_with_vendor_ir,omitempty"`         // hedging with vendor
	InboundRequestCode            string `json:"inboundRequestCode,omitempty" bson:"inbound_request_code,omitempty"`
	InboundRequestID              int64  `json:"inboundRequestID,omitempty" bson:"inbound_request_id,omitempty"`
	InboundRequestItemCode        string `json:"inboundRequestItemCode,omitempty" bson:"inbound_request_item_code,omitempty"`
	InboundRequestName            string `json:"inboundRequestName,omitempty" bson:"inbound_request_name,omitempty"`
	//
	QuantitySaveInitialStock int64   `json:"quantitySaveInitialStock,omitempty" bson:"quantity_save_initial_stock,omitempty"`
	Leadtime                 float64 `json:"leadtime,omitempty" bson:"leadtime,omitempty"` // để trace

	// tracking
	BlockReleaseToBiddingSession *bool `json:"blockReleaseToBiddingSession,omitempty" bson:"block_release_to_bidding_session,omitempty"`
	BlockReleaseToFavSession     *bool `json:"blockReleaseToFavSession,omitempty" bson:"block_release_to_fav_session,omitempty"`

	// output
	QuantityExpect       *int64 `json:"quantityExpect,omitempty" bson:"quantity_expect,omitempty"`
	VendorExpectQuantity *int64 `json:"vendorExpectQuantity,omitempty" bson:"vendor_expect_quantity,omitempty"`

	QuantityHedging int64 `json:"quantityHedging,omitempty" bson:"quantity_hedging,omitempty"`

	MaxInStock     int64                  `json:"maxInStock,omitempty" bson:"max_in_stock,omitempty"`
	MinInStock     int64                  `json:"minInStock,omitempty" bson:"min_in_stock,omitempty"`
	MinPerStep     int64                  `json:"minPerStep,omitempty" bson:"min_per_step,omitempty"`
	Formula        string                 `json:"formula,omitempty" bson:"formula,omitempty"`
	LogFormulaData map[string]interface{} `json:"logFormulaData,omitempty" bson:"log_formula_data,omitempty"`

	IsExisted bool `json:"isExisted,omitempty" bson:"is_existed,omitempty"`

	// For queue
	// SOCodesAll []string `json:"soCodesAll,omitempty" bson:"so_codes_all,omitempty"`
	SOCodes                    []string               `json:"soCodes,omitempty" bson:"so_codes,omitempty"`
	Logs                       []string               `json:"logs" bson:"logs,omitempty"` // For trace
	InboundRequestItemApplieds []string               `json:"inboundRequestItemApplieds,omitempty" bson:"inbound_request_item_applieds,omitempty"`
	TrackVendorPromotionExpect *VendorPromotionExpect `json:"trackVendorPromotionExpect,omitempty" bson:"track_vendor_promotion_expect,omitempty"`
	// For query
	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

type VendorPromotionExpect struct {
	PromotionRound        int64   `json:"promotionRound,omitempty" bson:"promotion_round,omitempty"`
	PromotionCode         string  `json:"promotionCode,omitempty" bson:"promotion_code,omitempty"`
	GiftExpectQty         float64 `json:"giftExpectQty,omitempty" bson:"gift_expect_qty,omitempty"`
	SuggestQty            float64 `json:"suggestQty,omitempty" bson:"suggest_qty,omitempty"`
	CurrentStockDay       float64 `json:"currentStockDay,omitempty" bson:"current_stock_day,omitempty"` // for sort
	VendorPromotionItemID int64   `json:"vendorPromotionItemID,omitempty" bson:"vendor_promotion_item_id,omitempty"`
	Range                 int64   `json:"range,omitempty" bson:"range,omitempty"`

	// checking
	QuantityHedging int64 `json:"quantityHedging,omitempty" bson:"quantity_hedging,omitempty"`
}

// QuotationExportDB ...
var QuotationExportDB = &db.Instance{
	ColName:        "quotation_export",
	TemplateObject: &QuotationExport{},
}

// QuotationExportDraftDB ...
var QuotationExportDraftDB = &db.Instance{
	ColName:        "quotation_export_draft",
	TemplateObject: &QuotationExport{},
}

var QuotationExportHedgingDraftDB = &db.Instance{
	ColName:        "quotation_export_hedging_draft",
	TemplateObject: &QuotationExport{},
}

func InitQuotationExportModel(s *mongo.Database) {
	QuotationExportDB.ApplyDatabase(s)
	QuotationExportDraftDB.ApplyDatabase(s)
	QuotationExportHedgingDraftDB.ApplyDatabase(s)

	// t := true
	// _ = QuotationExportDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationExportDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "quantity_expect", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = QuotationExportDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "so_codes", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// //============== DRAFT ==============
	// _ = QuotationExportDraftDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = QuotationExportDraftDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "version", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })

	// expire := int32((1 * 24 * time.Hour).Seconds())
	// _ = QuotationExportDraftDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background:         &t,
	// 	ExpireAfterSeconds: &expire,
	// })

}
