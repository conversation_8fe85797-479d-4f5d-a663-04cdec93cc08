package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type AdminPushingSetting struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	AdminPushingSettingID   int64  `json:"adminPushingSettingID,omitempty" bson:"admin_pushing_setting_id,omitempty"`
	AdminPushingSettingCode string `json:"adminPushingSettingCode,omitempty" bson:"admin_pushing_setting_code,omitempty"`

	SellerCode     string    `json:"sellerCode" bson:"seller_code,omitempty"`
	WarehouseCodes *[]string `json:"warehouseCodes" bson:"warehouse_codes,omitempty"`

	Weekdays    *[]int `json:"weekdays,omitempty" bson:"week_days,omitempty"`
	RunTimeHHMM string `json:"runTimeHHMM" bson:"run_time_hh_mm,omitempty"`

	IsAutoConfirm *bool `json:"isAutoConfirm,omitempty" bson:"is_auto_confirm,omitempty"`
	IsAutoRun     *bool `json:"isAutoRun,omitempty" bson:"is_auto_run,omitempty"`

	SKUVendorType enum.SKUVendorType             `json:"skuVendorType,omitempty" bson:"sku_vendor_type,omitempty"`
	ConfirmType   enum.AdminPushGroupConfirmType `json:"confirmType,omitempty" bson:"confirm_type,omitempty"`

	LastDayRun string `json:"lastDayRun,omitempty" bson:"last_day_run,omitempty"`

	ComplexQuery []*bson.M `json:"-" bson:"$and,omitempty"`
}

// AdminPushingSettingDB ...
var AdminPushingSettingDB = &db.Instance{
	ColName:        "admin_pushing_setting",
	TemplateObject: &AdminPushingSetting{},
}

// InitAdminPushingSettingModel ...
func InitAdminPushingSettingModel(s *mongo.Database) {
	AdminPushingSettingDB.ApplyDatabase(s)
}
