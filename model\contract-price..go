package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type ContractPrice struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// basic
	ContractPriceCode string `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	ContractPriceID   int64  `json:"contractPriceID,omitempty" bson:"contract_price_id,omitempty"`

	// info
	VendorCodes   []string `json:"vendorCodes,omitempty" bson:"vendor_codes,omitempty"`
	WarehouseCode string   `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	// date
	StartTime *time.Time `json:"startTime,omitempty" bson:"start_time,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"end_time,omitempty"`

	// status
	Status enum.ContractPriceStatusValue `json:"status,omitempty" bson:"status,omitempty"`

	// other
	HashTag   string  `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`
	Name      *string `json:"name,omitempty" bson:"name,omitempty"`
	Reference *string `json:"reference,omitempty" bson:"reference,omitempty"`
	Note      *string `json:"note,omitempty" bson:"note,omitempty"`

	// client
	StatusIn          []enum.ContractPriceStatusValue `json:"statusIn,omitempty" bson:"-"`
	ProductCodeIn     []string                        `json:"productCodeIn,omitempty" bson:"-"`
	VendorCodeIn      []string                        `json:"vendorCodeIn,omitempty" bson:"-"`
	WarehouseCodeIn   []string                        `json:"warehouseCodeIn,omitempty" bson:"-"`
	Search            string                          `json:"search,omitempty" bson:"-"`
	TimeFrom          *time.Time                      `json:"timeFrom,omitempty" bson:"-"`
	TimeTo            *time.Time                      `json:"timeTo,omitempty" bson:"-"`
	ActiveStatus      enum.PriceActiveStatusValue     `json:"activeStatus,omitempty" bson:"-"`
	TimeIn            *time.Time                      `json:"timeIn,omitempty" bson:"-"`
	TimeIntersectFrom *time.Time                      `json:"timeIntersectFrom,omitempty" bson:"-"`
	TimeIntersectTo   *time.Time                      `json:"timeIntersectTo,omitempty" bson:"-"`

	Files *[]ContractPriceFile `json:"files,omitempty" bson:"files,omitempty"`

	// response
	Items []*ContractPriceItem `json:"items,omitempty" bson:"-"`

	// query
	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`
	OrQuery      []bson.M `json:"-" bson:"$or,omitempty"`

	DiffDay int `json:"diffDay,omitempty" bson:"-"`
}

type ContractPriceFile struct {
	Url  string `json:"url,omitempty" bson:"url,omitempty"`
	Name string `json:"name,omitempty" bson:"name,omitempty"`
}

type ContractPriceItem struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// basic
	ContractPriceItemID int64  `json:"contractPriceItemID,omitempty" bson:"contract_price_item_id,omitempty"`
	ProductCode         string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductName         string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductID           int64  `json:"productID,omitempty" bson:"product_id,omitempty"`

	// ref
	ContractPriceCode string `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	ContractPriceID   int64  `json:"contractPriceID,omitempty" bson:"contract_price_id,omitempty"`
	WarehouseCode     string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`

	// price
	UnitPrice float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"` // giá mua chưa VAT
	VAT       float64 `json:"vat,omitempty" bson:"vat,omitempty"`              // VAT
	Discount  float64 `json:"discount,omitempty" bson:"discount,omitempty"`    // Discount

	UnitPriceBK float64 `json:"unitPriceBK,omitempty" bson:"unit_price_bk,omitempty"` // giá mua chưa VAT
	// date
	StartTime               *time.Time `json:"startTime,omitempty" bson:"-"`
	EndTime                 *time.Time `json:"endTime,omitempty" bson:"-"`
	WinnerUnitPrice         float64    `json:"winnerUnitPrice,omitempty" bson:"-"`
	WinnerContractPriceCode string     `json:"WinnerContractPriceCode,omitempty" bson:"-"`

	// query
	ComplexQuery []bson.M `json:"-" bson:"$and,omitempty"`

	Seller *Seller `json:"seller,omitempty" bson:"-"`
}

var ContractPriceDB = &db.Instance{
	ColName:        "contract_price",
	TemplateObject: &ContractPrice{},
}

var ContractPriceItemDB = &db.Instance{
	ColName:        "contract_price_item",
	TemplateObject: &ContractPriceItem{},
}

func InitContractPriceModel(s *mongo.Database) {
	ContractPriceDB.ApplyDatabase(s)
	ContractPriceItemDB.ApplyDatabase(s)
}
