package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"go.mongodb.org/mongo-driver/mongo"
)

// UpdateESJob ...
var UpdateESJob = &job.Executor{
	ColName: "update_es",
}

func InitUpdateESJob(database *mongo.Database) {
	UpdateESJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:   true,
		ChannelCount:       1,
		OldVersionTimeoutS: 60 * 1,
		CurVersionTimeoutS: 60 * 5,
	})
}
