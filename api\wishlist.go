package api

import (
	"encoding/json"
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// CreateWishList is func to create wishlist for customer
func CreateWishlist(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Wishlist
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Unable to read data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.ProductID == 0 || input.ProductCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: productID, productCode",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}

	// check product
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		input.VendorCode = vendors[0].Code
		return resp.Respond(action.CreateWishlist(input))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// GetWishList is func get wishlist for customer
func GetWishList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		q        = req.GetParam("q")
	)

	// fill query
	query := model.Wishlist{}
	if q != "" {
		err := json.Unmarshal([]byte(q), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	if len(query.ProductIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_id": bson.M{
				"$in": query.ProductIDIn,
			},
		})
	}

	// check account
	if len(query.ProductCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"product_code": bson.M{
				"$in": query.ProductCodeIn,
			},
		})
	}

	// check account
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetWishList(&query, offset, limit, getTotal))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// DeleteWishlist is func delete wishlist
func DeleteWishlist(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Wishlist
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Unable to read data",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.ProductID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: productID",
			ErrorCode: string(enum.ErrorCodeInvalid.ProductID),
		})
	}

	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		input.VendorCode = vendors[0].Code
		return resp.Respond(action.DeleteWishlist(input))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

func DeleteWishlistVendor(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("DeleteWishlistVendor START")
	defer fmt.Println("DeleteWishlistVendor END")

	var vendorCode = req.GetParam("vendorCode")
	var areYouSure = req.GetParam("areYouSure")

	if areYouSure != "true" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "areYouSure.",
		})
	}

	if vendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: vendorCode",
		})
	}

	//check vendor
	opt := client.APIOption{
		Keys: []string{vendorCode},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  vendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	vendorResp := seller.GetSellers(opt)
	if vendorResp.Status != common.APIStatus.Ok {
		return resp.Respond(vendorResp)
	}
	vendor := vendorResp.Data.([]*model.Seller)[0]
	//delete wishlist
	deleteWishlistResp := model.WishlistDB.Delete(&model.Wishlist{
		VendorCode: vendor.Code,
	})

	if deleteWishlistResp.Status != common.APIStatus.Ok {
		return resp.Respond(deleteWishlistResp)
	}

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing...",
	})
}

func InsertWishlistVendor(req sdk.APIRequest, resp sdk.APIResponder) error {
	fmt.Println("InsertWishlistVendor START")
	defer fmt.Println("InsertWishlistVendor END")

	var input struct {
		ProductIDIn []int64 `json:"productIDIn"`
		VendorCode  string  `json:"vendorCode"`
	}
	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: input",
		})
	}

	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: vendorCode",
		})
	}

	if len(input.ProductIDIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Invalid: productIDIn",
		})
	}

	totalProduct := len(input.ProductIDIn)
	totalCreate := 0
	totalExists := 0

	// get data product
	j := 0
	for i := 0; i < len(input.ProductIDIn); i += 100 {
		j += 100
		if j > len(input.ProductIDIn) {
			j = len(input.ProductIDIn)
		}

		option := client.APIOption{
			Keys: []string{"WISHLIST_MIGRATE"},
			Params: map[string]string{
				"ids":    utils.JoinInt64Param(input.ProductIDIn[i:j]...),
				"offset": "0",
				"limit":  "100",
			},
		}
		getListProductResp := marketplace.GetProductList(option)
		if getListProductResp.Status == common.APIStatus.Ok {
			products := getListProductResp.Data.([]*model.Product)
			for _, product := range products {
				// check wishlist
				wishlist := model.Wishlist{
					ProductID:   product.ProductID,
					ProductCode: product.Code,
					VendorCode:  input.VendorCode,
				}
				wishlistResp := action.GetWishList(&wishlist, 0, 1, false)
				if wishlistResp.Status != common.APIStatus.Ok {
					// insert wishlist
					wishlistResp = action.CreateWishlist(wishlist)
					if wishlistResp.Status != common.APIStatus.Ok {
						return resp.Respond(wishlistResp)
					}
					totalCreate++
				} else {
					totalExists++
				}
			}
		}
	}

	fmt.Println("totalProduct", totalProduct)
	fmt.Println("totalCreate", totalCreate)
	fmt.Println("totalExists", totalExists)

	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Processing...",
	})
}
