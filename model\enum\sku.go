package enum

type SkuTypeValue string
type skuType struct {
	NORMAL SkuTypeValue
	COMBO  SkuTypeValue
	DEAL   SkuTypeValue
}

var SKUType = &skuType{
	"NORMAL",
	"COMBO",
	"DEAL",
}

// SkuStatusValue ..
type SkuStatusValue string

// nolint
type skuStatus struct {
	NORMAL       SkuStatusValue
	LIMIT        SkuStatusValue // gioi han
	OUT_OF_STOCK SkuStatusValue // tam het hang
	SUSPENDED    SkuStatusValue // ngung kinh doanh
	// NEAR_EXPIRATION SkuStatusValue // can date
	GIFT SkuStatusValue // qua tang
}

// SkuStatus ...
var SkuStatus = &skuStatus{
	"NORMAL",
	"LIMIT",
	"OUT_OF_STOCK",
	"SUSPENDED",
	// "NEAR_EXPIRATION",
	"GIFT",
}

/** SKU vendor type */
type (
	SKUVendorType  string
	skuVendorValue struct {
		ALL         SKUVendorType
		NON_TRADING SKUVendorType
		TRADING     SKUVendorType
	}
)

var SKUVendorValue = skuVendorValue{
	ALL:         "ALL",
	NON_TRADING: "NON-TRADING",
	TRADING:     "TRADING",
}
