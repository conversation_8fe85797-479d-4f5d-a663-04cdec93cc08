/*
Package provide main API handlers
*/
package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

// GetActionSource ...
func GetActionSource(req sdk.APIRequest) *model.Account {
	return getActionSource(req)
}

// GetActionSource ...
func getActionSource(req sdk.APIRequest) *model.Account {

	if conf.IS_DEBUG {
		return &model.Account{
			AccountID: 10451,
			Type:      "EMPLOYEE",
			Fullname:  "BHT debugger",
			Username:  "bht.debugger",
		}
	}

	var source []*model.ActionSource
	sourceAttr := req.GetAttribute("X-Source")
	if sourceAttr != nil {
		source = sourceAttr.([]*model.ActionSource)
		if source == nil || source[0] == nil || source[0].Account == nil || source[0].Account.AccountID <= 0 {
			return nil
		}
		return source[0].Account
	}
	sourceStr := req.GetHeader("X-Source")
	if sourceStr == "" {
		return nil
	}

	// parse source
	var sourceHeader *model.ActionSource
	err := json.Unmarshal([]byte(sourceStr), &sourceHeader)
	if err != nil || sourceHeader == nil || sourceHeader.Account == nil { //|| sourceHeader.Account.AccountID <= 0
		return nil
	}

	return sourceHeader.Account
}
