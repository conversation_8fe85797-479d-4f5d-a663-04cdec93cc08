package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"go.mongodb.org/mongo-driver/mongo"
)

var wisClient *client.Client

const (
	putStatusRefundBill = "PUT::/refund-bill/switch-status"

	getFirstMileTicket        = "GET::/first-mile-ticket/list"
	postCreateFirstMileTicket = "POST::/first-mile-ticket"
	switchStatusFMTicket      = "PUT::/first-mile-ticket/switch-status"

	postWrongTicket       = "POST::/wrong-ticket"
	getWrongTicket        = "GET::/wrong-ticket"
	getWrongTicketItem    = "GET::/wrong-ticket-item"
	updateWrongTicketItem = "PUT::/wrong-ticket-item"

	warmupWrongTicketShortage = "PUT::/wrong-ticket/shortage-warmup"
)

func InitWisClient(database *mongo.Database) {
	const wisV1 = "/seller/wis/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	wisClient = client.NewClient(wisV1, headers, 0)
	wisClient.WithDatabase(database)
	wisClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     putStatusRefundBill,
				Name:     "seller__PUT__status_refund_bill",
				Database: database,
			},
			{
				Path:     getFirstMileTicket,
				Name:     "seller__GET__first_mile_ticket",
				Database: database,
			},
			{
				Path:     switchStatusFMTicket,
				Name:     "seller__PUT__switch_status_first_mile_ticket",
				Database: database,
			},
			{
				Path:     getWrongTicket,
				Name:     "seller__GET__wrong_ticket",
				Database: database,
			},
			{
				Path:     getWrongTicketItem,
				Name:     "seller__GET__wrong_ticket_item",
				Database: database,
			},
			{
				Path:     updateWrongTicketItem,
				Name:     "seller__PUT__wrong_ticket_item",
				Database: database,
			},
			{
				Path:     postWrongTicket,
				Name:     "seller__POST__wrong-ticket",
				Database: database,
			},
			{
				Path:     warmupWrongTicketShortage,
				Name:     "seller__PUT__warmup_wrong_ticket_shortage",
				Database: database,
			},
		}...,
	)
}

func SwitchStatusRefundBill(opts ...client.APIOption) *common.APIResponse {
	requestOption := wisClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := wisClient.WithRequest(putStatusRefundBill, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func CreateFirstMileTicket(opts ...client.APIOption) *common.APIResponse {
	requestOption := wisClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := wisClient.WithRequest(postCreateFirstMileTicket, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func GetFirstMileTicket(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp FirstMileTicketResponse
	_, err := wisClient.WithRequest(getFirstMileTicket, requestOpts, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func SwitchStatusFirstMileTicket(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := wisClient.WithRequest(switchStatusFMTicket, requestOpts, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func GetWrongTicket(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp WrongTicketResponse
	_, err := wisClient.WithRequest(getWrongTicket, requestOpts, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetWrongTicketItem(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp WrongTicketItemResponse
	_, err := wisClient.WithRequest(getWrongTicketItem, requestOpts, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
func UpdateWrongTicketItem(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := wisClient.WithRequest(updateWrongTicketItem, requestOpts, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

/**
 * WIS response
 */
type (
	WrongTicketResponse struct {
		Data []*model.WrongTicket `json:"data"`
		common.APIResponse
	}
)

func CreateWrongTicket(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp WrongTicketResponse
	_, err := wisClient.WithRequest(postWrongTicket, requestOpts, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func WarmupWrongTicketShortage(opts ...client.APIOption) *common.APIResponse {
	requestOpts := wisClient.WithAPIOption(opts...)
	var resp WrongTicketResponse
	_, err := wisClient.WithRequest(warmupWrongTicketShortage, requestOpts, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
