package enum

type BankingPaymentOrderTypeValue string
type bankingPaymentOrderType struct {
	ADVANCE BankingPaymentOrderTypeValue
	DEBT    BankingPaymentOrderTypeValue
}

var BankingPaymentOrderType = &bankingPaymentOrderType{
	"ADVANCE",
	"DEBT",
}

type BankingPaymentOrderStatusValue string
type bankingPaymentOrderStatus struct {
	DRAFT     BankingPaymentOrderStatusValue
	SUBMITTED BankingPaymentOrderStatusValue
	CONFIRMED BankingPaymentOrderStatusValue
	APPROVED  BankingPaymentOrderStatusValue
	CANCELLED BankingPaymentOrderStatusValue
}

var BankingPaymentOrderStatus = &bankingPaymentOrderStatus{
	"DRAFT",
	"SUBMITTED",
	"CONFIRMED",
	"APPROVED",
	"CANCELLED",
}

type BankingPaymentOrderItemStatusValue string
type bankingPaymentOrderItemStatus struct {
	DRAFT      BankingPaymentOrderItemStatusValue
	PROCESSING BankingPaymentOrderItemStatusValue
	SUCCESS    BankingPaymentOrderItemStatusValue
	FAILED     BankingPaymentOrderItemStatusValue
}

var BankingPaymentOrderItemStatus = &bankingPaymentOrderItemStatus{
	"DRAFT",
	"PROCESSING",
	"SUCCESS",
	"FAILED",
}

type TransferPaymentStatusValue string
type transferPaymentStatus struct {
	SUCCESSFUL_TRANSFERRED         TransferPaymentStatusValue
	PARTIAL_SUCCESSFUL_TRANSFERRED TransferPaymentStatusValue
	FAILED_TRANSFERRED             TransferPaymentStatusValue
}

var TransferPaymentStatus = &transferPaymentStatus{
	"SUCCESSFUL_TRANSFERRED",
	"PARTIAL_SUCCESSFUL_TRANSFERRED",
	"FAILED_TRANSFERRED",
}

type TransactionStatusEnum string
type transactionStatusEnum struct {
	Success              TransactionStatusEnum
	Fail                 TransactionStatusEnum
	SignatureVerifyError TransactionStatusEnum
}

var TransactionStatusValue = &transactionStatusEnum{
	Success:              "01",
	Fail:                 "02",
	SignatureVerifyError: "03",
}
