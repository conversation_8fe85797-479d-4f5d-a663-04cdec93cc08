package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateOldAddressDefault updates the default address for a vendor.
// If the input address is marked as default, it will find the existing default address
// for the same vendor and purpose, and update it to be non-default.
//
// Parameters:
// - input: The address to be updated.
//
// Returns:
// - None
//
// Note: This function assumes that the input address has a valid VendorCode and PurposeCode.

// CreateAddress creates a new address and updates the old default address if necessary.
//
// Parameters:
// - input: The address to be created.
//
// Returns:
// - *common.APIResponse: The response from the address creation operation.
//
// Note: This function generates a new AddressID and AddressCode for the input address.

// UpdateAddress updates an existing address and updates the old default address if necessary.
//
// Parameters:
// - input: The address to be updated.
//
// Returns:
// - *common.APIResponse: The response from the address update operation.
//
// Note: This function assumes that the input address has a valid AddressCode.

// DeleteAddress deletes an address by its address code.
//
// Parameters:
// - addressCode: The code of the address to be deleted.
//
// Returns:
// - *common.APIResponse: The response from the address deletion operation.

// GetAddressByCode retrieves an address by its address code.
//
// Parameters:
// - addressCode: The code of the address to be retrieved.
//
// Returns:
// - *common.APIResponse: The response containing the address data.

// GetAddressList retrieves a list of addresses based on the query parameters.
//
// Parameters:
// - query: The query parameters for retrieving addresses.
// - offset: The offset for pagination.
// - limit: The limit for pagination.
// - getTotal: A flag indicating whether to get the total count of addresses.
//
// Returns:
// - *common.APIResponse: The response containing the list of addresses and optionally the total count.
func UpdateOldAddressDefault(input model.Address) {
	if input.IsDefaultAddress != nil && *input.IsDefaultAddress {
		// check default address exist
		query := &model.Address{
			VendorCode:       input.VendorCode,
			PurposeCode:      input.PurposeCode,
			IsDefaultAddress: utils.Pointer.WithBool(true),
		}

		if enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO {
			query.WarehouseCode = input.WarehouseCode
		}

		addressesResp := model.AddressDB.Query(query, 0, 100, &primitive.M{"_id": -1})

		if addressesResp.Status == common.APIStatus.Ok {
			addressList := addressesResp.Data.([]*model.Address)

			for _, address := range addressList {
				if address.AddressCode != input.AddressCode {
					address.IsDefaultAddress = utils.Pointer.WithBool(false)
					model.AddressDB.UpdateOne(
						model.Address{
							AddressCode: address.AddressCode,
						},
						address)
				}
			}
		}
	}
}


// CreateAddress creates a new address record in the database.
// It generates a new AddressID and AddressCode for the input address,
// then attempts to create the address in the database.
// If the creation is successful, it updates the old address default status.
//
// Parameters:
//   - input: model.Address - The address information to be created.
//
// Returns:
//   - *common.APIResponse - The response from the database creation operation,
//     including the status and any created data.
func CreateAddress(input model.Address) *common.APIResponse {
	// create
	input.AddressID, input.AddressCode = model.GetAddressID()
	createResp := model.AddressDB.Create(input)

	if createResp.Status == common.APIStatus.Ok {
		createdData := createResp.Data.([]*model.Address)[0]
		UpdateOldAddressDefault(*createdData)
	}
	return createResp
}

// UpdateAddress updates an address in the database based on the provided input.
// It first queries the database for an existing address with the same AddressCode.
// If the address is found, it updates the address with the new input data.
// If the update is successful and the status is OK, it calls UpdateOldAddressDefault to handle any additional logic.
// The function returns an APIResponse indicating the result of the query and update operations.
//
// Parameters:
//   - input: model.Address containing the new address data to be updated.
//
// Returns:
//   - *common.APIResponse: The response from the database query and update operations.
func UpdateAddress(input model.Address) *common.APIResponse {
	// query
	resp := model.AddressDB.QueryOne(&model.Address{
		AddressCode: input.AddressCode,
	})

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	updateResp := model.AddressDB.UpdateOne(
		model.Address{
			AddressCode: input.AddressCode,
		},
		input)

	if updateResp.Status == common.APIStatus.Ok {
		UpdateOldAddressDefault(input)
	}

	return updateResp
}

// Delete Address ...
// DeleteAddress deletes an address based on the provided address code.
// It first retrieves the address using the address code. If the address
// retrieval is unsuccessful, it returns the response from the retrieval
// attempt. If the address is found, it proceeds to delete the address
// from the database.
//
// Parameters:
//   - addressCode: A string representing the code of the address to be deleted.
//
// Returns:
//   - *common.APIResponse: The response indicating the result of the delete operation.
func DeleteAddress(addressCode string) *common.APIResponse {
	addressResp := GetAddressByCode(addressCode)
	if addressResp.Status != common.APIStatus.Ok {
		return addressResp
	}
	return model.AddressDB.Delete(&model.Address{ID: addressResp.Data.([]*model.Address)[0].ID})
}

// get address by code
func GetAddressByCode(addressCode string) *common.APIResponse {
	return model.AddressDB.QueryOne(&model.Address{AddressCode: addressCode})
}

// get address list
func GetAddressList(query *model.Address, offset, limit int64, getTotal bool) *common.APIResponse {

	result := model.AddressDB.Query(query, offset, limit, &primitive.M{"_id": -1})

	if getTotal {
		result.Total = model.AddressDB.Count(query).Total
	}

	return result
}
