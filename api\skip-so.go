package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetListSkipSO ...
func GetListSkipSO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")

		query = model.SkipSO{}
	)

	// parse input
	if len(qStr) > 0 {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Can not parse input data.",
			})
		}
	}

	if limit > 1000 {
		limit = 1000
	}

	// validate input
	if len(query.SOCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"so_code": bson.M{
				"$in": query.SOCodeIn,
			},
		})
	}

	// validate input
	if len(query.Search) > 0 {
		searchTerm := utils.NormalizeString(query.Search)
		query.ComplexQuery = append(query.ComplexQuery, bson.M{
			"hashtag": bson.M{"$regex": searchTerm},
		})
	}

	// if query.OrderTimeFrom != nil {
	// 	orderFrom := utils.GetFirstTimeOfDate(query.OrderTimeFrom.In(utils.TimeZoneVN))
	// 	query.ComplexQuery = append(query.ComplexQuery, bson.M{
	// 		"order_time": bson.M{
	// 			"$gte": orderFrom,
	// 		},
	// 	})
	// }
	// if query.OrderTimeTo != nil {
	// 	orderTo := utils.GetLastTimeOfDate(query.OrderTimeTo.In(utils.TimeZoneVN))
	// 	query.ComplexQuery = append(query.ComplexQuery, bson.M{
	// 		"order_time": bson.M{
	// 			"$lte": orderTo,
	// 		},
	// 	})
	// }

	return resp.Respond(action.GetSkipSO(&query, offset, limit, getTotal))
}

// func CreateSingleSkipSO(req sdk.APIRequest, resp sdk.APIResponder) error {
// 	var input = model.SkipSO{}
// 	err := req.GetContent(&input)
// 	if err != nil {
// 		return resp.Respond(&common.APIResponse{
// 			Status:    common.APIStatus.Invalid,
// 			Message:   "Can not parse input data. " + err.Error(),
// 			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
// 		})
// 	}

// 	return resp.Respond(action.CreateSkipSO(&input))
// }

// CreateListSkipSO
func CreateListSkipSO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input = []model.SkipSO{}
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),	
		})
	}

	return resp.Respond(action.CreateListSkipSO(input))
}

// DeleteSkipSO
func DeleteSkipSO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		qStr  = req.GetParam("q")
		query = model.SkipSO{}
	)

	if len(qStr) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Query is empty",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// parse input
	err := json.Unmarshal([]byte(qStr), &query)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.DeleteSkipSO(&query))
}
