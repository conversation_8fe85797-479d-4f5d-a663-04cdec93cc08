package api

import (
	"encoding/json"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

// GetBankingPaymentOrder ...
func GetBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	var query model.BankingPaymentOrder
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// Created time
	if query.CreatedTimeFromQuery != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedTimeFromQuery,
			},
		})
	}

	// Created time
	if query.CreatedTimeToQuery != nil {
		y, m, d := query.CreatedTimeToQuery.In(utils.TimeZoneVN).Date()
		endDate := time.Date(y, m, d, 23, 59, 59, 0, utils.TimeZoneVN)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": endDate,
			},
		})
	}

	// Updated time
	if len(query.POCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_codes": bson.M{
			"$in": query.POCodeIn,
		}})
	}

	// Updated time
	if len(query.VendorBillIDIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"vendor_bill_ids": bson.M{
			"$in": query.VendorBillIDIn,
		}})
	}

	// Updated time
	if len(query.VendorCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"vendor_codes": bson.M{
			"$in": query.VendorCodeIn,
		}})
	}

	// Updated time
	if len(query.BankingPaymentOrderIDs) > 0 {
		query.BankingPaymentOrderID = 0
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"banking_payment_order_id": bson.M{
			"$in": query.BankingPaymentOrderIDs,
		}})
	}

	// do action
	return resp.Respond(action.GetBankingPaymentOrder(query, offset, limit, getTotal, nil))
}

// CreateBankingPaymentOrder ...
func CreateBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BankingPaymentOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	//validate payment order
	if input.BankingPaymentOrderType == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "BankingPaymentOrderType is required.",
			ErrorCode: string(enum.ErrorCodeInvalid.BankingPaymentOrderType),
		})
	}

	// validate item
	if len(input.Items) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Items is required.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	//get setting for map BankCode to beneficiaryBankShortName
	getSettingResp := model.SettingDB.QueryOne(model.Setting{})
	if getSettingResp.Status != common.APIStatus.Ok {
		return resp.Respond(getSettingResp)
	}
	setting := getSettingResp.Data.([]*model.Setting)[0]
	if setting.SourceNumber == "" || setting.MapBankVP == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Connect with VP is not setting, please check again.",
			ErrorCode: string(enum.ErrorCodeInvalid.VPNotConfig),
		})
	}

	//validate item
	for _, item := range input.Items {
		if item.VendorCode == "" {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "VendorCode is required.",
				ErrorCode: string(enum.ErrorCodeInvalid.VendorCode),
			})
		}

		// validate POCodes or VendorBillIDs
		if item.Amount <= 0 {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Amount is required.",
				ErrorCode: string(enum.ErrorCodeInvalid.Amount),
			})
		}

		// validate POCodes or VendorBillIDs
		if _, ok := setting.MapBankVP[item.BankCode]; !ok {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Not support this bank in this time, please check again.",
				ErrorCode: string(enum.ErrorCodeInvalid.BankNotFound),
			})
		}

		item.BeneficiaryBankShortName = setting.MapBankVP[item.BankCode]
		item.SourceNumber = setting.SourceNumber
		item.TargetNumber = item.BankAccountNumber
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CreateBankingPaymentOrder(&input, acc))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// UpdateBankingPaymentOrder ...
func UpdateBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BankingPaymentOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// validate payment order
	if input.BankingPaymentOrderCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderCode is required.",
		})
	}

	// validate payment order
	checkBankingOrderResp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrder{BankingPaymentOrderCode: input.BankingPaymentOrderCode})
	if checkBankingOrderResp.Status != common.APIStatus.Ok {
		return resp.Respond(checkBankingOrderResp)
	}
	dataCheck := checkBankingOrderResp.Data.([]*model.BankingPaymentOrder)[0]
	if dataCheck.Status != enum.BankingPaymentOrderStatus.DRAFT {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Only draft status can be updated.",
		})
	}

	// validate item
	if len(input.Items) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Items is required.",
		})
	}

	//get setting for map BankCode to beneficiaryBankShortName
	getSettingResp := model.SettingDB.QueryOne(model.Setting{})
	if getSettingResp.Status != common.APIStatus.Ok {
		return resp.Respond(getSettingResp)
	}
	setting := getSettingResp.Data.([]*model.Setting)[0]
	if setting.SourceNumber == "" || setting.MapBankVP == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Error,
			Message:   "Connect with VP is not setting, please check again.",
			ErrorCode: string(enum.ErrorCodeInvalid.VPNotConfig),
		})
	}

	//validate item
	for _, item := range input.Items {
		if item.VendorCode == "" {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "VendorCode is required.",
			})
		}

		// validate POCodes or VendorBillIDs
		if len(item.POCodes) == 0 && len(item.VendorBillIDs) == 0 {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "POCodes or VendorBillIDs is required.",
			})
		}

		// validate POCodes or VendorBillIDs
		if item.Amount <= 0 {
			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Amount is required.",
			})
		}

		// validate POCodes or VendorBillIDs
		if _, ok := setting.MapBankVP[item.BankCode]; !ok {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Not support this bank in this time, please check again.",
				ErrorCode: string(enum.ErrorCodeInvalid.BankNotFound),
			})
		}

		item.BeneficiaryBankShortName = setting.MapBankVP[item.BankCode]
		item.SourceNumber = setting.SourceNumber
		item.TargetNumber = item.BankAccountNumber
	}

	// do action
	return resp.Respond(action.UpdateBankingPaymentOrder(&input))
}

// CloneBankingPaymentOrder ...
func CloneBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var code = req.GetParam("code")
	var isOnlyFailed = req.GetParam("isOnlyFailed") == "true"
	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Code is required.",
		})
	}

	// do action
	if acc := getActionSource(req); acc != nil {
		return resp.Respond(action.CloneBankingPaymentOrder(code, acc, isOnlyFailed))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// SwitchStatusBankingPaymentOrder ...
func SwitchStatusBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BankingPaymentOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// validate payment order
	if input.BankingPaymentOrderCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderCode is required.",
		})
	}

	// validate payment order
	if input.Status == enum.BankingPaymentOrderStatus.APPROVED || input.Status == enum.BankingPaymentOrderStatus.CONFIRMED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Status is invalid.",
		})
	}

	// validate payment order
	getResp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrder{BankingPaymentOrderCode: input.BankingPaymentOrderCode})
	if getResp.Status != common.APIStatus.Ok {
		return resp.Respond(getResp)
	}
	bankCheck := getResp.Data.([]*model.BankingPaymentOrder)[0]
	getItemsResp := model.BankingPaymentOrderItemDB.Query(model.BankingPaymentOrder{BankingPaymentOrderCode: bankCheck.BankingPaymentOrderCode}, 0, 1000, nil)
	if getItemsResp.Status != common.APIStatus.Ok {
		return resp.Respond(getItemsResp)
	}
	bankCheck.Items = getItemsResp.Data.([]*model.BankingPaymentOrderItem)
	//check valid bank mapping with VP when change status to Submitted
	if input.Status == enum.BankingPaymentOrderStatus.SUBMITTED {
		settingResp := model.SettingDB.QueryOne(model.Setting{})
		if settingResp.Status != common.APIStatus.Ok {
			return resp.Respond(settingResp)
		}
		setting := settingResp.Data.([]*model.Setting)[0]

		for _, item := range bankCheck.Items {
			if _, ok := setting.MapBankVP[item.BankCode]; !ok {
				return resp.Respond(&common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Bank mapping with VP is invalid, please check again.",
				})
			}

			//validate remake length only
			if len(item.Remark) > 210 {
				return resp.Respond(&common.APIResponse{
					Status:  common.APIStatus.Invalid,
					Message: "Remark length is invalid, maximum is 210 characters.",
				})
			}
		}
	}

	// validate payment order
	if bankCheck.Status == enum.BankingPaymentOrderStatus.CANCELLED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change status of cancelled banking payment order.",
		})
	}

	// validate payment order
	if bankCheck.Status == input.Status {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change to same status.",
		})
	}

	// validate payment order
	if acc := getActionSource(req); acc != nil {
		input.ApprovedBy = acc.Fullname
		input.ConfirmedBy = acc.Fullname
		headers := req.GetHeaders()
		return resp.Respond(action.SwitchStatusBankingPaymentOrder(input, headers))
	}

	// return error
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// ConfirmStatusBankingPaymentOrder
func ConfirmStatusBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var code = req.GetParam("code")
	// validate payment order
	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderCode is required.",
		})
	}

	// validate payment order
	getResp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrder{BankingPaymentOrderCode: code})
	if getResp.Status != common.APIStatus.Ok {
		return resp.Respond(getResp)
	}
	bankCheck := getResp.Data.([]*model.BankingPaymentOrder)[0]

	// validate payment order
	if bankCheck.Status == enum.BankingPaymentOrderStatus.CANCELLED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change status of cancelled banking payment order.",
		})
	}

	if bankCheck.Status == enum.BankingPaymentOrderStatus.CONFIRMED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change to same status.",
		})
	}

	if bankCheck.Status != enum.BankingPaymentOrderStatus.SUBMITTED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Only submitted status can be confirmed.",
		})
	}

	// validate payment order
	if acc := getActionSource(req); acc != nil {
		bankCheck.ApprovedBy = acc.Fullname
		bankCheck.ConfirmedBy = acc.Fullname
		bankCheck.Status = enum.BankingPaymentOrderStatus.CONFIRMED
		headers := req.GetHeaders()
		return resp.Respond(action.SwitchStatusBankingPaymentOrder(*bankCheck, headers))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// ApproveSwitchStatusBankingPaymentOrder
func ApproveSwitchStatusBankingPaymentOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var code = req.GetParam("code")
	if code == "" {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "BankingPaymentOrderCode is required.",
		})
	}

	// validate payment order
	getResp := model.BankingPaymentOrderDB.QueryOne(model.BankingPaymentOrder{BankingPaymentOrderCode: code})
	if getResp.Status != common.APIStatus.Ok {
		return resp.Respond(getResp)
	}
	bankCheck := getResp.Data.([]*model.BankingPaymentOrder)[0]

	// validate payment order
	if bankCheck.Status == enum.BankingPaymentOrderStatus.CANCELLED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change status of cancelled banking payment order.",
		})
	}

	if bankCheck.Status == enum.BankingPaymentOrderStatus.APPROVED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not change to same status.",
		})
	}

	// validate status
	if bankCheck.Status != enum.BankingPaymentOrderStatus.CONFIRMED {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Only confirmed status can be approved.",
		})
	}

	// check permission
	if acc := getActionSource(req); acc != nil {
		bankCheck.ApprovedBy = acc.Fullname
		bankCheck.ConfirmedBy = acc.Fullname
		bankCheck.Status = enum.BankingPaymentOrderStatus.APPROVED
		headers := req.GetHeaders()
		return resp.Respond(action.SwitchStatusBankingPaymentOrder(*bankCheck, headers))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// GetBankingPaymentOrder ...
func GetBankingPaymentOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	// parse query
	var query model.BankingPaymentOrderItem
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetBankingPaymentOrderItem(query, offset, limit, getTotal, nil))
}

// Sync banking-payment-order transaction item
func SyncBankingPaymentOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.BankingPaymentOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "Can not parse input data.",
		})
	}

	// validate payment order
	if acc := getActionSource(req); acc != nil {
		headers := req.GetHeaders()
		return resp.Respond(action.SyncBankingPaymentOrderItemStatus(&input, headers))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}
