package api

import (
	"encoding/json"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// CreatePIC ...
func CreateAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Address

	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.PurposeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Purpose code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ProvinceCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Province/ City code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.DistrictCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "District code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.WardCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Commune/ Ward code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Street == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Street/ Building/ Address no. is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if (enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO) && utils.IsNil(input.WarehouseCode) {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}
	// do action
	return resp.Respond(action.CreateAddress(input))
}

// UpdatePIC ...
func UpdateAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.Address

	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.AddressCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Address code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.VendorCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Vendor code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.PurposeCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Purpose code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.ProvinceCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Province/ City code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.DistrictCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "District code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.WardCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Commune/ Ward code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if input.Street == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Street/ Building/ Address no. is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if (enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_PICKUP || enum.PurposeCodeValue(input.PurposeCode) == enum.PurposeCode.BUYMED_DELIVERS_TO) && utils.IsNil(input.WarehouseCode) {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Warehouse code is required",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.UpdateAddress(input))
}

func DeleteAddress(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		addressCode = req.GetParam("addressCode")
	)

	// validate input
	if addressCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Address Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.DeleteAddress(addressCode))
}

// GetAddressByCode ...
func GetAddressByCode(req sdk.APIRequest, resp sdk.APIResponder) error {
	addressCode := req.GetParam("addressCode")

	if addressCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid Address Code.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// do action
	return resp.Respond(action.GetAddressByCode(addressCode))
}

// GetAddressList ...
func GetAddressList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal = req.GetParam("getTotal") == "true"
		qStr     = req.GetParam("q")
	)

	var query model.Address
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// do action
	return resp.Respond(action.GetAddressList(&query, offset, limit, getTotal))
}

// GetAddressMe ...
func GetAddressMe(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset   = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit    = sdk.ParseInt64(req.GetParam("limit"), 1000)
		getTotal = req.GetParam("getTotal") == "true"
	)

	var query model.Address

	// get account
	if acc := getActionSource(req); acc != nil {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)
		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetAddressList(&query, offset, limit, getTotal))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}
