package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// GetSkuItemFromSKU retrieves a SKU item based on the provided product ID, seller code, and warehouse code.
// It first constructs an API option with the given parameters and fetches a list of SKU items from the marketplace.
// If the API response status is not OK, it returns nil.
// It then retrieves the warehouse data by the provided warehouse code.
// If the warehouse data is not found, it returns nil.
// The function iterates through the list of SKU items and checks if the location codes are available.
// It then maps the locations to the warehouse delivery and assigns the first location to the SKU item's LocationWarehouse field.
// If a valid SKU item is found, it is returned; otherwise, the function returns nil.
//
// Parameters:
// - productID: The ID of the product.
// - sellerCode: The code of the seller.
// - wHCode: The code of the warehouse.
//
// Returns:
// - A pointer to the SKUItem if found, otherwise nil.
func GetSkuItemFromSKU(productID int64, sellerCode, wHCode string) *model.SKUItem {
	regionMap := getRegionMap()
	option := client.APIOption{
		Keys:  []string{fmt.Sprint(productID), sellerCode, model.CLASS_INTERNAL},
		Limit: utils.Pointer.WithInt(1000),
		Q: model.SKUItem{
			ProductID:   productID,
			SellerCode:  sellerCode,
			SellerClass: model.CLASS_INTERNAL,
		},
	}
	// Get list SKU item
	skusResp := marketplace.GetListSKUItem(option)
	if skusResp.Status != common.APIStatus.Ok {
		return nil
	}

	dataWarehouse, isOK := utils.GetWarehouseByCode(wHCode)
	if !isOK {
		return nil
	}

	// Get list SKU item
	skus := skusResp.Data.([]*model.SKUItem)
	for _, skuItem := range skus {
		if skuItem == nil || skuItem.LocationCodes == nil {
			continue
		}

		// Get location to warehouse delivery
		location := getLocationsToWarehouseDelivery(regionMap, *skuItem.LocationCodes, []*model.Warehouse{&dataWarehouse})
		if len(location) == 0 {
			continue
		}

		skuItem.LocationWarehouse = location[0]

		return skuItem
	}
	return nil
}
