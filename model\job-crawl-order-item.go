package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type WorkerCountingVariable struct {
	Version       string   `json:"-" bson:"version,omitempty"`
	SOCodesInput  []string `json:"-" bson:"-"`
	SellerCode    string   `json:"-" bson:"sellerCode,omitempty"`
	WarehouseCode string   `json:"-" bson:"warehouseCode,omitempty"`
	Setting       Setting  `json:"-" bson:"setting,omitempty"`
}

type CrawlOrderItem struct {
	SaleOrder SaleOrder `json:"-" bson:"sale_order,omitempty"`
	OrderItem OrderItem `json:"-" bson:"order_item,omitempty"`

	WorkerCountingVariable WorkerCountingVariable `json:"-" bson:"worker_counting_variable,omitempty"`
	QuantityIncoming       int64                  `json:"-" bson:"quantity_incoming,omitempty"`
	SkuConfig              SkuConfig              `json:"-" bson:"sku_config,omitempty"`
	VendorInfo             Seller                 `json:"-" bson:"vendor_info,omitempty"`
	CrawlType              string                 `json:"-" bson:"crawl_type,omitempty"` // ORDER, INITIAL_STOCK
	VendorPromotions       []*VendorPromotion     `json:"-" bson:"vendor_promotions,omitempty"`
	// QuantityFromSOCount int64 `json:"-" bson:"quantity_from_so_count,omitempty"`
}

// GenPOJob ...
var CrawlOrderItemJob = &job.Executor{
	ColName: "crawl_order_item_job",
}

func InitCrawlOrderItemJob(database *mongo.Database, consumerFn job.ExecutionFn) {
	CrawlOrderItemJob.InitWithConfig(database, database.Name(), &job.ExecutorConfiguration{
		WaitForReadyTime:   true,
		ChannelCount:       10,
		OldVersionTimeoutS: 60 * 1,
		CurVersionTimeoutS: 60 * 5,
		SortedItem:         true,
	})

	CrawlOrderItemJob.SetTopicConsumer("CRAWL_ORDER_ITEM", consumerFn)

	// DB trace
	CrawlOrderItemCheckDB.ApplyDatabase(database)
}

// CrawlOrderItemCheckDB ...
var CrawlOrderItemCheckDB = &db.Instance{
	ColName:        "crawl_order_item_job",
	TemplateObject: &bson.M{},
}
