package api

import (
	"encoding/json"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

// GetSellerPOList ...
func GetSellerPOList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset            = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit             = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal          = req.GetParam("getTotal") == "true"
		getInboundRequest = req.GetParam("getInboundRequest") == "true"
		sellerCode        = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode     = req.GetVar("purchaserCode")
		qStr              = req.GetParam("q")
		sort              = req.GetParam("sort")
	)

	var query model.PurchaseOrder

	// validate input
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// add complex query
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}

	// add complex query
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lte": query.CreatedTo,
			},
		})
	}

	// add complex query
	if query.IssuedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$gte": query.IssuedFrom,
			},
		})
	}
	// add complex query
	if query.IssuedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$lt": query.IssuedTo,
			},
		})
	}

	// add complex query
	if query.ConfirmedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$gte": query.ConfirmedFrom,
			},
		})
	}

	// add complex query
	if query.ConfirmedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$lt": query.ConfirmedTo,
			},
		})
	}

	// add complex query
	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_code": bson.M{
			"$in": query.POCodeIn,
		}})
	}

	// add complex query
	if query.HashTag != "" {
		search := utils.ParserQ(query.HashTag)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: search},
		})
		query.HashTag = ""
	}

	// add complex query
	if len(query.SKUIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"skus": query.SKUIn[0]})
	}
	// add complex query
	if len(query.OriginalPOIn) > 0 {
		query.OriginalPOs = nil
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"original_pos": bson.M{
				"$in": query.OriginalPOIn,
			},
		})
	}

	// add complex query
	if query.CheckedIn != nil {
		if *query.CheckedIn {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{"first_inbound_time": bson.M{"$ne": nil}})
		} else {
			query.ComplexQuery = append(query.ComplexQuery, &bson.M{"first_inbound_time": nil})
		}
	}

	// add complex query
	if len(query.StatusIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"status": bson.M{
			"$in": query.StatusIn,
		}})
	}

	query.SellerCode = sellerCode
	query.PurchaserCode = purchaserCode

	// do action
	return resp.Respond(action.GetSellerPOList(query, offset, limit, getTotal, getInboundRequest, sort))
}

// GetPOList
func GetPOList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset            = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit             = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal          = req.GetParam("getTotal") == "true"
		getInboundRequest = req.GetParam("getInboundRequest") == "true"
		qStr              = req.GetParam("q")
	)

	// validate input
	var query model.PurchaseOrder
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// add complex query
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}

	// add complex query
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	// add complex query
	if query.IssuedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$gte": query.IssuedFrom,
			},
		})
	}

	// add complex query
	if query.IssuedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$lt": query.IssuedTo,
			},
		})
	}

	// add complex query
	if query.ConfirmedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$gte": query.ConfirmedFrom,
			},
		})
	}

	// add complex query
	if query.ConfirmedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$lt": query.ConfirmedTo,
			},
		})
	}
	// add complex query
	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_code": bson.M{
			"$in": query.POCodeIn,
		}})
	}

	// add complex query
	if len(query.StatusIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"status": bson.M{
			"$in": query.StatusIn,
		}})
	}

	// add complex query
	if query.HashTag != "" {
		search := utils.ParserQ(query.HashTag)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: search},
		})
		query.HashTag = ""
	}

	// add complex query
	if len(query.POIDIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_id": bson.M{
			"$in": query.POIDIn,
		}})
	}

	// add complex query
	if len(query.SKUIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"skus": bson.M{
			"$in": query.SKUIn,
		}})
	}

	// add complex query
	if len(query.VendorCodeIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"vendor_code": bson.M{
			"$in": query.VendorCodeIn,
		}})
	}

	// // add complex query
	// if len(query.BillStatusIn) > 0 {
	// 	query.ComplexQuery = append(query.ComplexQuery, &bson.M{"bill_status": bson.M{
	// 		"$in": query.BillStatusIn,
	// 	}})
	// }

	// do action
	return resp.Respond(action.GetSellerPOList(query, offset, limit, getTotal, getInboundRequest, "-id"))
}

// GetVendorPOList
func GetVendorPOList(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset            = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit             = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal          = req.GetParam("getTotal") == "true"
		getInboundRequest = req.GetParam("getInboundRequest") == "true"
		qStr              = req.GetParam("q")
	)

	// validate input
	var query model.PurchaseOrder
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}

	// add complex query
	if query.CreatedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$gte": query.CreatedFrom,
			},
		})
	}

	// add complex query
	if query.CreatedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"created_time": bson.M{
				"$lt": query.CreatedTo,
			},
		})
	}

	// add complex query
	if query.IssuedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$gte": query.IssuedFrom,
			},
		})
	}

	// add complex query
	if query.IssuedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"issued_time": bson.M{
				"$lt": query.IssuedTo,
			},
		})
	}

	// add complex query
	if query.ConfirmedFrom != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$gte": query.ConfirmedFrom,
			},
		})
	}

	// add complex query
	if query.ConfirmedTo != nil {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"confirmed_time": bson.M{
				"$lt": query.ConfirmedTo,
			},
		})
	}

	// add complex query
	if len(query.POCodeIn) > 0 {
		query.POCode = ""
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"po_code": bson.M{
			"$in": query.POCodeIn,
		}})
	}

	// add complex query
	if len(query.StatusIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"status": bson.M{
			"$in": query.StatusIn,
		}})
	}

	// add complex query
	if query.HashTag != "" {
		search := utils.ParserQ(query.HashTag)
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"hash_tag": primitive.Regex{Pattern: search},
		})
		query.HashTag = ""
	}

	// add complex query
	if len(query.SKUIn) > 0 {
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{"skus": bson.M{
			"$in": query.SKUIn,
		}})
	}

	// add complex query
	if len(query.OriginalPOIn) > 0 {
		query.OriginalPOs = nil
		query.ComplexQuery = append(query.ComplexQuery, &bson.M{
			"original_pos": bson.M{
				"$in": query.OriginalPOIn,
			},
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		// vendorResp := client.SellerClient.GetSellerVendorList(&model.Seller{Username: acc.Username}, "")
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)

		// do action
		query.VendorCode = vendors[0].Code
		return resp.Respond(action.GetSellerPOList(query, offset, limit, getTotal, getInboundRequest, "-id"))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// CreatePurchaseOrder ...
func CreatePurchaseOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
	)

	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// add complex query
	if input.Status != "" && input.Status != enum.PurchaseOrderStatus.DRAFT {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid status.",
			ErrorCode: string(enum.ErrorCodeInvalid.Status),
		})
	}

	input.SellerCode = sellerCode
	input.PurchaserCode = purchaserCode

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		// do action
		return resp.Respond(action.CreatePurchaseOrder(input, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
		// ErrorCode: "ACTION_NOT_FOUND",
	})
}

// UpdatePurchaseOrder ...
func UpdatePurchaseOrder(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder
	err := req.GetContent(&input)

	// check permission
	acc := getActionSource(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Your account cannot perform this action",
			ErrorCode: string(enum.ErrorCodeInvalid.Action),
		})
	}

	isVendor := false
	var (
		sellerCode    string = ""
		purchaserCode string = ""
	)

	// check if vendor
	vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
	if vendorResp.Status == common.APIStatus.Ok {
		sellerCode = input.SellerCode
		purchaserCode = input.PurchaserCode
		vendors := vendorResp.Data.([]*model.Seller)
		if len(vendors) > 0 {
			vendor := vendors[0]
			if vendor.SellerClass == model.CLASS_VENDOR {
				isVendor = true
			}
		}
	}

	// check if not vendor
	if !isVendor {
		sellerCode = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")

		input.SellerCode = sellerCode
		input.PurchaserCode = purchaserCode
	}

	// validate input
	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	// validate input
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: poCode",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		})
	}

	return resp.Respond(action.UpdatePurchaseOrder(input, isVendor))
}

// UpdatePurchaseOrderInfo ...
func UpdatePurchaseOrderInfo(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		sellerCode    = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")
	)

	// validate input
	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	// parse input
	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: poCode",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		})
	}
	input.SellerCode = strings.ToUpper(sellerCode)
	input.PurchaserCode = purchaserCode

	// do action
	return resp.Respond(action.UpdatePurchaseOrderInfo(input))
}

// SwitchStatusPO ...
func SwitchStatusPO(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid POID / POCode",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {

		switch input.Status {
		case enum.PurchaseOrderStatus.CONFIRMED:
			// return resp.Respond(action.ConfirmSellerPO(input.POCode))
			return resp.Respond(action.SentSellerPO(input.POCode, acc))

		// case enum.PurchaseOrderStatus.SENT_PO:
		// 	return resp.Respond(action.SentSellerPO(input.POCode))
		// case enum.PurchaseOrderStatus.COMPLETED:
		// 	return resp.Respond(action.CompleteSellerPO(input.POCode))

		// case enum.PurchaseOrderStatus.PROCESSING:
		case enum.PurchaseOrderStatus.HANDOVER_COMPLETED, enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED:
			return resp.Respond(action.ProcessingSellerPO(input.POCode, input.Status))
		case enum.PurchaseOrderStatus.CANCELED:
			return resp.Respond(action.CancelSellerPO(input.POCode))

		case "LOCK":
			return resp.Respond(action.LockPO(input.POCode, input.CreateShortageTicket))
		case "UNLOCK":
			return resp.Respond(action.UnLockPO(input.POCode, input.MustInbound))

		default:
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Invalid status",
				ErrorCode: string(enum.ErrorCodeInvalid.Status),
			})
		}
	}

	// return unauthorized
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// VendorUpdatePO ...
func VendorUpdatePO(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.PurchaseOrder
	err := req.GetContent(&input)

	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {
		vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
		if vendorResp.Status != common.APIStatus.Ok {
			return resp.Respond(vendorResp)
		}
		vendors := vendorResp.Data.([]*model.Seller)

		// do action
		return resp.Respond(action.VendorUpdatePO(input, vendors[0]))
	}

	// return unauthorized
	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// NotifyVendorPO ...
func NotifyVendorPO(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.NotifyVendorPO(input.POCode))
}

// ClonePurchaserOrder
func ClonePurchaserOrder(req sdk.APIRequest, resp sdk.APIResponder) error {
	var poCode = req.GetParam("poCode")

	if poCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid poCode",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		})
	}

	// check permission and do action
	if acc := getActionSource(req); acc != nil {

		return resp.Respond(action.ClonePurchaserOrder(poCode, acc))
	}

	return resp.Respond(&common.APIResponse{
		Status:    common.APIStatus.Unauthorized,
		Message:   "Your account cannot perform this action",
		ErrorCode: string(enum.ErrorCodeInvalid.Action),
	})
}

// DeletePromotionPO
func DeletePromotionPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var poCode = req.GetParam("poCode")

	if poCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid POCode",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		})
	}

	return resp.Respond(action.DeletePromotionPO(poCode))
}

// ResetActualPO
func ResetActualPO(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.PurchaseOrder

	// parse input
	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if len(input.POCodeIn) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "poCodeIn is required",
		})
	}

	// do action
	return resp.Respond(action.ResetActualPO(input.POCodeIn))
}

// UpdatePOTag
func UpdatePOTag(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder

	if err := req.GetContent(&input); err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.UpdatePOTag(&input))
}

// ToolUpdatePurchaserOrderItem
func ToolUpdatePurchaserOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {

	// parse input
	var input model.PurchaseOrderItem
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// do action
	return resp.Respond(action.ToolUpdatePurchaserOrderItem(&input))
}

// VendorUpdatePurchaseOrder ...
func VendorUpdatePurchaseOrder(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder
	err := req.GetContent(&input)

	// check permission
	acc := getActionSource(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Your account cannot perform this action",
			ErrorCode: string(enum.ErrorCodeInvalid.Action),
		})
	}

	isVendor := false
	var (
		sellerCode    string = ""
		purchaserCode string = ""
	)

	// check if vendor
	vendorResp := seller.GetVendorMe(client.APIOption{Headers: req.GetHeaders()})
	if vendorResp.Status == common.APIStatus.Ok {
		sellerCode = input.SellerCode
		purchaserCode = input.PurchaserCode
		vendors := vendorResp.Data.([]*model.Seller)
		if len(vendors) > 0 {
			vendor := vendors[0]
			if vendor.SellerClass == model.CLASS_VENDOR {
				isVendor = true
			}
		}
	}

	// check if not vendor
	if !isVendor {
		sellerCode = strings.ToUpper(req.GetVar("sellerCode"))
		purchaserCode = req.GetVar("purchaserCode")

		input.SellerCode = sellerCode
		input.PurchaserCode = purchaserCode
	}

	// validate input
	if response, ok := utils.ValidSellerPurchaser(sellerCode, purchaserCode); !ok {
		return resp.Respond(response)
	}

	// validate input
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	if input.POCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid: poCode",
			ErrorCode: string(enum.ErrorCodeInvalid.POCode),
		})
	}

	// do action
	return resp.Respond(action.VendorUpdatePurchaseOrder(input, isVendor))
}

// AppendPurchaseOrderItem ...
func AppendPurchaseOrderItem(req sdk.APIRequest, resp sdk.APIResponder) error {

	var input model.PurchaseOrder
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// check permission
	acc := getActionSource(req)
	if acc == nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Unauthorized,
			Message:   "Your account cannot perform this action",
			ErrorCode: string(enum.ErrorCodeInvalid.Action),
		})
	}

	// validat
	return resp.Respond(action.AppendPurchaseOrderItems(input))
}
