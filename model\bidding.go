package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Bidding struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	// SellerCode    string `json:"sellerCode,omitempty" bson:"seller_code,omitempty"`
	VendorCode     string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`
	VendorPriority *int64 `json:"vendorPriority,omitempty" bson:"vendor_priority,omitempty"`

	PurchaserCode         string `json:"purchaserCode,omitempty" bson:"purchaser_code,omitempty"` // for UI query
	WarehouseCode         string `json:"warehouseCode,omitempty" bson:"warehouse_code,omitempty"`
	DeliveryWarehouseCode string `json:"deliveryWarehouseCode,omitempty" bson:"delivery_warehouse_code,omitempty"`

	UnitPrice         float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"` // without vat
	VAT               *int64  `json:"vat,omitempty" bson:"vat,omitempty"`
	UnitPriceAfterVAT float64 `json:"unitPriceAfterVat,omitempty" bson:"unit_price_after_vat,omitempty"`
	// Unit       string `json:"unit,omitempty" bson:"unit,omitempty"`
	// MinPerStep int64  `json:"minPerStep,omitempty" bson:"min_per_step,omitempty"`

	// FE internal
	QuantityBidding *int64    `json:"quantityBidding,omitempty" bson:"quantity_bidding,omitempty"`
	QuantityExpect  int64     `json:"quantityExpect,omitempty" bson:"-"`
	SelectedBys     *[]string `json:"selectedBys,omitempty" bson:"selected_bys,omitempty"`
	// IsSelected      *bool  `json:"isSelected,omitempty" bson:"is_selected,omitempty"`

	ComplexQuery    []*bson.M `json:"-" bson:"$and,omitempty"`
	ProductIDIn     []int64   `json:"productIDIn,omitempty" bson:"-"`
	SelectedBy      string    `json:"selectedBy,omitempty" bson:"-"`
	VendorPrioritys []int64   `json:"vendorPrioritys,omitempty" bson:"-"`
	WarehouseCodes  []string  `json:"warehouseCodes,omitempty" bson:"-"`
	PurchaserCodeIn []string  `json:"purchaserCodeIn,omitempty" bson:"-"`
}

// BiddingDB ...
var BiddingDB = &db.Instance{
	ColName:        "bidding",
	TemplateObject: &Bidding{},
}

func InitBiddingModel(s *mongo.Database) {
	BiddingDB.ApplyDatabase(s)

	// t := true
	// _ = BiddingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = BiddingDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "product_id", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
