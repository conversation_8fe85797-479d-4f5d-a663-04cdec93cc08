/*
Package provide global configuration for application
*/

package conf

import (
	"fmt"
	"os"
	"time"

	"gitlab.buymed.tech/sdk/golang/configuration"
)

var CURRENCY_ROUND float64 = 1 // VN

type OutboundAPICredential struct {
	Url  string
	Auth string
}

type config struct {
	Env      string
	Protocol string
	Version  string

	MainDBConf configuration.Database
	LogDBConf  configuration.Database
	JobDBConf  configuration.Database

	// MainDBName  string
	// MainAuthDB  string
	// LogDBName   string
	// LogAuthDB   string
	// JobDBName   string
	// JobAuthDB   string
	// CacheDBName string

	APIHost         string
	APIKey          string
	SSOHost         string
	SSOToken        string
	BillingHost     string
	BillingToken    string
	BillingSSOHost  string
	BillingSSOToken string

	OrgID        int64
	BranchCode   string
	CurrencyCode string
	CompanyCode  string

	SFTPIntegration

	SearchProductModel string
	VendorAppID        int64

	UATSuffix string

	// SSOServiceConfig *OutboundAPICredential

	WorkflowTransferVendor string
}

var IS_DEBUG = false

type LegalCompanyInfo struct {
	Code            string    `json:"code"`
	CountryCode     string    `json:"countryCode"`
	LastUpdatedTime time.Time `json:"lastUpdatedTime"`
	Name            string    `json:"name"`
	OrgID           int       `json:"orgID"`
	TaxCode         string    `json:"taxCode"`
}
type ERPConfig struct {
	Host   string
	APIKey string
}

type SFTPConfig struct {
	Host     string
	Port     int64
	Username string
	Password string
	Dir      string
}

type SFTPIntegration struct {
	Zuellig SFTPConfig
}

// Config main config object
var Config *config

var SearchVersion = ""

func init() {
	env := os.Getenv("env")
	protocol := os.Getenv("protocol")
	version := os.Getenv("version")

	IS_DEBUG = os.Getenv("debug-local") == "true"

	switch env {

	// config for staging
	case "dev":
		Config = &config{
			Env:      "dev",
			Protocol: protocol,
			Version:  version,

			// MainDBName:  "seller_dev_purchasing",
			// MainAuthDB:  "admin",
			// LogDBName:   "seller_dev_purchasing_log",
			// LogAuthDB:   "admin",
			// JobDBName:   "seller_dev_purchasing_job",
			// JobAuthDB:   "admin",
			// CacheDBName: "seller_dev_purchasing",

			// APIHost: "https://api.v2-dev.thuocsi.vn",
			// APIKey:  "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",

			SearchProductModel: "dev.seller.purchasing.quotation",

			SFTPIntegration: SFTPIntegration{
				Zuellig: SFTPConfig{
					Host:     "editest.zuelligpharma.com",
					Port:     22,
					Username: "tstviepha002",
					Password: "@cc3s$vie",
					Dir:      "home",
				}},

			// SSOServiceConfig: &OutboundAPICredential{
			// 	Url:  "http://proxy-service.buymed-com-dev",
			// 	Auth: "Basic c2VsbGVyX2NvcmVfc3lzdGVtOmV5SjBlWEJsSWpvaVlXTmpaWE56WDNSdmEyVnVJaXdpYVhOeklqb2lRblY1YldWa0xVRlFJaXdpZEc5clpXNGlPaUpyTW1Sc2JUaEVVemREUVdrME4yNXlOMGxyZEZkdFpUYzFOekl5YkdWT1ozSkVVRFIwY2paT1RIZHJVMUpSY25ZaUxDSmpiR2xsYm5RaU9pSTNWRFozYUdsNk9YVnNkRmxsWjBkTVlXdE5TVWszTldKNmNUSkhkek5UWmpWMlJFUTRPV2RzT0dOSk5UYzFaSEVpZlFvPQ==",
			// },
			VendorAppID: 1002,

			WorkflowTransferVendor: "WF1201",
		}

	case "stg":
		Config = &config{
			Env:      "stg",
			Protocol: protocol,
			Version:  version,

			UATSuffix: "",

			// MainDBName:  "seller_stg_purchasing",
			// MainAuthDB:  "admin",
			// LogDBName:   "seller_stg_purchasing_log",
			// LogAuthDB:   "admin",
			// JobDBName:   "seller_stg_purchasing_job",
			// JobAuthDB:   "admin",
			// CacheDBName: "seller_stg_purchasing",

			// APIHost: "https://api.v2-stg.thuocsi.vn",
			// APIKey:  "Basic UEFSVE5FUi92Mi5jdXN0b21lci5jdXN0b21lcjpWNHRqTDI5UVQ0",

			// BillingHost:  "api-gateway-service.platform-billing-stg",
			// BillingToken: "YmlsbGluZ19hY2NvdW50X3N5c3RlbTpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKUk0weE5lR3BFUjNGMmQycHhjbVJzZDFwRU5EVnBPRmhGZEc1Uk4zQjRhWFZDTkhsM2VIZGtWVmx0VVdOTk9HNGlMQ0pqYkdsbGJuUWlPaUkxU0ZCdFlUbERUVXRoVWtOUVYzRjBPWGhJZGpGb2JEVXpOM1pJYzNoUlpEUTVOVFZZV2pSVk9XcHJjVU5KUlRjaWZRPT0=",

			// SSOHost:  "http://proxy-service.buymed-com-stg",
			// SSOToken: "aWFtX2NvcmVfc3lzdGVtOmV5SjBlWEJsSWpvaVlXTmpaWE56WDNSdmEyVnVJaXdpYVhOeklqb2lRblY1YldWa0xVRlFJaXdpZEc5clpXNGlPaUpNVUVzeE1VYzVjRXBhV1dWSk9UVm9jV0ZOUjJKVFMxUk9aV3BEZDNsNFNFTldhRWxaTWpaSmVVVjVaR0ZPZFhZaUxDSmpiR2xsYm5RaU9pSTNWRFozYUdsNk9YVnNkRmxsWjBkTVlXdE5TVWszTldKNmNUSkhkek5UWmpWMlJFUTRPV2RzT0dOSk5UYzFaSEVpZlFvPQ==",

			SearchProductModel: "stg.seller.purchasing.quotation",

			SFTPIntegration: SFTPIntegration{
				Zuellig: SFTPConfig{
					Host:     "editest.zuelligpharma.com",
					Port:     22,
					Username: "tstviepha002",
					Password: "@cc3s$vie",
					Dir:      "home",
				}},

			// SSOServiceConfig: &OutboundAPICredential{
			// 	Url:  "http://proxy-service.buymed-com-stg",
			// 	Auth: "Basic c2VsbGVyX2NvcmVfc3lzdGVtOmV5SjBlWEJsSWpvaVlXTmpaWE56WDNSdmEyVnVJaXdpYVhOeklqb2lRblY1YldWa0xVRlFJaXdpZEc5clpXNGlPaUpyTW1Sc2JUaEVVemREUVdrME4yNXlOMGxyZEZkdFpUYzFOekl5YkdWT1ozSkVVRFIwY2paT1RIZHJVMUpSY25ZaUxDSmpiR2xsYm5RaU9pSTNWRFozYUdsNk9YVnNkRmxsWjBkTVlXdE5TVWszTldKNmNUSkhkek5UWmpWMlJFUTRPV2RzT0dOSk5UYzFaSEVpZlFvPQ==",
			// },
			VendorAppID: 1002,

			WorkflowTransferVendor: "WF1201",
		}

	case "uat":
		Config = &config{
			Env:      "uat",
			Protocol: protocol,
			Version:  version,

			// UATSuffix: "_v0",
			UATSuffix: "",

			// MainDBName:  "seller_prd_purchasing",
			// MainAuthDB:  "seller_prd_purchasing",
			// LogDBName:   "seller_uat_purchasing_log",
			// LogAuthDB:   "admin",
			// JobDBName:   "seller_uat_purchasing_job",
			// JobAuthDB:   "admin",
			// CacheDBName: "seller_uat_purchasing",

			// APIHost: "https://api.v2-uat.thuocsi.vn",
			// APIKey:  "Basic UEFSVE5FUi92Mi5wdXJjaGFzaW5nOjJEa3hsd1BzaXJWdXdwOTIwV3JnZw==",

			// BillingHost:  "api-gateway-service.platform-billing-uat",
			// BillingToken: "dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKbU5HcDFOa1ZMY1UxRmFHMDJVbFpGYzFadVMwNU5TMjFLV0c1U2ExbDZSWEl6T0RkUk5HSndSVGhhTkRWNE9YUWlMQ0pqYkdsbGJuUWlPaUkxU0ZCdFlUbERUVXRoVWtOUVYzRjBPWGhJZGpGb2JEVXpOM1pJYzNoUlpEUTVOVFZZV2pSVk9XcHJjVU5KUlRjaWZRbz0=",

			// SSOHost:  "http://proxy-service.buymed-com-uat",
			// SSOToken: "dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKVU9XbzFNVE51TVhNeWNIaEVkMGx4ZG1oQmJuZFJPVGN6UTNFMFlXTktlR0pJTWtnemJtRmxNblZoWm1aRWVXd2lMQ0pqYkdsbGJuUWlPaUkzVkRaM2FHbDZPWFZzZEZsbFowZE1ZV3ROU1VrM05XSjZjVEpIZHpOVFpqVjJSRVE0T1dkc09HTkpOVGMxWkhFaWZRbz0=",

			SearchProductModel: "prd.seller.purchasing.quotation",

			// This is old information
			// SFTPIntegration: SFTPIntegration{
			// 	Zuellig: SFTPConfig{
			// 		Host:     "ediprod.zuelligpharma.com",
			// 		Port:     22,
			// 		Username: "prviepha001",
			// 		Password: "@cc3st1vie",
			// 		Dir:      "E2E/IN",
			// 	}},

			SFTPIntegration: SFTPIntegration{
				Zuellig: SFTPConfig{
					Host:     "zpextsftp.zuelligpharma.com",
					Port:     22,
					Username: "prviephasys003",
					Password: "v3eph@zpvn3ss",
					Dir:      "E2E/IN",
				}},

			// SSOServiceConfig: &OutboundAPICredential{
			// 	Url:  "http://proxy-service.buymed-com-uat",
			// 	Auth: "Basic dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKVU9XbzFNVE51TVhNeWNIaEVkMGx4ZG1oQmJuZFJPVGN6UTNFMFlXTktlR0pJTWtnemJtRmxNblZoWm1aRWVXd2lMQ0pqYkdsbGJuUWlPaUkzVkRaM2FHbDZPWFZzZEZsbFowZE1ZV3ROU1VrM05XSjZjVEpIZHpOVFpqVjJSRVE0T1dkc09HTkpOVGMxWkhFaWZRbz0=",
			// },
			VendorAppID: 1002,

			WorkflowTransferVendor: "WF1105",
		}

	case "prd":
		Config = &config{
			Env:      "prd",
			Protocol: protocol,
			Version:  version,

			UATSuffix: "",

			// MainDBName:  "seller_prd_purchasing",
			// MainAuthDB:  "seller_prd_purchasing",
			// LogDBName:   "seller_prd_purchasing_log",
			// LogAuthDB:   "admin",
			// JobDBName:   "seller_prd_purchasing_job",
			// JobAuthDB:   "admin",
			// CacheDBName: "seller_prd_purchasing",

			// APIHost: "http://proxy-service.frontend-prd",
			// APIKey:  "Basic UEFSVE5FUi92Mi5wdXJjaGFzaW5nOjJEa3hsd1BzaXJWdXdwOTIwV3JnZw==",

			// BillingHost:  "api-gateway-service.platform-billing-prd",
			// BillingToken: "dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKbU5HcDFOa1ZMY1UxRmFHMDJVbFpGYzFadVMwNU5TMjFLV0c1U2ExbDZSWEl6T0RkUk5HSndSVGhhTkRWNE9YUWlMQ0pqYkdsbGJuUWlPaUkxU0ZCdFlUbERUVXRoVWtOUVYzRjBPWGhJZGpGb2JEVXpOM1pJYzNoUlpEUTVOVFZZV2pSVk9XcHJjVU5KUlRjaWZRbz0=",

			// SSOHost:  "http://proxy-service.buymed-com-prd",
			// SSOToken: "dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKVU9XbzFNVE51TVhNeWNIaEVkMGx4ZG1oQmJuZFJPVGN6UTNFMFlXTktlR0pJTWtnemJtRmxNblZoWm1aRWVXd2lMQ0pqYkdsbGJuUWlPaUkzVkRaM2FHbDZPWFZzZEZsbFowZE1ZV3ROU1VrM05XSjZjVEpIZHpOVFpqVjJSRVE0T1dkc09HTkpOVGMxWkhFaWZRbz0=",

			SearchProductModel: "prd.seller.purchasing.quotation",

			// SFTPIntegration: SFTPIntegration{
			// 	Zuellig: SFTPConfig{
			// 		Host:     "ediprod.zuelligpharma.com",
			// 		Port:     22,
			// 		Username: "prviepha001",
			// 		Password: "@cc3st1vie",
			// 		Dir:      "E2E/IN",
			// 	}},

			SFTPIntegration: SFTPIntegration{
				Zuellig: SFTPConfig{
					Host:     "zpextsftp.zuelligpharma.com",
					Port:     22,
					Username: "prviephasys003",
					Password: "v3eph@zpvn3ss",
					Dir:      "E2E/IN",
				}},

			// SSOServiceConfig: &OutboundAPICredential{
			// 	Url:  "http://proxy-service.buymed-com-prd",
			// 	Auth: "Basic dGh1b2NzaS52bi5zZWxsZXIucHVyY2hhc2luZzpleUowZVhCbElqb2lZV05qWlhOelgzUnZhMlZ1SWl3aWFYTnpJam9pUW5WNWJXVmtMVUZRSWl3aWRHOXJaVzRpT2lKVU9XbzFNVE51TVhNeWNIaEVkMGx4ZG1oQmJuZFJPVGN6UTNFMFlXTktlR0pJTWtnemJtRmxNblZoWm1aRWVXd2lMQ0pqYkdsbGJuUWlPaUkzVkRaM2FHbDZPWFZzZEZsbFowZE1ZV3ROU1VrM05XSjZjVEpIZHpOVFpqVjJSRVE0T1dkc09HTkpOVGMxWkhFaWZRbz0=",
			// },

			VendorAppID: 1002,

			WorkflowTransferVendor: "WF1105",
		}
	}

	// DB config
	{
		dbFormat := "seller_%s_purchasing"
		dbJobFormat := "seller_%s_purchasing_job"
		dbLogFormat := "seller_%s_purchasing_log"

		Config.MainDBConf = fineDBConfig(configuration.Get("db").ToDatabaseConfig(), dbFormat, env)
		Config.JobDBConf = fineLogQueueDBConfig(configuration.Get("jobDB").ToDatabaseConfig(), dbJobFormat, env)
		Config.LogDBConf = fineLogQueueDBConfig(configuration.Get("logDB").ToDatabaseConfig(), dbLogFormat, env)

	}

	//  Service
	{
		buymedVNClient := configuration.Get("buymed-vn-client").ToServiceConfig()
		Config.APIHost = buymedVNClient.Host
		Config.APIKey = buymedVNClient.Authorization

		ssoClient := configuration.Get("sso-client").ToServiceConfig()
		Config.SSOHost = ssoClient.Host
		Config.SSOToken = ssoClient.Authorization

		billingClient := configuration.Get("billing-client").ToServiceConfig()
		Config.BillingHost = billingClient.Host
		Config.BillingToken = billingClient.Authorization

		billingSSOClient := configuration.Get("billing-sso-client").ToServiceConfig()
		Config.BillingSSOHost = billingSSOClient.Host
		Config.BillingSSOToken = billingSSOClient.Authorization
	}

	{ // other
		Config.OrgID = 2
		Config.BranchCode = "VN"
		Config.CurrencyCode = "VND"
		Config.CompanyCode = "MEDX_HCM"
	}
}

func fineDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	// UAT và PRD dùng chung db main và cache của prd
	if env == "uat" {
		env = "prd"
	}
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}

func fineLogQueueDBConfig(_config configuration.Database, dbNameFormat, env string) configuration.Database {

	// UAT và PRD dùng db log và db job riêng
	_config.DatabaseName = fmt.Sprintf(dbNameFormat, env)

	if _config.Auth == "" {
		_config.Auth = _config.DatabaseName
	}
	return _config
}
