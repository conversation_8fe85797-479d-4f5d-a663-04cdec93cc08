// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_source_context_proto = "google/protobuf/source_context.proto"

// Names for google.protobuf.SourceContext.
const (
	SourceContext_message_name     protoreflect.Name     = "SourceContext"
	SourceContext_message_fullname protoreflect.FullName = "google.protobuf.SourceContext"
)

// Field names for google.protobuf.SourceContext.
const (
	SourceContext_FileName_field_name protoreflect.Name = "file_name"

	SourceContext_FileName_field_fullname protoreflect.FullName = "google.protobuf.SourceContext.file_name"
)

// Field numbers for google.protobuf.SourceContext.
const (
	SourceContext_FileName_field_number protoreflect.FieldNumber = 1
)
