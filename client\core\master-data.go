package core

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	regionListPath = "GET::/region/list"
)

var (
	masterData *client.Client
)

func InitMasterDataClient(database *mongo.Database) {
	const masterDataCoreV1 = "/core/master-data/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	masterData = client.NewClient(masterDataCoreV1, headers, 0)
	masterData.WithDatabase(database)
	masterData.WithConfiguration(
		[]client.Configuration{
			{
				Path:     regionListPath,
				Name:     "masterdata_core__GET__region_list",
				Database: database,
			},
		}...,
	)
}

// GetRegionList ...
func GetRegionList(opts ...client.APIOption) *common.APIResponse {
	requestOption := masterData.WithAPIOption(opts...)
	var resp MasterdataResponse
	_, err := masterData.WithRequest(regionListPath, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
