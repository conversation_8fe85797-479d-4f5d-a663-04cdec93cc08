package tools

import (
	"fmt"
	"sort"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

type RequestVAT struct {
	POCode    string `json:"poCode"`
	ProductID int64  `json:"productID"`
}

type RespondVAT struct {
	VAT                  *int64          `json:"vat"`
	ProductID            int64           `json:"productID"`
	POCode               string          `json:"poCode"`
	VendorCode           string          `json:"vendorCode"`
	VendorType           enum.SellerType `json:"vendorType"`
	IsIngredientSpecialq *bool           `json:"isIngredientSpecial"`
	Source               string          `json:"source"`
}

func GetMasterDataProductVAT(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input RequestVAT
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	if input.POCode == "" || input.ProductID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "POCode and ProductID is required",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	// get data PO
	poResp := model.PurchaseOrderDB.QueryOne(model.PurchaseOrder{POCode: input.POCode})
	if poResp.Status != common.APIStatus.Ok {
		return resp.Respond(poResp)
	}
	po := poResp.Data.([]*model.PurchaseOrder)[0]

	//getPOItems and check productID exists in PO
	getPOItemsResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: po.POCode}, 0, 1000, nil)
	if getPOItemsResp.Status != common.APIStatus.Ok {
		return resp.Respond(getPOItemsResp)
	}
	items := getPOItemsResp.Data.([]*model.PurchaseOrderItem)
	selectedPOItems := []*model.PurchaseOrderItem{}

	for i := range items {
		item := items[i]
		if item.ProductID == input.ProductID {
			selectedPOItems = append(selectedPOItems, item)
		}
	}
	if len(selectedPOItems) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ProductID not found in PO",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// get data vendor
	opt := client.APIOption{
		Keys: []string{"GET_VAT_MASTER", input.POCode, fmt.Sprint(input.ProductID)},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  po.VendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	getVendorResp := seller.GetSellers(opt)
	if getVendorResp.Status != common.APIStatus.Ok {
		return resp.Respond(getVendorResp)
	}
	vendor := getVendorResp.Data.([]*model.Seller)[0]

	vatData := RespondVAT{
		ProductID:  input.ProductID,
		POCode:     input.POCode,
		VendorCode: po.VendorCode,
		VendorType: vendor.SellerType,
	}
	if vendor.SellerType == enum.SellerTypeValue.NON_TRADING {
		vatData.VAT = utils.Pointer.WithInt64(-1)
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Ok,
			Message: "Query success",
			Data:    []RespondVAT{vatData},
		})
	}

	{
		// vb first
		vbResp := model.VendorBillDB.Query(model.VendorBill{
			POCode: input.POCode,
			ComplexQuery: []*bson.M{
				{"status": bson.M{"$in": []enum.VendorBillStatusValue{
					enum.VendorBillStatus.APPROVED,
					enum.VendorBillStatus.PAID,
					enum.VendorBillStatus.WAIT_TO_PAID,
				}}},
			},
		}, 0, 1000, nil)
		listVbItemsOfCurrnentProduct := []*model.VendorBillItem{}
		if vbResp.Status == common.APIStatus.Ok {
			vbs := vbResp.Data.([]*model.VendorBill)
			for _, vb := range vbs {
				vbItemsResp := model.VendorBillItemDB.Query(model.VendorBillItem{VendorBillID: vb.VendorBillID}, 0, 1000, nil)
				if vbItemsResp.Status != common.APIStatus.Ok {
					continue
				}
				vbItems := vbItemsResp.Data.([]*model.VendorBillItem)
				for j := range vbItems {
					vbItem := vbItems[j]
					if vbItem.ProductID == input.ProductID {
						listVbItemsOfCurrnentProduct = append(listVbItemsOfCurrnentProduct, vbItem)
						break
					}
				}
			}
			sort.Slice(listVbItemsOfCurrnentProduct, func(i, j int) bool {
				iItem := listVbItemsOfCurrnentProduct[i]
				jItem := listVbItemsOfCurrnentProduct[j]
				if iItem.TotalPrice == nil {
					iItem.TotalPrice = new(float64)
				}
				if jItem.TotalPrice == nil {
					jItem.TotalPrice = new(float64)
				}

				return *iItem.TotalPrice > *jItem.TotalPrice
			})

			if len(listVbItemsOfCurrnentProduct) > 0 {
				if listVbItemsOfCurrnentProduct[0].Vat != nil {
					vatData.VAT = utils.Pointer.WithInt64(int64(*listVbItemsOfCurrnentProduct[0].Vat))
					vatData.Source = fmt.Sprintf("VBItem:%v", listVbItemsOfCurrnentProduct[0].VendorBillItemCode)
				}

				return resp.Respond(&common.APIResponse{
					Status:  common.APIStatus.Ok,
					Message: "Query success",
					Data:    []RespondVAT{vatData},
				})
			}
		}
	}

	{
		// po second
		if len(selectedPOItems) > 0 {
			sort.Slice(selectedPOItems, func(i, j int) bool {
				iItem := selectedPOItems[i]
				jItem := selectedPOItems[j]
				if iItem.TotalPrice == nil {
					iItem.TotalPrice = new(float64)
				}
				if jItem.TotalPrice == nil {
					jItem.TotalPrice = new(float64)
				}

				return *iItem.TotalPrice > *jItem.TotalPrice
			})

			if selectedPOItems[0].VAT != nil {
				vatData.VAT = utils.Pointer.WithInt64(int64(*selectedPOItems[0].VAT))
				vatData.Source = fmt.Sprintf("POItem:%v", selectedPOItems[0].POItemID)
			}

			return resp.Respond(&common.APIResponse{
				Status:  common.APIStatus.Ok,
				Message: "Query success",
				Data:    []RespondVAT{vatData},
			})
		}
	}

	// product market place third
	productResp := marketplace.GetSingleProduct(
		client.APIOption{
			Q: model.Product{ProductID: input.ProductID},
		},
	)
	if productResp.Status != common.APIStatus.Ok {
		return resp.Respond(productResp)
	}
	product := productResp.Data.([]*model.Product)[0]
	vatData.VAT = product.Vat
	vatData.Source = fmt.Sprintf("ProductMKP:%v", product.ProductID)
	vatData.IsIngredientSpecialq = product.IsIngredientSpecial
	return resp.Respond(&common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Query success",
		Data:    []RespondVAT{vatData},
	})
}
