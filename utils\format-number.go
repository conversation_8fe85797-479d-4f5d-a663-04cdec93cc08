package utils

/*
Slightly adapted from the source to fit go-humanize.
Author: https://github.com/gorhill
Source: https://gist.github.com/gorhill/5285193
*/

import (
	"math"
	"strings"
)

// FormatVNDCurrency formats a string representing a number into VND currency format
func FormatVNDCurrency(str string) string {
	// Break the string into groups of three digits
	groups := breakIntoGroupOfThree(str)

	// Join the groups with a dot separator to form the final formatted string
	return strings.Join(groups, ".")
}

// breakIntoGroupOfThree splits a string into groups of three characters
func breakIntoGroupOfThree(str string) []string {
	var groups []string
	var remainder = len(str) % 3

	// Determine the length of the first group if the string length is greater than or equal to 3
	if len(str) >= 3 {
		var lengthOfGroup = math.Ceil(float64(len(str) / 3))
		// var lengthOfGroup = len(str) / 3

		// Adjust the remainder if the calculated length of groups is greater than or equal to 3
		if lengthOfGroup >= 3 {
			remainder = len(str) - int(lengthOfGroup*3)
		}
	}

	// Append the initial group if there is a remainder
	//Cắt lấy những phần tử không đủ 3 số
	if remainder != 0 {
		groups = append(groups, str[:remainder])
		str = str[remainder:]
	}

	// Continue splitting the remaining string into groups of three
	//Lấy những group còn lại

	for {
		if len(str) < 3 {
			break
		}
		groups = append(groups, str[:3])
		str = str[3:]
	}

	// Return the list of groups
	return groups
}
