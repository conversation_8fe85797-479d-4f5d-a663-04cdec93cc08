
package utils

import (
	"bytes"
	"html/template"
	"strings"
)

// ParseTemplate attempts to parse and execute a template with the provided content and value.
func ParseTemplate(templateContent string, value any) (string, error) {
	var tplBytes bytes.Buffer

	// Create a new template and parse the content into it.
	tpl, err := template.New("emailTemplate").Parse(templateContent)
	if err != nil {
		// If parsing fails, use a custom parsing function.
		return parseTemplateCustom_(templateContent, value)
	}
	// Set the template option to error on missing keys.
	tpl.Option("missingkey=error")

	// Execute the template with the provided value.
	err = tpl.Execute(&tplBytes, value)
	if err != nil {
		// If execution fails, use a custom parsing function.
		return parseTemplateCustom_(templateContent, value)
	}

	// Return the executed template as a string.
	return tplBytes.String(), nil
}

// parseTemplateCustom_ is a fallback function to handle template parsing with a custom approach.
func parseTemplateCustom_(templateContent string, value any) (string, error) {
	// Modify the template content to ensure correct parsing.
	templateContent = strings.ReplaceAll(templateContent, "{{", "{{.")
	var tplBytes bytes.Buffer

	// Create a new template and parse the modified content into it.
	tpl, err := template.New("emailTemplate").Parse(templateContent)
	if err != nil {
		// Return an error if parsing fails.
		return "", err
	}
	// Set the template option to error on missing keys.
	tpl.Option("missingkey=error")

	// Execute the template with the provided value.
	err = tpl.Execute(&tplBytes, value)
	if err != nil {
		// Return an error if execution fails.
		return "", err
	}

	// Return the executed template as a string.
	return tplBytes.String(), nil
}
