/*
Package provide enum options
*/
package enum

// AdminPushingStatusValue ..
type AdminPushingStatusValue string

type adminPushingStatus struct {
	DRAFT      AdminPushingStatusValue
	PROCESSING AdminPushingStatusValue
	DONE       AdminPushingStatusValue // bỏ
	COMPLETED  AdminPushingStatusValue
	CANCELED   AdminPushingStatusValue
}

// AdminPushingStatus ...
var AdminPushingStatus = &adminPushingStatus{
	"DRAFT",
	"PROCESSING",
	"DONE", // bỏ
	"COMPLETED",
	"CANCELED",
}

type (
	AdminPushGroupConfirmType string
	adminPushGroupConfirmType struct {
		OVERWRIDE AdminPushGroupConfirmType
		// ADD              AdminPushGroupConfirmType
		REMOVE_AND_WRITE AdminPushGroupConfirmType
	}
)

var AdminPushGroupConfirmTypeValue = adminPushGroupConfirmType{
	OVERWRIDE: "OVERRIDE",
	// ADD:              "ADD",
	REMOVE_AND_WRITE: "REMOVE-AND-WRITE",
}
