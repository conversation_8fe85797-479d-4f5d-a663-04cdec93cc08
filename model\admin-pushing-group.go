/*
Package provide standard Model from Database collection - use for JSON, BSON
*/
package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// AdminPushingGroup mode Đợt mua hàng
type AdminPushingGroup struct {
	ID              *primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	ConfirmedTime   *time.Time          `json:"confirmedTime,omitempty" bson:"confirmed_time,omitempty"`

	GroupType string `json:"groupType" bson:"group_type,omitempty"` // AUTO, FORCE

	WarehouseCodes []string `json:"warehouseCodes" bson:"warehouse_codes,omitempty"`
	SellerCode     string   `json:"sellerCode" bson:"seller_code,omitempty"`

	RunTime     *time.Time `json:"runTime" bson:"run_time,omitempty"`
	PushingTime *time.Time `json:"pushingTime" bson:"pushing_time,omitempty"`

	GroupID   int64  `json:"groupID,omitempty" bson:"group_id,omitempty"`
	GroupCode string `json:"groupCode,omitempty" bson:"group_code,omitempty"`
	// GroupName string `json:"groupName,omitempty" bson:"group_name,omitempty"`

	RunTimeHHMM  string `json:"runTimeHHMM" bson:"run_time_hh_mm,omitempty"`
	RunTimeYMDHM string `json:"runTimeYMDHM" bson:"run_time_ymd_hm,omitempty"` // 2006-01-02 15:04

	// IsCurrent   *bool                        `json:"isCurrent" bson:"is_current,omitempty"`
	Status      enum.AdminPushingStatusValue `json:"status" bson:"status,omitempty"`
	IsConfirmed bool                         `json:"isConfirmed" bson:"is_confirmed,omitempty"`
	IsImported  bool                         `json:"isImported" bson:"is_imported,omitempty"`

	SKUVendorType enum.SKUVendorType             `json:"skuVendorType,omitempty" bson:"sku_vendor_type,omitempty"`
	ConfirmType   enum.AdminPushGroupConfirmType `json:"confirmType,omitempty" bson:"confirm_type,omitempty"`

	Version string   `json:"version" bson:"version,omitempty"`  // 2006-01-02 15:04:05
	SOCodes []string `json:"soCodes" bson:"so_codes,omitempty"` // chỉ cào SO này

	OutputDetail *AdminPushingGroupDetail `json:"outputDetail,omitempty" bson:"output_detail,omitempty"`

	ComplexQuery []*bson.M  `json:"-" bson:"$and,omitempty"`
	CreatedFrom  *time.Time `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo    *time.Time `json:"createdTo,omitempty" bson:"-"`
}

type AdminPushingGroupDetail struct {
	SkuCount            int64   `json:"skuCount,omitempty" bson:"sku_count,omitempty"`
	TotalAllQuantity    int64   `json:"totalAllQuantity,omitempty" bson:"total_all_quantity,omitempty"`
	TotalVendorQuantity int64   `json:"totalVendorQuantity,omitempty" bson:"total_vendor_quantity,omitempty"`
	TotalVendorPrice    float64 `json:"totalVendorPrice,omitempty" bson:"total_vendor_price,omitempty"`
}

// AdminPushingGroupDB ...
var AdminPushingGroupDB = &db.Instance{
	ColName:        "admin_pushing_group",
	TemplateObject: &AdminPushingGroup{},
}

// InitSkuModel ...
func InitAdminPushingGroupModel(s *mongo.Database) {
	AdminPushingGroupDB.ApplyDatabase(s)

	// t := true
	// _ = AdminPushingGroupDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = AdminPushingGroupDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
