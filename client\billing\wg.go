package billing

import (
	"context"
	"fmt"
	"sync"
)

type WgGroup struct {
	wg     *sync.WaitGroup
	err    error
	ctx    context.Context
	cancel context.CancelFunc
}

func NewWgGroup() *WgGroup {
	ctx, cancel := context.WithCancel(context.Background())
	return &WgGroup{
		wg:     new(sync.WaitGroup),
		ctx:    ctx,
		cancel: cancel,
	}
}

func (g *WgGroup) Wait() error {
	defer g.cancel()
	g.wg.Wait()
	return g.err
}

func (g *WgGroup) Go(f func() error) {
	g.wg.Add(1)
	go func(contextF context.Context) {
		defer g.wg.Done()
		g.err = f()
		if contextF != nil {
			go func() {
				for {
					select {
					case <-contextF.Done():
						fmt.Println("Exiting go routine")
						return
					}
				}
			}()
		}
	}(g.ctx)
}
