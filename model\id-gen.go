package model

import (
	"math"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	VENDOR_ID                     = "VENDOR_ID"
	PURCHASE_ORDER                = "PURCHASE_ORDER"
	PURCHASE_ORDER_ITEM           = "PURCHASE_ORDER_ITEM"
	VENDOR_BILL                   = "VENDOR_BILL"
	VENDOR_BILL_ITEM              = "VENDOR_BILL_ITEM"
	ADMIN_PUSHING_SETTING_ID      = "ADMIN_PUSHING_SETTING"
	INBOUND_LINE_ID               = "INBOUND_LINE_ID"
	REQUEST_ID_TO_REQ             = "REQUEST_ID_TO_REQ"
	PAYMENT_ID                    = "PAYMENT_ID"
	PAYMENT_ITEM_ID               = "PAYMENT_ITEM_ID"
	INBOUND_REQUEST_ID            = "INBOUND_REQUEST_ID"
	INBOUND_REQUEST_ITEM_ID       = "INBOUND_REQUEST_ITEM_ID"
	ADJUSTMENT_BILL_ID            = "ADJUSTMENT_BILL_ID"
	ADJUSTMENT_BILL_ITEM_ID       = "ADJUSTMENT_BILL_ITEM_ID"
	CONTRACT_PRICE_ID             = "CONTRACT_PRICE_ID"
	CONTRACT_PRICE_ITEM_ID        = "CONTRACT_PRICE_ITEM_ID"
	ADDRESS_ID                    = "ADDRESS_ID"
	CONTACT_ID                    = "CONTACT_ID"
	BANKING_PAYMENT_ORDER_ID      = "BANKING_PAYMENT_ORDER_ID"
	BANKING_PAYMENT_ORDER_ITEM_ID = "BANKING_PAYMENT_ORDER_ITEM_ID"
	REASON_ID                     = "REASON_ID"
	REASON_TYPE_ID                = "REASON_TYPE_ID"
	QUOTA_MANAGE_ID               = "QUOTA_MANAGE_ID"
)

// IDGen DB entity for gen code
type IDGen struct {
	ID    string `json:"id,omitempty" bson:"_id,omitempty"`
	Value int64  `json:"value,omitempty" bson:"value,omitempty"`
}

// IDGenDB DB model for gen code
var IDGenDB = &db.Instance{
	ColName:        "_id_gen",
	TemplateObject: &IDGen{},
}

// InitIDGenModel init model
func InitIDGenModel(s *mongo.Database) {
	IDGenDB.ApplyDatabase(s)

	IDGenDB.Create(IDGen{
		ID:    VENDOR_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    PURCHASE_ORDER,
		Value: 100000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    PURCHASE_ORDER_ITEM,
		Value: 200000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    VENDOR_BILL,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    VENDOR_BILL_ITEM,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    ADMIN_PUSHING_SETTING_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    INBOUND_LINE_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    REQUEST_ID_TO_REQ,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    PAYMENT_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    PAYMENT_ITEM_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    INBOUND_REQUEST_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    INBOUND_REQUEST_ITEM_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    ADJUSTMENT_BILL_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    ADJUSTMENT_BILL_ITEM_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    CONTRACT_PRICE_ID,
		Value: 1000, // default value
	})
	IDGenDB.Create(IDGen{
		ID:    CONTRACT_PRICE_ITEM_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    ADDRESS_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    CONTACT_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    BANKING_PAYMENT_ORDER_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    BANKING_PAYMENT_ORDER_ITEM_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    REASON_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    REASON_TYPE_ID,
		Value: 1000, // default value
	})

	IDGenDB.Create(IDGen{
		ID:    QUOTA_MANAGE_ID,
		Value: 1000, // default value
	})
}

// convertToCode convert id from int to string
func convertToCode(number int64, length int64, template string) string {
	var result = ""
	var i = int64(0)
	var ln = int64(len(template))
	var capacity = int64(math.Pow(float64(ln), float64(length)))
	number = number % capacity
	for i < length {
		var cur = number % ln
		if i > 0 {
			cur = (cur + int64(result[i-1])) % ln
		}
		result = result + string(template[cur])
		number = number / ln
		i++
	}
	return result
}

// GetVendorID ...
func GetVendorID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: VENDOR_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetVendorBillID is func gen code for vendor id
func GetVendorBillID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: VENDOR_BILL,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetVendorBillItemID is func gen code for vendor id
func GetVendorBillItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: VENDOR_BILL_ITEM,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetAdminPushingSettingID is func gen code for vendor id
func GetAdminPushingSettingID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ADMIN_PUSHING_SETTING_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetPOID ...
func GetPOID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: PURCHASE_ORDER,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetPOID ...
func GetPOItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: PURCHASE_ORDER_ITEM,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetInboundLineID ...
func GetInboundLineID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: INBOUND_LINE_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetReqIDToReq ...
func GetReqIDToReq() int64 {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: REQUEST_ID_TO_REQ,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value
}

// GetPaymentID ...
func GetPaymentID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: PAYMENT_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetPaymentItemID ...
func GetPaymentItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: PAYMENT_ITEM_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetInboundRequestItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: INBOUND_REQUEST_ITEM_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetInboundRequestID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: INBOUND_REQUEST_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetAdjustmentBillID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ADJUSTMENT_BILL_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetAdjustmentBillItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ADJUSTMENT_BILL_ITEM_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetContractPriceID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: CONTRACT_PRICE_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetContractPriceItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: CONTRACT_PRICE_ITEM_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetAddressID ...
func GetAddressID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: ADDRESS_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetContactID ...
func GetContactID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: CONTACT_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetBankingPaymentOrderID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: BANKING_PAYMENT_ORDER_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetBankingPaymentOrderItemID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: BANKING_PAYMENT_ORDER_ITEM_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetReasonID ...
func GetReasonID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: REASON_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

// GetReasonTypeID ...
func GetReasonTypeID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: REASON_TYPE_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}

func GetQuotaManageID() (int64, string) {
	increResult := IDGenDB.IncreOne(IDGen{
		ID: QUOTA_MANAGE_ID,
	}, "value", 1)
	val := increResult.Data.([]*IDGen)[0]
	return val.Value, convertToCode(val.Value, 8, "1246789HJKLXCVBQWERTYUPADFG")
}
