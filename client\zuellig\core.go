package zuellig

import (
	"io"
	"log"
	"os"

	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
)

var zuelligClient client.SFTPClient

func InitSFTPZuellig() {
	cl, err := client.InitSFTPClient(conf.Config.Zuellig)
	if err != nil {
		log.Println(err.Error())
	}
	zuelligClient = cl
}

// func ListFiles(remoteDir string) ([]fs.FileInfo, error) {
// 	files, err := zuelligClient.ReadDir(remoteDir)
// 	if err != nil {
// 		fmt.Fprintf(os.Stderr, "Unable to list remote dir: %v\n", err)
// 		return nil, err
// 	}
// 	return files, nil
// }

// upload file to remote host
// remote file should look like: "home/title.txt"
func UploadContent(remotePath string, reader io.Reader) error {
	dstFile, err := zuelligClient.OpenFile(remotePath, (os.O_WRONLY | os.O_CREATE | os.O_TRUNC))
	if err != nil {
		return err
	}

	defer dstFile.Close()

	_, err = io.Copy(dstFile, reader)
	if err != nil {
		return err
	}
	return nil
}
