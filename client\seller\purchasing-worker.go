package seller

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	putManualCheckApproveVB = "PUT::/vendor-bill/auto-approve-manual"
)

var purchasingWorkerClient *client.Client

func InitPurchasingWorkerClient(database *mongo.Database) {
	const sellerPurchasingWorkerV1 = "/seller/purchasing-worker/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	purchasingWorkerClient = client.NewClient(sellerPurchasingWorkerV1, headers, 0)
	purchasingWorkerClient.WithDatabase(database)
	purchasingWorkerClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     putManualCheckApproveVB,
				Name:     "seller__PUT__vendor_bill_auto_approve_manual",
				Database: database,
			},
		}...,
	)
}

func ManualCheckApproveVB(opts ...client.APIOption) *common.APIResponse {
	requestOption := purchasingWorkerClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := purchasingWorkerClient.WithRequest(putManualCheckApproveVB, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
