package warehouse

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var warehouseInboundClient *client.Client

const (
	getInboundTicket  = "GET::/inbound-ticket"
	postReceipt       = "POST::/inbound-ticket"
	putSyncStatus     = "PUT::/inbound-ticket/sync"
	putInboundLotDate = "PUT::/inbound-ticket/lot-date"
)

func InitWarehouseInbound(database *mongo.Database) {
	const warehouseInboundV1 = "/warehouse/inbound/v1"

	headers := map[string]string{"Authorization": conf.Config.APIKey}
	warehouseInboundClient = client.NewClient(warehouseInboundV1, headers, 0)
	warehouseInboundClient.WithDatabase(database)
	warehouseInboundClient.WithConfiguration(
		[]client.Configuration{
			// {
			// 	Path:     getInboundTicket,
			// 	Name:     "warehouse_inbound__GET__inbound_ticket",
			// 	Database: database,
			// },
			{
				Path:     postReceipt,
				Name:     "warehouse_inbound__POST__create_ticket",
				Database: database,
			},
			{
				Path:     putSyncStatus,
				Name:     "warehouse_inbound__PUT__sync_ticket_status",
				Database: database,
			},
			{
				Path:     putInboundLotDate,
				Name:     "warehouse_inbound__PUT__sync_lot_date",
				Database: database,
			},
		}...,
	)
}

func GetInboundTicket(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInboundClient.WithAPIOption(opts...)
	var resp InboundTicketResponse
	_, err := warehouseInboundClient.WithRequest(getInboundTicket, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func CreateReceipt(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInboundClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := warehouseInboundClient.WithRequest(postReceipt, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func SyncStatus(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInboundClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := warehouseInboundClient.WithRequest(putSyncStatus, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func CallInboundLotDate(opts ...client.APIOption) *common.APIResponse {
	requestOption := warehouseInboundClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := warehouseInboundClient.WithRequest(putInboundLotDate, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
