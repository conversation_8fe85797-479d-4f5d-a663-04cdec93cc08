package model

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SKUItemEstimatedDemand struct {
	ID                       *primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	NumberOfDayDemand        *float64            `json:"numberOfDayDemand,omitempty" bson:"number_of_day_demand,omitempty"`
	TotalDays                *float64            `json:"totalDays,omitempty" bson:"total_days,omitempty"`
	SKU                      string              `json:"sku,omitempty" bson:"sku,omitempty"`
	ItemCode                 string              `json:"itemCode,omitempty" bson:"item_code,omitempty"`
	DateStr                  string              `json:"dateStr,omitempty" bson:"date_str,omitempty"`
	DemandClassificationCode string              `json:"demandClassificationCode,omitempty" bson:"demand_classification_code,omitempty"`
	ProductID                int64               `json:"productID,omitempty" bson:"product_id,omitempty"`
}

type SKUItemEstDemandQuery struct {
	LastID   *primitive.ObjectID `json:"lastId,omitempty" bson:"last_id,omitempty"`
	Offset   int64               `json:"offset,omitempty" bson:"offset,omitempty"`
	Limit    int64               `json:"limit,omitempty" bson:"limit,omitempty"`
	GetTotal bool                `json:"getTotal,omitempty" bson:"get_total,omitempty"`
}

var SKUItemEstimationDemandDB = &db.Instance{
	ColName:        "sku_item_estimation_demand",
	TemplateObject: &SKUItemEstimatedDemand{},
}

func InitSKUItemEstimatedDemandModel(s *mongo.Database) {
	SKUItemEstimationDemandDB.ApplyDatabase(s)
}
