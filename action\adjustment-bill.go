package action

import (
	"errors"
	"fmt"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	mappingSwitchStatusAB = map[enum.AdjustmentBillStatusType][]enum.AdjustmentBillStatusType{
		// nháp
		enum.AdjustmentBillStatus.DRAFT: {
			enum.AdjustmentBillStatus.WAIT_TO_PAID,
			enum.AdjustmentBillStatus.CANCEL,
		},

		// đã duyệt
		enum.AdjustmentBillStatus.WAIT_TO_PAID: {
			enum.AdjustmentBillStatus.CANCEL,
			enum.AdjustmentBillStatus.PAID,
		},

		// đã hủy
		enum.AdjustmentBillStatus.CANCEL: {},

		// đã thanh toán
		enum.AdjustmentBillStatus.PAID: {
			enum.AdjustmentBillStatus.CANCEL,
		},
	}
)

// get main
// GetListAdjustmentBillMain retrieves a list of adjustment bills based on the provided query parameters.
// 
// Parameters:
// - query: A pointer to an AdjustmentBill model containing the query criteria.
// - offset: The number of records to skip for pagination.
// - limit: The maximum number of records to return.
// - getTotal: A boolean indicating whether to include the total count of records matching the query.
// - sortFields: A pointer to a bson.M map specifying the fields to sort by. If nil, defaults to sorting by "_id" in descending order.
//
// Returns:
// - A pointer to an APIResponse containing the list of adjustment bills and optionally the total count.
func GetListAdjustmentBillMain(query *model.AdjustmentBill, offset, limit int64, getTotal bool, sortFields *bson.M) *common.APIResponse {
	if sortFields == nil {
		sortFields = &bson.M{"_id": -1}
	}
	getResp := model.AdjustmentBillDB.Query(query, offset, limit, sortFields)

	if getTotal {
		getResp.Total = model.AdjustmentBillDB.Count(query).Total
	}

	return getResp
}

// 
// GetListAdjustmentBillItem retrieves a list of AdjustmentBillItem based on the provided query, offset, limit, and sort fields.
// If getTotal is true, it also returns the total count of items matching the query.
// 
// Parameters:
//   - query: A pointer to an AdjustmentBillItem model containing the query criteria.
//   - offset: The number of items to skip before starting to collect the result set.
//   - limit: The maximum number of items to return.
//   - getTotal: A boolean indicating whether to include the total count of matching items.
//   - sortFields: A pointer to a bson.M map specifying the fields to sort by. If nil, defaults to sorting by "_id" in descending order.
// 
// Returns:
//   - A pointer to an APIResponse containing the list of AdjustmentBillItem and optionally the total count.
func GetListAdjustmentBillItem(query *model.AdjustmentBillItem, offset, limit int64, getTotal bool, sortFields *bson.M) *common.APIResponse {
	if sortFields == nil {
		sortFields = &bson.M{"_id": -1}
	}
	getResp := model.AdjustmentBillItemDB.Query(query, offset, limit, sortFields)
	if getTotal {
		getResp.Total = model.AdjustmentBillItemDB.Count(query).Total
	}
	return getResp
}

// GetListAdjustmentBillMainCodeFromItem retrieves a list of main adjustment bill codes from the given query parameters.
// It fetches the adjustment bill items based on the provided query, offset, limit, and sort fields.
// If the API response status is not OK, it returns an empty list of strings.
// Otherwise, it extracts the adjustment bill codes from the fetched items and returns them as a list of strings.
//
// Parameters:
//   - query: A pointer to an AdjustmentBillItem model containing the query parameters.
//   - offset: An int64 representing the offset for pagination.
//   - limit: An int64 representing the limit for pagination.
//   - sortFields: A pointer to a bson.M containing the fields to sort by.
//
// Returns:
//   - A slice of strings containing the main adjustment bill codes.
func GetListAdjustmentBillMainCodeFromItem(query *model.AdjustmentBillItem, offset, limit int64, sortFields *bson.M) []string {
	getItemResp := GetListAdjustmentBillItem(query, offset, limit, false, sortFields)
	if getItemResp.Status != common.APIStatus.Ok {
		return make([]string, 0)
	}

	var (
		items     = getItemResp.Data.([]*model.AdjustmentBillItem)
		mainCodes = make([]string, 0, len(items))
	)

	for _, item := range items {
		mainCodes = append(mainCodes, item.AdjustmentBillCode)
	}

	return mainCodes
}

// CreateAdjustmentBill creates a new adjustment bill based on the provided input.
// It performs the following steps:
// 1. Validates the input adjustment bill.
// 2. Sums the adjustment bill amount.
// 3. Generates a main adjustment bill ID and code.
// 4. Normalizes the hashtag for the adjustment bill.
// 5. Sets the status of the adjustment bill to DRAFT.
// 6. Generates item IDs and codes for each item in the adjustment bill.
// 7. Creates the main adjustment bill record in the database.
// 8. Creates the adjustment bill items in the database.
//
// Parameters:
// - input: A pointer to a model.AdjustmentBill containing the details of the adjustment bill to be created.
//
// Returns:
// - A pointer to a common.APIResponse containing the status, message, and data of the operation.
func CreateAdjustmentBill(input *model.AdjustmentBill) *common.APIResponse {
	// validate
	if err := validateAdjustmentBill(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// sum
	if err := sumAdjustmentBillAmount(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// gen main
	mainID, _ := model.GetAdjustmentBillID()
	input.AdjustmentBillID = mainID
	input.AdjustmentBillCode = fmt.Sprintf("AB%d", mainID)
	input.Hashtag = normalizeHashtagAB(input)
	input.Status = enum.AdjustmentBillStatus.DRAFT

	// gen item
	for _, item := range input.Items {
		itemID, _ := model.GetAdjustmentBillItemID()
		item.AdjustmentBillItemID = itemID
		item.AdjustmentBillCode = input.AdjustmentBillCode
	}

	items := input.Items

	// create data form input
	createMainResp := model.AdjustmentBillDB.Create(input)
	if createMainResp.Status != common.APIStatus.Ok {
		return createMainResp
	}

	// create data item from input
	createItemResp := model.AdjustmentBillItemDB.CreateMany(items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "create success",
		Data: []*model.AdjustmentBill{
			input,
		},
	}
}


// UpdateAdjustmentBill updates an existing adjustment bill with the provided input data.
// It performs the following steps:
// 1. Validates the input adjustment bill.
// 2. Sums the adjustment bill amount.
// 3. Checks if the adjustment bill code is provided.
// 4. Processes the main adjustment bill by normalizing the hashtag, resetting certain fields, and updating the main record in the database.
// 5. Deletes existing adjustment bill items associated with the main adjustment bill code.
// 6. Creates new adjustment bill items with updated IDs and the main adjustment bill code.
// 7. Returns an API response indicating the success or failure of the update operation.
//
// Parameters:
// - input: A pointer to the AdjustmentBill model containing the data to update.
//
// Returns:
// - A pointer to an APIResponse indicating the result of the update operation.
func UpdateAdjustmentBill(input *model.AdjustmentBill) *common.APIResponse {
	// validate
	if err := validateAdjustmentBill(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// sum
	if err := sumAdjustmentBillAmount(input); err != nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: err.Error(),
		}
	}

	// check main code
	if len(input.AdjustmentBillCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "adjustment bill code is required",
		}
	}

	var (
		items    = input.Items
		mainCode = input.AdjustmentBillCode
	)

	// process main
	input.Hashtag = normalizeHashtagAB(input)
	input.AdjustmentBillID = 0
	input.AdjustmentBillCode = ""
	input.Status = ""

	updateMainResp := model.AdjustmentBillDB.UpdateOne(&model.AdjustmentBill{AdjustmentBillCode: mainCode}, input)
	if updateMainResp.Status != common.APIStatus.Ok {
		return updateMainResp
	}
	// process item
	deleteResp := model.AdjustmentBillItemDB.Delete(&model.AdjustmentBillItem{AdjustmentBillCode: mainCode})
	if deleteResp.Status != common.APIStatus.Ok {
		return deleteResp
	}

	// get ItemID, ItemCode
	for _, item := range items {
		itemID, _ := model.GetAdjustmentBillItemID()
		item.AdjustmentBillItemID = itemID
		item.AdjustmentBillCode = mainCode
	}

	// Create items id
	createItemResp := model.AdjustmentBillItemDB.CreateMany(items)
	if createItemResp.Status != common.APIStatus.Ok {
		return createItemResp
	}

	output := updateMainResp.Data.([]*model.AdjustmentBill)[0]
	output.Items = items

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "update success",
		Data: []*model.AdjustmentBill{
			output,
		},
	}
}

// SwitchStatusAdjustmentBill switches the status of an adjustment bill identified by abCode to the nextStatus.
// It performs several checks to ensure the validity of the operation:
// - Checks if the abCode and nextStatus are not empty.
// - Checks if the adjustment bill exists in the database.
// - Validates if the status transition is allowed.
// - If the next status is CANCEL and the current status is PAID or WAIT_TO_PAID, it ensures there are no associated payments.
//
// Parameters:
// - abCode: The code of the adjustment bill to switch status.
// - nextStatus: The new status to switch to.
//
// Returns:
// - *common.APIResponse: The response containing the status and message of the operation.
func SwitchStatusAdjustmentBill(abCode string, nextStatus enum.AdjustmentBillStatusType) *common.APIResponse {
	if len(abCode) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "invalid adjustment bill code",
		}
	}

	if len(nextStatus) == 0 {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "invalid status",
		}
	}

	// check adjustment bill
	abCheckResp := model.AdjustmentBillDB.QueryOne(model.AdjustmentBill{AdjustmentBillCode: abCode})
	if abCheckResp.Status != common.APIStatus.Ok {
		return abCheckResp
	}

	abCheck := abCheckResp.Data.([]*model.AdjustmentBill)[0]

	// validate status
	if ok := validSwitchStatus(abCheck.Status, nextStatus); !ok {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "invalid switch status",
		}
	}

	// nếu đã thanh toán và còn payment thì buộc user tự huỷ payment trước khi chuyển trạng thái
	if nextStatus == enum.AdjustmentBillStatus.CANCEL && (abCheck.Status == enum.AdjustmentBillStatus.PAID || abCheck.Status == enum.AdjustmentBillStatus.WAIT_TO_PAID) {
		queryPayment := model.PaymentPlatform{
			AdjustmentBillCodeIn: []string{abCode},
		}

		getPaymentResp := GetNewPayment(queryPayment, 0, 200, model.QueryOption{}, "")
		if getPaymentResp.Status == common.APIStatus.Ok {
			payments := getPaymentResp.Data.([]*model.PaymentPlatform)
			paymentCodes := make([]string, 0)
			for _, payment := range payments {
				paymentCodes = append(paymentCodes, payment.PaymentCode)
			}

			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "adjustment bill has payment, please delete payment first",
				Data:    paymentCodes,
			}
		}
	}

	// update status
	updater := model.AdjustmentBill{Status: nextStatus}
	updateResp := model.AdjustmentBillDB.UpdateOne(model.AdjustmentBill{AdjustmentBillCode: abCheck.AdjustmentBillCode}, updater)
	if updateResp.Status != common.APIStatus.Ok {
		return updateResp
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "switch status success",
	}
}


// validateAdjustmentBill validates the fields of an AdjustmentBill struct.
// It returns an error if any of the required fields are missing or invalid.
//
// Parameters:
//   - input: A pointer to the AdjustmentBill struct to be validated.
//
// Returns:
//   - error: An error if validation fails, or nil if validation succeeds.
//
// Validation rules:
//   - input must not be nil.
//   - VendorCode must not be empty.
//   - InvoiceNumber must not be empty.
//   - IssuedTime must not be nil.
//   - DueTime must not be nil.
//   - AdjustType must not be empty.
//   - Items must not be empty.
//   - Each item in Items must have:
//     - OriginInvoiceNumber not empty.
//     - ProductCode not empty.
//     - OriginIssuedTime not nil.
//     - AdjustPrice not nil and greater than or equal to 0.
//     - Quantity greater than or equal to 1.
func validateAdjustmentBill(input *model.AdjustmentBill) error {
	if utils.IsNil(input) {
		return errors.New("invalid input")
	}
	if len(input.VendorCode) == 0 {
		return errors.New("vendor code is required")
	}
	if len(input.InvoiceNumber) == 0 {
		return errors.New("actual invoice number is require")
	}

	if input.IssuedTime == nil {
		return errors.New("issuedtime is required")
	}
	if input.DueTime == nil {
		return errors.New("duetime is required")
	}
	if len(input.AdjustType) == 0 {
		return errors.New("invalid adjust type")
	}
	if len(input.Items) == 0 {
		return errors.New("items are required")
	}

	// validate items
	for _, item := range input.Items {
		if len(item.OriginInvoiceNumber) == 0 {
			return errors.New("item origin invoice number is required")
		}
		if len(item.ProductCode) == 0 {
			return errors.New("product code is required")
		}
		if item.OriginIssuedTime == nil {
			return errors.New("item origin issued time is required")
		}
		if item.AdjustPrice == nil {
			return fmt.Errorf("adjust amount is required")
		}
		if *item.AdjustPrice < 0 {
			return fmt.Errorf("adjust amount: %f not greater than 0", *item.AdjustPrice)
		}
		if item.Quantity < 1 {
			return fmt.Errorf("quantity must >= 1")
		}

	}

	return nil
}


// sumAdjustmentBillAmount calculates the total amounts for an AdjustmentBill.
// It sums up the discount, VAT, and adjusted amounts for each item in the bill,
// and assigns the calculated totals to the corresponding fields in the AdjustmentBill.
//
// Parameters:
//   - input: A pointer to the AdjustmentBill to be processed.
//
// Returns:
//   - error: An error if the input is nil, if any item's AdjustPrice is nil or less than 0,
//            or if any item's Quantity is less than 1.
func sumAdjustmentBillAmount(input *model.AdjustmentBill) error {
	if utils.IsNil(input) {
		return errors.New("invalid input")
	}

	var (
		totalDiscountAmount float64
		totalBeforeVAT      float64
		totalVAT            float64
		totalAfterVAT       float64
	)

	// sum fields  { vat , discount , adjust_amount }
	for _, item := range input.Items {
		if item.AdjustPrice == nil {
			return fmt.Errorf("adjust amount is required")
		}
		if *item.AdjustPrice < 0 {
			return fmt.Errorf("adjust amount: %f not greater than 0", *item.AdjustPrice)
		}
		if item.Quantity < 1 {
			return fmt.Errorf("quantity must >= 1")
		}

		var (
			vatPrice      float64 = 0
			discountPrice float64 = 0
			quantity              = float64(item.Quantity)
			unitAmount            = quantity * (*item.AdjustPrice)
		)

		if item.Discount != nil && *item.Discount > 0 {
			discountPrice = utils.ValueFromPecent(*item.AdjustPrice, *item.Discount)
			// gắn vào item
			item.DiscountPrice = utils.Pointer.WithFloat64(discountPrice)
		}
		if item.Vat != nil && *item.Vat > 0 {
			vatPrice = utils.ValueFromPecent(*item.AdjustPrice-discountPrice, *item.Vat)
			// gắn vào item
			item.VatPrice = utils.Pointer.WithFloat64(vatPrice)
		}

		var (
			discountAmount  = discountPrice * quantity
			vatAmount       = vatPrice * quantity
			beforeVATAmount = utils.MaxFloat64(0, unitAmount-discountAmount)
		)

		// sum [tiền * số lượng] tích luỹ vào main
		totalDiscountAmount += discountAmount
		totalVAT += vatAmount
		totalBeforeVAT += beforeVATAmount
		totalAfterVAT += (beforeVATAmount + vatAmount)

		// gắn vào item
		item.TotalAmountAfterVAT = utils.Pointer.WithFloat64(beforeVATAmount + vatAmount)
	}

	// assigment
	input.TotalDiscountAmount = utils.Pointer.WithFloat64(totalDiscountAmount)
	input.TotalAmountBeforeVAT = utils.Pointer.WithFloat64(totalBeforeVAT)
	input.TotalVAT = utils.Pointer.WithFloat64(totalVAT)
	input.TotalAmountAfterVAT = utils.Pointer.WithFloat64(totalAfterVAT)
	input.RemainingMoney = utils.Pointer.WithFloat64(totalAfterVAT)

	return nil
}

// gen hashtag adjustment bill
// normalizeHashtagAB takes an AdjustmentBill model as input and returns a normalized
// string that concatenates the InvoiceNumber, VendorCode, and VendorName fields,
// with spaces replaced by hyphens. The normalization process involves standardizing
// the string format using the utils.NormalizeString function.
func normalizeHashtagAB(input *model.AdjustmentBill) string {
	return strings.Replace(utils.NormalizeString(input.InvoiceNumber+" "+input.VendorCode+" "+input.VendorName), " ", "-", -1)
}

// validSwitchStatus checks if the transition from the current status to the next status
// is valid based on predefined allowed status transitions.
//
// Parameters:
//   - current: The current status of type enum.AdjustmentBillStatusType.
//   - next: The next status of type enum.AdjustmentBillStatusType.
//
// Returns:
//   - bool: Returns true if the transition from current to next status is allowed, otherwise false.
func validSwitchStatus(current, next enum.AdjustmentBillStatusType) bool {
	allowStatuses, ok := mappingSwitchStatusAB[current]
	if !ok {
		return false
	}

	for _, allowStatus := range allowStatuses {
		if allowStatus == next {
			return true
		}
	}

	return false
}
