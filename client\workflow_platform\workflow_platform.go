package workflow_platform

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	pathGetActin = "QUERY::/action"
)

var workflowClient *client.Client

func InitWorkflowClient(database *mongo.Database) {
	const paymentCore = "/organization/workflow/v1"

	headers := map[string]string{"Authorization": conf.Config.SSOToken}
	workflowClient = client.NewClientWithHost(conf.Config.SSOHost, paymentCore, headers, 0)
	workflowClient.WithDatabase(database)
	workflowClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     pathGetActin,
				Name:     "payment_platform__QUERY__action",
				Database: database,
			},
		}...,
	)
}

func GetActionList(opts ...client.APIOption) *common.APIResponse {
	requestOption := workflowClient.WithAPIOption(opts...)
	var resp ActionPlatformReponse
	_, err := workflowClient.WithRequest(pathGetActin, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}
