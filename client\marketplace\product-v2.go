package marketplace

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"go.mongodb.org/mongo-driver/mongo"
)

var productClient *client.Client
var productWorkerClient *client.Client

const (
	getListSKUItem      = "GET::/sku-item/list"
	getListSKU          = "GET::/sku/list"
	getSingleProduct    = "GET::/product"
	getProductList      = "GET::/product/list"
	postSkuVendorBill   = "POST::/purchaser/vendor-bill"
	getCicProduct       = "GET::/product-cic/list"
	postUpdateSkuStatus = "POST::/tool/trigger-calc-update-status"
	getSingleSku        = "GET::/sku"
	getDealTicket       = "GET::/deal-ticket"
	getListDeal         = "GET::/deal/list"
	putDeal             = "PUT::/deal"

	// _ = server.SetHandler(common.APIMethod.PUT, "/deal", api.UpdateDeal)
)

func InitProductV2Client(database *mongo.Database) {
	const productV2 = "/marketplace/product/v2"
	headers := map[string]string{"Authorization": conf.Config.APIKey}
	productClient = client.NewClient(productV2, headers, 0)
	productClient.WithDatabase(database)
	productClient.WithConfiguration(
		[]client.Configuration{
			// {
			// 	Path:     getListSKUItem,
			// 	Name:     "product__GET__sku_item",
			// 	Database: database,
			// },
			// {
			// 	Path:     getSingleProduct,
			// 	Name:     "product__GET__single_product",
			// 	Database: database,
			// },
			// {
			// 	Path:     getProductList,
			// 	Name:     "product__GET__product_list",
			// 	Database: database,
			// },
			{
				Path:     postSkuVendorBill,
				Name:     "product__POST__update_purchase_price",
				Database: database,
			},
			{
				Path:     getCicProduct,
				Name:     "product__GET__cic_product",
				Database: database,
			},
			{
				Path:     getListSKU,
				Name:     "product__GET__sku_list",
				Database: database,
			},

			{
				Path:     putDeal,
				Name:     "product__PUT__deal",
				Database: database,
			},
			// {
			// 	Path:     getSingleSku,
			// 	Name:     "product__GET__single_sku",
			// 	Database: database,
			// },
			// {
			// 	Path:     getDealTicket,
			// 	Name:     "marketplace__GET__deal_ticket",
			// 	Database: database,
			// },

			// {
			// 	Path:     getListDeal,
			// 	Name:     "marketplace__GET__list_deal",
			// 	Database: database,
			// },
		}...,
	)

	const productWorker = "/marketplace/product-worker/v1"
	productWorkerClient = client.NewClient(productWorker, headers, 0)
	productWorkerClient.WithDatabase(database)
	productWorkerClient.WithConfiguration(
		[]client.Configuration{
			{
				Path:     postUpdateSkuStatus,
				Name:     "product__POST__sku_update_status",
				Database: database,
			},
		}...,
	)

}

func GetListSKU(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp SKUListResponse
	_, err := productClient.WithRequest(getListSKU, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetListSKUItem(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp SKUItemResponse
	_, err := productClient.WithRequest(getListSKUItem, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetProductList(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp ProductResponse
	_, err := productClient.WithRequest(getProductList, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetSingleProduct(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp ProductResponse
	_, err := productClient.WithRequest(getSingleProduct, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func UpdateVendorBill(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := productClient.WithRequest(postSkuVendorBill, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func GetCICProductList(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp CICProductResponse
	_, err := productClient.WithRequest(getCicProduct, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func UpdateSKUStatus(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := productWorkerClient.WithRequest(postUpdateSkuStatus, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}

func GetSingleSKU(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp SKUListResponse
	_, err := productClient.WithRequest(getSingleSku, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetDealTicket(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp DealTicketResponse
	_, err := productClient.WithRequest(getDealTicket, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func GetListDeal(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp DealResponse
	_, err := productClient.WithRequest(getListDeal, requestOption, &resp)
	return client.FormatResponse(resp.APIResponse, resp.Data, err)
}

func UpdateDeal(opts ...client.APIOption) *common.APIResponse {
	requestOption := productClient.WithAPIOption(opts...)
	var resp common.APIResponse
	_, err := productClient.WithRequest(putDeal, requestOption, &resp)
	return client.FormatResponse(resp, resp.Data, err)
}
