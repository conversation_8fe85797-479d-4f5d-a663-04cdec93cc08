package enum

// InboundRequestStatusValue ..
type InboundRequestStatusValue string

// nolint
type inboundRequestStatus struct {
	DRAFT     InboundRequestStatusValue
	CONFIRMED InboundRequestStatusValue
	CANCELED  InboundRequestStatusValue
}

var InboundRequestStatus = &inboundRequestStatus{
	"DRAFT",
	"CONFIRMED",
	"CANCELED",
}

// InboundRequestTypeValue ..
type InboundRequestTypeValue string

// nolint
type inboundRequestType struct {
	PROMOTION              InboundRequestTypeValue
	SAVE_INITIAL_STOCK     InboundRequestTypeValue
	OTHER                  InboundRequestTypeValue // hedging
	INCREASE_INITIAL_STOCK InboundRequestTypeValue
	DECREASE_INITIAL_STOCK InboundRequestTypeValue
}

var InboundRequestType = &inboundRequestType{
	"PROMOTION",
	"SAVE_INITIAL_STOCK",
	"OTHER", // hedging
	"INCREASE_INITIAL_STOCK",
	"DECREASE_INITIAL_STOCK",
}

type InboundRequestReasonToOtherValue string

/*
Đạt target rebate (ACHIEVE_REBATE_TARGET)
Thu thưởng rebate (COLLECT_REBATE_REWARD)
NCC/ hãng tăng giá (INCREASE_PRICE)
Trữ hàng KM (PROMOTIONAL_STOCK)
Trữ hàng Unstable/ Quota (USTABLE_OR_QUOTA)
*/
type inboundRequestReasonToOther struct {
	ACHIEVE_REBATE_TARGET InboundRequestReasonToOtherValue
	COLLECT_REBATE_REWARD InboundRequestReasonToOtherValue
	INCREASE_PRICE        InboundRequestReasonToOtherValue
	PROMOTIONAL_STOCK     InboundRequestReasonToOtherValue
	UNSTABLE_OR_QUOTA     InboundRequestReasonToOtherValue
}

var InboundRequestReasonToOther = &inboundRequestReasonToOther{
	"ACHIEVE_REBATE_TARGET",
	"COLLECT_REBATE_REWARD",
	"INCREASE_PRICE",
	"PROMOTIONAL_STOCK",
	"UNSTABLE_OR_QUOTA",
}

var InboundRequestReasonToOtherMap = map[InboundRequestReasonToOtherValue]struct{}{
	"ACHIEVE_REBATE_TARGET": {},
	"COLLECT_REBATE_REWARD": {},
	"INCREASE_PRICE":        {},
	"PROMOTIONAL_STOCK":     {},
	"UNSTABLE_OR_QUOTA":     {},
}

// DayOfWeekValue ...
type DayOfWeekValue int

type dayOfWeekType struct {
	MONDAY    DayOfWeekValue
	TUESDAY   DayOfWeekValue
	WEDNESDAY DayOfWeekValue
	THURSDAY  DayOfWeekValue
	FRIDAY    DayOfWeekValue
	SATURDAY  DayOfWeekValue
	SUNDAY    DayOfWeekValue
}

var DayOfWeekType = &dayOfWeekType{
	MONDAY:    2,
	TUESDAY:   3,
	WEDNESDAY: 4,
	THURSDAY:  5,
	FRIDAY:    6,
	SATURDAY:  7,
	SUNDAY:    8,
}
