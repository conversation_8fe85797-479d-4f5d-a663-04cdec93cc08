package api

import (
	"encoding/json"
	"strings"

	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetAdminPushingGroup ...
func GetAdminPushingSetting(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		offset     = sdk.ParseInt64(req.GetParam("offset"), 0)
		limit      = sdk.ParseInt64(req.GetParam("limit"), 20)
		getTotal   = req.GetParam("getTotal") == "true"
		sellerCode = req.GetVar("sellerCode")
		qStr       = req.GetParam("q")
	)

	// validate input
	var query model.AdminPushingSetting
	if qStr != "" {
		err := json.Unmarshal([]byte(qStr), &query)
		if err != nil {
			return resp.Respond(&common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Can not parse input data.",
				ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
			})

		}
	}
	query.SellerCode = strings.ToUpper(sellerCode)

	// do action
	return resp.Respond(action.GetAdminPushingSetting(&query, offset, limit, getTotal))
}

// UpdateAdminPushingSetting ...
func CreateAdminPushingSetting(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.AdminPushingSetting
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.SellerCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "MISSING_SELLER_CODE",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		})
	}

	// do action
	return resp.Respond(action.CreateAdminPushingSetting(&input))
}

// UpdateAdminPushingSetting ...
func UpdateAdminPushingSetting(req sdk.APIRequest, resp sdk.APIResponder) error {

	var (
		sellerCode = strings.ToUpper(req.GetVar("sellerCode"))
	)

	if sellerCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "MISSING_SELLER_CODE",
			ErrorCode: string(enum.ErrorCodeInvalid.SellerCode),
		})
	}

	// validate input
	var input model.AdminPushingSetting
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data.",
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}

	// validate input
	if input.AdminPushingSettingCode == "" {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "MISSING_PUSHING_CODE",
			ErrorCode: string(enum.ErrorCodeInvalid.PushingSettingCode),
		})
	}

	input.SellerCode = sellerCode

	// do action
	return resp.Respond(action.UpdateAdminPushingSetting(&input))
}

// DeleteAdminPushingSetting
func DeleteAdminPushingSetting(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		adminPushingSettingID = sdk.ParseInt64(req.GetParam("adminPushingSettingID"), 0)
	)

	if adminPushingSettingID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Invalid pushing setting ID",
			ErrorCode: string(enum.ErrorCodeInvalid.PushingSettingID),
		})
	}

	// do action
	return resp.Respond(action.DeleteAdminPushingSetting(adminPushingSettingID))

}
