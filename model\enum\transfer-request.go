package enum

// TransferStatusValue ..
type TransferStatusValue string

// nolint
type transferStatus struct {
	DRAFT      TransferStatusValue
	CONFIRMED  TransferStatusValue
	PROCESSING TransferStatusValue
	COMPLETED  TransferStatusValue
	CANCELED   TransferStatusValue
	DELIVERING TransferStatusValue
}

// TransferStatus ...
var TransferStatus = &transferStatus{
	"DRAFT",
	"CONFIRMED",
	"PROCESSING",
	"COMPLETED",
	"CANCELED",
	"DELIVERING",
}
