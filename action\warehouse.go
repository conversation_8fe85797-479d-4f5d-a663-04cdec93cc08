package action

import (
	"fmt"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/core"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/warehouse"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
)

func CreateReceptToWarehouse(input *model.PurchaseOrder) *common.APIResponse {
	if input == nil {
		return &common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "input is nil",
		}
	}

	// get poItems
	purchaseOrderItemResp := model.PurchaseOrderItemDB.Query(model.PurchaseOrderItem{POCode: input.POCode}, 0, 1000, nil)
	if purchaseOrderItemResp.Status != common.APIStatus.Ok {
		return purchaseOrderItemResp
	}
	purchaseOrderItems := purchaseOrderItemResp.Data.([]*model.PurchaseOrderItem)

	// combine inbound ticket
	inboundTicket := model.InboundTicket{
		POID:                    input.POID,
		POCode:                  input.POCode,
		ExternalType:            "PURCHASE_SYS",
		Type:                    string(input.Type),
		WarehouseCode:           input.WarehouseCode,
		SellerCode:              input.SellerCode,
		VendorCode:              input.VendorCode,
		SellerInfo:              input.SellerInfo,
		EstimatedDeliveryTime:   input.EstimatedDeliveryTime,
		PickupAtVendorWarehouse: utils.Pointer.WithBool(input.FirstMileBy == enum.FirstMileBy.BUYMED),
		Tags:                    input.Tags,
	}

	if input.OriginalPOs != nil {
		inboundTicket.InboundRefs = *input.OriginalPOs
	}

	// items
	for _, item := range purchaseOrderItems {

		vendorProductName := ""
		productMappingResp := model.ProductMappingDB.QueryOne(model.ProductMapping{
			ProductID: item.ProductID,
		})
		if productMappingResp.Status == common.APIStatus.Ok {
			productMapping := productMappingResp.Data.([]*model.ProductMapping)[0]
			if productMapping.ProductVendors != nil {
				for _, productVendor := range *productMapping.ProductVendors {
					if productVendor.VendorCode == item.VendorCode && len(productVendor.ProductVendorNames) > 0 {
						vendorProductName = productVendor.ProductVendorNames[0]
					}
				}
			}
		}

		inboundTicket.InboundTicketItems = append(
			inboundTicket.InboundTicketItems,
			model.InboundTicketItem{
				LineID:            item.POItemID,
				ProductID:         item.ProductID,
				ProductCode:       item.ProductCode,
				SKU:               item.SKU,
				DemandQuantity:    item.ExpectQuantity,
				Name:              item.ProductName,
				ImageURL:          item.ImageURL,
				Packaging:         item.Unit,
				VAT:               item.VAT,
				InvoiceExportable: item.VAT != nil && *item.VAT >= 0,
				IsNearExp:         item.IsNearExpiration,

				OldSKU:  item.WHOldSKU,
				ErrorID: item.WHErrorID,

				VendorProductName: vendorProductName,
			},
		)
	}

	return warehouse.CreateReceipt(client.APIOption{
		Body: inboundTicket,
		Keys: []string{inboundTicket.POCode},
	})
}

func executeLotDateInbound(input model.VendorBill, validPO model.PurchaseOrder) *common.APIResponse {

	if validPO.POCode == "" && validPO.DeliveryWarehouseCode == "" {
		return &common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "DeliveryWarehouseCode, POCode required.",
			ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
		}
	}

	inbound := model.InboundTicketLotDate{
		RequestID:     model.GetReqIDToReq(), // mỗi VB chỉ bắn qua 1 lần
		CheckSystem:   "ACCOUNTANT",
		WarehouseCode: validPO.WarehouseCode,
		POCode:        validPO.POCode,
	}

	// VBitem -> inbound item
	for i := range input.Items {
		item := input.Items[i]

		if item.Quantity == nil { // item.Lot == "" || item.ExpDate == "" ||
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   "Wrong lot/date/quantity.",
				ErrorCode: string(enum.ErrorCodeInvalid.RequiredFields),
			}
		}
		if item.SKU == "" {
			return &common.APIResponse{
				Status:    common.APIStatus.Invalid,
				Message:   fmt.Sprintf("ProductID %v do not have SKU", item.ProductID),
				ErrorCode: string(enum.ErrorCodeInvalid.SKU),
			}
		}

		// validateLotDate
		checkLotResp := validateLotDate(item)
		if checkLotResp.Status != common.APIStatus.Ok {
			return checkLotResp
		}

		ibItem := model.InboundTicketLotDateItem{
			// ProductID:   item.ProductID,
			SKU:         item.SKU,
			Lot:         item.Lot,
			ExpiredDate: item.ExpDate,
			Quantity:    *item.Quantity,

			VendorProductName: item.ItemName,
		}
		if ibItem.Lot == "-" {
			ibItem.Lot = ""
		}
		if ibItem.ExpiredDate == "-" {
			ibItem.ExpiredDate = ""
		}

		inbound.Items = append(
			inbound.Items,
			ibItem,
		)
	}

	// gửi qua Kho từng PO
	if validPO.Status == enum.PurchaseOrderStatus.CONFIRMED || validPO.Status == enum.PurchaseOrderStatus.HANDOVER_COMPLETED ||
		validPO.Status == enum.PurchaseOrderStatus.PARTIALLY_RECEIVED || validPO.Status == enum.PurchaseOrderStatus.RECEIVED {
		resp := warehouse.CallInboundLotDate(
			client.APIOption{
				Body: inbound,
				Keys: []string{input.POCode},
			},
		)
		if resp.Status != common.APIStatus.Ok {
			resp.Message = validPO.POCode + ": " + resp.Message
			return resp
		}
	}

	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Inbound call successful.",
	}
}

func validateLotDate(item model.VendorBillItem) *common.APIResponse {
	lotReplace := utils.ReplaceLot(item.Lot)
	if item.Lot != "" && item.Lot != "-" {
		if item.Lot != lotReplace {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "LOT cannot contain special characters",
			}
		}
		// có Lot mà không có ExpDate
		if item.ExpDate == "" || item.ExpDate == "-" {
			return &common.APIResponse{
				Status:  common.APIStatus.Invalid,
				Message: "Invalid: have LOT, but not ExpDate",
			}
		}
	}
	return &common.APIResponse{
		Status: common.APIStatus.Ok,
	}
}

type purchaseOrderItem struct {
	ActualQuantity *int64 `json:"actualQuantity,omitempty" bson:"actual_quantity,omitempty"` // callback
}

// getRegionMap retrieves a map of regions and their corresponding province codes.
// It initializes a map with a default region "00" containing an empty slice of strings.
// The function then calls an external API to get a list of regions. If the API call is
// unsuccessful, it returns the initialized map.
// If the API call is successful, it iterates over the regions and populates the map
// with the region codes and their associated province codes. Additionally, it ensures
// that the default region "00" contains all unique province codes from all regions.
//
// Returns:
//
//	map[string][]string: A map where the keys are region codes and the values are slices
//	of province codes.
func getRegionMap() map[string][]string {
	regionMap := map[string][]string{
		"00": []string{},
	}

	// get region
	clientResp := core.GetRegionList(client.APIOption{})
	if clientResp.Status != common.APIStatus.Ok {
		return regionMap
	}

	// fill province of region
	for _, reg := range clientResp.Data.([]*model.Region) {
		regionMap[reg.Code] = reg.ProvinceCodes
		if reg.Code != "00" {
			for _, it := range reg.ProvinceCodes {
				if !utils.IsContains(regionMap["00"], it) {
					regionMap["00"] = append(regionMap["00"], it)
				}
			}
		}
	}

	return regionMap
}

// ParseLocationsToWarehouseDelivery takes a list of location codes and a list of warehouse data,
// and returns a list of warehouse codes that deliver to the specified locations.
//
// Parameters:
//   - locationCodes: A slice of strings representing location codes.
//   - warehouseData: A slice of pointers to Warehouse structs containing warehouse information.
//
// Returns:
//   - A slice of strings representing the warehouse codes that deliver to the specified locations.
//
// The function first flattens the location codes using a region map. Then, it iterates through the
// flattened location codes and the warehouse data to find and return the warehouse codes that
// deliver to the specified locations.
func ParseLocationsToWarehouseDelivery(locationCodes []string, warehouseData []*model.Warehouse) []string {
	regionMap := getRegionMap()
	locationFlatten := []string{}
	for _, location := range locationCodes {
		if regionMap[location] != nil {
			locationFlatten = append(locationFlatten, regionMap[location]...)
		} else {
			locationFlatten = append(locationFlatten, location)
		}
	}

	// find warehouse
	var warehouseCodes []string
	for _, location := range locationFlatten {
		for _, wh := range warehouseData {
			if utils.IsContains(wh.Areas, location) && !utils.IsContains(warehouseCodes, wh.Code) {
				warehouseCodes = append(warehouseCodes, wh.Code)
			}
		}
	}
	return warehouseCodes
}
