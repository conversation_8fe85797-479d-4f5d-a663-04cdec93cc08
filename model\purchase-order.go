package model

import (
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/db"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type PurchaseOrder struct {
	ID                    *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime           *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime       *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`
	FirstInboundTime      *time.Time          `json:"firstInboundTime,omitempty" bson:"first_inbound_time,omitempty"`
	LastInboundTime       *time.Time          `json:"lastInboundTime,omitempty" bson:"last_inbound_time,omitempty"`
	LastUnlockTime        *time.Time          `json:"lastUnlockTime,omitempty" bson:"last_unlock_time,omitempty"`
	IssuedTime            *time.Time          `json:"issuedTime,omitempty" bson:"issued_time,omitempty"`                  // "2020-11-01T04:00:14.084+00:00", -- Ngày phát hành hóa đơn
	StartQuotationTime    *time.Time          `json:"startQuotationTime,omitempty" bson:"start_quotation_time,omitempty"` // "2020-11-01T04:00:14.084+00:00", -- Ngày phát hành hóa đơn
	EstimatedDeliveryTime *time.Time          `json:"estimatedDeliveryTime,omitempty" bson:"estimated_delivery_time,omitempty"`

	// Ngày lên kế hoạch sẽ lấy tự động là ngày tạo phiếu nếu danh sách cho tiết sản phẩm trống/chưa có
	// Ngày lên kế hoạch sẽ tự động lấy ngày lên kế hoạch của chi tiết sản phẩm được tạo sớm nhất trong danh sách cho tiết sản phẩm
	PlanningTime  *time.Time `json:"planningTime,omitempty" bson:"planning_time,omitempty"`
	ConfirmedTime *time.Time `json:"confirmedTime,omitempty" bson:"confirmed_time,omitempty"`

	CreatedByName string                `json:"createdByName,omitempty" bson:"created_by_name,omitempty"`
	CreatedByID   int64                 `json:"createdById,omitempty" bson:"created_by_id,omitempty"`
	FirstMileBy   enum.FirstMileByValue `json:"firstMileBy,omitempty" bson:"first_mile_by,omitempty"`

	// dùng để tách PO
	CreatedBySystem string `json:"createdBySystem,omitempty" bson:"created_by_system,omitempty"`
	IsBulky         *bool  `json:"isBulky,omitempty" bson:"is_bulky,omitempty"` // have sku qty > 100

	// parent
	// GroupID   int64  `json:"groupID,omitempty" bson:"group_id,omitempty"`
	// GroupCode string `json:"groupCode,omitempty" bson:"group_code,omitempty"`

	POID   int64       `json:"poID" bson:"po_id,omitempty"`
	POCode string      `json:"poCode" bson:"po_code,omitempty"`
	Type   enum.POType `json:"type" bson:"type,omitempty"`

	DeliveryWarehouseCode string `json:"deliveryWarehouseCode" bson:"delivery_warehouse_code,omitempty"`
	WarehouseCode         string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	PurchaserCode string `json:"purchaserCode" bson:"purchaser_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	AccountName     string `json:"accountName,omitempty" bson:"account_name,omitempty"`
	VendorName      string `json:"vendorName,omitempty" bson:"vendor_name,omitempty"`
	VendorReference string `json:"vendorReference,omitempty" bson:"vendor_reference,omitempty"`

	HashTag string `json:"hashTag,omitempty" bson:"hash_tag,omitempty"`
	// PackageQuantity int64  `json:"packageQuantity,omitempty" bson:"package_quantity,omitempty"`
	Tags *[]string `json:"tags,omitempty" bson:"tags,omitempty"`

	PaymentTerm    *float64 `json:"paymentTerm,omitempty" bson:"payment_term,omitempty"`
	FiscalPosition *float64 `json:"fiscalPosition,omitempty" bson:"fiscal_position,omitempty"`
	Incoterm       string   `json:"incoterm,omitempty" bson:"incoterm,omitempty"`
	// PurchasingCompany string   `json:"purchasingCompany,omitempty" bson:"purchasing_company,omitempty"`
	EmployeeCode string `json:"employeeCode,omitempty" bson:"employee_code,omitempty"`
	// SOCodes      []string `json:"soCodes" bson:"so_codes,omitempty"`
	// VBCodes        *[]string `json:"vbCodes" bson:"vb_codes,omitempty"`
	SourceDocument *string   `json:"sourceDocument,omitempty" bson:"source_document,omitempty"`
	OriginalPOs    *[]string `json:"originalPOs,omitempty" bson:"original_pos,omitempty"`

	Status enum.PurchaseOrderStatusValue `json:"status,omitempty" bson:"status,omitempty"`
	// BillStatus enum.POBillStatusValue        `json:"billStatus,omitempty" bson:"bill_status,omitempty"`

	CreateShortageTicket bool  `json:"createShortageTicket,omitempty" bson:"-"`
	MustInbound          *bool `json:"mustInbound,omitempty" bson:"must_inbound,omitempty"`

	//sync to warehouse
	SellerInfo SellerInfo `json:"sellerInfo,omitempty" bson:"-"`

	RequireConfirm *enum.RequireConfirmValue `json:"requireConfirm,omitempty" bson:"require_confirm,omitempty"`

	TotalVatPrice        *float64 `json:"totalVatPrice,omitempty" bson:"total_vat_price,omitempty"`                // 3693000, -- Tổng số tiền thuế VAT
	TotalWithoutVatPrice *float64 `json:"totalWithoutVatPrice,omitempty" bson:"total_without_vat_price,omitempty"` // 36930000, -- Tổng số tiền chưa VAT
	TotalPrice           *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`                       // 40623000, -- Tổng số tiền sau VAT

	FulfillmentPercent *float64 `json:"fulfillmentPercent,omitempty" bson:"fulfillment_percent,omitempty"`
	OverFulfill        *bool    `json:"overFulfill,omitempty" bson:"over-fulfill,omitempty"`

	ApplyPromotion  *bool       `json:"applyPromotion,omitempty" bson:"apply_promotion,omitempty"`
	Metadata        *POMetadata `json:"metadata,omitempty" bson:"metadata,omitempty"`
	IsMigrateSeller *bool       `json:"isMigrateSeller,omitempty" bson:"is_migrate_seller,omitempty"`

	MigrateMetadata *map[string]string `json:"migrateMetadata,omitempty" bson:"migrate_metadata,omitempty"`

	CreatedTransferRequestCodes []string `json:"createdTransferRequestCodes,omitempty" bson:"created_transfer_request_codes,omitempty"`

	//sp filter item
	SKUs []string `json:"skus,omitempty" bson:"skus,omitempty"`

	InboundRequests []*InboundRequest `json:"inboundRequests,omitempty" bson:"-"`

	ComplexQuery  []*bson.M                       `json:"-" bson:"$and,omitempty"`
	Items         []*PurchaseOrderItem            `json:"items,omitempty" bson:"-"`
	ItemsCheck    []PurchaseOrderItem             `json:"itemsCheck,omitempty" bson:"-"`
	CreatedFrom   *time.Time                      `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo     *time.Time                      `json:"createdTo,omitempty" bson:"-"`
	IssuedFrom    *time.Time                      `json:"issuedFrom,omitempty" bson:"-"`
	IssuedTo      *time.Time                      `json:"issuedTo,omitempty" bson:"-"`
	ConfirmedFrom *time.Time                      `json:"confirmedFrom,omitempty" bson:"-"`
	ConfirmedTo   *time.Time                      `json:"confirmedTo,omitempty" bson:"-"`
	POCodeIn      []string                        `json:"poCodeIn,omitempty" bson:"-"`
	POIDIn        []int                           `json:"poIDIn,omitempty" bson:"-"`
	StatusIn      []enum.PurchaseOrderStatusValue `json:"statusIn,omitempty" bson:"-"`
	SKUIn         []string                        `json:"skuIn,omitempty" bson:"-"`
	UpdateType    []enum.PurchaseOrderStatusValue `json:"updateType,omitempty" bson:"-"`
	OriginalPOIn  []string                        `json:"originalPOIn,omitempty" bson:"-"`
	IDFrom        *primitive.ObjectID             `json:"idFrom,omitempty" bson:"-"`
	IDTo          *primitive.ObjectID             `json:"idTo,omitempty" bson:"-"`
	VendorCodeIn  []string                        `json:"vendorCodeIn,omitempty" bson:"-"`
	CheckedIn     *bool                           `json:"checkedIn,omitempty" bson:"-"`
	// BillStatusIn  []enum.POBillStatusValue        `json:"billStatusIn,omitempty" bson:"-"`
	Leadtime *float64 `json:"leadtime,omitempty" bson:"leadtime,omitempty"`
}

type POMetadata struct {
	ApplyPromotionCodes []string `json:"applyPromotionCodes,omitempty" bson:"apply_promotion_codes,omitempty"`
}

type SellerInfo struct {
	SellerId int64  `json:"sellerId" bson:"-"`
	Name     string `json:"name" bson:"-"`
	Phone    string `json:"phone" bson:"-"`
}

// PurchaseOrderDB ...
var PurchaseOrderDB = &db.Instance{
	ColName:        "purchase_order",
	TemplateObject: &PurchaseOrder{},
}

var PurchaseOrderReplicaDB = &db.Instance{
	ColName:        "purchase_order",
	TemplateObject: &PurchaseOrder{},
}

// InitSkuModel ...
func InitPurchaseOrderModel(s *mongo.Database) {
	PurchaseOrderDB.ApplyDatabase(s)

	// // - INDEX
	// // - BE
	// // POID
	// // POCode
	// // POCode, Status
	// // Status
	// // WarehouseCode, Status
	// // Status, CreatedBySystem, CreatedTime
	// // VendorCode, Status, CreatedBySystem
	// // BillStatus
	// // - FE
	// // POCode
	// // SellerCode, IssuedTime, Status
	// // SellerCode, ConfirmedTime, Status

	// t := true
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "po_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "warehouse_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // WORKER
	// // confirm po
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "created_by_system", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// // create po, debt
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "created_by_system", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// // waiting bill
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "bill_status", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // FE
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "issued_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "confirmed_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "hash_tag", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_name", Value: 1},
	// 	primitive.E{Key: "status", Value: 1},
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}

func InitPurchaseOrderReplicaModel(s *mongo.Database) {
	PurchaseOrderReplicaDB.ApplyDatabase(s)
}

type PurchaseOrderItem struct {
	ID              *primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	CreatedTime     *time.Time          `json:"createdTime,omitempty" bson:"created_time,omitempty"`
	LastUpdatedTime *time.Time          `json:"lastUpdatedTime,omitempty" bson:"last_updated_time,omitempty"`

	// parent
	// GroupID   int64  `json:"groupID,omitempty" bson:"group_id,omitempty"`
	// GroupCode string `json:"groupCode,omitempty" bson:"group_code,omitempty"`
	POID   int64  `json:"poID" bson:"po_id,omitempty"`
	POCode string `json:"poCode" bson:"po_code,omitempty"`

	POItemID int64 `json:"poItemID" bson:"po_item_id,omitempty"`

	DeliveryWarehouseCode string `json:"deliveryWarehouseCode" bson:"delivery_warehouse_code,omitempty"`
	WarehouseCode         string `json:"warehouseCode" bson:"warehouse_code,omitempty"`

	SellerCode    string `json:"sellerCode" bson:"seller_code,omitempty"`
	PurchaserCode string `json:"purchaserCode" bson:"purchaser_code,omitempty"`
	VendorCode    string `json:"vendorCode,omitempty" bson:"vendor_code,omitempty"`

	Description string `json:"description,omitempty" bson:"description,omitempty"`
	// IsImportant *bool  `json:"isImportant,omitempty" bson:"is_important,omitempty"`

	IsNearExpiration *bool `json:"isNearExpiration,omitempty" bson:"is_near_expiration,omitempty"`

	ProductName string `json:"productName,omitempty" bson:"product_name,omitempty"`
	ProductCode string `json:"productCode,omitempty" bson:"product_code,omitempty"`
	ProductID   int64  `json:"productID,omitempty" bson:"product_id,omitempty"`
	SKU         string `json:"sku,omitempty" bson:"sku,omitempty"`

	ImageURL string `json:"imageUrl,omitempty" bson:"image_url,omitempty"`

	Unit       string   `json:"unit,omitempty" bson:"unit,omitempty"`
	Packaging  string   `json:"packaging,omitempty" bson:"packaging,omitempty"`
	UnitPrice  float64  `json:"unitPrice,omitempty" bson:"unit_price,omitempty"`
	TotalPrice *float64 `json:"totalPrice,omitempty" bson:"total_price,omitempty"`
	VAT        *float64 `json:"vat,omitempty" bson:"vat,omitempty"`
	VATPrice   *float64 `json:"vatPrice,omitempty" bson:"vat_price,omitempty"`

	// Quotation
	ReferPrice float64 `json:"referPrice,omitempty" bson:"refer_price,omitempty"` // đã bao gồm VAT

	DiscountPercent    *float64 `json:"discountPercent,omitempty" bson:"discount_percent,omitempty"`
	DiscountPrice      *float64 `json:"discountPrice,omitempty" bson:"discount_price,omitempty"`
	PriceAfterDiscount *float64 `json:"priceAfterDiscount,omitempty" bson:"price_after_discount,omitempty"`
	PriceAfterRebate   *float64 `json:"priceAfterRebate,omitempty" bson:"price_after_rebate,omitempty"`

	PriceAfterRebateWithoutAdhoc *float64 `json:"priceAfterRebateWithoutAdhoc,omitempty" bson:"price_after_rebate_without_adhoc,omitempty"`
	ContractPriceCode            *string  `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`

	// quantity
	ExpectQuantity int64  `json:"expectQuantity,omitempty" bson:"expect_quantity,omitempty"`
	ActualQuantity int64  `json:"actualQuantity,omitempty" bson:"actual_quantity,omitempty"` // callback
	BillQuantity   *int64 `json:"billQuantity,omitempty" bson:"bill_quantity,omitempty"`     // callback

	// metadata
	Metadata               *POItemMetadata    `json:"metadata,omitempty" bson:"metadata,omitempty"`
	POItemLog              *POItemLog         `json:"poItemLog,omitempty" bson:"po_item_log,omitempty"`
	IsGift                 *bool              `json:"isGift,omitempty" bson:"is_gift,omitempty"`
	SourceQuotation        enum.QuotationType `json:"sourceQuotation,omitempty" bson:"source_quotation,omitempty"`
	InboundRequestCode     string             `json:"inboundRequestCode,omitempty" bson:"inbound_request_code,omitempty"`
	InboundRequestItemCode string             `json:"inboundRequestItemCode,omitempty" bson:"inbound_request_item_code,omitempty"`
	InboundRequestID       int64              `json:"inboundRequestID,omitempty" bson:"inbound_request_id,omitempty"`

	// reference with wrong ticket
	//Wrong ticket
	WrongTicketCode     string `json:"wrongTicketCode,omitempty" bson:"wrong_ticket_code,omitempty"`
	WrongTicketID       int64  `json:"wrongTicketID" bson:"wrong_ticket_id,omitempty"`
	WrongTicketItemCode string `json:"wrongTicketItemCode,omitempty" bson:"wrong_ticket_item_code,omitempty"`
	WrongTicketItemID   int64  `json:"wrongTicketItemID,omitempty" bson:"wrong_ticket_item_id,omitempty"`

	// unknown
	WHOldSKU  string `json:"whOldSKU,omitempty" bson:"wh_old_sku,omitempty"` // MEDX.UNKNOWN_PO686818_4
	WHErrorID int64  `json:"whErrorID,omitempty" bson:"wh_error_id,omitempty"`

	// query
	ComplexQuery  []*bson.M           `json:"-" bson:"$and,omitempty"`
	POCodeIn      []string            `json:"poCodeIn,omitempty" bson:"-"`
	ProductCodeIn []string            `json:"productCodeIn,omitempty" bson:"-"`
	CreatedFrom   *time.Time          `json:"createdFrom,omitempty" bson:"-"`
	CreatedTo     *time.Time          `json:"createdTo,omitempty" bson:"-"`
	IDFrom        *primitive.ObjectID `json:"idFrom,omitempty" bson:"-"`
	IDTo          *primitive.ObjectID `json:"idTo,omitempty" bson:"-"`

	ErrorCodes        []string `json:"errorCode,omitempty" bson:"-"`
	WrongTicketCodeIn []string `json:"wrongTicketCodeIn,omitempty" bson:"-"`
}

type POItemMetadata struct {
	ApplyPromotions []ApplyPromotion `json:"applyPromotions,omitempty" bson:"apply_promotions,omitempty"`

	// range discount to calculate
	DiscountAbsolutes       []float64 `json:"discountAbsolutes,omitempty" bson:"discount_absolutes,omitempty"`
	DiscountPercentRoots    []float64 `json:"discountPercentRoots,omitempty" bson:"discount_percent_roots,omitempty"`
	DiscountPercentCurrents []float64 `json:"discountPercentCurrents,omitempty" bson:"discount_percent_currents,omitempty"`

	MigrateSeller MigrateSeller `json:"migrateSeller,omitempty" bson:"migrate_seller,omitempty"`
}

type POItemLog struct {

	// CONTACT_PRICE || SKU_CONFIG
	Source            string `json:"source,omitempty" bson:"source,omitempty"`
	ContractPriceCode string `json:"contractPriceCode,omitempty" bson:"contract_price_code,omitempty"`
	// price
	UnitPrice *float64 `json:"unitPrice,omitempty" bson:"unit_price,omitempty"` // giá mua chưa VAT
	VAT       *float64 `json:"vat,omitempty" bson:"vat,omitempty"`              // VAT
	Discount  *float64 `json:"discount,omitempty" bson:"discount,omitempty"`    // Discount
}

type MigrateSeller struct {
	OldSKU    string `json:"oldSKU,omitempty" bson:"old_sku,omitempty"`
	OldSeller string `json:"oldSeller,omitempty" bson:"old_seller"`
}
type ApplyPromotion struct {
	Type enum.VendorPromotionTypeValue `json:"type,omitempty" bson:"type,omitempty"`
	Code string                        `json:"code,omitempty" bson:"code,omitempty"`
}

// PurchaseOrderItemDB ...
var PurchaseOrderItemDB = &db.Instance{
	ColName:        "purchase_order_item",
	TemplateObject: &PurchaseOrderItem{},
}
var PurchaseOrderMigrateDB = &db.Instance{
	ColName:        "purchase_order_migrate",
	TemplateObject: &PurchaseOrder{},
}

// PurchaseOrderItemDB ...
var PurchaseOrderItemMigrateDB = &db.Instance{
	ColName:        "purchase_order_item_migrate",
	TemplateObject: &PurchaseOrderItem{},
}

// InitSkuModel ...
func InitPurchaseOrderItemModel(s *mongo.Database) {
	PurchaseOrderItemDB.ApplyDatabase(s)

	PurchaseOrderMigrateDB.ApplyDatabase(s)
	PurchaseOrderItemMigrateDB.ApplyDatabase(s)
	// // - INDEX
	// // - BE
	// // POCode
	// // POCode, SKU
	// // POCode, ProductCode
	// // - FE
	// // POCode
	// // SellerCode
	// // VendorCode

	t := true
	_ = PurchaseOrderItemDB.CreateIndex(bson.D{
		primitive.E{Key: "po_code", Value: 1},
	}, &options.IndexOptions{
		Background: &t,
	})

	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // FE
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// - INDEX
	// - BE
	// POCode
	// POCode, SKU
	// POCode, ProductCode
	// - FE
	// POCode
	// SellerCode
	// VendorCode

	// t := true
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_item_id", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// 	Unique:     &t,
	// 	Sparse:     &t,
	// })

	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "sku", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "po_code", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })

	// // FE
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "seller_code", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
	// // lịch sử bidding supplier
	// _ = PurchaseOrderItemDB.CreateIndex(bson.D{
	// 	primitive.E{Key: "vendor_code", Value: 1},
	// 	primitive.E{Key: "product_code", Value: 1},
	// 	primitive.E{Key: "created_time", Value: 1},
	// }, &options.IndexOptions{
	// 	Background: &t,
	// })
}
