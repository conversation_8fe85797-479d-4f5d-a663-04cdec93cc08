package payment_platform

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
)

type (
	PaymentPlatformReponse struct {
		Data []*model.PaymentPlatform `json:"data"`
		common.APIResponse
	}
	PaymentItemResponse struct {
		Data []*model.PaymentItemPlatform `json:"data"`
		common.APIResponse
	}
	ReasonSettingResponse struct {
		Data []*model.ReasonSetting `json:"data"`
		common.APIResponse
	}
	TransferRequestResponse struct {
		Data []*model.TransferRequest `json:"data"`
		common.APIResponse
	}
)
