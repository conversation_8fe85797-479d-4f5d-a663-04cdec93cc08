.PHONY: integration integration_w_race benchmark

integration:
	go test -integration -v ./...
	go test -testserver -v ./...
	go test -integration -testserver -v ./...
	go test -integration -allocator -v ./...
	go test -testserver -allocator -v ./...
	go test -integration -testserver -allocator -v ./...

integration_w_race:
	go test -race -integration -v ./...
	go test -race -testserver -v ./...
	go test -race -integration -testserver -v ./...
	go test -race -integration -allocator -v ./...
	go test -race -testserver -allocator -v ./...
	go test -race -integration -allocator -testserver -v ./...

COUNT ?= 1
BENCHMARK_PATTERN ?= "."

benchmark:
	go test -integration -run=NONE -bench=$(BENCHMARK_PATTERN) -benchmem -count=$(COUNT)

benchmark_w_memprofile:
	go test -integration -run=NONE -bench=$(BENCHMARK_PATTERN) -benchmem -count=$(COUNT) -memprofile memprofile.out
	go tool pprof -svg -output=memprofile.svg memprofile.out
