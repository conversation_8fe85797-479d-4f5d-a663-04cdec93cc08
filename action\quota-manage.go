package action

import (
	"fmt"
	"log"
	"runtime/debug"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/marketplace"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// GetQuotaList retrieves a list of quota management records based on the provided query parameters.
//
// Parameters:
//   - query: A pointer to a model.QuotaManage struct containing the query criteria.
//   - offset: The number of records to skip before starting to return records.
//   - limit: The maximum number of records to return.
//   - getTotal: A boolean indicating whether to include the total count of records in the response.
//   - sort: A string specifying the field to sort by and the sort order.
//     Supported values are "createdTime", "-createdTime", "id", and "-id".
//
// Returns:
// - A pointer to a common.APIResponse struct containing the list of quota management records and optionally the total count.
func GetQuotaList(query *model.QuotaManage, offset, limit int64, getTotal bool, sort string) *common.APIResponse {
	sortField := &primitive.M{"_id": -1}
	switch sort {
	case "createdTime":
		sortField = &primitive.M{"created_time": 1}
	case "-createdTime":
		sortField = &primitive.M{"created_time": -1}
	case "id":
		sortField = &primitive.M{"_id": 1}
	case "-id":
		sortField = &primitive.M{"_id": -1}
	}

	// query quota management records
	quotaResp := model.QuotaManageDB.Query(query, offset, limit, sortField)
	if getTotal {
		count := model.QuotaManageDB.Count(query)
		quotaResp.Total = count.Total
	}

	return quotaResp
}

// CreateQuota creates a new quota management entry in the database.
// It takes a QuotaManage model as input, sets the CreatedTime to the current local time,
// generates a new QuotaManageID and QuotaManageCode, and then saves the entry to the database.
// It returns an APIResponse indicating the result of the operation.
//
// Parameters:
//   - input: model.QuotaManage - The quota management data to be created.
//
// Returns:
//   - *common.APIResponse - The response from the database operation.
func CreateQuota(input model.QuotaManage) *common.APIResponse {
	var resp *common.APIResponse
	now := time.Now().Local()
	input.CreatedTime = &now

	input.QuotaManageID, input.QuotaManageCode = model.GetQuotaManageID()

	resp = model.QuotaManageDB.Create(&input)
	return resp
}

// UpdateQuota updates the quota for a given product, vendor, and warehouse based on the provided input.
// It takes a model.QuotaManage struct as input and returns a pointer to a common.APIResponse.
//
// Parameters:
//   - input: model.QuotaManage struct containing the details of the quota to be updated.
//
// Returns:
//   - *common.APIResponse: A pointer to the API response indicating the result of the update operation.
func UpdateQuota(input model.QuotaManage) *common.APIResponse {
	resp := model.QuotaManageDB.UpdateOne(model.QuotaManage{
		ProductID:     input.ProductID,
		VendorCode:    input.VendorCode,
		WarehouseCode: input.WarehouseCode,
		Period:        input.Period,
	}, input)

	return resp
}

var IsRunActualReceiptQty bool

// CalculateActualReceiptQty calculates the actual receipt quantity for purchase orders
// within a specified period and updates the quota management records accordingly.
// It handles panic recovery, ensures the function runs only once at a time, and logs
// the start and end of the calculation process. The function performs the following steps:
// 1. Retrieves a list of quota management records.
// 2. For each quota, determines the period and vendor codes.
// 3. Constructs a complex query to filter purchase orders based on the period, vendor codes, and status.
// 4. Retrieves purchase orders and their items, and calculates the actual quantities.
// 5. Syncs CIC accounts based on vendor and product.
// 6. Checks if the actual quantity exceeds the quota and prepares for notification if necessary.
// 7. Updates the actual quantities and other relevant information in the quota management records.
func CalculateActualReceiptQty() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("panic in processor: %s: %s", r, debug.Stack())
		}
	}()

	// ensure the function runs only once at a time
	if IsRunActualReceiptQty {
		return
	} else {
		IsRunActualReceiptQty = true
	}

	defer func() {
		IsRunActualReceiptQty = false
		fmt.Println("End CalculateActualReceiptQty")
	}()

	fmt.Println("Start CalculateActualReceiptQty")

	// retrieve a list of quota management records
	var poList []*model.PurchaseOrder
	var poItems []*model.PurchaseOrderItem
	validStatus := []enum.PurchaseOrderStatusValue{
		// enum.PurchaseOrderStatus.PROCESSING,
		enum.PurchaseOrderStatus.PARTIALLY_RECEIVED, enum.PurchaseOrderStatus.RECEIVED,
		enum.PurchaseOrderStatus.COMPLETED,
		enum.PurchaseOrderStatus.AWAITING_BILLED, enum.PurchaseOrderStatus.BILLED,
	}

	offset := int64(0)
	limit := int64(100)
	// loop through all quota management records
	for {
		// query quota management records
		quotaResp := model.QuotaManageDB.Query(nil, int64(offset), int64(limit), &primitive.M{"created_time": -1})
		// break if there is an error or no more records
		if quotaResp.Status != common.APIStatus.Ok {
			break
		}

		// loop through each quota management record
		quotaList := quotaResp.Data.([]*model.QuotaManage)
		for _, quota := range quotaList {
			// calculate period
			var actualQuantities int64 = 0
			startDate, endDate := getPeriodTime(quota.Period)

			// distinguish vendor is the parent or child or normal vendor
			var vendorCodes []string
			opts := client.APIOption{
				Keys: []string{quota.VendorCode, "PARENT_VENDOR"},
				Q: model.Seller{
					SellerClass: model.CLASS_VENDOR,
					LineManager: quota.VendorCode,
				},
			}
			// get vendor
			getVendorResp := seller.GetSellers(opts)
			if getVendorResp.Status == common.APIStatus.Ok { // vendor is the parent vendor
				children := getVendorResp.Data.([]*model.Seller)
				for _, child := range children {
					vendorCodes = append(vendorCodes, child.Code, quota.VendorCode)
				}
			} else {
				vendorCodes = append(vendorCodes, quota.VendorCode)
			}

			// construct complex query to filter purchase orders
			quota.ComplexQuery = append(quota.ComplexQuery, &bson.M{
				"created_time": bson.D{
					{Key: "$gte", Value: startDate},
					{Key: "$lte", Value: endDate},
				},
				"vendor_code": bson.M{"$in": vendorCodes},
				"status":      bson.M{"$in": validStatus},
			})

			// retrieve purchase orders and their items
			for {
				filter := model.PurchaseOrder{
					ComplexQuery: quota.ComplexQuery,
				}
				if quota.WarehouseCode != "00" {
					filter.WarehouseCode = quota.WarehouseCode
				}
				poResp := model.PurchaseOrderDB.Query(filter, 0, 200, &bson.M{"_id": 1})

				// break if there are no more purchase orders
				if poResp.Status != common.APIStatus.Ok {
					break
				}

				// loop through each purchase order
				poList = poResp.Data.([]*model.PurchaseOrder)
				for _, po := range poList {
					poItemResp := model.PurchaseOrderItemDB.Query(
						model.PurchaseOrderItem{
							POCode:    po.POCode,
							ProductID: quota.ProductID,
						}, 0, 200, nil,
					)
					if poItemResp.Status != common.APIStatus.Ok {
						continue
					}

					poItems = poItemResp.Data.([]*model.PurchaseOrderItem)
					// loop through each purchase order item
					for _, item := range poItems {
						if item.ActualQuantity < 0 {
							continue
						}
						if item.IsGift != nil && *item.IsGift {
							continue
						}
						actualQuantities += item.ActualQuantity
					}
				}
				break
			}

			// sync CIC based on vendor
			cicVendor := getCICAccountByVendor(quota.VendorCode, quota.WarehouseCode)

			// TODO: pending get CIC by product
			cicProduct := getCICAccountByProduct(quota.ProductID, quota.WarehouseCode)
			// check whether the actual quantity exceeds the quota
			//TODO: prepare for notification
			var switchNoti bool
			if quota.ActualQuantity != nil && quota.Quota != nil && *quota.ActualQuantity != 0 {
				if *quota.Quota <= *quota.ActualQuantity {
					switchNoti = true
				}
			}

			// update actual quantities into quota manage record
			model.QuotaManageDB.UpdateOne(
				model.QuotaManage{
					ID: quota.ID,
				}, model.QuotaManage{
					ActualQuantity:    &actualQuantities,
					CICAccountVendor:  &cicVendor,
					CICAccountProduct: &cicProduct,
					IsNotification:    &switchNoti,
				},
			)
		}

		offset += limit
	}
}

// getPeriodTime returns the start and end dates for a given period type.
// The period parameter should be one of the following values:
// - enum.PeriodTypeValue.MONTH: Returns the start and end dates of the current month.
// - enum.PeriodTypeValue.QUARTER: Returns the start and end dates of the current quarter.
// - enum.PeriodTypeValue.HALF_YEAR: Returns the start and end dates of the current half-year.
// - enum.PeriodTypeValue.YEAR: Returns the start and end dates of the current year.
//
// Parameters:
// - period: A string representing the period type.
//
// Returns:
// - startDate: A pointer to the start date of the period.
// - endDate: A pointer to the end date of the period.
func getPeriodTime(period string) (startDate *time.Time, endDate *time.Time) {
	curDate := time.Now().Local()
	y, m, _ := curDate.Date()

	var start time.Time
	var end time.Time

	switch period {
	// start is the first day of the current month and end is the last day of the current month
	case string(enum.PeriodTypeValue.MONTH):
		start = time.Date(y, m, 1, 0, 0, 0, 0, time.UTC)
		end = start.AddDate(0, 1, 0).Add(-time.Nanosecond)

	// start is the first day of the first month and end is the last day of the last month in a quarterly
	case string(enum.PeriodTypeValue.QUARTER):
		quarter := (int(m) - 1) / 3
		start = time.Date(y, time.Month(quarter*3)+1, 1, 0, 0, 0, 0, time.UTC)
		end = start.AddDate(0, 3, 0).Add(-time.Nanosecond)

	// determine which half year is it
	case string(enum.PeriodTypeValue.HALF_YEAR):
		if m <= 6 {
			start = time.Date(y, 1, 1, 0, 0, 0, 0, time.UTC)
			end = time.Date(y, 7, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
		} else {
			start = time.Date(y, 7, 1, 0, 0, 0, 0, time.UTC)
			end = time.Date(y+1, 1, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
		}

	// start is the first day of the year and end is the last day of this year
	case string(enum.PeriodTypeValue.YEAR):
		start = time.Date(y, 1, 1, 0, 0, 0, 0, time.UTC)
		end = time.Date(y+1, 1, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond)
	}

	return &start, &end
}

// getCICAccountByVendor retrieves the CIC account ID associated with a given vendor and warehouse.
// It takes two string parameters:
// - vendorCode: the code identifying the vendor.
// - warehouseCode: the code identifying the warehouse.
// It returns an int64 representing the CIC account ID. If no CIC account ID is found, it returns 0.
func getCICAccountByVendor(vendorCode, warehouseCode string) int64 {
	var cicID int64

	// get CIC account by vendor
	cicVendorResp := model.PICDB.QueryOne(model.PIC{
		ObjectCode:    vendorCode,
		ObjectType:    enum.ObjectType.VENDOR,
		WarehouseCode: warehouseCode,
	})

	// if CIC account is found, return the CIC account ID
	if cicVendorResp.Status == common.APIStatus.Ok {
		cicVendor := cicVendorResp.Data.([]*model.PIC)[0]
		if cicVendor.CICAccountID != nil {
			cicID = *cicVendor.CICAccountID
		}
	}

	return cicID
}

// getCICAccountByProduct retrieves the CIC (Category In Charge) account ID for a given product and warehouse code.
// It takes a product ID and a warehouse code as input parameters and returns the CIC account ID.
//
// Parameters:
//   - productID: The ID of the product for which the CIC account is to be retrieved.
//   - warehouseCode: The code of the warehouse where the product is stored.
//
// Returns:
//   - int64: The CIC account ID associated with the given product and warehouse code. If no CIC account is found, it returns 0.
func getCICAccountByProduct(productID int64, warehouseCode string) int64 {
	var cicID int64
	opts := client.APIOption{
		Keys: []string{warehouseCode},
		Q: model.Product{
			ProductID: productID,
		},
	}

	// get CIC account by product
	getCicProduct := marketplace.GetCICProductList(opts)
	if getCicProduct.Status == common.APIStatus.Ok {
		cicProduct := getCicProduct.Data.([]*model.ProductCategoryInCharge)[0]
		if len(cicProduct.CategoryInChargeData) > 0 {
			for _, data := range cicProduct.CategoryInChargeData {
				if data.CategoryInCharge != nil {
					cicID = *data.CategoryInCharge
				}
			}
		}
	}

	return cicID
}

// Delete quota ...
func DeleteQuota(code string) *common.APIResponse {
	quotaResp := model.QuotaManageDB.QueryOne(model.QuotaManage{QuotaManageCode: code})
	if quotaResp.Status != common.APIStatus.Ok {
		return quotaResp
	}
	return model.QuotaManageDB.Delete(&model.QuotaManage{ID: quotaResp.Data.([]*model.QuotaManage)[0].ID})
}
