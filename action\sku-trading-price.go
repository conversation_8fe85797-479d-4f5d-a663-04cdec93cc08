package action

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSkuTradingPrice retrieves a list of SKU trading prices based on the provided query, offset, limit, and sorting options.
// It also optionally returns the total count of matching records.
//
// Parameters:
//   - query: model.SkuTradingPrice - The query object to filter the SKU trading prices.
//   - offset: int64 - The number of records to skip for pagination.
//   - limit: int64 - The maximum number of records to return.
//   - getTotal: bool - A flag indicating whether to include the total count of matching records.
//   - sort: enum.SkuTradingPriceSort - The sorting option for the results.
//
// Returns:
//   - *common.APIResponse - The API response containing the list of SKU trading prices and optionally the total count.
func GetSkuTradingPrice(query model.SkuTradingPrice, offset, limit int64, getTotal bool, sort enum.SkuTradingPriceSort) *common.APIResponse {
	sortKey := bson.M{"_id": -1}

	switch sort {
	case enum.SkuTradingPriceSorts.SettingPrice_ASC:
		sortKey = bson.M{"setting_price": 1}
	case enum.SkuTradingPriceSorts.SettingPrice_DESC:
		sortKey = bson.M{"setting_price": -1}
	case enum.SkuTradingPriceSorts.Sku_ASC:
		sortKey = bson.M{"setting_price": 1}
	case enum.SkuTradingPriceSorts.Sku_DESC:
		sortKey = bson.M{"setting_price": -1}
	}

	// Query primary DB
	resp := model.SkuTradingPriceDB.Query(query, offset, limit, &sortKey)
	if getTotal {
		countResp := model.SkuTradingPriceDB.Count(query)
		resp.Total = countResp.Total
	}

	return resp
}
