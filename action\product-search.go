package action

import (
	"errors"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/job"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/integration"
	"gitlab.com/thuocsi.vn/seller/purchasing/conf"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
	"go.mongodb.org/mongo-driver/bson"
)

// setupSearchData setup search data
func setupSearchData(productCode string, product *model.Quotation) (*model.UpdateProductContentRequest, error) {

	// if product is not given, let query it
	if product == nil {
		productResult := model.QuotationDB.QueryOne(model.Quotation{
			ProductCode: productCode,
		})

		if productResult.Status != common.APIStatus.Ok {
			return nil, errors.New(productResult.Message)
		}
		product = productResult.Data.([]*model.Quotation)[0]
	}

	// init data
	request := model.UpdateProductContentRequest{
		Model:  conf.Config.SearchProductModel + conf.SearchVersion,
		Key:    product.ProductCode,
		Text:   product.ProductName,
		Filter: map[string]string{},
	}

	// get all SKU of this product
	quotationR := model.QuotationDB.Query(model.Quotation{
		ProductCode: productCode,
	}, 0, 1000, nil)
	if quotationR.Status == common.APIStatus.Ok {
		quotations := quotationR.Data.([]*model.Quotation)

		for _, quotation := range quotations {
			request.Filter["quotationStatus."+string(product.QuotationStatus)] = "1"
			if quotation.VendorCodes != nil {
				for _, vd := range *quotation.VendorCodes {
					request.Filter["vendorCode."+vd] = "1"
				}
			} else {
				request.Filter["vendorCode.00"] = "1"
			}

			if quotation.Origin != "" {
				request.Filter["origin."+quotation.Origin] = "1"
			}
		}

		return &request, nil
	}

	return nil, nil
}

// WarmupSearch warmup action for 1 product
func WarmupSearch(productCode string, product *model.Quotation) {
	now := time.Now()
	data, err := setupSearchData(productCode, product)
	if err == nil {
		if data != nil {
			model.UpdateESJob.Push(
				data,
				&job.JobItemMetadata{
					Keys:      []string{productCode},
					Topic:     "UPDATE_ES",
					ReadyTime: &now,
				},
			)
		}
	}
}

// removeProductSearch removes a product from the search index based on the given product code.
// It first checks if the product exists in the Quotation database. If the product is not found,
// it proceeds to remove the product from the search index using the integration service.
//
// Parameters:
//   - productCode: A string representing the code of the product to be removed.
func removeProductSearch(productCode string) {
	checkResp := model.QuotationDB.QueryOne(model.Quotation{ProductCode: productCode})
	if checkResp.Status == common.APIStatus.NotFound {
		params := map[string]string{
			"model":      conf.Config.SearchProductModel,
			"documentId": productCode,
		}
		integration.RemoveProduct(client.APIOption{Params: params})

	}
}

// used for filter product
type QuotationFilter struct {
	QuotationStatus enum.QuotationStatusValue `json:"quotationStatus" bson:"-"`
	VendorCode      string                    `json:"vendorCode,omitempty" bson:"-"`

	// ManufacturerCodes []string `json:"manufacturerCodes" bson:"-"`
	Origins []string `json:"origins" bson:"-"`
}
type SearchQuery struct {
	// query
	Text string `json:"text"`
	// filter
	Filter QuotationFilter `json:"filter,omitempty"`
	// pagination
	Offset int64 `json:"offset"`
	Limit  int64 `json:"limit"`

	// sort
	// Sort *enum.ProductSortTypeValue `json:"sort"`
}

// SearchProduct searches for products based on the provided search query.
// It trims and converts the search text to lowercase before performing the search.
// If the search text is not empty, it performs a fuzzy search using the searchFuzzy function.
// If the search text is empty, it currently does not perform a simple listing (commented out).
// It returns an API response with the search results.
//
// Parameters:
//   - acc: A pointer to the Account model.
//   - query: A pointer to the SearchQuery containing the search criteria.
//
// Returns:
//   - A pointer to an APIResponse containing the status, message, search results, and total count.
func SearchProduct(acc *model.Account, query *SearchQuery) *common.APIResponse {

	// trim and convert text to lowercase
	text := strings.Trim(query.Text, " ")
	text = strings.ToLower(text)

	resp := &common.APIResponse{
		Status:  common.APIStatus.Invalid,
		Message: "Required text",
	}
	data := []*model.Quotation{}

	// if need to search via text
	if text != "" {
		query.Text = text
		// call searchFuzzy
		resp, data = searchFuzzy(query)
	}

	if resp.Status != common.APIStatus.Ok {
		return resp
	}

	// return response
	return &common.APIResponse{
		Status:  common.APIStatus.Ok,
		Message: "Search product successfully.",
		Data:    data,
		Total:   resp.Total,
	}
}

// searchFuzzy performs a fuzzy search for quotations based on the provided search query.
// It first attempts to search in the database and if no results are found, it searches in Elasticsearch.
//
// Parameters:
//   - query: A pointer to a SearchQuery struct containing the search parameters.
//
// Returns:
//   - A pointer to a common.APIResponse struct containing the status, message, error code, and total results.
//   - A slice of pointers to model.Quotation structs containing the search results.
func searchFuzzy(query *SearchQuery) (*common.APIResponse, []*model.Quotation) {
	data := []*model.Quotation{}

	searchInput := model.SearchInput{
		Text:   query.Text,
		Model:  conf.Config.SearchProductModel + conf.SearchVersion,
		Limit:  query.Limit,
		Offset: query.Offset,
	}

	// apply filter
	filterMap := map[string]string{}
	queryAfterSearch := bson.M{}
	if query.Filter.VendorCode != "" {
		filterMap["vendorCode."+query.Filter.VendorCode] = "1"
		queryAfterSearch["vendor_codes"] = query.Filter.VendorCode
	}
	if query.Filter.QuotationStatus != "" {
		filterMap["quotationStatus."+string(query.Filter.QuotationStatus)] = "1"
		queryAfterSearch["quotation_status"] = query.Filter.QuotationStatus
	}

	if len(query.Filter.Origins) > 0 {
		for _, origin := range query.Filter.Origins {
			filterMap["origin."+origin] = "1"
		}
		queryAfterSearch["origin"] = bson.M{
			"$in": query.Filter.Origins,
		}
	}
	if len(filterMap) > 0 {
		searchInput.Filter = filterMap
	}

	// ưu tiên search trong DB trước
	arrText := strings.Split(strings.Trim(searchInput.Text, " "), " ")
	if len(arrText) == 1 {
		queryAfterSearch["$text"] = bson.M{
			"$search": "\"" + searchInput.Text + "\"",
		}

		// Get all ProductInfo
		cacheResult := model.QuotationDB.Query(queryAfterSearch, searchInput.Offset, searchInput.Limit, nil)
		if cacheResult.Status == common.APIStatus.Ok {
			data = cacheResult.Data.([]*model.Quotation)
			return cacheResult, data
		}
	}

	// search trong ES
	result := integration.SearchProduct(
		client.APIOption{
			Body: searchInput,
			Keys: []string{"Search", searchInput.Text},
		},
	)

	// return response
	resp := &common.APIResponse{
		Status:    result.Status,
		Message:   result.Message,
		ErrorCode: result.ErrorCode,
		Total:     result.Total,
	}
	if result.Status != common.APIStatus.Ok {
		return resp, data
	}

	// Get all ProductInfo
	searchData := result.Data.([]string)
	queryAfterSearch["product_code"] = bson.M{"$in": searchData}
	cacheResult := model.QuotationDB.Query(queryAfterSearch, 0, 1000*2, nil)
	if cacheResult.Status != common.APIStatus.Ok {
		return cacheResult, nil
	}

	data = cacheResult.Data.([]*model.Quotation)
	return resp, data
}
