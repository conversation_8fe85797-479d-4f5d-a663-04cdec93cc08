package api

import (
	"gitlab.buymed.tech/sdk/go-sdk/sdk"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.com/thuocsi.vn/seller/purchasing/action"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/model/enum"
)

// GetValidationDeal ...
func GetValidationDeal(req sdk.APIRequest, resp sdk.APIResponder) error {
	var input model.DealRequest

	// parse input
	err := req.GetContent(&input)
	if err != nil {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "Can not parse input data. " + err.Error(),
			ErrorCode: string(enum.ErrorCodeInvalid.ParseData),
		})
	}
	
	// validate input
	if len(input.ProductIDs) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "ProductIDs is not empty",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// validate input
	if len(input.WarehouseCodes) == 0 {
		return resp.Respond(&common.APIResponse{
			Status:    common.APIStatus.Invalid,
			Message:   "WarehouseCodes is not empty",
			ErrorCode: string(enum.ErrorCodeInvalid.InputData),
		})
	}

	// do action
	return resp.Respond(action.GetValidationDeal(input))
}

// GetPriceForDeal ...
func GetPriceForDeal(req sdk.APIRequest, resp sdk.APIResponder) error {
	var (
		productID     = sdk.ParseInt64(req.GetParam("productID"), 0)
		locationCodes = req.GetParam("locationCodes")
		skuCode       = req.GetParam("skuCode")
	)
	if productID == 0 {
		return resp.Respond(&common.APIResponse{
			Status:  common.APIStatus.Invalid,
			Message: "productID is required",
		})
	}

	return resp.Respond(action.GetPriceForDeal(productID, skuCode, locationCodes))
}
