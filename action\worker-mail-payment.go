package action

import (
	"fmt"
	"strings"
	"time"

	"gitlab.buymed.tech/sdk/go-sdk/sdk/common"
	"gitlab.buymed.tech/sdk/go-sdk/sdk/schedule"
	"gitlab.com/thuocsi.vn/seller/purchasing/client"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/integration"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/payment_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/seller"
	"gitlab.com/thuocsi.vn/seller/purchasing/client/workflow_platform"
	"gitlab.com/thuocsi.vn/seller/purchasing/model"
	"gitlab.com/thuocsi.vn/seller/purchasing/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func MailPayment(prevTime *time.Time, _ *schedule.Config) (error, string, *time.Time) {
	fmt.Println("MailPayment begin...")
	defer fmt.Println("MailPayment end")

	// Calculate the next execution time: first day of the next month at 8:00 AM in Vietnam timezone
	now := time.Now().In(utils.TimeZoneVN)
	next := time.Date(now.Year(), now.Month(), now.Day(), 21, 0, 0, 0, utils.TimeZoneVN).AddDate(0, 0, 1)

	// ==================================== get action template mail ====================================
	// Retrieve the email template action from the workflow using the action code
	actionResp := workflow_platform.GetActionList(client.APIOption{
		Body: bson.M{
			"q": bson.M{
				"code": "PURCHASING_MAIL_PAYMENT_TO_VENDOR",
			},
		},
	})
	// If the action retrieval is not successful, return early with the next execution time
	if actionResp.Status != common.APIStatus.Ok {
		return nil, "success", &next
	}
	action := actionResp.Data.([]*model.Action)[0]
	// If the email template is not defined in the action, return early
	if action.EmailTemplate == nil {
		return nil, "success", &next
	}

	// now = time.Date(2025, 2, 13, 0, 0, 0, 0, utils.TimeZoneVN)
	firstTime := utils.GetFirstTimeOfDate(now)
	endTime := utils.GetLastTimeOfDate(now)

	// ==================================== get payment list =====================================
	mapPaymentByVendor := map[string][]*model.PaymentPlatform{}

	query := model.PaymentPlatform{
		PartnerType: "THUOCSIVN_VENDOR",
		PaidFrom:    &firstTime,
		PaidTo:      &endTime,
	}
	var limit int64 = 100
	var offset int64 = 0
	for {
		cliOption := client.APIOption{
			Body: model.PaymentPlatformQuery{
				Query:  query,
				Offset: offset,
				Limit:  limit,
				Option: model.QueryOption{
					Items: true,
				},
			},
			SaveLog: utils.Pointer.WithBool(false),
		}
		offset += limit

		// get payment list from client
		paymentResp := payment_platform.GetPaymentList(cliOption)
		if paymentResp.Status != common.APIStatus.Ok {
			break
		}
		payments := paymentResp.Data.([]*model.PaymentPlatform)

		for i := range payments {
			pm := payments[i]

			if pm.Status == "CANCELLED" || pm.ReasonCode != "ADVANCE_MONEY" && pm.ReasonCode != "BILL" {
				continue
			}

			mapPaymentByVendor[pm.PartnerCode] = append(mapPaymentByVendor[pm.PartnerCode], pm)
		}
	}

	// do action
	for key, payments := range mapPaymentByVendor {
		doMail(key, payments, action)
	}

	return nil, "success", &next
}

func doMail(vendorCode string, payments []*model.PaymentPlatform, action *model.Action) {

	// check new vendor in line-manager
	opt := client.APIOption{
		Keys: []string{vendorCode},
		Params: map[string]string{
			"sellerClass": model.CLASS_VENDOR,
			"sellerCode":  vendorCode,
		},
		Offset: utils.Pointer.WithInt(0),
		Limit:  utils.Pointer.WithInt(1),
	}

	// get old vendor
	getVendor := seller.GetSellers(opt)
	if getVendor.Status != common.APIStatus.Ok {
		fmt.Println("getVendor error", getVendor.Message)
		return
	}
	vendor := getVendor.Data.([]*model.Seller)[0]

	finalTotalAmount := 0.0

	// Define a structure to hold summary data for the email table
	type TableOnMail struct {
		PaymentCode string
		PaymentType string
		FinalAmount string

		//
		POCodes        []string
		InvoiceNumbers []string
		Amounts        []string
	}

	poTables := []TableOnMail{}
	vbTables := []TableOnMail{}
	for i := range payments {
		pm := payments[i]

		finalTotalAmount += pm.Amount
		tb := TableOnMail{
			PaymentCode: pm.PaymentCode,
			FinalAmount: utils.FormatVNDCurrency(fmt.Sprint(int64(pm.Amount))),
		}

		for j := range pm.Items {
			pmi := pm.Items[j]

			for _, extra := range pmi.ItemExtraData {
				if extra.Value == nil {
					continue
				}

				if extra.Key == "PO_CODE" {
					tb.POCodes = append(tb.POCodes, fmt.Sprint(extra.Value))
				}
				if extra.Key == "VENDOR_BILL_CODE" {
					vbCode := fmt.Sprint(extra.Value)
					resp := model.VendorBillDB.QueryOne(model.VendorBill{VendorBillCode: vbCode})
					if resp.Status == common.APIStatus.Ok {
						vb := resp.Data.([]*model.VendorBill)[0]
						if vb.InvoiceNumber != "" {
							tb.InvoiceNumbers = append(tb.InvoiceNumbers, fmt.Sprint(vb.InvoiceNumber))
						}
					}
				}
			}

			if pmi.Amount == nil {
				pmi.Amount = utils.Pointer.WithFloat64(0)
			}
			tb.Amounts = append(tb.Amounts, utils.FormatVNDCurrency(fmt.Sprint(int64(*pmi.Amount))))
		}

		if pm.ReasonCode == "BILL" {
			tb.PaymentType = "Công nợ"
			vbTables = append(vbTables, tb)
		} else {
			tb.PaymentType = "Tạm ứng"
			tb.POCodes = []string{}
			for j := range pm.ExtraData {
				ex := pm.ExtraData[j]
				if ex.Key == "PREPAID_PO_CODE" {
					tb.POCodes = append(tb.POCodes, fmt.Sprint(ex.Value))
				}
			}
			poTables = append(poTables, tb)
		}
	}

	// ==================================== build email content ====================================
	// Define the last month's string representation for the email
	now := time.Now().In(utils.TimeZoneVN)
	// Map fields to be used in the email template
	mapFields := map[string]any{
		"PaymentDate":      now.Format("02/01/2006"),
		"VendorName":       vendor.DisplayName,
		"FinalTotalAmount": utils.FormatVNDCurrency(fmt.Sprint(int64(finalTotalAmount))),
		"PaymentCount":     utils.FormatVNDCurrency(fmt.Sprint(len(payments))),
		"ArrTablePO":       poTables,
		"ArrTableVB":       vbTables,
	}

	// build content
	// Parse the email content using the content template and mapped fields
	content, err := utils.ParseTemplate(action.EmailTemplate.ContentTemplate, mapFields)
	if err != nil {
		// Log the error and continue if template parsing fails
		fmt.Println(err)
		return
	}

	// build subject
	// Parse the email subject using the subject template and mapped fields
	subject, err := utils.ParseTemplate(action.EmailTemplate.SubjectTemplate, mapFields)
	if err != nil {
		// Log the error and continue if template parsing fails
		fmt.Println(err)
		return
	}

	// ==================================== build email end ====================================

	// mail
	// Set the sender name if not provided in the template
	if action.EmailTemplate.SenderName == "" {
		action.EmailTemplate.SenderName = "Công ty TNHH Dược Phẩm MEDX"
	}
	// Prepare the email input structure
	emailInput := &model.InputSendEmail{
		From: model.Email{
			Email: "<EMAIL>",
			User:  "Mail system",
			Name:  action.EmailTemplate.SenderName,
		},
		Subject: subject,
		Content: []model.Content{
			{
				Type:  "text/html",
				Value: content,
			},
		},
		To:  []model.Email{},
		Bcc: []model.Email{},
		Cc:  []model.Email{},
	}

	if vendor.AccountantEmail != "" {
		vendor.AccountantEmail = strings.Trim(vendor.AccountantEmail, " ")
		arr := strings.Split(vendor.AccountantEmail, ",")

		for _, email := range arr {
			emailInput.To = append(emailInput.To, model.Email{
				Email: strings.Trim(email, " "),
				User:  vendor.Username,
				Name:  vendor.DisplayName,
			})
		}
	}

	// Add "To" recipients from the email template
	for _, toMail := range action.EmailTemplate.ToTemplate {
		emailInput.Bcc = append(emailInput.Bcc, model.Email{ // Bcc
			Email: toMail,
		})
	}
	// Add "Bcc" recipients from the email template
	for _, bccMail := range action.EmailTemplate.BccTemplate {
		emailInput.Bcc = append(emailInput.Bcc, model.Email{
			Email: bccMail,
		})
	}
	// Add "Cc" recipients from the email template
	for _, ccMail := range action.EmailTemplate.CCTemplate {
		emailInput.Cc = append(emailInput.Cc, model.Email{
			Email: ccMail,
		})
	}

	// ==================================== send email ====================================

	// lock send mail under 1h
	ttl := time.Now().Add(time.Hour)
	lockResp := model.LockActionDB.Create(model.LockAction{
		Key:         "SEND_MAIL_PM:" + vendorCode,
		CreatedTime: &ttl,
	})
	if lockResp.Status != common.APIStatus.Ok {
		return
	}

	// Send the email asynchronously using the integration service
	integration.SendEmail(
		client.APIOption{
			Body: emailInput,
			Keys: []string{"MailPaymentVendor", "purchasing"},
		},
	)
}
